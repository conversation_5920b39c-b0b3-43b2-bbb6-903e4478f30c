import { Skeleton } from '@mui/material';
import Item from '../Item/index';
//eslint-disable-next-line no-unused-vars
import { ItemProps, ItemsListProps, SideBarMouseHandlerType } from '../interface';
import { StyledItemsList } from './styled';

const ItemsList: React.FC<ItemsListProps> = ({
  loading,
  itemsList,
  level,
  onLevel1Click,
  onLevel2Click,
  onLevel4Click,
  onMenuItemClick,
  sidebarExpanded,
  selectedLevel1,
  selectedLevel2,
  selectedMenu,
}) => {
  //Handlers
  function onItemClick(item: ItemProps, position: number, handlerType: SideBarMouseHandlerType) {
    if (level === 0) onMenuItemClick?.(item);
    if (level === 1) onLevel1Click?.(item, position, handlerType);
    if (level === 2) onLevel2Click?.(item, position, handlerType);
    if (level === 3) onLevel4Click?.(item);
  }

  //Jsx
  if (loading) {
    return <Skeleton variant="rounded" width={'100%'} height={'48px'} sx={{ backgroundColor: '#15324f', borderRadius: '10px' }} />;
  }
  return (
    <StyledItemsList>
      {itemsList?.map((item) => (
        <Item
          key={item.id}
          id={item.id}
          label={item.label}
          name={item.name}
          icon={item.icon}
          enabled={item.enabled}
          onClick={onItemClick}
          sidebarExpanded={sidebarExpanded}
          selectedLevel1={selectedLevel1}
          selectedLevel2={selectedLevel2}
          selectedMenu={selectedMenu}
          level={level}
        />
      ))}
    </StyledItemsList>
  );
};

export default ItemsList;
