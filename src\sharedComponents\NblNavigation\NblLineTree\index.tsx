import { SimpleTreeView } from '@mui/x-tree-view/SimpleTreeView';
import { StyledTreeItem } from './styled';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblDivider from 'sharedComponents/NblDivider';
import NblTypography from 'sharedComponents/NblTypography';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

type Node = Array<{ id: string; label: string; child?: Array<{ id: string; label: string }> | [] }> | [];

interface NblLineTreeProps {
  node: Node;
  onTreeClick?: (i: number) => void;
  onBranchClick?: (i: number, j: number) => void;
  expandedIndex?: number;
  selectedBranch?: number;
  renderElement?: React.ReactNode;
  branchHeading?: string;
}

const NblLineTree: React.FC<NblLineTreeProps> = ({
  node,
  onBranchClick,
  onTreeClick,
  expandedIndex = -1,
  renderElement,
  selectedBranch,
  branchHeading,
}) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();

  //Utils
  function getChildrenLabel(label: string, index: number) {
    return (
      <NblFlexContainer width="auto" height="auto" padding="0" margin="20px 0 0 -8px" alignItems="center">
        <NblDivider length="30px" opacity={1} color={theme.palette.secondary.shade5} strokeWidth={0} />
        <NblTypography variant="h5" weight={index === selectedBranch ? 'bold' : 'regular'}>
          {label}
        </NblTypography>
      </NblFlexContainer>
    );
  }
  //JSX
  return (
    <SimpleTreeView expandedItems={[node[expandedIndex]?.id]} sx={{ width: '100%', height: '100%' }}>
      {node.map((n, i) => (
        <StyledTreeItem key={n.id} itemId={n.id} label={n.label} onClick={() => onTreeClick?.(i)} sx={{ marginTop: '20px' }}>
          <NblTypography variant="subtitle2" color="shade6" margin="28px 0 -12px 38px" weight="bold">
            {branchHeading}
          </NblTypography>
          {branchHeading && n.child && n.child?.length < 1 && (
            <NblTypography variant="body1" color="shade1" margin="15px 0 -5px 38px">
              No {branchHeading}&apos;s
            </NblTypography>
          )}
          {n.child?.map((c, j) => (
            <StyledTreeItem key={c.id} itemId={c.id} label={getChildrenLabel(c.label, j)} onClick={() => onBranchClick?.(i, j)} />
          ))}
          {renderElement}
        </StyledTreeItem>
      ))}
    </SimpleTreeView>
  );
};

export default NblLineTree;
