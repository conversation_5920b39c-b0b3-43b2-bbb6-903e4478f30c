import React, { useState } from 'react';
// eslint-disable-next-line no-unused-vars
import NblFormContainer, { NblFormHelpers } from 'sharedComponents/NblContainers/NblFormContainer';
import { useApiService } from 'api/ApiService/context';
import { SuccessPageProps } from 'sharedComponents/NblSuccessfulPage';
import { CatalogLevel04Data, FormProps } from 'types';
import { validationSchema } from 'yupSchema/CreateSpecFormEKS';
import { isIconDefined } from 'utils/common';
import CreateSpecFormEKS from './CreateSpecFormEKS';
import { MultiENVProjectsResponse } from '../../../types/Interfaces/MultiENVProjectsResponse';
import withFetchMultiENVProjects from '../../../hoc/withFetchMultiENVProjects';

export type Credentials = {
  name: string;
  path: string;
  provider: string;
};

export type FormValues = {
  project: string;
  projectName: string;
  domain: string;
  application: string;
  environment: string;
  iacProjectName: string;
  namespaceId: number;
  zone_id: string;
  cluster_name: string;
  cluster_version: string;
  ami_id: string;
  min_size: number;
  desired_size: number;
  max_size: number;
  disk_size: number;
  vpc_id: string;
  aws_region: string;
  control_plane_subnets: Array<string>;
  public_subnets: Array<string>;
  node_group_subnets: Array<string>;
  create_alb: boolean;
  alb_internal: boolean;
  waf_name: string;
  default_action: string;
  falcon_cid: string;
  falcon_client: string;
  falcon_secret: string;
  acm_domain_name: string;
  credentials: Array<Credentials>;
};

interface SpecFormEKS extends FormProps {
  formDetails?: CatalogLevel04Data;
  projectData?: MultiENVProjectsResponse;
}

const SpecFormEKS: React.FunctionComponent<SpecFormEKS> = ({ formDetails, projectData }) => {
  const [responseData, setResponseData] = useState<SuccessPageProps>({
    buttonTitle: 'Track Request',
    title: 'EKS',
    requestId: '',
  });
  const Icon = formDetails && isIconDefined(formDetails.icon);
  const { apiSpecFlowService } = useApiService();
  const initialValues = {
    project: '',
    projectName: '',
    iacProjectName: '',
    domain: '',
    application: '',
    environment: '',
    namespaceId: 0,
    zone_id: '',
    cluster_name: '',
    cluster_version: '',
    ami_id: '',
    min_size: 0,
    desired_size: 0,
    max_size: 0,
    disk_size: 0,
    vpc_id: '',
    aws_region: '',
    control_plane_subnets: [],
    public_subnets: [],
    node_group_subnets: [],
    create_alb: false,
    alb_internal: false,
    waf_name: '',
    default_action: '',
    falcon_cid: '',
    falcon_client: '',
    falcon_secret: '',
    acm_domain_name: '',
    credentials: [
      { name: 'artifactory_username', path: '', provider: 'ssm' },
      { name: 'artifactory_password', path: '', provider: 'ssm' },
      { name: 'splunk_token', path: '', provider: 'ssm' },
      { name: 'datadog_app_key', path: '', provider: 'ssm' },
      { name: 'datadog_api_key', path: '', provider: 'ssm' },
      { name: 'contrast_api_key', path: '', provider: 'ssm' },
      { name: 'contrast_service_key', path: '', provider: 'ssm' },
      { name: 'contrast_username', path: '', provider: 'ssm' },
    ],
  };

  const handleSubmitForm = (values: FormValues, nblFormHelpers: NblFormHelpers<FormValues>) => {
    const { environment, projectName } = values;
    const payload = {
      platformContext: {
        catalogId: formDetails?.id,
        envId: environment,
        domainId: null,
      },
      ...values,
    };
    const selectedProject = projectData?.find((project) => project.id === projectName);
    payload.projectName = selectedProject?.name || projectName;
    apiSpecFlowService
      .createEKSWithSpec(payload)
      .then((res) => {
        if (res.status) {
          setResponseData({
            ...responseData,
            requestId: res.data.serviceRequestId || res.data.id,
          });
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        nblFormHelpers.setSubmitting(false);
      });
  };

  return (
    <>
      <NblFormContainer<FormValues>
        title={formDetails ? formDetails.name : ''}
        Icon={Icon}
        caption={'Fill the necessary details needed to create an EKS'}
        formInitialValues={initialValues}
        formValidationSchema={validationSchema}
        steps={[
          {
            caption: '',
            errorFields: [],
            icon: '',
            status: 'completed',
            title: '',
          },
        ]}
        formType="simple"
        onSubmit={handleSubmitForm}
        responseData={responseData}
        showPreview={false}
      >
        <CreateSpecFormEKS projectData={projectData} catalogShortName={formDetails?.shortName} />
      </NblFormContainer>
    </>
  );
};

export default withFetchMultiENVProjects(SpecFormEKS);
