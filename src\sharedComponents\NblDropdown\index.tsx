import React, { useState } from 'react';
import { isArray } from 'lodash';
//eslint-disable-next-line no-unused-vars
import { Checkbox, ListItemText, SelectChangeEvent, useTheme } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

import { StyledMenuItem, StyledSelect } from './styled';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { NebulaTheme } from 'NebulaTheme/type';

interface NblDropdownProps {
  options: Array<{ label: string; value: string | number }>;
  value: Array<string | number> | string | number;
  onChange?: (option: any) => void;
  Icon?: React.ElementType;
  multiple?: boolean;
}

const NblDropdown: React.FC<NblDropdownProps> = ({ options, value, onChange, Icon = KeyboardArrowDownIcon, multiple }) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();

  //States
  const [open, setOpen] = useState(false);

  //Local
  const palette = theme.palette.dropdown;

  //Handlers
  const handleChange = (event: SelectChangeEvent<any>) => {
    const {
      target: { value },
    } = event;
    if (multiple) {
      onChange?.(typeof value === 'string' ? value.split(',') : value);
    } else {
      onChange?.(value);
    }
  };

  //Renders
  const IconComponent = () => {
    return (
      <NblFlexContainer
        onClick={() => setOpen((prev) => !prev)}
        center
        width="25px"
        height="25px"
        borderRadius="50%"
        border={`2px solid ${palette.border}`}
        flex="0 0 25px"
        cursor="pointer"
      >
        <Icon sx={{ color: palette.border }} />
      </NblFlexContainer>
    );
  };

  //JSX
  return (
    <StyledSelect
      multiple={multiple}
      variant="outlined"
      value={value}
      onChange={handleChange}
      onClose={() => setOpen(false)}
      IconComponent={IconComponent}
      {...(multiple && { renderValue: (selected: any) => selected?.join(', ') })}
      MenuProps={{
        PaperProps: {
          sx: {
            background: palette.menuBg,
            border: `1px solid ${palette.menuBorder}`,
            borderRadius: '8px',
            maxHeight: '300px',
          },
        },
        MenuListProps: {
          sx: {
            border: 'none',
          },
        },
      }}
      open={open}
    >
      {options.map((option) => (
        <StyledMenuItem
          key={option.label}
          value={option.value}
          className={'menuItem'}
          sx={{ ...(option.value == value && { color: palette.text }) }}
        >
          {multiple && isArray(value) ? (
            <>
              <Checkbox checked={value?.includes(option.value)} color="info" />
              <ListItemText primary={option.label} />
            </>
          ) : (
            option.label
          )}
        </StyledMenuItem>
      ))}
    </StyledSelect>
  );
};

export default NblDropdown;
