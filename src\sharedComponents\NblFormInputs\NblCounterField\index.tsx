import React, { useEffect, useState } from 'react';
import NblInputLabel from 'sharedComponents/NblFormInputs/NblInputLabel';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblInputHelperText from 'sharedComponents/NblFormInputs/NblInputHelperText';
import { StyledCounterOutlinedInput, StyledButton } from './styled';
import { DecreaseVMCountIcon, IncreaseVMCountIcon } from 'assets/images/icons/custom-icons';

interface CounterFieldProps {
  type: 'number' | 'text';
  disabled?: boolean;
  error?: boolean;
  label: string;
  name: string;
  readOnly?: boolean;
  handleChange: (value: number) => void;
  helperText?: string;
  mandatory?: boolean;
  handleBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  initialValue: number;
  maxValue: number;
  width?: string | number;
  endAdornment?: React.ReactElement;
}

const NblCounterField: React.FunctionComponent<CounterFieldProps> = ({
  type,
  disabled,
  error,
  label,
  name,
  readOnly,
  handleBlur,
  onMouseEnter,
  onMouseLeave,
  mandatory = false,
  helperText = '',
  initialValue = 1,
  maxValue = 10,
  handleChange,
  width,
  endAdornment
}: CounterFieldProps) => {
  const [localCount, setLocalCount] = useState<number>(initialValue);

  useEffect(() => {
    if (localCount !== initialValue) {
      setLocalCount(initialValue);
    }
  }, [initialValue]);

  const handleIncrement = () => {
    if (localCount < maxValue) {
      setLocalCount((prevCount) => {
        const newCount = prevCount + 1;
        if (handleChange) handleChange(newCount);
        return newCount;
      });
    }
  };

  const handleDecrement = () => {
    if (localCount > 1) {
      setLocalCount((prevCount) => {
        const newCount = prevCount - 1;
        if (handleChange) handleChange(newCount);
        return newCount;
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Number(e.target.value);
    if (newValue >= 1 && newValue <= maxValue) {
      setLocalCount(newValue);
      if (handleChange) handleChange(newValue);
    }
  };

  return (
    <NblFlexContainer direction="column">
      <NblInputLabel label={label} name={name} mandatory={mandatory} disabled={disabled} />
      <NblFlexContainer alignItems="center" height="auto">
        <StyledButton onClick={handleDecrement} disabled={disabled || readOnly}>
          <DecreaseVMCountIcon />
        </StyledButton>
        <StyledCounterOutlinedInput
          type={type}
          id={name}
          value={localCount}
          name={name}
          onChange={handleInputChange}
          onBlur={handleBlur}
          disabled={disabled}
          readOnly={readOnly}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          error={Boolean(error)}
          width={width}
          endAdornment={endAdornment}
        />
        <StyledButton onClick={handleIncrement} disabled={disabled || readOnly}>
          <IncreaseVMCountIcon />
        </StyledButton>
      </NblFlexContainer>
      <NblInputHelperText error={Boolean(error)} helperText={helperText} />
    </NblFlexContainer>
  );
};

export default NblCounterField;
