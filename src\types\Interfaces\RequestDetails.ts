import MetaData from './MetaData';
import DownStreamError from './DownStreamError';
import { ApprovalDetail } from 'api/ApiService/type';
import { RequestStatus, RequestType } from 'types/Enums';
import CreateAvailableNetwork from './CreateAvailableNetwork';
import VmDetails from './VmDetails';
import CreateOrganization from './CreateOrganization';
import FirewallDetails from './FirewallDetails';
import DNPDetails from './DNPDetails';
import StorageS3Details from './StorageS3Details';
import BlueVmdetails from './BlueVmDetails';
import DbDetails from './DbDetails';
import ReserveIPAddress from './ReserveIPAddress';
import CreateStorageNFS from './CreateStorageNFS';
import OnBoardingDeviceDetails from './OnBoardingDeviveDetails';
import ManageSubAccountGroupsAndUsers from './ManageSubAccountGroupsAndUsers';
import CreatePermissionSet from './CreatePermissionSet';
import OffBoardingDeviceDetails from './OffboardDeviceDetail';
import FirewallMigrateDetails from './FirewallMigrateDetails';
import CreateSubAccount from './CreateSubAccount';
import ReleaseIPAddress from './ReleaseIPAddress';
import { CommonFirewallRequest } from './CommonFirewallRequest';
import ManagePermissionRemove from './ManagePermissionRemove';
import DeleteVmDetails from './DeleteVmDetails';
import DeleteDBDetails from './DeleteDBDetails';
import ManageSubAccountGroups from './ManageSubAccountGroup';
import CertificateFormValues from './CertificateFormValues';
import CatalogAccessRequest from './CatalogAccessRequest';
import OnboardNewProject from './OnboardNewProject';
import OnboardGroupRequest from './OnboardGroupRequest';
import CreateSpecFormS3 from './CreateSpecFormS3';
import CreateSpecFormEC2 from './CreateSpecFormEC2';
import CreateSpecFormEKS from './CreateSpecFormEKS';
import Namespace from './Nampespace';
import VPCDetails from './VPCDetails';
import ReconfigureVM from './ReconfigureVM';
import ZTPSingleDeviceData from './ZTPSingleDeviceDetails';

export type CatalogRequestType =
  | RequestType.CREATE_AVAILABLE_NETWORK
  | RequestType.COMMON_FIREWALL_POLICY
  | RequestType.CREATE_VM_LINUX89
  | RequestType.CREATE_ORGANIZATION
  | RequestType.DNP
  | RequestType.CREATE_STORAGE_S3
  | RequestType.CREATE_DB_MONGODB6
  | RequestType.CREATE_DB_MONGODB7
  | RequestType.CREATE_DB_POSTGRESDB15
  | RequestType.CREATE_DB_POSTGRESDB16
  | RequestType.CREATE_DB_REDISDB6
  | RequestType.CREATE_DB_REDISDB7
  | RequestType.CREATE_DB_ORACLEDB19C
  | RequestType.CREATE_DB_ORACLEDB21C
  | RequestType.RESERVE_IP
  | RequestType.CREATE_STORAGE_NFS
  | RequestType.AWS_CREATE_USER
  | RequestType.AWS_REMOVE_USER
  | RequestType.MANAGE_PERMISSION_SET_CREATE
  | RequestType.REMOVE_DEVICE
  | RequestType.FIREWALL_V2
  | RequestType.MANAGE_PERMISSION_SET_REMOVE
  | RequestType.MANAGE_SUB_ACCOUNT_GROUP_PERMISSION;

export type RequestPayloadMap = {
  [RequestType.CREATE_AVAILABLE_NETWORK]: CreateAvailableNetwork;
  [RequestType.COMMON_FIREWALL_POLICY]: {};
  [RequestType.CREATE_ORGANIZATION]: CreateOrganization;
  [RequestType.CREATE_VM_LINUX89]: VmDetails;
  [RequestType.FIREWALL_V2]: FirewallDetails;
  [RequestType.DNP]: DNPDetails;
  [RequestType.CREATE_STORAGE_S3]: StorageS3Details;
  [RequestType.CREATE_LINUX_CORPNET]: BlueVmdetails;
  [RequestType.CREATE_DB_MONGODB6]: DbDetails;
  [RequestType.CREATE_DB_MONGODB7]: DbDetails;
  [RequestType.CREATE_DB_REDISDB6]: DbDetails;
  [RequestType.CREATE_DB_REDISDB7]: DbDetails;
  [RequestType.CREATE_DB_POSTGRESDB15]: DbDetails;
  [RequestType.CREATE_DB_POSTGRESDB16]: DbDetails;
  [RequestType.CREATE_DB_ORACLEDB19C]: DbDetails;
  [RequestType.CREATE_DB_ORACLEDB21C]: DbDetails;
  [RequestType.RESERVE_IP]: ReserveIPAddress;
  [RequestType.CREATE_STORAGE_NFS]: CreateStorageNFS;
  [RequestType.ADD_DEVICE]: OnBoardingDeviceDetails;
  [RequestType.AWS_CREATE_USER]: ManageSubAccountGroupsAndUsers;
  [RequestType.AWS_REMOVE_USER]: ManageSubAccountGroupsAndUsers;
  [RequestType.MANAGE_PERMISSION_SET_CREATE]: CreatePermissionSet;
  [RequestType.REMOVE_DEVICE]: OffBoardingDeviceDetails;
  [RequestType.FIREWALL]: FirewallMigrateDetails;
  [RequestType.AWS_CREATE_SUBACCOUNT]: CreateSubAccount;
  [RequestType.RELEASE_IP]: ReleaseIPAddress;
  [RequestType.MANAGE_PERMISSION_SET_REMOVE]: ManagePermissionRemove;
  [RequestType.DELETE_VM]: DeleteVmDetails;
  [RequestType.DELETE_DB]: DeleteDBDetails;
  [RequestType.MANAGE_SUB_ACCOUNT_GROUP_PERMISSION]: ManageSubAccountGroups;
  [RequestType.INTERNAL_CERTIFICATE]: CertificateFormValues;
  [RequestType.CATALOG_ACCESS_REQUEST]: CatalogAccessRequest;
  [RequestType.ONBOARD_PROJECT]: OnboardNewProject;
  [RequestType.ONBOARD_GROUP]: OnboardGroupRequest;
  [RequestType.SPEC_FLOW_S3]: CreateSpecFormS3;
  [RequestType.SPEC_FLOW_EC2]: CreateSpecFormEC2;
  [RequestType.SPEC_FLOW_EKS]: CreateSpecFormEKS;
  [RequestType.CREATE_VM_LINUX89_V3]: VmDetails;
  [RequestType.CREATE_NAMESPACE]: Namespace;
  [RequestType.PUBLIC_CLOUD_VPC]: VPCDetails;
  [RequestType.RECONFIGURE_VM]: ReconfigureVM;
  [RequestType.ZERO_TOUCH_PROVISIONING]: ZTPSingleDeviceData;
};

export interface RequestMetaData {
  id: string;
  serviceRequestId: string;
  status: RequestStatus;
  requesterEmail: string;
  canApprove: boolean;
  systemUpdate?: any;
  requestType?: string;
  downstreamResponseData?: CommonFirewallRequest['downstreamResponseData'] & {
    resubmittedId?: string;
    network?: string;
  };
  downstreamError?: DownStreamError[];
  catalogStepsId?: string;
  crqToggle?: boolean;
  dapFlag?: string;
}

interface CommonDetails extends RequestMetaData {
  name?: string;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  requestType?: string;
  approvalDetails?: ApprovalDetail[] | any;
  datacenter?: string;
  createdBy?: string;
  metadata?: MetaData;
  downstreamError?: DownStreamError[];
  resourceDetails?: any;
  catalogItem?: string;
  systemUpdate?: any;
  projectName?: string;
  approvalStatus?: string;
  startDate?: string;
  updatedAt?: string;
}

type RequestDetails<T extends CatalogRequestType> = CommonDetails & {
  requestType: T;
  payload: RequestPayloadMap[T];
  status?: string;
  catalogStepsId?: string;
  id: string;
  resourceId: string;
};

export default RequestDetails;
