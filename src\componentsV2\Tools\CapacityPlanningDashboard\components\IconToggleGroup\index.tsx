import { Box, Divider, IconButton, styled, useTheme } from '@mui/material';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';

interface StyledIconButtonProps {
  selected: boolean;
}

const StyledIconButton = styled(IconButton)<StyledIconButtonProps>(({ selected }) => {
  const theme: NebulaTheme = useTheme();
  return {
    backgroundColor: selected ? `${theme.palette.primary.main}` : 'transparent',
    '&:active': {
      backgroundColor: selected ? `${theme.palette.primary.main}` : `${theme.palette.secondary.shade3}`,
    },
    '&:hover': {
      backgroundColor: selected ? `${theme.palette.primary.main}` : `${theme.palette.secondary.shade3}`,
    },
  };
});

interface ToggleIcon {
  label: string;
  value: string;
  component: JSX.Element;
  disabled?: boolean;
}

interface IconToggleGroupProps {
  icons: ToggleIcon[];
  selected: string | undefined;
  onChange: Function;
}

const IconToggleGroup: React.FunctionComponent<IconToggleGroupProps> = ({ icons, selected, onChange }: IconToggleGroupProps) => {
  const theme: NebulaTheme = useTheme();
  return (
    <Box
      // spacing={0}
      display={'flex'}
      border={`2px solid ${theme.palette.secondary.shade3}`}
      borderRadius="10px"
      alignItems={'center'}
      sx={{ backgroundColor: `${theme.palette.secondary.shade3}` }}
      height="40px"
    >
      {icons?.map((icon: any, index: any) => {
        const isFirst = index === 0;
        const isLast = index === icons.length - 1;
        return (
          <Box key={index}>
            <StyledIconButton
              disabled={icon.disabled}
              selected={selected === icon.value}
              onClick={() => onChange(icon.value)}
              color="inherit"
              style={{ borderRadius: isFirst ? '10px 0 0 10px' : isLast ? '0 10px 10px 0' : 0 }}
            >
              {icon.component}
            </StyledIconButton>
            {index < icons.length - 1 && (
              <Divider orientation="vertical" flexItem style={{ borderColor: `${theme.palette.primary.main}` }} />
            )}
          </Box>
        );
      })}
    </Box>
  );
};

export default IconToggleGroup;
