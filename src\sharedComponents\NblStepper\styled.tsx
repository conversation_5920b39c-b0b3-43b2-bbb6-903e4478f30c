import { Typography } from '@mui/material';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledTypography = styled(Typography)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography } = theme;
  return {
    '&.MuiTypography-subtitle2': {
      ...typography.subtitle2,
      whiteSpace: 'nowrap',
      fontWeight: theme.typography.bold.fontWeight,
    },
    '&.MuiTypography-body3': {
      ...typography.body3,
      letterSpacing: '-0.24px',
      fontWeight: typography.medium.fontWeight,
    },
  };
});
