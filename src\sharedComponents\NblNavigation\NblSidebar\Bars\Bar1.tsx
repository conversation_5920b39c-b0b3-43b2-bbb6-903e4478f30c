import { Typography, useTheme } from '@mui/material';
import React from 'react';

import ItemsList from '../ItemsList';
//eslint-disable-next-line no-unused-vars
import { Bar1Props } from '../interface';
import { StyledBar, StyledItemListContainer, StyledLogoContainer } from './styled';
import NblDivider from 'sharedComponents/NblDivider';
import { NebulaTheme } from 'NebulaTheme/type';

const Bar1: React.FC<Bar1Props> = ({
  itemsList,
  onLevel1Click,
  onLogoClick,
  onMenuItemClick,
  selectedLevel1,
  selectedMenu,
  sidebarExpanded,
}) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();

  //Jsx
  return (
    <StyledBar expanded={sidebarExpanded} className={'bar1'}>
      <StyledLogoContainer expanded={sidebarExpanded} onClick={onLogoClick}>
        <img src={theme.nebulaLogo} id={'nebula-logo'} alt="Nebula Logo" className={'logoImage'} />
        <Typography variant="h3" className={'logoText'}>
          Nebula
        </Typography>
      </StyledLogoContainer>
      <StyledItemListContainer bar={1} listStartPosition={0}>
        <ItemsList
          loading={itemsList[0].loading}
          itemsList={itemsList[0].items}
          level={1}
          onLevel1Click={onLevel1Click}
          selectedLevel1={selectedLevel1}
          sidebarExpanded={sidebarExpanded}
        />

        {itemsList.slice(1).map((otherItems, index) => (
          <React.Fragment key={index}>
            <NblDivider orientation="horizontal" length="100%" borderRadius={1} mt={10} mb={10} />
            <ItemsList
              loading={otherItems.loading}
              itemsList={otherItems.items}
              level={0}
              onMenuItemClick={onMenuItemClick}
              sidebarExpanded={sidebarExpanded}
              selectedMenu={selectedMenu}
            />
          </React.Fragment>
        ))}
      </StyledItemListContainer>
    </StyledBar>
  );
};

export default Bar1;
