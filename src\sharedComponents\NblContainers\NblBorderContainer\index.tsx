import React, { ReactElement } from 'react';

import { StyledBorderContainer } from './styled';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';

interface NblBorderContainerProps {
  children: React.ReactNode;
  width?: string;
  height?: string;
  borderRadius?: string;
  padding?: string;
  backgroundColor?: string;
  onClick?: (e: React.MouseEvent) => void;
  ariaDisabled?: boolean;
  title?: string;
  border?: string;
  borderType?: 'dashed' | 'solid';
  link?: {
    text: string;
    icon?: ReactElement;
    navigateHandler: () => void;
  };
  isHover?: boolean;
  margin?: string;
  minHeight?: string;
}

const NblBorderContainer: React.FC<NblBorderContainerProps> = ({
  children,
  width = '100%',
  height = '100%',
  minHeight,
  borderRadius = '10px',
  padding = '0',
  backgroundColor = 'initial',
  title,
  link,
  onClick,
  ariaDisabled,
  border,
  borderType = 'solid',
  isHover = false,
  margin,
}) => {
  return (
    <StyledBorderContainer
      width={width}
      height={height}
      minHeight={minHeight}
      borderRadius={borderRadius}
      padding={padding}
      backgroundColor={backgroundColor}
      onClick={onClick}
      border={border}
      borderType={borderType}
      aria-disabled={ariaDisabled}
      isHover={isHover}
      margin={margin}
    >
      {title && (
        <NblFlexContainer justifyContent={'space-between'} width="auto" height="auto">
          <NblTypography variant="subtitle1" color={'shade1'} weight={'bold'} textTransform={'capitalize'}>
            {title}
          </NblTypography>
          {link?.text && (
            <NblFlexContainer width="auto" alignItems="center" spacing={1} height="auto" cursor={'pointer'} onClick={link?.navigateHandler}>
              {link.icon}
              <NblTypography variant={'body3'} weight={'medium'} color={'shade11'} textTransform={'capitalize'}>
                {link.text}
              </NblTypography>
            </NblFlexContainer>
          )}
        </NblFlexContainer>
      )}
      {children}
    </StyledBorderContainer>
  );
};

export default NblBorderContainer;
