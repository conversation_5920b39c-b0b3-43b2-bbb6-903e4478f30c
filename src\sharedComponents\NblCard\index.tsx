import React from 'react';
import StarOutlineRoundedIcon from '@mui/icons-material/StarOutlineRounded';
import StarRoundedIcon from '@mui/icons-material/StarRounded';
import icons from 'assets/images/icons';
import NblChip from '../NblChip';
import NblTooltip from '../NblTooltip';
import { StyledCard, StyledFooter, StyledHeader, StyledTypography, StyledTitle } from './styled';

interface NblCardProps {
  id: string;
  onClickCard: (v: any) => void;
  onFavourite?: (v: any) => void;
  isFavourite?: boolean;
  expanded?: boolean;
  color?: 'primary' | 'success' | 'warning' | 'error';
  shortName: string;
  label?: string;
  chip?: string;
  title: string;
  description?: string;
  icon?: string;
  disabled?: boolean;
  width?: string;
  active?: boolean;
  maxWidth?: string;
  tooltipMessage?: string;
}

const NblCard: React.FC<NblCardProps> = ({
  id,
  onClickCard,
  onFavourite,
  isFavourite,
  expanded = true,
  color = 'primary',
  icon,
  shortName,
  label,
  chip,
  title,
  description,
  disabled = false,
  width,
  active = false,
  maxWidth,
  tooltipMessage = ''
}) => {
  //Handlers
  function handlekeyDown(e: React.KeyboardEvent<HTMLDivElement>) {
    if (e.key === 'Enter' || e.key === '') {
      e.preventDefault();
      onCardClickHandler();
    }
  }
  function onCardClickHandler(e?: React.MouseEvent) {
    e?.stopPropagation();
    onClickCard?.(shortName);
  }

  function onFavouriteHandler(e: React.MouseEvent) {
    e.stopPropagation();
    onFavourite?.(id);
  }

  //Renders
  const renderIcon = (icon: string) => {
    /* @ts-ignore */
    const Icon = icons[icon];
    if (!Icon) return null;
    return (
      <Icon
        data-testid="mui-icon"
        style={{
          fontSize: expanded ? '1.5rem' : '1rem',
        }}
      />
    );
  };

  //Jsx
  return (
    <NblTooltip tooltipMessage={tooltipMessage}>
      <StyledCard
        id={`${id}-card`}
        expanded={expanded}
        disabled={disabled}
        width={width}
        maxWidth={maxWidth}
        data-testid="card"
        role="button"
        onClick={disabled ? () => { } : onCardClickHandler}
        tabIndex={disabled ? -1 : 0}
        onKeyDown={handlekeyDown}
        aria-disabled={disabled}
        active={active}
      >
        <StyledHeader expanded={expanded}>
          {expanded && label && (
            <StyledTypography variant="body3" title={label}>
              {label}
            </StyledTypography>
          )}
          {expanded && chip && (
            <span style={{ position: 'absolute', right: -10 }}>
              <NblChip label={chip} id={id} color={color} />
            </span>
          )}
          <StyledTitle expanded={expanded}>
            {icon && renderIcon(icon)}
            <StyledTypography expanded={expanded} variant="subtitle1" title={title}>
              {title}
            </StyledTypography>
          </StyledTitle>
        </StyledHeader>
        {expanded && (
          <StyledFooter>
            <StyledTypography variant="subtitle2" title={description}>
              {description}
            </StyledTypography>
            <button data-testid="fav-button" onClick={onFavouriteHandler} disabled={disabled}>
              {isFavourite ? <StarRoundedIcon color="action" /> : <StarOutlineRoundedIcon color="action" />}
            </button>
          </StyledFooter>
        )}
      </StyledCard>
    </NblTooltip>
  );
};

export default NblCard;
