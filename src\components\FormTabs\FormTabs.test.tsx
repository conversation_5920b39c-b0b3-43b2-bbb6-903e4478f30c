import { act, render, fireEvent } from '@testing-library/react';

import FormTabs from '.';
import ThemeProvider from 'mock/ThemeProvider';

describe('Render the FormTabs', () => {
  const onTabChangeHandler = jest.fn();
  const TabsList = [
    {
      label: 'VM details',
      id: 'vmDetails',
      errorFields: [],
      icon: null,
    },
    {
      label: 'DB details',
      id: 'dbDetails',
      errorFields: [],
      icon: null,
    },
  ];

  test('Should render the form tabs', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <FormTabs tabsList={TabsList} currentTab={0} onTabChangeHandler={onTabChangeHandler} />
        </ThemeProvider>
      )
    );

    expect(getByText(TabsList[0]['label'])).toBeInTheDocument();
    expect(getByText(TabsList[1]['label'])).toBeInTheDocument();

    fireEvent.click(getByText(TabsList[0]['label']));
    expect(onTabChangeHandler).toHaveBeenCalledTimes(1);
  });
});
