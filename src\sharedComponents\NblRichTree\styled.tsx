import { styled } from '@mui/material';
import { TreeItem, treeItemClasses } from '@mui/x-tree-view/TreeItem';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledTreeItem = styled(TreeItem)<{ theme?: NebulaTheme }>(({ theme }) => ({
  color: theme.palette.primary.main,
  borderLeft: `1px solid ${theme.palette.secondary.shade4}`,
  paddingBottom: theme.spacing(0),
  marginBottom: theme.spacing(0),
  '& *:not(button,hr,.Mui-selected .MuiTreeItem-label)': {
    backgroundColor: 'transparent !important',
  },
  [`& .${treeItemClasses.content} `]: {
    position: 'relative',
    padding: theme.spacing(1, 0, 1, 2.5),
    borderRadius: theme.spacing(0.5),
    [`& .${treeItemClasses.label}`]: {
      position: 'initial',
      padding: theme.spacing(0.5),
      fontSize: theme.typography.h6.fontSize,
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '26px',
        left: '0',
        width: 12,
        borderTop: `1px solid ${theme.palette.secondary.shade4}`,
      }
    },
    [`&.Mui-expanded`]: {
      [`& .${treeItemClasses.label}`]: {
        fontWeight: 'bold'
      }
    }
  },
  [`& .MuiTreeItem-content.Mui-selected`]: {
    [`& .${treeItemClasses.label}`]: {
      backgroundColor: theme.palette.secondary.shade3,
    }
  },
  [`& .${treeItemClasses.iconContainer}`]: {
    position: 'absolute',
    right: '10px',
    top: '18px',
  },
  [`& .${treeItemClasses.groupTransition}`]: {
    marginTop: theme.spacing(0.25),
    marginLeft: theme.spacing(5),
    paddingLeft: theme.spacing(0),
    borderLeft: `1px solid ${theme.palette.secondary.shade4}`,
  },
}));
