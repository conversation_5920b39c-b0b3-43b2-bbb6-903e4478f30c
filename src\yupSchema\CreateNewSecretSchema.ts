import { SecretType } from 'types/Enums/SecretType';
import * as yup from 'yup';

export const CreateNewSecretSchema = yup.object().shape({
  type: yup.string().required('Please select secret type'),
  namespaceName: yup.string(),
  path: yup.string().required('Path is required'),
  vaultKey: yup.string().required('Vault key is required'),
  vaultPassword: yup.string().required('Vault Password is required'),
  policyId: yup.string().when('type', {
    is: SecretType.RotatingSecret,
    then: (schema) => schema.required('Please select Secret policy'),
    otherwise: (schema) => schema.notRequired().nullable(),
  }),
  isPasswordValid: yup
    .boolean()
    .oneOf([true], 'Password must be valid')
    .required('Password validation is required'),
  userNameKey: yup.string().when('type', {
    is: SecretType.RotatingSecret,
    then: (schema) => schema.required('Username key is required'),
    otherwise: (schema) => schema.notRequired().nullable(),
  }),
  userNamePassword: yup.string().when('type', {
    is: SecretType.RotatingSecret,
    then: (schema) => schema.required('Please provide username'),
    otherwise: (schema) => schema.notRequired().nullable(),
  }),
  secretTTLInHours: yup.string().when('type', {
    is: SecretType.RotatingSecret,
    then: (schema) => schema.required('Please provide Rotation interval'),
    otherwise: (schema) => schema.notRequired().nullable(),
  }),
  nextRotationDate: yup
    .string()
    .nullable()
    .when('type', {
      is: SecretType.RotatingSecret,
      then: (schema) => schema.required('Please provide First run date'),
      otherwise: (schema) => schema.notRequired(),
    }),
  notifyBeforeTokenExpiry: yup.boolean().nullable().notRequired(),
});
