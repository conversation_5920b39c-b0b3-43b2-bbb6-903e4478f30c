import { useEffect, useMemo, useState } from 'react';
// eslint-disable-next-line no-unused-vars
import { GridRenderCellParams } from '@mui/x-data-grid';
import { AddDiskIcon } from 'assets/images/icons/custom-icons';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { NblFormErrors, NblFormTouched, useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import NblAutoComplete from 'sharedComponents/NblFormInputs/NblAutoComplete';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
// eslint-disable-next-line no-unused-vars
import { ColumnData, NblTable } from 'sharedComponents/NblTable';
import { generateEnum } from 'utils/common';
import { FormValues } from '..';
import NblAlert from 'sharedComponents/NblFormInputs/NblAlert';
import ActionsColumn from 'componentsV2/Administration/ActionsColumn';
import NblTypography from 'sharedComponents/NblTypography';
import { ResourcesDetails } from 'types';
import { useApiService } from 'api/ApiService/context';
import TAG_ACTION from 'types/Enums/EditVMWareTagsActions';

export interface TagRow {
  tagName: string;
  tagValue: string;
  action: TAG_ACTION.add | undefined;
  isExisting: boolean;
}

export const defaultRow: TagRow = {
  tagName: '',
  tagValue: '',
  isExisting: false,
  action: TAG_ACTION.add,
};

interface EditVMWareTagListProps {
  resourceData: ResourcesDetails;
  formErrors?: NblFormErrors<TagRow>[];
  formTouched?: NblFormTouched<TagRow>[];
}

const EditVMWareTagList: React.FC<EditVMWareTagListProps> = ({ resourceData, formErrors, formTouched }) => {
  const FIELD_NAMES = generateEnum(defaultRow);
  const { nblFormProps, nblFormValues } = useNblForms<FormValues>();
  const { apiAdministrationService } = useApiService();
  const [tagKeys, setTagKeys] = useState<Array<{ label: string; value: any }>>([]);
  const rows = useMemo(() => {
    return (nblFormValues.tags || []).map((item, index) => ({
      id: index,
      ...item,
    }));
  }, [nblFormValues.tags]);

  useEffect(() => {
    apiAdministrationService.getTagKeys().then((res) => {
      if (res.status) {
        setTagKeys(res.data.tagKeys.map((val) => ({ label: val.name, value: val.name })));
      }
    });
  }, []);

  useEffect(() => {
    if (resourceData?.tags?.length) {
      const initialTags = resourceData.tags.map((tag) => ({
        tagName: tag.name,
        tagValue: tag.value,
        isExisting: true, // Set this flag to indicate an existing tag
      }));
      nblFormProps.setFieldValue('tags', initialTags);
    }
  }, [resourceData]);

  const handleAddRow = () => {
    const newRows = [...(nblFormValues.tags || [])];
    newRows.push({ ...defaultRow, action: TAG_ACTION.add });
    nblFormProps.setFieldValue('tags', newRows);
  };

  const handleDeletePermission = (id: number) => {
    const tag = nblFormValues.tags[id];
    if (tag?.isExisting) return; // Prevent deleting existing tags

    const updatedRows = nblFormValues.tags.filter((_, index) => index !== id);
    nblFormProps.setFieldValue('tags', updatedRows);
  };

  function renderHeader(label: string, mandatory: boolean) {
    return (
      <NblFlexContainer>
        <NblTypography variant="h5" color="shade1" weight="semiBold">
          {label}
        </NblTypography>
        {mandatory && (
          <NblTypography variant="h5" color="shade9">
            *
          </NblTypography>
        )}
      </NblFlexContainer>
    );
  }

  const columns: ColumnData[] = [
    {
      field: FIELD_NAMES.tagName,
      headerName: '',
      renderHeader: () => renderHeader('Tag Key', false),
      flex: 1,
      editable: true,
      renderCell: (params: GridRenderCellParams) => {
        const currentRowIndex = +params.id;
        const currentTag = nblFormValues.tags[currentRowIndex];

        // Collect used tagNames from all rows except the current one
        const usedTagNames = (nblFormValues.tags || []).filter((_, index) => index !== currentRowIndex).map((tag) => tag.tagName);

        // Filter out used tag keys
        const filteredOptions = tagKeys.filter((tag) => !usedTagNames.includes(tag.value));

        return (
          <NblAutoComplete
            name={`tags[${currentRowIndex}].${FIELD_NAMES.tagName}`}
            value={currentTag?.tagName}
            onChange={(selectedVal) => nblFormProps.setFieldValue(`tags[${currentRowIndex}].${FIELD_NAMES.tagName}`, selectedVal)}
            handleBlur={nblFormProps.handleBlur}
            placeholder="Select the tag"
            label={' '}
            disabled={currentTag?.isExisting}
            options={filteredOptions.map((tag) => ({ value: tag.label, label: tag.value }))}
            helperText={formErrors?.[currentRowIndex]?.tagName || ' '}
            error={Boolean(formTouched?.[currentRowIndex]?.tagName && formErrors?.[currentRowIndex]?.tagName)}
          />
        );
      },
    },
    {
      field: FIELD_NAMES.tagValue,
      headerName: '',
      renderHeader: () => renderHeader('Tag Value', false),
      flex: 1,
      editable: true,
      disableColumnMenu: true,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <NblFlexContainer padding="8px">
            <NblTextField
              type={'text'}
              name={`tags[${params.id}].${FIELD_NAMES.tagValue}`}
              value={nblFormValues.tags[+params.id]?.tagValue}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              disabled={nblFormValues.tags[+params.id]?.isExisting}
              helperText={formErrors?.[+params.id]?.tagValue || ' '}
              error={Boolean(formTouched?.[+params.id]?.tagValue && formErrors?.[+params.id]?.tagValue)}
            />
          </NblFlexContainer>
        );
      },
    },

    {
      field: 'actions',
      headerName: '',
      renderHeader: () => renderHeader('Actions', false),
      disableColumnMenu: true,
      sortable: false,
      width: 100,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        return (
          <NblFlexContainer padding="4px">
            <ActionsColumn
              disableEdit
              permissions={{ canDelete: !nblFormValues.tags[+params.id]?.isExisting }}
              onDeleteHandler={() => handleDeletePermission(params.row.id)}
              showConfirmWarning={false}
            />
          </NblFlexContainer>
        );
      },
    },
  ];
  return (
    <NblFlexContainer direction="column" padding="40px 0 0 0">
      <NblFlexContainer>
        <NblFlexContainer justifyContent="start">
          <NblTypography variant={'h4'} color="shade1" weight="bold" padding="15px 0 0 0">
            Tags
          </NblTypography>
        </NblFlexContainer>
        <NblFlexContainer justifyContent="end" height="auto">
          <NblButton
            buttonID="buttonAddVolumes"
            variant="contained"
            color="info"
            margin={'10px'}
            onClick={handleAddRow}
            startIcon={<AddDiskIcon />}
            disabled={nblFormProps.errors.tags && !Array.isArray(nblFormProps.errors.tags) ? true : undefined}
          >
            Add Tags
          </NblButton>
          {nblFormProps.errors.tags && Boolean(nblFormProps.errors.tags) && !Array.isArray(nblFormProps.errors.tags) && (
            <NblFlexContainer width="auto" height="40px" center margin="10px 0 0 0">
              <NblAlert variant={'error'} title={''} message={nblFormProps.errors.tags} />
            </NblFlexContainer>
          )}
        </NblFlexContainer>
      </NblFlexContainer>
      <NblBorderContainer padding="10px" height="500px">
        {rows.length > 0 ? (
          <NblFlexContainer>
            <NblTable columns={columns} rows={rows} showGridBorder={false} showResetFilter={false} hideFooterAndPagination rowSize={'40'} />
          </NblFlexContainer>
        ) : (
          <NblFlexContainer center>
            <NblTypography variant={'subtitle1'} color="shade1" textAlign="center" margin="60px 0 0 0">
              No Tags Added
            </NblTypography>
          </NblFlexContainer>
        )}
      </NblBorderContainer>
    </NblFlexContainer>
  );
};

export default EditVMWareTagList;
