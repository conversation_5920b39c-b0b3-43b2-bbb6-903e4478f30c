import React from 'react';
// eslint-disable-next-line
import <PERSON>blFormContainer, { NblFormHelpers } from 'sharedComponents/NblContainers/NblFormContainer';
import SiteMapForm from './SiteMapForm';
// eslint-disable-next-line
import { NblStepperProps } from 'sharedComponents/NblStepper';
import useNblNavigate from 'hooks/useNblNavigate';
import { useDispatch } from 'react-redux';
import { useApiService } from 'api/ApiService/context';
import { toast } from 'react-toastify';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { sitemapValidationSchema } from 'yupSchema/AddSiteMapForm';

interface AddSiteMapProps {}

type FormValues = {
  siteMapData: [];
};

const initialValues: FormValues = {
  siteMapData: [],
};

const AddSiteMapForm: React.FunctionComponent<AddSiteMapProps> = () => {
  const { apiSiteMapService } = useApiService();
  const dispatch = useDispatch();
  const navigate = useNblNavigate();

  const STEPS: NblStepperProps['steps'] = [
    {
      icon: 'CloudOutlined',
      title: 'Upload',
      caption: 'Upload a valid sitemap data xlsx',
      status: 'current',
      errorFields: ['siteMapData'],
    },
    {
      icon: 'CloudOutlined',
      title: 'Review and Submit',
      caption: 'Review the uploaded sitemap data',
      status: 'pending',
      errorFields: [],
    },
  ];

  const onCancel = () => {
    navigate(-2);
  };

  const handleSubmit = async (values: FormValues, nblFormHelpers: NblFormHelpers<FormValues>) => {
    let { siteMapData } = values;
    if (siteMapData.length) {
      dispatch(showSpinner({ id: SPINNER_IDS.tools, status: true, message: 'Sitemaps are being added...' }));
      nblFormHelpers.setSubmitting(true);
      siteMapData.forEach((item: any) => delete item.id);
      apiSiteMapService
        .postSiteMapData(siteMapData)
        .then((res: any) => {
          dispatch(showSpinner({ id: SPINNER_IDS.tools, status: false, message: '' }));
          if (res.status) {
            toast.success(res.data?.message || 'Sitemap data added successfully', {
              position: toast.POSITION.BOTTOM_CENTER,
            });
            navigate(-2);
            nblFormHelpers.resetForm();
          } // Don't need the else block as the default error handling is provided by API Service class
        })
        .finally(() => {
          nblFormHelpers.setSubmitting(false);
        });
    } else {
      toast.error('No data found for upload!', {
        position: toast.POSITION.BOTTOM_CENTER,
      });
    }
  };

  return (
    <>
      <NblFormContainer<FormValues>
        title="Add Site Map"
        caption="Upload, Review and Submit Site Map data here"
        formInitialValues={initialValues}
        steps={STEPS}
        formValidationSchema={sitemapValidationSchema}
        onSubmit={handleSubmit}
        onCancel={onCancel}
        showPreview={false}
      >
        <SiteMapForm />
      </NblFormContainer>
    </>
  );
};

export default AddSiteMapForm;
