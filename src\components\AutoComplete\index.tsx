import { Autocomplete, TextField, Stack, SxProps, FormHelperText, InputLabel, MenuItem, Typography } from '@mui/material';
import { truncateLabel } from 'sharedComponents/NblFormInputs/common';

interface AutoCompleteProps {
  error?: boolean | string;
  label: string;
  name: string;
  options: any;
  value: string;
  placeholder: string;
  readOnly?: boolean;
  disabled?: boolean;
  truncateSelected?: number;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onChange: (value: any) => void;
  sx?: SxProps;
}

function AutoComplete({
  error,
  label,
  name,
  options,
  value,
  readOnly,
  disabled,
  truncateSelected,
  placeholder,
  onMouseEnter,
  onMouseLeave,
  onChange,
  sx,
}: AutoCompleteProps) {

  return (
    <Stack spacing={1} sx={sx}>
      <InputLabel htmlFor={name}>{label}</InputLabel>
      <Autocomplete
        options={options}
        value={value ? options.find((option: any) => option.value === value)?.label : null}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        readOnly={readOnly}
        onChange={(_, newValue: any) => {
          onChange(newValue?.value);
        }}
        isOptionEqualToValue={(option: any, value) => option.label === value}
        renderInput={(params) => <TextField {...params} placeholder={placeholder} id={name} name={name} disabled={disabled} />}
        renderOption={(props, option: any) => {
          return (
            <MenuItem {...props} key={`${name}-option-${option.label}-${option.value}`} value={option.label}>
              <Typography variant="body1">{truncateSelected ? truncateLabel(option.label, truncateSelected) : option.label}</Typography>
            </MenuItem>
          );
        }}
      />

      {error && <FormHelperText error>{error}</FormHelperText>}
    </Stack>
  );
}

export default AutoComplete;
