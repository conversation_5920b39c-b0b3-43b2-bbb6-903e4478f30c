import { useTheme } from '@mui/material';
import { useApiService } from 'api/ApiService/context';
import { NebulaTheme } from 'NebulaTheme/type';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router';
import NblAccordion from 'sharedComponents/Accordion/NblAccordion';
import NblButton from 'sharedComponents/Buttons/NblButton';
// eslint-disable-next-line no-unused-vars
import NblActivityStepper, { ActivityStatusIcon, ActivityStatus, NblActivityStepperProps } from 'sharedComponents/NblActivtyStepper';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblTypography from 'sharedComponents/NblTypography';
import { ActivityLog } from 'types/Interfaces/ActivityLogData';
import RetryDialog from './RetryDialog';
import { RequestType } from 'types/Enums';
import { getFormattedTotalTimeTaken } from 'utils/common';
import { ACTIVITYLOGSTATUS } from 'types/Enums/ActivityLogStatus';
import NblViewDetailsAccordion from 'componentsV2/NblViewDetailsAccordion';

interface ActivityLoggerProps {
  status: string;
  activityLogs: ActivityLog[];
  serviceRequestType: RequestType | undefined;
}

export interface VMData {
  hostName: string;
  resourceId: string;
  status: string;
  ipAddress: {};
}

let isVmCreationRequest = [
  RequestType.CREATE_VM_LINUX89,
  RequestType.CREATE_VM_UBUNTU,
  RequestType.CREATE_VM_WINDOWS,
  RequestType.CREATE_VM_LINUX89_VMWARE,
  RequestType.CREATE_VM_LINUX89_ADMIN_VMWARE,
  RequestType.CREATE_VM_UBUNTU_VMWARE,
  RequestType.CREATE_VM_UBUNTU_ADMIN_VMWARE,
  RequestType.CREATE_VM_WINDOWS_VMWARE,
  RequestType.CREATE_VM_WINDOWS_ADMIN_VMWARE,
];

const ActivityLogger: React.FC<ActivityLoggerProps> = ({ status, activityLogs, serviceRequestType }) => {
  //Hooks
  const apiService = useApiService();
  const params = useParams();
  const theme = useTheme<NebulaTheme>();

  //States
  const [selectedActivity, setSelectedActivity] = useState('');
  const [retryCount, setRetryCount] = useState<number>(0);
  const [retryButtonEnabled, setRetryButtonEnabled] = useState<boolean>(false);
  const [retryCheck, setRetryCheck] = useState<boolean>(false);
  const [vmData, setVmData] = useState<VMData[]>([]);
  const [openRetryDialog, setOpenRetryDialog] = useState(false);
  const [hasActivityStatusError, setHasActivityStatusError] = useState(false);

  //Local Variables
  const IS_REACT_APP_PURGE_RETRY_INTEGRATION = process.env?.REACT_APP_PURGE_RETRY_INTEGRATION === 'enabled';
  const serviceRequestId = params.serviceRequestId;

  //Memoization
  const constructActivityLog = useCallback((): NblActivityStepperProps['activities'] => {
    return activityLogs?.map((activity) => ({
      name: activity.eventName,
      startTime: activity.startTime,
      endTime: activity.endTime,
      status: getStatus(activity.status),
      timeTaken: activity.timeTaken,
      toolTipMessage: activity.downStreamErrorData || '',
      hasSubtasks: Boolean(activity?.subTaskLogs?.length),
    }));
  }, [activityLogs]);

  const subTaskLogs = useMemo(
    () => activityLogs?.find((activity) => activity.eventName === selectedActivity)?.subTaskLogs,
    [selectedActivity, activityLogs]
  );

  //Utils
  function getStatus(status: ActivityLog['status']): ActivityStatus {
    switch (status) {
      case ACTIVITYLOGSTATUS.COMPLETED:
        return 'completed';
      case ACTIVITYLOGSTATUS.STARTED:
        return 'progress';
      case ACTIVITYLOGSTATUS['NOT_STARTED']:
        return 'not started';
      case ACTIVITYLOGSTATUS.SKIPPED:
        return 'skipped';
      case ACTIVITYLOGSTATUS.FAILED:
        return 'failed';
      case ACTIVITYLOGSTATUS['COMPLETED_WITH_ERRORS']:
        return 'completed with error';
      default:
        return 'pending';
    }
  }
  const checkRetryButtonEnabled = (count: number) => {
    if ((count as number) < 10) {
      setRetryButtonEnabled(false);
    } else {
      setRetryButtonEnabled(true);
    }
  };

  const checkVMcreationTimeTaken = (data: ActivityLog[] | undefined | any) => {
    if (data !== undefined) {
      const firstLog = data[0];
      const lastLog = data.at(-1);
      const requestStartTime = new Date(firstLog?.startTime);
      const requestEndTime = lastLog?.endTime ? new Date(lastLog?.endTime) : new Date();
      const totalTimeTaken = getFormattedTotalTimeTaken(requestStartTime, requestEndTime);
      return totalTimeTaken;
    } else {
      return '0 min';
    }
  };

  //Side effects
  useEffect(() => {
    if (serviceRequestId && status) {
      let totalTimeTaken: string = checkVMcreationTimeTaken(activityLogs) || '0 min';
      if (status === ACTIVITYLOGSTATUS['COMPLETED_WITH_ERRORS'] || status === ACTIVITYLOGSTATUS.FAILED) {
        setHasActivityStatusError(true);
      }
      if (
        Object.values(isVmCreationRequest).includes(serviceRequestType as RequestType) &&
        (status === ACTIVITYLOGSTATUS.FAILED || status === ACTIVITYLOGSTATUS['COMPLETED_WITH_ERRORS'])
      ) {
        setHasActivityStatusError(true);
      } else if (
        Object.values(isVmCreationRequest).includes(serviceRequestType as RequestType) &&
        parseFloat(totalTimeTaken.replace(' min', '')) >= 60 &&
        status === ACTIVITYLOGSTATUS.PROCESSING
      ) {
        setHasActivityStatusError(true);
      } else {
        setHasActivityStatusError(false);
      }
    }
  }, [serviceRequestId, status, serviceRequestType]);

  useEffect(() => {
    if (activityLogs?.length > 0) {
      const count: ActivityLog | undefined = activityLogs?.find((val: ActivityLog) =>
        Object.prototype.hasOwnProperty.call(val, 'retryCount')
      );
      if (count !== undefined) {
        setRetryCount(count.retryCount as number);
        checkRetryButtonEnabled(count.retryCount as number);
      } else {
        checkRetryButtonEnabled(retryCount as number);
      }
    }
  }, [activityLogs]);

  //Handlers
  const handleRetryCount = () => {
    setOpenRetryDialog(true);
    if (serviceRequestId && !vmData.length)
      apiService.apiAssetService.getVmActivityLogs(serviceRequestId).then((res) => {
        setVmData(res.data);
      });

    if (retryCount < 10) setRetryCheck(true);
  };

  //JSX
  return (
    <NblViewDetailsAccordion hasDivider summary="Request Status - Activity Log">
      <NblFlexContainer direction="column" alignItems="center" spacing={1.5}>
        <NblFlexContainer alignSelf="flex-end" width="auto" height="auto" right="0">
          {IS_REACT_APP_PURGE_RETRY_INTEGRATION && hasActivityStatusError && (
            <NblButton buttonID={'purge-retry-btn'} variant="contained" disabled={retryButtonEnabled} onClick={handleRetryCount}>
              Purge & Retry ({retryCount})
            </NblButton>
          )}
        </NblFlexContainer>
        <NblActivityStepper selectedActivity={selectedActivity} onActivityClick={setSelectedActivity} activities={constructActivityLog()} />
        {subTaskLogs && (
          <NblBorderContainer width="100%" backgroundColor={theme.palette.secondary.main}>
            <NblFlexContainer direction="column" padding="8px">
              {subTaskLogs.map((subtask, index) => (
                <NblAccordion summary={`Subtask - ${index + 1}`} key={subtask.resourceName} hasDivider={false}>
                  <NblGridContainer columns={2}>
                    <NblGridItem>
                      <NblTypography variant="subtitle2" color="shade1" weight="bold">
                        Resource Details
                      </NblTypography>
                    </NblGridItem>
                    <NblGridItem>
                      <NblTypography variant="subtitle2" color="shade1" textAlign="right" weight="bold">
                        Status
                      </NblTypography>
                    </NblGridItem>
                    <NblGridItem>
                      <NblTypography variant="subtitle2" color="shade1" textTransform="capitalize">
                        {subtask.resourceName}
                      </NblTypography>
                    </NblGridItem>
                    <NblGridItem>
                      <NblTypography
                        variant="subtitle2"
                        color={theme.palette.activityStepper.variant[getStatus(subtask.status)].subtaskStatus}
                        textAlign="right"
                      >
                        {subtask.status}
                      </NblTypography>
                    </NblGridItem>
                  </NblGridContainer>
                </NblAccordion>
              ))}
            </NblFlexContainer>
          </NblBorderContainer>
        )}
      </NblFlexContainer>
      <RetryDialog
        vmData={vmData}
        open={openRetryDialog && retryCheck && vmData && vmData?.length > 0}
        setRetryCheck={setRetryCheck}
        handleClose={() => setOpenRetryDialog(false)}
      />
    </NblViewDetailsAccordion>
  );
};

export default ActivityLogger;
