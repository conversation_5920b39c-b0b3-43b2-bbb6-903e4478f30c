import { Grid } from '@mui/material';

import AdminCatalogItem from './CatalogItem/AdminCatalogItem';
import ServiceCatalogItem from './CatalogItem/ServiceCatalogItem';
import ContentViewport from '../ContentViewport';
import { CatalogTilesData } from 'types';

interface CatalogProps {
  catalogItems: CatalogTilesData[];
  isAdminTiles?: boolean;
}
export const Catalog: React.FC<CatalogProps> = (props: CatalogProps) => {
  const { catalogItems, isAdminTiles } = props;
  return (
    <ContentViewport>
      <Grid className="Catalog-Grid" container columnGap={5.3} rowGap={7.5} sx={{ px: 6 }}>
        {catalogItems?.map((item, index: number) => (
          <Grid key={`tiles-content-${index}`}>{isAdminTiles ? <AdminCatalogItem {...item} /> : <ServiceCatalogItem {...item} />}</Grid>
        ))}
      </Grid>
    </ContentViewport>
  );
};

export default Catalog;
