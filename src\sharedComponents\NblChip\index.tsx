import React from 'react';
import StyledNblChip from './styled';
/* eslint-disable no-unused-vars */
import { NebulaTheme } from 'NebulaTheme/type';
import { useTheme } from '@mui/material';
import { formatId } from 'utils/common';

type label = React.ReactNode | string | number;
export interface NblCardChipProps {
  id: string;
  label: label;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'default';
  extracolor?: 'primary' | 'transparent' | 'default';
  borderRadius?: 'sm' | 'lg';
  clickable?: boolean;
  onClick?: (label: label) => void;
  onDelete?: (label: label) => void;
  disabled?: boolean;
  borderColor?: `#${string}` | string;

}

const NblChip: React.FC<NblCardChipProps> = ({
  id,
  label,
  borderRadius = 'sm',
  color,
  clickable = false,
  onClick,
  onDelete,
  disabled,
  extracolor,
  borderColor,


}) => {
  const theme: NebulaTheme = useTheme();
  const additionalColor = theme.palette.chip.additionalColor;

  //Handler
  function HandlerClick(e: React.MouseEvent<HTMLElement>) {
    e.stopPropagation();
    onClick?.(label);
  }

  return (
    <StyledNblChip
      id={formatId(`chip-${label}`)}
      label={label}
      variant={'filled'}
      radius={borderRadius}
      color={color}
      clickable={clickable}
      onClick={disabled ? undefined : HandlerClick}
      onDelete={disabled ? undefined : onDelete}
      disabled={disabled}
      {...(extracolor && { extracolor: additionalColor[extracolor] })}
      borderColor={borderColor}
      title={label?.toString()}
    />
  );
};

export default NblChip;
