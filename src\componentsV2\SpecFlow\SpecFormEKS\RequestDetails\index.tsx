import NblDataViewer from 'sharedComponents/NblDataViewer';
import { RequestPayloadMap } from 'types/Interfaces/RequestDetails';
import RequestType from 'types/Enums/RequestType';
import NblViewDetailsAccordion from 'componentsV2/NblViewDetailsAccordion';

interface CreateSpecFormEKSDetailsProps {
  formName: string;
  requestPayload: RequestPayloadMap[RequestType.SPEC_FLOW_EKS];
}

const CreateSpecFormEKSDetails: React.FunctionComponent<CreateSpecFormEKSDetailsProps> = ({ formName, requestPayload }) => {
  const data = [
    { name: 'Project', value: requestPayload.projectName },
    { name: 'IAC Project Name', value: requestPayload.iacProjectName },
    { name: 'Namespace', value: requestPayload.namespaceId },
    { name: 'Zone Id', value: requestPayload.zone_id },
    { name: 'Clsuter Name ', value: requestPayload.cluster_name },
    { name: 'Cluster Version', value: requestPayload.cluster_version },
    { name: 'AMI Id', value: requestPayload.ami_id },
    { name: 'Minimum Size', value: requestPayload.min_size },
    { name: 'Desired Size', value: requestPayload.desired_size },
    { name: 'Maximum Size', value: requestPayload.max_size },
    { name: 'Disk Size', value: requestPayload.disk_size },
    { name: 'VPC ID', value: requestPayload.vpc_id },
    { name: 'AWS Region', value: requestPayload.aws_region },
    { name: 'Control Plane ', value: requestPayload.control_plane_subnets },
    { name: 'Public Subnets', value: requestPayload.public_subnets },
    { name: 'Node Group', value: requestPayload.node_group_subnets },
    { name: 'Create ALB', value: requestPayload.create_alb },
    { name: 'ALB Internal', value: requestPayload.alb_internal },
    { name: 'WAF Name', value: requestPayload.waf_name },
    { name: 'Default Action', value: requestPayload.default_action },
    { name: 'Falcon Id', value: requestPayload.falcon_cid },
    { name: 'Falcon Client', value: requestPayload.falcon_client },
    { name: 'Falcon Secret', value: requestPayload.falcon_secret },
    { name: 'ACM Domain Name', value: requestPayload.acm_domain_name },
  ];

  return (
    <NblViewDetailsAccordion hasDivider summary={formName}>
      <NblDataViewer data={data} />
    </NblViewDetailsAccordion>
  );
};

export default CreateSpecFormEKSDetails;
