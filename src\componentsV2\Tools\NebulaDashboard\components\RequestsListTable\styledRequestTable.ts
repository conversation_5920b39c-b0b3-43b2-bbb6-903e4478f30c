import { Chip, styled, TableCell, TableRow } from '@mui/material';

export const StyledTableTitle = styled(TableCell)(({ theme }) => {
  return {
    alignItems: 'flex-start',
    '&.MuiTableCell-root': {
      borderBottom: 'none !important',
      fontSize: '20px',
      color: '#102F54',
      fontWeight: 'bold',
      fontFamily: 'Gotham',
      [theme.breakpoints.down('xl')]: {
        fontSize: '15px',
      },
    },
  };
});

export const StyledTableHead = styled(TableCell)({
  '&.MuiTableCell-root': {
    fontSize: '16px',
    color: '#102F54',
    fontWeight: 'medium',
    fontFamily: 'Gotham',
  },
});

export const StyledTableRows = styled(TableRow)({
  '&:hover .MuiTableCell-root': {
    fontWeight: 700,
    color: '#003057 ',
    fontSize: '15px',
    cursor: 'pointer',
  },
});
export const StyledTableData = styled(TableCell)({
  '&.MuiTableCell-root': {
    fontSize: '14px',
    color: '#102F54',
    fontWeight: 'normal',
    fontFamily: 'Gotham',
  },
});

export const StyledChip = styled(Chip)(({ theme }) => {
  return {
    fontWeight: 'bold',
    borderRadius: '90px',
    border: 'none',
    '& .MuiChip-label': {
      fontSize: '14px',
      fontWeight: 'normal',
      fontFamily: 'Gotham',
      [theme.breakpoints.down('xl')]: {
        fontSize: '13px',
      },
    },
  };
});
