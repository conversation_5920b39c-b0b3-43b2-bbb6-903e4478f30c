// eslint-disable-next-line no-unused-vars
import { VropResource, VropResourceStatistic, Facility } from './types';
// eslint-disable-next-line no-unused-vars
import { AllMetricTypes, NormalizedStats, MetricType, StatisticInfo, NormalizedStatsVM } from './statisticinfo';
import { CalculatedVropsResource } from './types';
import { getStatusLabel } from './status';
import { getUsageColor } from './colors';
import { CplanResourceType } from './constant';

export function extractStats(stats: VropResourceStatistic[], statType: StatisticInfo, defaultVal = 0) {
  const stat = stats && stats?.find((s) => s.statname === statType.raw);
  return stat ? stat.statvalue : defaultVal;
}
export function extractStatsAndFormat(stats: VropResourceStatistic[], statType: StatisticInfo, defaultVal = 0) {
  const stat = stats?.find((s) => s.statname === statType.raw);
  const statVal = stat ? stat.statvalue : defaultVal;
  return statType.formatScale(statVal);
}
export function calcUsagePercentage(stats: VropResourceStatistic[], metricType: MetricType, defaultVal = 0) {
  const usage = extractStats(stats, NormalizedStats[metricType].usage);

  // CPU usage stats are already a percentage
  if (usage && metricType === MetricType.CPU) {
    return usage;
  }

  const total = extractStats(stats, NormalizedStats[metricType].total_capacity);
  return usage && total ? (usage / total) * 100 : defaultVal;
}
export function calcUsagePercentageVM(stats: VropResourceStatistic[], metricType: MetricType) {
  const usage = extractStats(stats, NormalizedStatsVM[metricType].usage);
  return usage;
}

export function calcAllocationPercentage(stats: VropResourceStatistic[], metricType: MetricType, defaultVal = 0) {
  const usage = extractStats(stats, NormalizedStats[metricType].capacity_provisioned);
  const total = extractStats(stats, NormalizedStats[metricType].total_capacity);
  return usage && total ? (usage / total) * 100 : defaultVal;
}

const CalculateVropsVirtualMachine = (resource: VropResource) => {
  let usageStatistics = {} as CalculatedVropsResource;

  const stats =
    resource &&
    AllMetricTypes.map((vmmetric) => {
      const totalStatName = NormalizedStatsVM[vmmetric].total_capacity;
      const totalValue = extractStats(resource.lateststats as VropResourceStatistic[], totalStatName);
      const totalLabel = `(${totalStatName.formatScale(totalValue)})`;
      return {
        value: calcUsagePercentageVM(resource.lateststats as VropResourceStatistic[], vmmetric),
        label: vmmetric,
        totalValue,
        totalLabel,
      };
    });

  const extractDetails = (stats: any) => {
    const getUtilizationsValue = (label: string) => {
      return Math.round(
        stats?.find((item: any) => {
          return item.label === label;
        }).value
      );
    };
    usageStatistics.cpuUtilized = getUtilizationsValue(MetricType.CPU);
    usageStatistics.memoryUtilized = getUtilizationsValue(MetricType.Memory);
    usageStatistics.storageUtilized = getUtilizationsValue(MetricType.Storage);
    const getLabel = (label: string) => {
      return stats?.find((item: any) => {
        return item.label === label;
      }).totalLabel;
    };
    const getCPULabel = (label: string) => {
      return stats?.find((item: any) => {
        return item.label === label;
      }).totalValue;
    };

    const formatNumber = (num: number) => {
      return new Intl.NumberFormat('en-US').format(Math.round(num));
    };
    usageStatistics.cpu = !isNaN(getCPULabel(MetricType.CPU)) ? formatNumber(getCPULabel(MetricType.CPU)) : '';
    usageStatistics.memory = getLabel(MetricType.Memory)?.split('(').join('').split(')').join('');
    usageStatistics.storage = getLabel(MetricType.Storage)?.split('(').join('').split(')').join('');
  };
  extractDetails(stats);
  usageStatistics.resourcename = resource.resourcename;
  usageStatistics.resourceid = resource.resourceid;
  const maxUtilization = Math.max(usageStatistics.cpuUtilized, usageStatistics.memoryUtilized, usageStatistics.storageUtilized);
  usageStatistics.status = getStatusLabel(maxUtilization);
  return usageStatistics;
};

export const CalculateVropsResource = (resource: VropResource) => {
  if (resource.resourcetype === CplanResourceType.VM) {
    return CalculateVropsVirtualMachine(resource);
  }

  let usageStatistics = {} as CalculatedVropsResource;

  const stats =
    resource &&
    AllMetricTypes.map((metric) => {
      const totalStatName = NormalizedStats[metric].total_capacity;
      const totalValue = extractStats(resource.lateststats as VropResourceStatistic[], totalStatName);
      const totalLabel = `(${totalStatName.formatScale(totalValue)})`;
      return {
        value: calcUsagePercentage(resource.lateststats as VropResourceStatistic[], metric),
        label: metric,
        allocatedValue: calcAllocationPercentage(resource.lateststats as VropResourceStatistic[], metric),
        totalValue,
        totalLabel,
      };
    });

  const extractDetails = (stats: any) => {
    const getUtilizationsValue = (label: string) => {
      return Math.round(
        stats?.find((item: any) => {
          return item.label === label;
        }).value
      );
    };
    usageStatistics.cpuUtilized = getUtilizationsValue(MetricType.CPU);
    usageStatistics.memoryUtilized = getUtilizationsValue(MetricType.Memory);
    usageStatistics.storageUtilized = getUtilizationsValue(MetricType.Storage);

    const getAllocatedValue = (label: string) => {
      return Math.round(
        stats?.find((item: any) => {
          return item.label === label;
        }).allocatedValue
      );
    };
    usageStatistics.cpuAllocated = getAllocatedValue(MetricType.CPU);
    usageStatistics.memoryAllocated = getAllocatedValue(MetricType.Memory);
    usageStatistics.storageAllocated = getAllocatedValue(MetricType.Storage);

    const getLabel = (label: string) => {
      return stats?.find((item: any) => {
        return item.label === label;
      }).totalLabel;
    };
    const getCPULabel = (label: string) => {
      return stats?.find((item: any) => {
        return item.label === label;
      }).totalValue;
    };

    const formatNumber = (num: number) => {
      return new Intl.NumberFormat('en-US').format(Math.round(num));
    };
    usageStatistics.cpu = !isNaN(getCPULabel(MetricType.CPU)) ? formatNumber(getCPULabel(MetricType.CPU)) : '';
    usageStatistics.memory = getLabel(MetricType.Memory)?.split('(').join('').split(')').join('');
    usageStatistics.storage = getLabel(MetricType.Storage)?.split('(').join('').split(')').join('');
  };
  extractDetails(stats);
  usageStatistics.resourcelabel = resource.label;
  usageStatistics.resourcename = resource.resourcename;
  usageStatistics.resourceid = resource.resourceid;
  const maxUtilization = Math.max(usageStatistics.cpuUtilized, usageStatistics.memoryUtilized, usageStatistics.storageUtilized);
  usageStatistics.status = getStatusLabel(maxUtilization);
  usageStatistics.domainid = resource.domainid;
  usageStatistics.domainname = resource.domainname;

  return usageStatistics;
};
interface ProcessUtilization {
  numericPart: string;
  unitPart: string;
}

export const processUtilization = (utilizationData: string): ProcessUtilization => {
  const regex = /[a-zA-Z]/;
  if (typeof utilizationData === 'string' && regex.test(utilizationData)) {
    const numericPart = utilizationData.slice(0, -2);
    const unitPart = utilizationData.slice(-2);
    return { numericPart, unitPart };
  }
  return { numericPart: utilizationData, unitPart: '' };
};

export const addOpacityToColor = (hexColor: string, opacity: number) => {
  if (hexColor.startsWith('#')) {
    hexColor = hexColor.slice(1);
  }
  let r = parseInt(hexColor.substring(0, 2), 16);
  let g = parseInt(hexColor.substring(2, 4), 16);
  let b = parseInt(hexColor.substring(4, 6), 16);
  return `rgba(${r},${g},${b},${opacity})`;
};

export const filterBasedOnRegion = (
  selectedRegion: string,
  resourceList: VropResource[] | [],
  facilityList: Facility[] | [],
  setFilteredResourcesList: React.Dispatch<React.SetStateAction<VropResource[]>>,
  setPage?: React.Dispatch<React.SetStateAction<number>>,
  setPaginatedResourceList?: React.Dispatch<React.SetStateAction<VropResource[]>>
) => {
  if (selectedRegion && selectedRegion === 'ALL') {
    setFilteredResourcesList(resourceList);
  } else if (selectedRegion) {
    const filteredResources = resourceList.filter((resource: VropResource) => {
      const facility = facilityList.find((facility: Facility) => {
        return facility.region === selectedRegion && facility?.resources?.some((res) => res.resourceid === resource.resourceid);
      });
      return facility !== undefined;
    });
    setPaginatedResourceList?.([]);
    setFilteredResourcesList(filteredResources);
    setPage?.(1);
  }
};

export const sortCalcResourcesByMetric = (data: CalculatedVropsResource[], criteria: string): CalculatedVropsResource[] => {
  switch (criteria) {
    case 'ALL':
      return [...data];
    case 'CPU Only':
      return [...data].sort((a, b) => b.cpuUtilized - a.cpuUtilized);
    case 'Memory Only':
      return [...data].sort((a, b) => b.memoryUtilized - a.memoryUtilized);
    case 'Storage Only':
      return [...data].sort((a, b) => b.storageUtilized - a.storageUtilized);
    case 'Ascending Alphabetical':
      return [...data].sort((a, b) => {
        if (a.resourcelabel && b.resourcelabel) {
          return a.resourcelabel.toLowerCase().localeCompare(b.resourcelabel.toLowerCase());
        }

        if (a.resourcelabel) {
          return a.resourcelabel.toLowerCase().localeCompare(b.resourcename.toLowerCase());
        }

        if (b.resourcelabel) {
          return a.resourcename.toLowerCase().localeCompare(b.resourcelabel.toLowerCase());
        }

        return a.resourcename.toLowerCase().localeCompare(b.resourcename.toLowerCase());
      });
  }
  return [];
};

export const downloadFile = (data: Blob, fileName: string) => {
  const url = window.URL.createObjectURL(new Blob([data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(url);
  link.remove();
};
export const colorDeciderFunction = (selectedMetric: string, cpuUtilized: number, memoryUtilized: number, storageUtilized: number) => {
  switch (selectedMetric) {
    case 'ALL':
      return getUsageColor(Math.max(cpuUtilized, memoryUtilized, storageUtilized));
    case 'CPU Only':
      return getUsageColor(cpuUtilized);
    case 'Memory Only':
      return getUsageColor(memoryUtilized);
    case 'Storage Only':
      return getUsageColor(storageUtilized);
    default:
      return getUsageColor(Math.max(cpuUtilized, memoryUtilized, storageUtilized));
  }
};

// move an item to the last of an array by input index
export function moveItemToLastByIndex(arr: any[], index: number | null) {
  if (index === null || index === -1) return arr;
  return [...arr.slice(0, index), ...arr.slice(index + 1), arr[index]];
}

export const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
  if (event.key === 'e' || event.key === 'E') {
    event.preventDefault();
  }
};
