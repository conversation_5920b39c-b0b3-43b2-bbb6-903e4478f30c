import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import useNblNavigate from 'hooks/useNblNavigate';
import AddPermisssion from 'componentsV2/Administration/Permissions/AddPermission';

const AddPermission = () => {
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const navigateToPermissionDetails = () => {
    navigate('/administration/permissions');
  };

  return <AddPermisssion title="" permissions={{}} onSuccess={navigateToPermissionDetails} onClose={navigateToPermissionDetails} />;
};

export default AddPermission;
