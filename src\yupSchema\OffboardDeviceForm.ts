import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';

export const validationSchema = yup.object().shape({
  hostname: yup
    .string()
    .trim()
    .required('Device Name is required')
    .matches(yupMatchesParams.fqdn.pattern, yupMatchesParams.fqdn.errorMessage)
    .test('no-leading-trailing-dots', 'Device Name cannot start or end with a dot.', (value) => {
      if (!value) return true;
      return !value.startsWith('.') && !value.endsWith('.');
    }),
});
