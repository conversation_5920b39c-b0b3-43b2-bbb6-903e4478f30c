import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';
import { Address4, Address6 } from 'ip-address';

export const validationSchema = yup.object().shape({
  project: yup.string().required('Project is required'),
  domain: yup.string().required('Domain is required'),
  hostname: yup
    .string()
    .required('Hostname is required')
    .matches(yupMatchesParams.ztpHostName.pattern, yupMatchesParams.ztpHostName.errorMessage),
  serial_number: yup.string().required('Serial Number / MAC Address is required'),
  ipv4_address: yup.string().test('valid-ip-address', 'Please enter valid IPv4 address', function (value) {
    if (Address4.isValid(value!)) {
      return true;
    } else return false;
  }),
  ipv6_address: yup
    .string()
    .required('IPv6 Address is required')
    .test('valid-ip-address', 'Please enter valid IPv6 address', function (value) {
      if (Address6.isValid(value!)) {
        return true;
      } else return false;
    }),

  platform: yup.string().required('Platform is required'),
  role: yup.string().required('Role is required'),
  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});
