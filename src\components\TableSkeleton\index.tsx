import { Table, TableHead, TableBody, TableRow, TableCell, Skeleton } from '@mui/material';

interface TableSkeletonProps {
  columnsNum: number;
  rowsNum: number;
}

const TableSkeleton = ({ columnsNum, rowsNum }: TableSkeletonProps) => {
  const renderTableRow = (rowType: string) => {
    return Array(columnsNum)
      .fill(0)
      .map((_, index) => (
        <TableCell key={`${rowType}-column-${index}`} component="th" scope="row">
          {' '}
          <Skeleton animation="wave" variant="text" />
        </TableCell>
      ));
  };

  return (
    <Table>
      <TableHead>
        <TableRow>{renderTableRow('header')}</TableRow>
      </TableHead>
      <TableBody>
        {[...Array(rowsNum)].map((_, index) => (
          <TableRow key={index}>{renderTableRow(`body-${index}`)}</TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default TableSkeleton;
