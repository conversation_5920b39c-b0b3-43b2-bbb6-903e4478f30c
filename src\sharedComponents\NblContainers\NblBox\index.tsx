import React from 'react';

import { Box } from '@mui/material';

interface NblBoxProps {
  children?: React.ReactNode;
  display?: React.CSSProperties['display'];
  margin?: React.CSSProperties['margin'];
  padding?: React.CSSProperties['padding'];
}

const NblBox: React.FC<NblBoxProps> = ({
  children,
  display,
  margin,
  padding
}) => {
  //JSX
  return (
    <Box style={{ display, margin, padding }}>
      {children}
    </Box>
  );
};

export default NblBox;
