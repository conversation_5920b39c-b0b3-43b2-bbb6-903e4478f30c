import React, { useEffect, useState } from 'react';
import { useTheme } from '@mui/material';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import { StyledSwitchTab, SwitchWrapper } from './styled';
import NblTypography from 'sharedComponents/NblTypography';
import { formatId } from 'utils/common';

export type TabProp = {
  label: string;
  disabled?: boolean;
};

export interface NblSwitchTabBarProps {
  tabs: TabProp[];
  activeTab?: number;
  onTabChange?: (tab: string) => void;
}

const NblSwitchTabBar: React.FC<NblSwitchTabBarProps> = ({ tabs, activeTab = 0, onTabChange }) => {
  // eslint-disable-next-line no-unused-vars
  const theme = useTheme<NebulaTheme>();
  const [active, setActive] = useState<number>(activeTab);

  useEffect(() => {
    // Update internal active state when prop changes
    setActive(activeTab);
  }, [activeTab]);

  const handleClick = (index: number) => {
    if (tabs[index].disabled) return;
    setActive(index);
    onTabChange?.(tabs[index].label);
  };

  return (
    <SwitchWrapper>
      {tabs.map((tab, index) => (
        <StyledSwitchTab
          key={tab.label}
          id={formatId(`tab-${tab.label}`)}
          selected={active === index}
          disabled={tab.disabled}
          onClick={() => handleClick(index)}
          role="button"
          tabIndex={tab.disabled ? -1 : 0}
        >
          <NblTypography variant="subtitle2" weight="bold">
            {tab.label}
          </NblTypography>
        </StyledSwitchTab>
      ))}
    </SwitchWrapper>
  );
};

export { NblSwitchTabBar };
