//eslint-disable-next-line no-unused-vars
import { <PERSON>a, StoryObj } from '@storybook/react';
import { ComponentProps, useState } from 'react';
//eslint-disable-next-line no-unused-vars
import NblFormContainer, { NblFormProps, useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import NebulaTheme from 'NebulaTheme';
import { number, object, string } from 'yup';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from 'store';

type StoryProps = ComponentProps<typeof NblFormContainer>;

export default {
  title: 'Containers/Form/NblForm Container',
  tags: ['autodocs'],
  component: NblFormContainer,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    formType: { options: ['stepper', 'simple'] },
    showCancel: { type: 'boolean' },
    showNext: { type: 'boolean' },
    showPreview: { type: 'boolean' },
    showPrevious: { type: 'boolean' },
    showSubmit: { type: 'boolean' },
    onCancel: { action: 'onCancel', type: 'function' },
    onNext: { action: 'onNext', type: 'function' },
    onPreview: { action: 'onPreview', type: 'function' },
    onPrevious: { action: 'onPrevious', type: 'function' },
    onSubmit: { action: 'onSubmit', type: 'function' },
    onStepClick: { action: 'onStepClick', type: 'function' },
    wrap: { type: 'boolean' },
    canUpdate: { control: 'boolean', type: 'boolean' },
    canCreate: { type: 'boolean' },
  },
} as Meta<StoryProps>;

type Story = StoryObj<StoryProps>;

const Template: React.FC<{ preview: boolean; simpleForm: boolean }> = ({ preview, simpleForm }) => {
  //Hooks
  const { currentStep, nblFormProps, nblFormValues, steps } = useNblForms<{ firstname: string; lastname: string; age: string }>();

  //Local
  const errors = nblFormProps.errors as { [key: string]: any };

  //Jsx
  return (
    <div style={{ display: 'flex', flexDirection: 'column', padding: '10px', fontSize: '0.9rem' }}>
      {!preview && currentStep !== 2 && (
        <ul>
          <p>The below props will be available for forms</p>
          <li>values={JSON.stringify(nblFormValues)}</li>
          <li>Initial Form Values : initialValues={JSON.stringify(nblFormProps.initialValues)}</li>
          <li>errors={JSON.stringify(errors)}</li>
          <li>Current Step Index : {currentStep}</li>
          <li>etc..</li>
          {!simpleForm && (
            <li>
              Current Step requires these fields needs to be filled to proceed further:{' '}
              <b>
                {steps[currentStep].errorFields.map((v) => (
                  <span key={v} style={{ color: errors[v] ? 'red' : 'green' }}>
                    {v[0].toUpperCase() + v.slice(1) + `, `}
                  </span>
                ))}
              </b>
            </li>
          )}
        </ul>
      )}
      {!preview && <h2>Sample User Form</h2>}
      <NblGridContainer columns={2}>
        {!preview && currentStep === 0 && (
          <>
            <NblGridItem>
              <NblTextField
                label="First Name"
                value={nblFormValues.firstname}
                handleChange={nblFormProps.handleChange}
                handleBlur={nblFormProps.handleBlur}
                name="firstname"
                type="text"
                error={Boolean(nblFormProps.touched.firstname && errors.firstname)}
                helperText={errors.firstname}
              />
            </NblGridItem>
            <NblGridItem>
              <NblTextField
                label="Last Name"
                value={nblFormValues.lastname}
                handleChange={nblFormProps.handleChange}
                handleBlur={nblFormProps.handleBlur}
                name="lastname"
                type="text"
                error={Boolean(nblFormProps.touched.lastname && errors.lastname)}
                helperText={errors.lastname}
              />
            </NblGridItem>
          </>
        )}
        {!preview && (currentStep === 1 || simpleForm) && (
          <NblGridItem>
            <NblTextField
              label="Age"
              value={nblFormValues.age}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              name="age"
              type="number"
              error={Boolean(nblFormProps.touched.age && errors.age)}
              helperText={errors.age}
            />
          </NblGridItem>
        )}
      </NblGridContainer>
      {!preview && currentStep === 2 && <p>Click Preview to view your details</p>}
      {preview && (
        <>
          <h4>Your details are:</h4>
          <p>
            Name : {nblFormValues.firstname} {nblFormValues.lastname}
          </p>
          <p>Age : {nblFormValues.age}</p>
        </>
      )}
    </div>
  );
};

const Form = (args: StoryProps) => {
  //Hooks
  const [preview, setPreview] = useState(args.showPreview);

  //Local
  const formValidationSchema = object({
    firstname: string().required('First name is required'),
    lastname: string().required('Last name is required'),
    age: number().required('Age is required').min(10, 'Age should be atleast one'),
  });

  //JSX
  return (
    <NblFormContainer
      {...args}
      formValidationSchema={formValidationSchema}
      onPreview={() => setPreview(true)}
      onPrevious={() => setPreview(false)}
      onSubmit={(_, { setSubmitting }) => {
        alert('Form submitted successfully');
        setSubmitting(false);
      }}
      onCancel={() => setPreview(false)}
    >
      <Template preview={preview ?? false} simpleForm={args.formType === 'simple'} />
    </NblFormContainer>
  );
};

export const Default: Story = {
  args: {
    formType: 'stepper',
    title: 'User Profile',
    caption: 'Fill the necessary details needed to create account',
    steps: [
      { icon: 'CloudOutlined', title: 'Name', caption: 'Enter your name', status: 'current', errorFields: ['firstname', 'lastname'] },
      { icon: 'CloudOutlined', title: 'Age', caption: 'Enter your age', status: 'pending', errorFields: ['age'] },
      { icon: 'CloudOutlined', title: 'Confirm', caption: 'Are you sure to submit?', status: 'pending', errorFields: [] },
    ],
    wrap: true,
    onPreview: () => {},
    onNext: () => {},
    onPrevious: () => {},
    onCancel: () => {},
    onSubmit: () => {},
    onStepClick: () => {},
    formInitialValues: { firstname: '', lastname: '', age: 0 },
    readOnly: false,
    canCreate: true,
    canUpdate: true,
  },
  render: (args) => {
    return (
      <BrowserRouter>
        <Provider store={store}>
          <NebulaTheme>
            <Form {...args} />
          </NebulaTheme>
        </Provider>
      </BrowserRouter>
    );
  },
};
