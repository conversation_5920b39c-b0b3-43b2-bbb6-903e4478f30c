import * as yup from 'yup';

export const AddVCenterSchema = () =>
  yup.object().shape({
    domain: yup.string().required('Domain is required'),
    cloudDatacenter: yup.string().required('Cloud Datacenter is required'),
    vCenterName: yup.string().required('VCenter Name is required'),
    vCenterHost: yup.string().required('VCenter Host is required'),
    vCenterPort: yup.number().required('VCenter Port is required'),
    vCenterProtocol: yup.string().required('VCenter Protocol is required'),
    vCenterUser: yup.string().required('VCenter User is required'),
    vCenterPassword: yup.string().required('VCenter Password is required'),
  });
