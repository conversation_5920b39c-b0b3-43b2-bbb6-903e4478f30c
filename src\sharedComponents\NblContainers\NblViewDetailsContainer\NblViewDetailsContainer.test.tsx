import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import configureMockStore from 'redux-mock-store';
import NblViewDetailsContainer from './index';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import useLoadComponentDetails from 'hooks/useLoadComponentDetails';
import { useDispatch, Provider as ReduxProvider } from 'react-redux';
import { LoadComponentDetails } from 'types';

const folder: LoadComponentDetails = 'ResourceDetails';
const mockStore = configureMockStore();
const store = mockStore({ common: { expandedAccordions: [] } });

jest.mock('react-redux', () => {
  const actual = jest.requireActual('react-redux');
  return {
    ...actual,
    useDispatch: jest.fn(),
  };
});

jest.mock('hooks/useLoadComponentDetails', () => ({
  __esModule: true,
  default: jest.fn(),
}));

describe('NblViewDetailsContainer', () => {
  const mockDispatch = jest.fn();
  const defaultProps = {
    title: 'Test Title',
    componentName: 'TestComponent',
    data: {
      mock: 'data',
      requestId: 'REQ123',
      resourceId: 'RES456',
      catalogType: 'Standard',
      projectName: 'ProjectX',
      status: 'active',
    },
    viewDetailsFields: [{ title: 'Field 1', value: 'Value 1' }],
    loading: false,
    folder,
  };

  beforeEach(() => {
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    (useLoadComponentDetails as jest.Mock).mockReturnValue({
      Component: null,
      comingSoon: true,
    });
  });

  it('renders title and expand/collapse button', () => {
    render(
      <ReduxProvider store={store}>
        <NebulaThemeProvider>
          <NblViewDetailsContainer {...defaultProps} />
        </NebulaThemeProvider>
      </ReduxProvider>
    );

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /expand all/i })).toBeInTheDocument();
  });

  it('dispatches toggle action on expand/collapse button click', () => {
    render(<ReduxProvider store={store}><NebulaThemeProvider><NblViewDetailsContainer {...defaultProps} /></NebulaThemeProvider></ReduxProvider>);
    fireEvent.mouseDown(screen.getByRole('button', { name: /expand all/i }));
    expect(mockDispatch).toHaveBeenCalled();
  });

  it('renders NblViewDetailsOverview with props', () => {
    render(<ReduxProvider store={store}><NebulaThemeProvider><NblViewDetailsContainer {...defaultProps} /></NebulaThemeProvider></ReduxProvider>);
    expect(screen.getByText('Field 1')).toBeInTheDocument();
    expect(screen.getByText('Value 1')).toBeInTheDocument();
  });

  it('renders dynamic component if available', async () => {
    (useLoadComponentDetails as jest.Mock).mockReturnValue({
      Component: ({ data }: any) => <p data-testid={'mock-component'}>{data.mock}</p>,
      comingSoon: true,
    });
    render(<ReduxProvider store={store}><NebulaThemeProvider><NblViewDetailsContainer {...defaultProps} /></NebulaThemeProvider></ReduxProvider>);
    await waitFor(() => {
      expect(screen.getByTestId('mock-component')).toBeInTheDocument();
    });

    expect(screen.getByText('data')).toBeInTheDocument();
  });

  it('renders coming soon section when applicable', async () => {
    (useLoadComponentDetails as jest.Mock).mockReturnValue({
      Component: null,
      comingSoon: true,
    });

    render(<ReduxProvider store={store}><NebulaThemeProvider><NblViewDetailsContainer {...defaultProps} /></NebulaThemeProvider></ReduxProvider>);
    expect(screen.getByText('Request ID')).toBeInTheDocument();
    await waitFor(() => {
      expect(screen.getByText('More details Comming soon.')).toBeInTheDocument();
    });
  });

  it('renders Actions if provided', () => {
    render(<ReduxProvider store={store}><NebulaThemeProvider><NblViewDetailsContainer {...defaultProps} Actions={<div>Custom Action</div>} /></NebulaThemeProvider></ReduxProvider>);
    expect(screen.getByText('Custom Action')).toBeInTheDocument();
  });
});