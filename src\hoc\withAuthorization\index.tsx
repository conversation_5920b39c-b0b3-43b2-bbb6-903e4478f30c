import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import NonAuthorizedPage from 'componentsV2/Authentication/NonAuthorizedPage';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import PermissionService from 'api/ApiService/PermissionService';
import { setPermissions } from 'store/reducers/authorization';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';

interface WithAuthorizationProps {
  catalogLevel1: string;
  catalogLevel3: string;
}

const withAuthorization = <P extends object>(WrappedComponent: React.ComponentType<P>, showNonAuthorizePage = true) => {
  const WithAuthorization: React.FC<P & WithAuthorizationProps> = (props: P & WithAuthorizationProps) => {
    const { catalogLevel1, catalogLevel3 } = props;

    const apiPermissionService = new PermissionService();

    const [isUnAuthorized, setIsUnAuthorized] = useState<boolean>(false);
    const dispatch = useDispatch();
    const { permissions } = useSelector((state: State) => state.authorization);

    const isPermissionsFetched = permissions?.[`'${catalogLevel1}'`]?.[`'${catalogLevel3}'`];

    const getLevel3Permissions = () => {
      dispatch(showSpinner({ id: SPINNER_IDS.fetchingPermissions, status: true, message: 'Fetching permissions...' }));
      apiPermissionService
        .getLevel3MultiEnvPermissions(catalogLevel3)
        .then((res) => {
          if (res.status) {
            dispatch(
              setPermissions({
                ...permissions,
                [`'${catalogLevel1}'`]: {
                  ...permissions?.[`'${catalogLevel1}'`],
                  [`'${catalogLevel3}'`]: Object.keys(res.data).reduce((acc, key) => {
                    /**
                     * we are storing the object keys in string format so we shouldn't get
                     * any issue while accessing them when dot (.) is added in the catalog name
                     */
                    const stringKey = `'${key}'`;
                    acc[stringKey] = res.data[key];
                    return acc;
                  }, {} as { [key: string]: { Permissions: string[] } }),
                },
              })
            );
          } else {
            setIsUnAuthorized(true);
          }
        })
        .finally(() => {
          dispatch(showSpinner({ id: SPINNER_IDS.fetchingPermissions, status: false, message: '' }));
        });
    };

    useEffect(() => {
      if (!isPermissionsFetched) {
        getLevel3Permissions();
      }
    }, [catalogLevel3]);

    return isPermissionsFetched ? <WrappedComponent {...props} /> : isUnAuthorized && showNonAuthorizePage ? <NonAuthorizedPage /> : null;
  };

  return WithAuthorization;
};

export default withAuthorization;
