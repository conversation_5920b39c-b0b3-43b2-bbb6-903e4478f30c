import { StyledTreeItem } from './styled';
import { SxProps } from '@mui/material';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import { TreeViewBaseItem } from '@mui/x-tree-view/models';

interface NblRichTreeProps {
  items: TreeViewBaseItem[];
  onSelectedItemsChange: (event: React.SyntheticEvent, itemIds: string | string[] | null) => void;
  selectedItems: string | string[];
  onExpandedItemsChange: (event: React.SyntheticEvent, newExpanded: string[]) => void;
  multiSelect?: boolean;
  checkboxSelection?: boolean;
  expandedItems: string[];
  sx?: SxProps;
}

const NblRichTree: React.FC<NblRichTreeProps> = ({ items, onSelectedItemsChange, selectedItems, expandedItems, onExpandedItemsChange, multiSelect, checkboxSelection }) => {
  //Hooks

  //JSX
  return (
    <RichTreeView
      selectedItems={selectedItems}
      onSelectedItemsChange={onSelectedItemsChange}
      expandedItems={expandedItems}
      onExpandedItemsChange={onExpandedItemsChange}
      sx={{ width: '100%', height: '100%' }}
      items={items}
      slots={{ item: StyledTreeItem }}
      multiSelect={Boolean(multiSelect)}
      checkboxSelection={Boolean(checkboxSelection)}
    />
  );
};

export default NblRichTree;
