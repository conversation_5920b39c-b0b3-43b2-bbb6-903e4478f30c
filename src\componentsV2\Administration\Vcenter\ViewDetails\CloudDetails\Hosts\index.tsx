import React from 'react';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
// eslint-disable-next-line no-unused-vars
import { ColumnData, NblTable } from 'sharedComponents/NblTable';
import { Host } from '../Clusters/ClusterDetails/ClusterHostsDetails';

interface HostsProps {
  hosts: Host[];
  onChange: (updatedHosts: Host[]) => void;
}

const Hosts: React.FC<HostsProps> = ({ hosts, onChange }) => {
  const handleChecked = (hostMor: string, fieldName: keyof Host) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const updated = hosts.map((host) => (host.hostMor === hostMor ? { ...host, [fieldName]: e.target.checked } : host));
    onChange(updated);
  };

  const rows = hosts.map((host) => ({
    ...host,
    id: host.hostMor,
  }));

  const columns: ColumnData[] = [
    { field: 'name', headerName: 'Name', flex: 1 },
    { field: 'type', headerName: 'Type', flex: 1 },
    {
      field: 'disabled',
      headerName: 'Disable',
      flex: 1,
      renderCell: (params) => {
        const hostMor = params.id as string;
        return (
          <NblFlexContainer center>
            <NblCheckBox checked={!!params.row.disabled} label="" name="" onChange={handleChecked(hostMor, 'disabled')} onBlur={() => {}} />
          </NblFlexContainer>
        );
      },
    },
    { field: 'location', headerName: 'Location', flex: 1 },
    { field: 'vendor', headerName: 'Vendor', flex: 1 },
    { field: 'model', headerName: 'Model', flex: 1 },
    { field: 'cpu', headerName: 'CPU', flex: 1 },
    { field: 'core', headerName: 'Cores', flex: 1 },
    { field: 'memory', headerName: 'Memory', flex: 1 },
  ];

  return <NblTable columns={columns} rows={rows} />;
};

export default Hosts;
