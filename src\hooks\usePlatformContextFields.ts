import { useMemo } from 'react';
// eslint-disable-next-line no-unused-vars
import { ViewDetailsFields } from 'sharedComponents/NblContainers/NblViewDetailsContainer';
// eslint-disable-next-line no-unused-vars
import { ResourcesDetails } from 'types';

export const usePlatformContextFields = (resourceDataTemp?: ResourcesDetails): ViewDetailsFields[] => {
  return useMemo(() => {
    const platformContext = resourceDataTemp?.platformContext;

    return [
      {
        title: 'Domain',
        value: platformContext?.domainName ?? '-',
      },
      {
        title: 'Project Name',
        value: resourceDataTemp?.projectName ?? '-',
      },
      {
        title: 'Application Name',
        value: platformContext?.applicationName ?? '-',
      },
      {
        title: 'Environment Name',
        value: platformContext?.environmentName ?? '-',
      },
    ];
  }, [resourceDataTemp]);
};
