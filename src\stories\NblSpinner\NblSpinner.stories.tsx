//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblSpinner from 'sharedComponents/NblSpinner';

type StoryProps = ComponentProps<typeof NblSpinner>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Application/NblSpinner',
  component: NblSpinner,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

export const Default: Story = {
  args: {
    spinnerData: [{ id: '123', message: 'Loading', status: true }],
  },
  render: (args) => (
    <NebulaTheme>
      <NblSpinner {...args} />
    </NebulaTheme>
  ),
};
