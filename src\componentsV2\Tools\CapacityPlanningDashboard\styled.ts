import { ReportProblem } from '@mui/icons-material';
import { Accordion, AccordionDetails, AccordionSummary, Card, Dialog, Grid, Select, Box, MenuItem } from '@mui/material';
import { styled, useTheme } from '@mui/material/styles';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledGridOverUtilAndAllocated = styled(Grid)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    backgroundColor: theme.palette.secondary.shade6,
    borderRadius: 3,
    height: '1.5rem',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    [theme.breakpoints.down('2K')]: {
      width: '108%',
    },
  };
});

export const StyledDanger = styled(ReportProblem)(() => {
  const theme: NebulaTheme = useTheme();
  return { height: '12px', color: theme.palette.secondary.main };
});

export const StyledTableBodyGrid = styled(Grid)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    '&:hover': {
      background: `${theme.palette.secondary.shade3} 0% 0% no-repeat padding-box`,
      opacity: '0.8',
      cursor: 'pointer',
    },
    mb: 1,
    mt: 1,
    pt: 0.5,
    pb: 0.5,
  };
});

export const StyledCardGrid = styled(Grid)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    background: `${theme.palette.primary.main} 0% 0% no-repeat padding-box`,
    boxShadow: `0px 8px 24px ${theme.palette.secondary.shade6}`,
    border: `1px solid ${theme.palette.primary.main}`,
    borderRadius: '12px 12px 0px 0px;',
    opacity: 1,
    boxSizing: 'border-box',
  };
});
export const StyledCardGridLabels = styled(Grid)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    background: `${theme.palette.secondary.main} 0% 0% no-repeat padding-box`,
    borderRadius: '0px 0px 12px 12px',
    boxSizing: 'border-box',
  };
});

export const StyledDialog = styled(Dialog)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    '& .MuiDialog-paper': {
      borderRadius: '25px 0 0 25px',
      display: 'flex',
      background: '#0a3d65',
      boxShadow: `0px 8px 24px ${theme.palette.secondary.shade6}`,
      backdropFilter: 'blur(30px)',
      opacity: 1,
      '& .MuiAccordion-root::before': {
        backgroundColor: 'transparent !important',
      },
    },
  };
});

export const StyledUtilizationStatus = styled(Grid)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: theme.palette.secondary.main,
    marginTop: 'auto',
    marginBottom: '6px',
  };
});

export const StyledAccordion = styled(Accordion)({
  backgroundColor: 'transparent !important',
  boxShadow: 'none !important',
});

export const StyledAccordionSummary = styled(AccordionSummary)({
  backgroundColor: 'transparent !important',
  marginLeft: '30px',
  marginRight: '30px',
});

export const StyledAccordionDetails = styled(AccordionDetails)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    backgroundColor: `${theme.palette.primary.main} !important`,
    borderRadius: '12px',
    alignContent: 'center',
    alignItems: 'left',
    marginLeft: '30px',
    marginRight: '30px',
  };
});

export const StyledAccordionDetail = styled(AccordionDetails)({
  backgroundColor: 'transparent !important',
  borderRadius: '12px',
  alignContent: 'center',
  alignItems: 'left',
  marginLeft: '25px',
  marginRight: '25px',
});

export const StyledGrid = styled(Grid)({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  position: 'relative',
  marginTop: '-5px',
});

export const StyledCard = styled(Card)({
  backgroundColor: 'transparent',
  overflow: 'visible',
  boxShadow: 'none',
});

export const StyledTimeBox = styled(Box)({
  display: 'flex',
  justifyContent: 'flex-end',
  mb: 1,
  opacity: 0.77,
});

export const StyledCardViewGrid = styled(Grid)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    background: `${theme.palette.secondary.main} 0% 0% no-repeat padding-box`,
    boxShadow: `0px 6px 12px ${theme.palette.secondary.shade3}`,
    border: `1px solid ${theme.palette.secondary.shade3}`,
    borderRadius: '10px',
    width: 330,
    margin: 3,
  };
});

export const StyledSelect = styled(Select)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    '& .MuiSelect-icon': {
      color: `${theme.palette.primary.shade1}`,
      position: 'absolute',
      right: 0,
      top: 'calc(64% - 1em)',
      [theme.breakpoints.down('2K')]: {
        top: 'calc(64% - 1.3em)',
      },
    },
    '& .MuiSelect-select': {
      marginTop: '3px',
      marginLeft: '10px',
    },
    '& .MuiInput-input': {
      color: `${theme.palette.primary.shade1}`,
      font: ' normal normal medium 13px/15px Gotham',
      letterSpacing: '0.21px',
      fontWeight: 450,
      backgroundColor: 'transparent !important',
    },
  };
});

export const StyledNoDataGrid = styled(Grid)(({ theme }) => {
  return {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '65vh',
    [theme.breakpoints.down('2K')]: {
      minHeight: '55vh',
    },
  };
});
export const MenuItemStyle = styled(MenuItem)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    borderBottom: `1px dotted ${theme.palette.primary.shade1}`,
    '&:last-child': { borderBottom: 'none' },
    font: 'normal normal 500 13px/15px Gotham',
    padding: 10,
    marginLeft: 1,
    color: `${theme.palette.primary.shade1}`,
  };
});
