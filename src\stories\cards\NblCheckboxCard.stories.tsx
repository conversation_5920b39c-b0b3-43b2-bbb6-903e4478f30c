//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NblCheckboxCard from 'sharedComponents/NblCheckboxCard';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';

type StoryProps = ComponentProps<typeof NblCheckboxCard>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Cards/NblCheckboxCard',
  component: NblCheckboxCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onCardClickHandler: { action: 'onCardClickHandler', type: 'function' },
    active: { type: 'boolean' },
    name: { type: 'string' },
    icon: { type: 'string' },
    title: { type: 'string' },
    disabled: { control: 'boolean' },
    width: { type: 'string' },
    checkBoxCardId: { type: 'string' },
    error: { type: 'string' },
    height: { type: 'string' },
    backgroundColor: { type: 'string' },
    isHover: { type: 'boolean' },
  },
};

export default meta;

const cardobj = {
  disabled: false,
  icon: 'CloudOutlined',
  id: 'test',
  name: 'Qualys',
  onCardClickHandler: () => {},
  title: 'title',
};

const Template = (props: StoryProps) => {
  return <NblCheckboxCard {...props} />;
};

export const CheckboxCard: Story = {
  args: {
    ...cardobj,
    width: 'auto',
    description: 'Used to raise request to reserve the IP address block',
    active: false,
    isHover: true,
  },
  render: (args) => (
    <NebulaTheme>
      <Template {...args} />
    </NebulaTheme>
  ),
};
