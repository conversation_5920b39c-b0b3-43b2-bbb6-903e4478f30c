import { act, render, screen } from '@testing-library/react';
import { MemoryRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import ThemeProvider from 'mock/ThemeProvider';
import AddRoleForm from '.';
import { RolePermissions } from 'mock/roles';
import RoleService from 'api/ApiService/RoleService';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';

jest.mock('react-toastify');

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'roles', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/roles/add-role'];

describe('Create AddRole new request form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  let getRolePermissionsSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getRolePermissionsSpy = jest.spyOn(RoleService.prototype, 'getRolePermissions');
    getRolePermissionsSpy.mockResolvedValue(RolePermissions);

    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AddRoleForm title="Add Role" permissions={{ canCreate: true }} onClose={handleClose} onSuccess={handleSuccess} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    expect(screen.getByText('Role Name *')).toBeInTheDocument();
    expect(screen.getByText('Permission Name *')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    const submitButton = getByText('Submit');
    const cancelButton = getByText('Cancel');
    expect(submitButton).toBeEnabled();
    expect(cancelButton).toBeEnabled();
  });
});
