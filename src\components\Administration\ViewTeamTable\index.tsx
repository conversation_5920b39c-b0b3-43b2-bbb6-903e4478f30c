import React, { useState, useEffect } from 'react';
// eslint-disable-next-line no-unused-vars
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

import RequestsGrid from 'components/RequestsGrid';
import AdministrationService from 'api/ApiService/AdministrationService';
import { ViewTeamPayload } from 'types';

import { AdministrationDialogData } from 'types';
import AddTeamForm from '../AddTeamForm';
import DialogBox from 'components/DialogBox/Dialog';
import ActionsColumn from '../ActionsColumn';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'mantis/themes/type';
import { useMediaQuery, useTheme } from '@mui/material';
import { getAdminColumnWidth } from 'utils/common';

interface ViewTeamsProps {}

const DIALOG_DATA: AdministrationDialogData = {
  initialState: { open: false, type: '', title: '', content: '', confirmationText: '', data: {} },
  deleteDialog: {
    title: 'Confirmation',
    content: 'Shall we proceed with this delete request?',
    confirmationText: 'Delete',
  },
  editDialog: {
    title: 'Confirmation',
    content: 'Yet to be implemented',
    confirmationText: 'Edit',
  },
};

const ViewTeamTable: React.FunctionComponent<ViewTeamsProps> = () => {
  const apiAdministrationService = new AdministrationService();
  const [confirmDialog, setConfirmDialog] = useState<AdministrationDialogData['initialState']>(DIALOG_DATA.initialState);
  const [viewTeamListData, setTeamListData] = useState<ViewTeamPayload[]>([]);
  const [teamData, setEditTeamData] = useState({ open: false, updateTeam: { teamName: '', groups: '', description: '' } });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [viewDetailsDialog, setViewDetailsDialog] = useState<{ open: boolean; id: string }>({ open: false, id: '' });
  const theme: NebulaTheme = useTheme();
  const isSmaller = useMediaQuery(theme.breakpoints.down('xl'));

  const fetchServiceRequestCatalogList = () => {
    setIsLoading(true);
    apiAdministrationService
      .getTeams()
      .then((res) => {
        if (res.status) {
          setTeamListData(
            res.data.map((viewData) => ({
              teamName: viewData?.teamName,
              groups: viewData?.groups,
              id: viewData?.id,
              description: viewData?.description,
            }))
          );
        } else {
          setTeamListData([]);
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  useEffect(() => {
    fetchServiceRequestCatalogList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const handleEdit = (row: any) => {
    setEditTeamData({
      open: true,
      updateTeam: {
        teamName: row.teamName,
        groups: row.groups,
        description: row.description,
      },
    });
  };
  const columns: GridColDef[] = [
    {
      field: 'teamName',
      headerName: 'Team Name',
      flex: 1,
    },
    {
      field: 'groups',
      headerName: 'Groups',
      flex: 1,
    },
    { field: 'description', headerName: 'Description', flex: 1 },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      width: getAdminColumnWidth(isSmaller),
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        const deleteClickHandler = () => {
          setConfirmDialog({ open: true, type: 'DELETED', ...DIALOG_DATA.deleteDialog, data: params });
        };
        return (
          <ActionsColumn
            permissions={{ canDelete: false, canUpdate: false }}
            onEditHandler={() => handleEdit(params.row)}
            onDeleteHandler={deleteClickHandler}
          />
        );
      },
    },
  ];

  const dialogConfirmHandler = () => {
    const {
      row: { id },
    } = confirmDialog.data;
    setTeamListData(viewTeamListData.filter((obj) => obj.id !== id));
    setConfirmDialog(DIALOG_DATA.initialState);
  };
  const handleCloseDialog = () => {
    setEditTeamData({
      ...teamData,
      open: false,
    });
  };
  const dialogCloseHandler = (): void => {
    setConfirmDialog(DIALOG_DATA.initialState);
  };
  return (
    <>
      <RequestsGrid
        isLoading={isLoading}
        rows={viewTeamListData}
        columns={columns}
        viewDetailsDialog={viewDetailsDialog}
        setViewDetailsDialog={setViewDetailsDialog}
        confirmDialog={confirmDialog}
        dialogConfirmHandler={dialogConfirmHandler}
        dialogCloseHandler={dialogCloseHandler}
        fetchData={fetchServiceRequestCatalogList}
      />

      {teamData.open && (
        <DialogBox fullWidth maxWidth={'lg'} open={teamData.open} onClose={handleCloseDialog}>
          <AddTeamForm title="Edit Team" onClose={handleCloseDialog} teamData={teamData.updateTeam} />
        </DialogBox>
      )}
    </>
  );
};

export default ViewTeamTable;
