import { useSelector } from 'react-redux';
import { useParams, Route, Routes } from 'react-router-dom';
import { useTheme } from '@mui/material';

import useNblNavigate from 'hooks/useNblNavigate';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import Forms from './Forms';
import Level2Layout from './Level2Layout';
import Level4Layout from './Level4Layout';
import CatalogRequests from './CatalogRequests';
// eslint-disable-next-line no-unused-vars
import { CatalogsItem } from 'store/reducers/catalogs';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { NebulaTheme } from 'NebulaTheme/type';
import { useCallback } from 'react';
import MigrateV1FirewallRequests from 'componentsV2/IaaS/MigrateV1FirewallRequests/ViewDetails'

interface CatalogLayoutsProps {}

interface CatalogLayoutLevelsProps {}

const CatalogLayoutLevels: React.FunctionComponent<CatalogLayoutLevelsProps> = () => {
  //Hooks
  const { catalogLevel1, catalogLevel2, catalogLevel3, catalogItem } = useParams();
  const theme = useTheme<NebulaTheme>();
  const gridBg = theme.palette.secondary.main;
  const navigate = useNblNavigate();
  const { allCatalogItems } = useSelector((state: State) => state.catalogs);

  const getLevel2CatalogName = useCallback(
    (level3ShortName?: string) => {
      let catalogLevel2Name = '';
      let catalogLevel2ShortName = '';

      if (catalogLevel1 && !catalogLevel2 && level3ShortName) {
        const level2CatalogItems = catalogs.level2?.[catalogLevel1];

        // Find the matching level2 catalog item based on level3ShortName
        const matchedLevel2 = level2CatalogItems?.find((level2) => {
          const level3CatalogItems = catalogs.level3?.[level2.shortName];
          return level3CatalogItems?.some((level3) => level3.shortName === level3ShortName);
        });

        if (matchedLevel2) {
          catalogLevel2Name = matchedLevel2.name;
          catalogLevel2ShortName = matchedLevel2.shortName;
        }
      } else if (catalogLevel1 && catalogLevel2) {
        // Return the name of the level2 item when both catalogLevel1 and catalogLevel2 are provided
        const level2CatalogItems = catalogs.level2?.[catalogLevel1];
        const matchedLevel2 = level2CatalogItems?.find((catalog) => catalog.shortName === catalogLevel2);
        catalogLevel2Name = matchedLevel2?.name || '';
        catalogLevel2ShortName = matchedLevel2?.shortName || '';
      }

      return { catalogLevel2ShortName, catalogLevel2Name };
    },
    [catalogLevel1, catalogLevel2, catalogLevel3, catalogItem, allCatalogItems]
  );

  const getAllCatalogData = () => {
    let level3Catalogs: CatalogsItem[] = [];
    if (catalogLevel1 && catalogs?.level2?.[catalogLevel1]?.length) {
      catalogs.level2[catalogLevel1].map((level2) => {
        if (catalogs.level3[level2.shortName]?.length) {
          catalogs.level3[level2.shortName].map((level3) => {
            level3Catalogs.push(level3);
          });
        }
      });
    }
    return level3Catalogs;
  };

  const catalogs = useSelector((state: State) => state.catalogs);
  const level3CatalogItems = catalogLevel2 ? catalogs.level3[catalogLevel2] : getAllCatalogData();

  if (catalogItem && catalogLevel1 && catalogLevel2 && catalogLevel3) {
    return (
      <Forms
        key={catalogItem}
        catalogItem={catalogItem}
        catalogLevel1={catalogLevel1}
        catalogLevel2={catalogLevel2}
        catalogLevel3={catalogLevel3}
      />
    );
  }

  const getLevel4CatalogItems = (level3ShortName: string) => {
    const level4CatalogItems = catalogs.level4[level3ShortName] || [];
    return level4CatalogItems;
  };

  const catalogLevel1Name = (catalogLevel1 && catalogs?.level1?.find((level1) => level1.shortName === catalogLevel1)?.name) || '';

  return (
    <NblGridContainer columns={12} rows={3}>
      <NblGridItem colspan={10} rowspan={3} backgroundColor={gridBg}>
        <NblBorderContainer padding={'0 24px 24px'}>
          <Level2Layout />
          <Level4Layout
            catalogLevel1={catalogLevel1 || ''}
            catalogLevel2={catalogLevel2 || ''}
            level3CatalogItems={level3CatalogItems}
            getLevel2CatalogName={getLevel2CatalogName}
            getLevel4CatalogItems={getLevel4CatalogItems}
          />
        </NblBorderContainer>
      </NblGridItem>
      <NblGridItem colspan={2} rowspan={3} minWidth={'280px'} backgroundColor={gridBg}>
        <NblBorderContainer
          title={`${catalogLevel1Name} Requests`}
          link={{ text: 'View more', navigateHandler: () => navigate(`/requests`) }}
          padding={'20px'}
        >
          <CatalogRequests />
        </NblBorderContainer>
      </NblGridItem>
    </NblGridContainer>
  );
};

const CatalogLayouts: React.FunctionComponent<CatalogLayoutsProps> = () => {
  return (
    <Routes>
      <Route path={'*'} element={<CatalogLayoutLevels />} />
      <Route path={':catalogLevel2'} element={<CatalogLayoutLevels />} />
      <Route path={`:catalogLevel2/:catalogLevel3/:catalogItem/details`} element={<MigrateV1FirewallRequests/>} />
      <Route path={':catalogLevel2/:catalogLevel3/:catalogItem/*'} element={<CatalogLayoutLevels />} />
    </Routes>
  );
};

export default CatalogLayouts;
