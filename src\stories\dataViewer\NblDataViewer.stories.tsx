//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblDataViewer from 'sharedComponents/NblDataViewer';

type StoryProps = ComponentProps<typeof NblDataViewer>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'DataViewer/NblDataViewer',
  component: NblDataViewer,
  tags: ['autodocs'],
  argTypes: {
    title: { type: 'string' },
    isEdit: { control: 'boolean' },
    onEditClick: { action: 'onClick', type: 'function' },
  },
};

export default meta;

const defaultData = [
  { name: 'Project', value: 'Placeholder' },
  { name: 'VM Size', value: 'Placeholder' },
  { name: 'VM count', value: 'Placeholder' },
  { name: 'Hostname', value: 'Placeholder' },
  { name: 'OS Version', value: 'Placeholder' },
  { name: '<PERSON><PERSON>rik SLA', value: 'Placeholder' },
  { name: 'SSH Key', value: 'Placeholder' },
  { name: 'Initial Password', value: 'Placeholder' },
  { name: 'Description', value: 'Placeholder' },
];
export const DataViewer: Story = {
  args: {
    data: defaultData,
    title: 'VM Details',
    isEdit: true,
  },
  render: (args) => (
    <NebulaTheme>
      <NblDataViewer {...args} />
    </NebulaTheme>
  ),
};
