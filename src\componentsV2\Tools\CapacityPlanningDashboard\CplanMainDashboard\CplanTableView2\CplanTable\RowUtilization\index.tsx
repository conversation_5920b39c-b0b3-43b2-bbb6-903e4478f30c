import { Box } from '@mui/material';
import ColorIndicatorIcon from '../../../../components/ColorIndicationIcon';
import { getUsageColor } from '../../../../utils/colors';
import NblTypography from 'sharedComponents/NblTypography';

interface RowUtilizationProps {
  params: any;
  utilization: string;
}
const RowUtilization: React.FunctionComponent<RowUtilizationProps> = ({ params, utilization }) => {
  const exactutilization =
    utilization === 'cpu'
      ? params.row.cpuUtilized
      : utilization === 'memory'
      ? params.row.memoryUtilized
      : utilization === 'storage'
      ? params.row.storageUtilized
      : '';
  const color = getUsageColor(exactutilization);
  return (
    <Box display="flex" flexDirection="row" alignItems="center" height="100%" justifyContent="center">
      <ColorIndicatorIcon color={color} />
      <NblTypography variant="body1" color="shade1">
        {exactutilization}
      </NblTypography>
    </Box>
  );
};

export default RowUtilization;
