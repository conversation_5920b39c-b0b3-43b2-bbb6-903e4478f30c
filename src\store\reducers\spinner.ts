// types
import { createSlice } from '@reduxjs/toolkit';

export const SPINNER_IDS = {
  approvalRequests: 'fetch-approval-request',
  collectorGroups: 'fetch-collector-groups',
  myRequests: 'fetch-my-requests',
  myResources: 'fetch-my-resources',
  offboardingDeviceDetails: 'fetch-offboarding-device-details',
  organizations: 'fetch-organizations',
  releasedHostNames: 'fetch-released-host-names',
  reserveIPblockDropdowns: 'fetch-reserve-IPblock-dropdowns',
  snmpCredentials: 'fetch-snmp-credentials',
  viewDetails: 'fetch-view-details',
  fetchingPermissions: 'fetch-permissions',
  vmReferenceData: 'fetch-vmReferenceData',
  vmNetworkOptions: 'fetch-vmNetworkOptions',
  vmProjectDetails: 'fetch-vmProjectDetails',
  vmResourcesData: 'fetch-vmResourcesData',
  groupPermissions: 'fetch-group-permissions',
  groups: 'fetch-groups',
  groupDetails: 'fetch-group-details',
  tags: 'fetch-tags',
  tagDetails: 'fetch-tag-details',
  tagKeysData: 'tag-keys-data',
  tagValuesData: 'tag-values-data',
  tagDestinationData: 'tag-destination-data',
  projectDetails: 'fetch-project-details',
  tagCatalogItems: 'tag-catalog-items',
  rolePermission: 'fetch-role-permissions',
  roleDetails: 'fetch-role-details',
  catalogGroupDetails: 'fetch-catalog-groups-details',
  catalogListDetails: 'fetch-catalog-lists-details',
  adminTiles: 'fetch-admin-tiles',
  adminTileRoles: 'fetch-admin-tile-roles',
  networkVLAN: 'fetch-network-VLAN',
  activitylogs: 'activity-logs',
  vmAppInstance: 'vm-app-instance',
  networkDetails: 'network-details',
  fetchDAPDeviceCofig: 'fetch-DAP-Device-Cofig',
  fetchPathAnalysis: 'fetch-path-analysis',
  fetchPermissionSet: 'fetch-permission-set',
  fetchDNPDetails: 'fetch-DNP-Details',
  tools: 'tools',
  fetchDeviceConfigResponse: 'fetch-device-config-response',
  fetchCertificateDetails: 'fetch-Certificate-Details',
  vmAppInstances: 'fetch-app-instances',
  fwV2RetryTicket: 'fw-v2-retry-ticket',
  fetchSecretsDirectory: 'fetch-secrets-directory',
  fetchSecretsList: 'fetch-secrets-list',
  fetchAdhocDetails: 'fetch-Adhoc-Details',
  passwordPolicy: 'create-password-policy',
};

export type Spinner = {
  spinnerData: Array<{
    id: string;
    status: boolean;
    message: string;
  }>;
};

// initial state
const initialState: Spinner = {
  spinnerData: [],
};

// ==============================|| SLICE - Spinner ||============================== //

const spinner = createSlice({
  name: 'spinner',
  initialState,
  reducers: {
    showSpinner(state, action) {
      const spinnerData = [...state.spinnerData];
      if (action.payload.status) {
        state.spinnerData = spinnerData.some((row) => row.id === action.payload.id) ? spinnerData : [...spinnerData, action.payload];
      } else {
        state.spinnerData = spinnerData.filter((row) => row.id !== action.payload.id);
      }
    },
  },
});

export default spinner.reducer;

export const { showSpinner } = spinner.actions;
