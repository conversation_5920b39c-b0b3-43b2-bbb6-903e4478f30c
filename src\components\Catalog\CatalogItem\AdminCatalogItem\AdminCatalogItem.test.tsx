import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import AdminCatalogItem from '.';
import ThemeProvider from 'mock/ThemeProvider';

describe('AdminCatalogItem component', () => {
  test('Should render the admin catalog item', async () => {
    const item = {
      name: 'Manage Projects',
      path: 'view-projects',
      editPath: 'project',
      icon: 'VisibilityOutlined',
      id: 'view-projects',
      description: 'View, edit, or delete projects and adjust network and tag settings as needed',
      disabled: false,
      shortName: 'projects',
      permissions: ['canRead', 'canUpdate', 'canDelete'],
    };

    const mockStore = configureMockStore();
    const store = mockStore({
      authorization: {
        adminPermissions: [{ shortName: 'projects', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
      },
      common: {
        exposureParams: [],
      },
    });

    const { getByText } = render(
      <BrowserRouter>
        <ReduxProvider store={store}>
          <ThemeProvider>
            <AdminCatalogItem {...item} />
          </ThemeProvider>
        </ReduxProvider>
      </BrowserRouter>
    );
    expect(getByText(item.name)).toBeInTheDocument();
  });
});
