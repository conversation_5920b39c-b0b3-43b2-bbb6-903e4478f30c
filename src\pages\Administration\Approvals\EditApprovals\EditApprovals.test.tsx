import { render, act, fireEvent } from '@testing-library/react';

import { MemoryRouter as Router, Routes, Route } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import EditApprovals from './index';
import ThemeProvider from 'mock/ThemeProvider';
import { GetApprovalsSuccessResponse } from 'mock/Approvals';
import AdministrationService from 'api/ApiService/AdministrationService';
import PermissionService from 'api/ApiService/PermissionService';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';
import { GetGroups } from 'mock/Groups';

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'approvals', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/approvals/IPAM-Approval-ADMIN'];

describe('EditApprovals component', () => {
  let getApprovalDetailsSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;
  let getGroupsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);

    getApprovalDetailsSpy = jest.spyOn(AdministrationService.prototype, 'getApprovals');
    getApprovalDetailsSpy.mockResolvedValue(GetApprovalsSuccessResponse);

    getGroupsSpy = jest.spyOn(PermissionService.prototype, 'getGroups');
    getGroupsSpy.mockResolvedValue(GetGroups);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <ThemeProvider>
            <Router initialEntries={BASE_ROUTE}>
              <ReduxProvider store={store}>
                <Routes>
                  <Route path="/administration/Approvals/:ApprovalName" element={<EditApprovals />}></Route>
                </Routes>
              </ReduxProvider>
            </Router>
          </ThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });

  test('Should render the Edit Approval with pre populated values', async () => {
    const selectedApproval = GetApprovalsSuccessResponse.data[0];
    const { getByText, getByLabelText } = await act(async () =>
      render(
        <ThemeProvider>
          <Router initialEntries={BASE_ROUTE}>
            <ReduxProvider store={store}>
              <Routes>
                <Route path="/administration/Approvals/:ApprovalName" element={<EditApprovals />}></Route>
              </Routes>
            </ReduxProvider>
          </Router>
        </ThemeProvider>
      )
    );

    expect(getByText('Edit Approvals')).toBeInTheDocument();
  });

  test('Should navigate to view Approval details when user clicks on  Cancel button', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <Router initialEntries={BASE_ROUTE}>
            <ReduxProvider store={store}>
              <Routes>
                <Route path="/administration/Approvals/:ApprovalName" element={<EditApprovals />}></Route>
                <Route path="/administration/Approvals/view-Approvals" element={<div>View Approval details</div>} />
              </Routes>
            </ReduxProvider>
          </Router>
        </ThemeProvider>
      )
    );
    fireEvent.click(getByText('Cancel'));
    expect(getByText('View Approval details')).toBeInTheDocument();
  });
});
