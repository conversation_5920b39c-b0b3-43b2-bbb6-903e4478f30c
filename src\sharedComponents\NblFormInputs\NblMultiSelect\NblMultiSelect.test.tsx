import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblMultiSelect from '.';

describe('NblMultiSelect component', () => {
  const props = {
    label: 'Label',
    name: 'name',
    placeholder: 'Select options',
    
    options: [
      { label: '<PERSON>', value: 'opt1' },
      { label: '<PERSON>', value: 'opt2' },
      { label: 'Alex', value: 'opt3' },
      { label: 'Jack', value: 'opt4' },
      { label: 'Tom', value: 'opt5' },
      { label: 'Jane', value: 'opt6' },
    ],
    helperText: 'helper text',
    error: false,
    disabled: false,
    maxLength: 10,
    mandatory: false,
    contained: false,
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblMultiSelect {...props} handleChange={() => jest.fn()} value={[]} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
