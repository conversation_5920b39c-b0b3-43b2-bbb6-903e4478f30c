import {
  AssetPayload,
  Devices,
  DownStreamError,
  VmToolsData,
  VmReferenceData,
  ProjectDetails,
  GroupsPermissions,
  TagMappingData,
  TagMetaData,
  GroupPermissionsPayload,
  ResourcesDetails,
  ProjectNetworkOptions,
  ApprovalsPayload,
  DbaasConfig,
  CatalogLevel01Data,
  CatalogLevel02Data,
  CatalogLevel03Data,
  CatalogLevel04Data,
  CatalogLevel01Payload,
  CatalogLevel02Payload,
  CatalogLevel03Payload,
  CatalogLevel04Payload,
  ServiceCatalogGroupsData,
  TagDetailProps,
  ServiceCatalogListData,
  CatalogLevel04ByShortName,
  AdminTileDetails,
  Roles,
  VmTypes,
  ActivityLogData,
  FirewallSplittedRules,
  SubAccountPayload,
  SubAccountGroupPayload,
  AppInstances,
  VPCCIDRSPayload,
  AccessKeys,
  F5MetaData,
  PermissionSetData,
  CreatePermission,
  CertificatePolicyFolders,
  CatalogItem,
  CorpnetVMReferenceData,
  RefDataOptions,
  NetworkData,
  MultiCatalogLevel02Payload,
  MultiCatalogLevel02Data,
  MultiCatalogLevel03Payload,
  MultiCatalogLevel03Data,
} from 'types';
import MultiLevelApprovalData from 'types/Interfaces/MultilevelApprovalData';
import { Facility, VropResource } from '../../componentsV2/Tools/CapacityPlanningDashboard/utils/types';
import { PageInfoData } from '../../types/Interfaces/PaginationResponse';
import permissionSetPayload from 'types/Interfaces/PermissionSetPayload';
import { GetRequestList, MetricsData } from 'store/reducers/usagemetricsrequest';
import { MyRequestList } from 'hooks/useFetchMyRequests';
import RemovePermission from 'types/Interfaces/RemovePermission';
import { Dayjs } from 'dayjs';
import OnboardNewProject from 'types/Interfaces/OnboardNewProject';
import { OnboardGroup } from 'types/Interfaces/OnboardGroup';
import { FormValue as ManageMultiEnvProjectPayload } from 'componentsV2/Administration/Projects/ManageProjects';
import DomainList from 'types/Interfaces/DomainList';
import CatalogApprovalPayload from 'types/Interfaces/CatalogApprovalPayload';
import GetCatalogApprovalPayload from 'types/Interfaces/GetCatalogApprovalPayload';
import CatalogPermissionpayload from 'types/Interfaces/CatalogPermissionPayload';
import { CatalogPermission } from 'types/Interfaces/CatalogAccessRequest';
import MultiEnvServiceCatalogListData from '../../types/Interfaces/MultiEnvServiceCatalogListData';
import { MultiENVProjectsResponse } from 'types/Interfaces/MultiENVProjectsResponse';
import { MultiEnvMyProjects } from 'types/Interfaces/MultiEnvMyProjects';
import VmwareVmReferenceData from 'types/Interfaces/VmwareVmReferenceData';
import { ManageProjectPermission } from 'types/Interfaces/ManageProjectPermissions';
import { Tag as MultiENVTag } from 'componentsV2/Administration/TagsTable';
import CreateNamespace from 'types/Interfaces/CreateNamespace';
import SecretVersionData from 'types/Interfaces/SecretVersionData';
import VM_ACTION from 'types/Enums/VMReConfigureActions';
import { SecretType } from 'types/Enums/SecretType';
import TOOLS_ACTION from 'types/Enums/EditVMWareToolsActions';
import TAG_ACTION from 'types/Enums/EditVMWareTagsActions';
import SecretsDeviceData from 'types/Interfaces/SecretsResponse';
import NamespaceList from 'types/Interfaces/NamespaceList';
import DeviceList from 'types/Interfaces/DeviceList';
import SecretList from 'types/Interfaces/SecretList';
import { RequestType } from 'types/Enums';

export type APISuccessResponse = {
  status: boolean;
};

export type ServiceResponse = APISuccessResponse & {
  data: {
    id: string;
    serviceRequestId: string;
    message: string;
    catalogShortName: string;
    ticketId?: string;
    ticketUrl?: string;
    status?: string;
    error?: any;
  };
};

export type SingleDeviceResponse = APISuccessResponse & {
  data: {
    id: string;
    serviceRequestId: string;
    message: string;
  };
};

export type SingleDeviceMetaDataResponse = APISuccessResponse & {
  data: {
    platform: string[];
    role: string[];
  };
};

export type MigrateResponse = {
  status: boolean;
  data: {
    items: Array<{
      _id: string;
      payload: FirewallNewRequestSubmit;
      requestType: string;
      serviceRequestId: string;
      status: string;
      systemUpdate: {
        FirewallV2ServiceRequestId: string;
        migrationStatus: string;
        jiraStatus: {
          status: string;
        };
      };
      downstreamResponseData: {
        href: string;
        key: string;
      };
    }>;
    pageInfo: PageInfoData;
  };
};
export type GetUserProfileResponse = APISuccessResponse & {
  data: {
    email: string;
    firstName: string;
    lastName: string;
    isApprover: boolean;
    userId: string;
    isAdmin: boolean;
    favoriteTools: [
      {
        _id: string;
        name: string;
        description: string;
        shortName: string;
        icon: string;
        component: string;
        enabled: boolean;
        formLink: string;
      }
    ];
  };
};

export type AddFavoriteResponse = APISuccessResponse & {
  data: { message: string; statusCode: number };
};

export type RemoveFavoriteResponse = APISuccessResponse & {
  data: { message: string; statusCode: number };
};

export type CreateAvailableNetworkPayload = {
  company: string;
  environment: string;
  addrReachability: string;
  ipAllocType: string;
  ipAddrType: string;
  cidrBlock: string;
  datacenter: string;
};

export type CreateFirewallRequestPayload = {
  projectName: string;
  projectCreator: string;
  date: string;
  supportOrganization: string;
  region: string;
  jiraIssueLink: string;
};

export type CreateMonitoringOrgPayload = {
  monitoring: {
    organization: {
      description?: string;
      datacenter?: string;
      company: string;
      address?: string;
      city?: string;
      state?: string;
      country?: string;
      firstname?: string;
      lastname?: string;
      title?: string;
      dept?: string;
      billingId?: string;
      crmId?: string;
      phone?: string;
      fax?: string;
      tollfree?: string;
      email?: string;
      theme?: string;
      longitude?: string;
      latitude?: string;
    };
  };
};

export interface Metadata {
  ipAddrType: string[];
  datacenter: string[];
  company: string[];
  environment: string[];
  addrReachability: string[];
  ipAllocType: string[];
  cidrBlock: {
    IPV4: number[];
    IPV6: number[];
  };
}

export type GetMetadataResponse = APISuccessResponse & {
  data: Metadata;
};

export type GetReleaseIPResponse = APISuccessResponse & {
  data: Array<{
    name: string;
    ipAddress: string[];
    reserveRequestId: string[];
  }>;
};

export type CreateReleaseIPRequestPayload = {
  name: string;
  ipAddress: string[];
  reserveRequestId: string[];
};

export type OnboardDevicePayload = {
  monitoring: {
    device: {
      hostName: string;
      organizationId: string;
      snmpCredentialId: string;
      collectorGroupId: string;
    };
  };
};

export type GetDeviceDetailsResponse = APISuccessResponse & {
  data: {
    deviceType: string;
    deviceId: string;
    ip: string;
    organizationName: string;
  };
};

export type GetCollectorGroupsResponse = APISuccessResponse & {
  data: Array<{ id: string; name: string }>;
};

export type GetOrganizationsResponse = APISuccessResponse & {
  data: Array<{ id: string; name: string }>;
};

export type GetSnmpDetailsResponse = APISuccessResponse & {
  data: Array<{ id: string; name: string }>;
};

export type GetPendingApprovalsResponse = {
  status: boolean;
  data: {
    netopsaskTicket: string;
    id: string;
    requestType: string;
    status: string;
    createdBy: string;
    serviceRequestId?: string;
    metadata: {
      serviceCatalog: {
        catalogName: string;
        catalogType: string;
      };
    };
    projectName: string;
    payload: any;
    startedAt?: any;
    completedAt?: any;
    approvalStatus: string;
    schemaVersion?: any;
    approvalDetails?: any;
    downstreamError: DownStreamError[];
    createdAt: string;
    updatedAt: string;
    evaluationResult?: { canApprove: boolean; reason?: string };
    multiLevelApprovals?: MultiLevelApprovalData[];
    requesterEmail: string;
    crqToggle?: boolean;
  }[];
};

export type GetPendingApprovalsDetailsResponse = {
  status: boolean;
  data: {
    id: string;
    requestType: string;
    status: string;
    createdBy: string;
    serviceRequestId?: string;
    items: Array<{
      _id: string;
      createdAt: string;
      createdBy: string;
      updatedAt: string;
      startedAt: string;
      completedAt: string;
      evaluationResult?: { canApprove: boolean; reason: '' };
      approvalDetails: ApprovalData[] | any;
      downstreamError: DownStreamError[];
      status: string;
      approvalStatus?: string;
      downstreamResponseData: {
        key: string;
        href: string;
      };
      multiLevelApprovals?: MultiLevelApprovalData[];
      serviceRequestId?: string;
      payload?: {
        name: string;
        projectName: string;
      };
      metadata: {
        serviceCatalog: {
          catalogName: string;
        };
      };
    }>;
    metadata: {
      serviceCatalog: {
        catalogName: string;
        catalogType: string;
      };
    };
    payload: any;
    startedAt?: string;
    completedAt?: string;
    approvalStatus: string;
    downstreamError: DownStreamError[];
    createdAt: string;
    updatedAt: string;
    evaluationResult?: { canApprove: boolean; reason?: string };
    multiLevelApprovals?: MultiLevelApprovalData[];
    requesterEmail: string;
    crqToggle?: boolean;
  };
};

export type UpdateApprovalsRequestPayload = {
  status: string;
  rejectedReason?: string | null;
};

export type UpdateApprovalsRequestResponse = APISuccessResponse & ServiceResponse;

export type GetMyRequestsResponse = APISuccessResponse & {
  data: Array<{
    id: string;
    createdAt: string;
    createdBy: string;
    updatedAt: string;
    startedAt: string;
    completedAt: string;
    downstreamError: DownStreamError[];
    status: string;
    approvalStatus?: string;
    downstreamResponseData: {
      key: string;
      href: string;
    };
    multiLevelApprovals?: MultiLevelApprovalData[];
    serviceRequestId?: string;
    metadata: {
      serviceCatalog: {
        catalogName: string;
      };
    };
    systemUpdate?: {
      warnings?: {
        details?: {
          message?: string;
        }[];
      };
    };
  }>;
};

export type GetPaginatedMyRequestsResponse = APISuccessResponse & {
  data: {
    items: Array<{
      netopsaskTicket: string;
      approvalDetails: any;
      approvalGroup?: string;
      level?: string;
      approvedOrRejectedAt?: string;
      rejectedReason?: string;
      approvedOrRejectedBy?: string;
      _id: string;
      createdAt: string;
      createdBy: string;
      updatedAt: string;
      startedAt: string;
      completedAt: string;
      downstreamError: DownStreamError[];
      status: string;
      approvalStatus?: string;
      downstreamResponseData: {
        key: string;
        href: string;
      };
      multiLevelApprovals?: MultiLevelApprovalData[];
      serviceRequestId?: string;
      payload?: {
        name: string;
        projectName: string;
      };
      metadata: {
        serviceCatalog: {
          catalogName: string;
        };
      };
      systemUpdate?: {
        warnings?: {
          details?: {
            message?: string;
          }[];
        };
      };
    }>;
    pageInfo: PageInfoData;
  };
};

export type FirewallRequest = APISuccessResponse & {
  id: number | null;
  source: {
    location: string;
    hostName: string;
    ipAddress: string;
    port: string;
  };
  destination: {
    location: string;
    hostName: string;
    ipAddress: string;
    port: string;
  };
  protocol: string;
  notes: string;
  errors: {
    source: {
      location: string;
      hostName: string;
      ipAddress: string;
      port: string;
    };
    destination: {
      location: string;
      hostName: string;
      ipAddress: string;
      port: string;
    };
    protocol: string;
  };
  valid: boolean;
};
export type FirewallRequestData = APISuccessResponse & {
  firewallRules: FirewallRequest[];
};
export type FirewallNewRequestData = APISuccessResponse & {
  data: FirewallRequestData;
};

/**
 * Additional or custom fields for various jira project types
 * i.e. (firewall, request feature, etc)
 */
export interface AdditionalRequestData {
  projectName: string;
  jiraIssueLink: string[];
  netopsaskTicket: string;
  requestingOrganization: string;
  capabilityArea: string;
  priority: string;
  justification: string;
  watchers: string;
}

// Generic fields for jira story creation
export interface RequestFeatureSubmit {
  userName: string;
  userEmail: string;
  summary: string;
  description: string;
  // Additional unique Fields Specifc to request type
  additionalRequestData: Partial<AdditionalRequestData>;
}

export interface SearchQuerySubmit {
  [key: string]: string | Dayjs | null;
}

export interface FirewallRules {
  source: {
    location: string;
    hostName: string;
    ipAddress: string;
    port: string;
  };
  destination: {
    location: string;
    hostName: string;
    ipAddress: string;
    port: string;
  };
  protocol: string;
  notes: string;
  valid: string;
  errors: string;
}

export interface FirewallNewRequestSubmit {
  projectName: string;
  projectCreator: string;
  date: string;
  supportOrganization: string;
  region: string[];
  jiraIssueLink: string[];
  netopsaskTicket: string;
  appId: string;
  businessRequestDate: string;
  firewallRules: FirewallRules[] | { ipv4: Array<FirewallRules[]>; ipv6: FirewallRules[] };
}

export interface FirewallNewRequestSubmitV2 {
  projectName: string;
  projectCreator: string;
  date: string;
  supportOrganization: string;
  region: string[];
  netopsaskTicket: string;
  nebulaProject: string;
  jiraIssueLink: string[];
  firewallRules: FirewallSplittedRules;
}

export interface InternalCertificateFormSubmit {
  domain: string;
  projectName: string;
  application: string;
  environment: string;
  appId: string;
  applicationName: string;
  certificateName: string;
  subjectAlternateNames: string[];
  certPassword: string;
  confirmPassword: string;
  subject: string;
  certificateType: string;
  certificateFormat: string;
}

export interface CommonFirewallNewRequestSubmit {
  project: string;
  requestCreator: string;
  requestName: string;
  appId: string;
  commonApplication: string;
  applicationIpAddresses: string;
  inboundPortAndProtocol: string;
  outboundPortAndProtocol: string;
  reason: string;
}

export interface InternalCertificateRequestSubmit {
  applicationDetails: {
    domain: string;
    project: string;
    application: string;
    environment: string;
    appId: string;
    applicationName: string;
  };
  certificateDetails: {
    certificateName: string;
    certPassword: string;
    confirmPassword: string;
    subjectAlternateNames: string[];
    policyFolder: { name: string; value: string };
    subject: string;
    certificateType: string;
    certificateFormat: string;
  };
  platformContext: {
    catalogId: string;
    envId: string;
    domainId: string;
  };
  projectName: string;
  deeplinkUrl: string;
}

export interface FirewallRequestDropdowns {
  status: boolean;
  data: {
    supportGroups: { value: string; id: string }[];
    region: { value: string; id: string }[];
  };
}

export interface InternalCertificateMetaData {
  status: boolean;
  data: {
    policyFolder?: string[];
    keyType: string[];
    validity: string[];
    type: string[];
    format: string[];
  };
}

export interface CommonFirewallRegions {
  status: boolean;
  data: { value: string; id: string }[];
}

export type GetDetailsResponse = APISuccessResponse & {
  data: AssetPayload;
};

export type GetHostsResponse = APISuccessResponse & {
  data: Array<Devices>;
};

export type GetHostInterfacesResponse = APISuccessResponse & {
  data:
    | Array<{
        type: string;
        number: string;
        status: string;
        description: string;
        protocol: string;
      }>
    | { message: string };
};

export type PostDNPRequestPayload = {
  hosts: Array<{ hostname: string }>;
};

export type ThemeConfig = {
  name: string;
  palette: {
    primary: {
      main: string;
    };
    text: {
      primary: string;
      disabled: string;
    };
    breadCrumbs: {
      textPrimaryColor: string;
    };
    button: {
      contained: {
        bgColor: string;
        textColor: string;
      };
      outlined: {
        borderColor: string;
        bgColor: string;
        textColor: string;
      };
    };
    checkbox: {
      color: string;
    };
    contentCard: {
      backgroundColor: string;
      backdropFilter: string;
      boxShadow: string;
      borderColor: string;
      hexagonBgColor: string;
      iconColor: string;
      textPrimaryColor: string;
      textSecondaryColor: string;
      hoveringColor: string;
      disabledColor: string;
    };
    divider: string;
    dropdown: {
      menuItemColor: string;
      selectedMenuItemBgColor: string;
      dividerColor: string;
    };
    dialog: {
      textColor: string;
    };
    forms: {
      warningMessageColor: string;
      titleColor: string;
      titleIconBgColor: string;
      titleIconColor: string;
      wrapperBoderColor: string;
      fieldGroupTitleColor: string;
      fieldBorderColor: string;
      fieldBackgroundColor: string;
      fieldLabelColor: string;
      fieldPlaceHolderColor: string;
      fieldTextColor: string;
      fieldReadOnlyTextColor: string;
      fieldAutocompleteReadonlyBgColor: string;
      viewportBgColor: string;
      tabs: {
        color: string;
        bgcolor: string;
        borderColor: string;
      };
      navigation: {
        color: string;
      };
    };
    footer: {
      backgroundColor: string;
      textPrimaryColor: string;
    };
    header: {
      backgroundColor: string;
      textPrimaryColor: string;
      profileMenu: {
        avatarBackgroundColor: string;
        activeMenuItemBgColor: string;
        selectedThemeIconColor: string;
      };
      listItemColor: string;
      listIconColor: string;
    };
    layout: {
      blurContainerBorder: string;
      blurContainerBgColor: string;
    };
    scrollbar: {
      trackBorderColor: string;
      thumbBgColor: string;
    };
    tabs: {
      textColor: string;
    };
    table: {
      headerBgColor: string;
      evenRowBgColor: string;
      oddRowBgColor: string;
      borderColor: string;
      columnDividerColor: string;
      rowTextColor: string;
      refreshIconColor: string;
      lightTextColor: string;
    };
    sidebar: {
      hamburger: {
        buttonBgColor: string;
        iconColor: string;
      };
      textPrimaryColor: string;
      selected: {
        textPrimaryColor: string;
        bgColor: string;
      };
      backgroundColor: string;
    };
    spinner: {
      themeLoaderIconColor: string;
    };
    stepper: {
      steplabel: string;
      iconColor: string;
      pendingStepColor: string;
    };
  };
};

export type PostDNPRequestResponse = APISuccessResponse & ServiceResponse;

export type GetThemeConfigResponse = APISuccessResponse & {
  data: ThemeConfig;
};

export type GetThemeBackgroundResponse = APISuccessResponse & {
  data: string;
};

export type CreateVirtualServerPayload = {
  metadata: {
    trackingId: string;
    deploymentName: string;
    description: string;
    userProfile: {
      orgId: string;
      teamId: string;
      projectName: string;
    };
    dataCenter: {
      dataCenterName: string;
      regionName: string;
      availabilityZone: string;
    };
    serviceCatalog: {
      catalogId: string;
      catalogName: string;
      catalogType: string;
      catalogSubType: string;
    };
  };
  compute: {
    virtualServer: {
      hostname: string;
      vmCount: string;
      description: string;
      price: string;
      cpu: string;
      memory: string;
      storage: string;
      template: string;
    };
  };
};

export interface ProjectDataCenterResponse {
  data: ProjectDataCenterOptions[];
  status: boolean;
}

export interface ProjectNetworkResponse {
  data: ProjectNetworkOptions[];
  status: boolean;
}

export interface ProjectDataCenterOptions {
  id: string;
  name: string;
}

export type AddProjectResponse = {
  status: boolean;
  data: {
    id: string;
    message: string;
  };
};

export type GetServiceRequestCatalogsResponseType = {
  status: boolean;
  data: Array<{
    id: number;
    catalogGroupName: string;
    description: string;
  }>;
};

export type GetProjectType = {
  status: boolean;
  data: Array<{
    id: number;
    projectName: string;
    projectSetting: string;
    description: string;
  }>;
};

export interface GetProjectResponse {
  status: boolean;
  data: Array<{
    id: string;
    projectName: string;
    projectShortName: string;
    description: string;
    updatedAt: string;
    createdAt: string;
    createdBy: string;
    updatedBy: string;
    emailDistribution: string;
    appId?: string;
    appName: string;
    projectSettings: ProjectSettings;
  }>;
}

export interface ProjectSettings {
  dataCenters: DataCenter[];
  tags?: Tag[];
}

export interface DataCenter {
  name: string;
  description: string;
  networks: Network[];
}

export interface Network {
  id: string;
  name: string;
  displayName: string;
  subnetIpv4: string;
  subnetipv6: string;
  zoneName: string;
  zoneId: string;
  cloudId: string;
  cloudName: string;
  description: string;
}

export interface Tag {
  name: string;
  value: string;
}

export interface VMSizeEdit {
  status: boolean;
  data: VMSizeEditPayload;
}

export interface VMSizeEditPayload {
  name: string;
  displayName: string;
  sizeName: string;
  catalogId: string;
  message?: string;
  type: string;
  config: {
    customMemory?: number;
    root: number;
    home: number;
    opt: number;
    var: number;
    var_log: number;
    var_log_audit: number;
    var_tmp: number;
    tmp: number;
    customVolume?: number;
    customCores: number;
    vmSequence: number;
  };
}

export interface VMSizeSubmission {
  status: boolean;
  data: VMSizePayload;
}

export interface VMSizePayload {
  name: string;
  sizename: string;
  message?: string;
  config: Config;
}

export interface Config {
  customMemory: number;
  root?: number;
  home?: number;
  opt?: number;
  var?: number;
  var_log?: number;
  var_log_audit?: number;
  var_tmp?: number;
  tmp?: number;
  customCores: number;
  vmSequence: number;
}

export interface GetVMTypeResponse {
  status: boolean;
  data: Array<VmTypes>;
}

export interface GetVMDataResponse {
  status: boolean;
  data: Array<ViewVMSizeData>;
}

export interface ViewVMSizeData {
  _id: string;
  name: string;
  config: Config;
  shortName: string;
}

export interface Config {
  size: Size;
}

export interface Size {
  small: Small;
  medium?: Medium;
  large?: Large;
  tiny?: Tiny;
  Midum?: Midum;
  'x-large'?: XLarge;
  'xx-Large'?: XxLarge;
}
export interface CommonSizeProperties {
  customMemory: number;
  root: string;
  home?: string;
  opt?: string;
  var?: string;
  var_log?: string;
  var_log_audit?: string;
  var_tmp?: string;
  tmp?: string;
  customCores: number;
}

export interface Midum {
  customMemory: string;
  root: string;
  home: number;
  opt: number;
  var: string;
  var_log: number;
  var_log_audit: string;
  var_temp: number;
  customCores: number;
  total: number;
}

export interface Small extends CommonSizeProperties {
  total: number;
  addDisks?: string;
  addDisk2?: number;
  disk2name?: string;
  addDisk3?: number;
  disk3name?: string;
  addDisk4?: number;
  disk4name?: string;
  addDisk5?: number;
  disk5name?: string;
  diskFilesystem?: string;
  customVolume?: number;
}

export interface Medium extends CommonSizeProperties {
  total?: number;
  addDisks?: string;
  addDisk2?: number;
  disk2name?: string;
  addDisk3?: number;
  disk3name?: string;
  addDisk4?: number;
  disk4name?: string;
  addDisk5?: number;
  disk5name?: string;
  diskFilesystem?: string;
}

export interface Large extends CommonSizeProperties {
  total?: number;
  addDisks?: string;
  addDisk2?: number;
  disk2name?: string;
  addDisk3?: number;
  disk3name?: string;
  addDisk4?: number;
  disk4name?: string;
  addDisk5?: number;
  disk5name?: string;
  diskFilesystem?: string;
  customVolume?: number;
}

export interface Tiny extends CommonSizeProperties {
  total?: number;
  customVolume?: number;
}

export interface XLarge extends CommonSizeProperties {
  total?: number;
  customVolume?: number;
}

export interface XxLarge extends CommonSizeProperties {
  total: number;
}

export interface ProjectSubmission {
  status: boolean;
  data: ProjectSubmissionPayload;
}

export interface ProjectSubmissionPayload {
  appName: string;
  emailDistribution: string;
  appId?: string;
  projectName: string;
  description: string;
  message?: string;
  projectSettings: Array<{
    dataCenters: Array<{
      name: string;
      description: string;
      network: Array<{ id: number }>;
    }>;
  }>;
}

export type AddServiceRequestCatalogResponseType = {
  status: boolean;
  data: Array<{
    option: string;
  }>;
};

export type GetPermissionRequestCatalogsResponseType = {
  status: boolean;
  data: Array<{
    id: number;
    permissionName: string;
    description: string;
  }>;
};

export type GetServiceCatalogListType = {
  status: boolean;
  data: Array<{
    id: number;
    level01: string;
    level02: string;
    level03: string;
    description: string;
  }>;
};

export type FileDownloadResponse = APISuccessResponse & {
  data: Blob;
};

export type GetRolesResponse = {
  status: boolean;
  data: Array<{
    id: number;
    roleName: string;
    permissionName: string;
    description: string;
  }>;
};

export type GetMyResourceResponse = APISuccessResponse & {
  data: Array<ResourcesDetails>;
};

export type GetPaginatedMyResourceResponse = APISuccessResponse & {
  data: { items: Array<ResourcesDetails>; pageInfo: PageInfoData };
};

export type GetLevel3PermissionsResponse = {
  status: boolean;
  data: {
    [key: string]: {
      Permissions: string[];
    };
  };
};

export type GetTeamsResponse = {
  status: boolean;
  data: Array<{
    id: number;
    teamName: string;
    groups: string;
    description: string;
  }>;
};

export type GetVmReferenceDataResponse = APISuccessResponse & {
  data: VmReferenceData;
};

export type GetCorpnetVmReferenceDataResponse = APISuccessResponse & {
  data: CorpnetVMReferenceData;
};

export type GetVmProjectDetailsResponse = APISuccessResponse & {
  data: ProjectDetails;
};

export type GetVmResourcesResponse = APISuccessResponse & {
  data: Array<{ id: number; name: string }>;
};

export type GetCorpNetOptionsResponse = APISuccessResponse & {
  data: Array<{ value: number; name: string }>;
};

export type GetVmAppInstanceResponse = APISuccessResponse & {
  data: AppInstances[];
};

export type GetVmToolsDataResponse = APISuccessResponse & {
  data: VmToolsData;
};
export type GetProjectTagsResponse = APISuccessResponse & {
  data: { [key: string]: string };
};

export type GetCorpnetRefOptions = APISuccessResponse & {
  data: RefDataOptions[];
};

export type GetCorpnetNetworkResponse = APISuccessResponse & {
  data: { networks: NetworkData[] };
};

type GroupsResponse = Array<{
  _id: string;
  groupName: string;
  emailDistribution: string[];
  description: string;
  orgId: string;
  updatedAt: string;
  createdAt: string;
  projectPermissions: Array<{
    projectId: string;
    projectName: string;
    roles: Array<{ _id: string; roleName: string }>;
  }>;
  catalogPermissions: Array<{
    catalogId: string;
    catalogName: string;
    roles: Array<{ _id: string; roleName: string }>;
  }>;
  adminTilePermissions: Array<{
    adminTileId: string;
    tileName: string;
    roles: Array<{ _id: string; roleName: string }>;
  }>;
}>;

export type GetGroupsResponse = {
  status: boolean;
  data: GroupsResponse;
};

export type DeleteCatalogPermissionsResponse = APISuccessResponse & {
  data: { message: string; successCode: number };
};

export type GetGroupDetails = {
  status: boolean;
  data: GroupsResponse;
};

export type GetTagsResponse = {
  status: boolean;
  data: Array<{
    serviceCatalogId: string;
    name: string;
    tags: { tagKey: string }[];
    shortName: string;
    description: string;
  }>;
};

export type GetTagDetailsResponse = {
  status: boolean;
  data: TagDetailProps[];
};

export type CreateVmFormPayload = {
  name: string;
  size: string;
  targetLayout: { id: number; code: string; shortName: string };
  group: { id: number; name: string };
  datacenter: string;
  cloudId: number;
  hostname: string;
  vmCount: number;
  resource: { id: number; name: string };
  network?: {};
  ipv4: boolean;
  ipv6: boolean;
  securityTools: string[];
  complianceTools: string[];
  observabilityTools: string[];
  inventoryTools: string[];
  projectId: string;
  catalogId: string;
};

export type CreateVmFormResponse = APISuccessResponse & ServiceResponse;

export type CreateStorageFormResponse = APISuccessResponse & ServiceResponse;

export type GetResourceDetailsResponse = {
  status: boolean;
  data: ResourcesDetails;
};

export type GetCmpGroupsData = {
  status: boolean;
  data: string[];
};

export type SearchResultsType = { [key: string]: string | number };
export type GetResourceSearchResponse = {
  status: boolean;
  data: SearchResultsType[];
};

export type RegenerateKeyResponse = {
  status: boolean;
  data: { requestId: string; resourcesDetails: { accessKey: AccessKeys[] } };
};

export type ValidateHostnamePayload = {
  hostName: string;
  vmCount: number;
  sequenceNumber?: number;
};

export type ValidateHostnameResponse = {
  status: boolean;
  data: {
    isValid: boolean;
    hostNames: string[];
    suggestedSequenceNumber: number;
  };
};

export type GetApprovalResponse = {
  status: boolean;
  data: Array<{
    _id: string;
    catalogL4Name: string;
    name: string;
    shortName: string;
    description: string;
    createdBy: string;
    createdAt: string;
    updatedAt: string;
    updatedBy: string;
    serviceCatalogItem: string;
    approvalDetails: ApprovalDetail[];
    isDeleted: boolean;
    __v: number;
  }>;
};

export type EditApprovalResponse = {
  status: boolean;
  data: {
    id: string;
    name: string;
    shortName: string;
    description: string;
    serviceCatalogItem: string;
    approvalDetails: ApprovalDetail[];
    isDeleted: boolean;
    createdAt: string;
    updatedAt: string;
  };
};

export interface ApprovalData {
  id?: number;
  level?: number;
  approvalGroup?: string;
  approvedOrRejectedBy?: string;
  approvedOrRejectedAt: string;
  approvalStatus?: string;
  comments?: string;
  rejectedReason?: string;
}

export interface CatalogAccessRequestPayload {
  groupId: string;
  description: string;
  emailDistribution: string[];
  catalogPermissions: CatalogPermission[];
}

export type catalogAccessRequestResponse = APISuccessResponse & ServiceResponse;

export type catalogPermissionResponse = APISuccessResponse & {
  data: CatalogPermissionspayload & {
    id: string;
    catalogShortName: string;
    createdAt: string;
    updatedAt: string;
  };
};

export type DomainsResponse = APISuccessResponse & {
  data: DomainList[];
};

export interface ApprovalDetail {
  level: number;
  groupId: string;
  approverCount: number;
}

export type GetGroupsPermissionsResponse = APISuccessResponse & {
  data: GroupsPermissions;
};

export type GetAdminTileResponse = APISuccessResponse & {
  data: { id: string; tileName: string }[];
};

export type GetAdminTileRoleResponse = APISuccessResponse & {
  data: Roles[];
};

export type AddGroupPermissionsPayload = GroupPermissionsPayload;

export type AddGroupPermissionsResponse = APISuccessResponse & ServiceResponse;

export type EditGroupPermissionsPayload = GroupPermissionsPayload;

export type EditGroupPermissionsResponse = APISuccessResponse & ServiceResponse;

export type DeleteGroupPermissionsResponse = APISuccessResponse & ServiceResponse;

export type GetServiceCatalogResponse = APISuccessResponse & {
  data: Array<{ id: string; name: string }>;
};

export type GetRolePermissionsResponse = APISuccessResponse & {
  data: { permissions: Array<{ permissionKey: string }>; type: Array<string> };
};

export type AddApprovalsResponse = APISuccessResponse & ServiceResponse;

export type GetMultiEnvCatalogApprovalResponse = APISuccessResponse & {
  data: {
    id: string;
    description: string;
    serviceCatalogId: string;
    approvalDetails: [
      {
        groupId: string;
        domain: string;
        level: number;
        approverCount: number;
      }
    ];
    isDeleted: boolean;
    createdAt: string;
    updatedAt: string;
  };
};

export type ManageNewMultiEnvProjectResponse = APISuccessResponse & {
  data: {
    id: string;
    projectName: string;
    emailDistribution: string;
    projectAdminGroup: string[];
    organization: string;
    vertical: string;
    department: string;
    description: string;
    tags: MultiENVTag[];
    projectShortName: string;
    createdAt: string;
    updatedAt: string;
    applications: {
      id?: string;
      name: string;
      tags: MultiENVTag[];
      environments: {
        id?: string;
        name: string;
        approvalRequiredToModifyResources: boolean;
        settings: {
          type: string;
          configurations: {
            domain: string;
            dataCenter: string;
            value: { id: string; zoneName?: string; name?: string; displayName?: string };
            active: boolean;
            description: string;
          }[];
        }[];
        tags: MultiENVTag[];
      }[];
    }[];
  };
};

export type CreateMultiEnvProjectPayload = {
  id?: string;
  projectName: string;
  projectShortName?: string;
  emailDistribution: string;
  projectAdminGroup: string[];
  organization: string;
  vertical: string;
  department: string;
  description: string;
  tags: MultiENVTag[];
  applications: ManageNewMultiEnvProjectResponse['data']['applications'];
};

export type ProjectPermissionPayload = ManageProjectPermission[];

export type ProjectPermissionResponse = APISuccessResponse & {
  data: { message: string; statusCode: number };
};

export type ProjectPermissionDeleteResponse = APISuccessResponse & {
  data: { message: string; statusCode: number };
};

export type ManageNewMultiEnvCatalogResponse = APISuccessResponse & {
  data: {
    id: string;
    name: string;
    level01Id: string;
    level02Id: string;
    level03Id: string;
    shortName: string;
    icon: string;
    description: string;
    component: string;
    tags: [
      {
        key: string;
        value: string;
        active: boolean;
        description: string;
        type: string;
      }
    ];
    domain: Array<{ domainId: string }> | null;
    approvalRequired: boolean;
    enabled: boolean;
    sequenceNumber: number;
    isDeleted: boolean;
    createdBy: string;
    createdAt: '2025-04-09T09:21:02.365Z';
    updatedAt: '2025-04-09T09:21:02.365Z';
  } & {
    id: string;
    catalogShortName: string;
  };
};

export type MultiEnvCatalogPermissionResponse = APISuccessResponse & {
  data: [
    {
      groupId: 'string';
      groupName: 'string';
      domain: [
        {
          domainId: 'string';
          roles: [
            {
              roleId: 'string';
            }
          ];
        }
      ];
    }
  ];
};

export type GetMultiEnvProjectDetailsResponse = APISuccessResponse & {
  data: MultiENVProjectsResponse;
};

export type GetMultiENVMyProjectsResponse = APISuccessResponse & {
  data: MultiEnvMyProjects;
};

export type GetAllMultiEnvProjectResponse = APISuccessResponse & {
  data: Array<
    ManageMultiEnvProjectPayload & {
      id: string;
      projectShortName: string;
      createdAt: string;
      updatedAt: string;
    }
  >;
};

export type GetEnvPermissionResponse = APISuccessResponse & {
  data: {
    applicationId: string;
    applicationName: string;
    groups: {
      groupId: string;
      groupName: string;
      envPermission: {
        envId: string;
        roles: {
          roleId: string;
          roleName: string;
        }[];
      };
    }[];
  }[];
};

export type GetEnvRolesResponse = APISuccessResponse & {
  data: Array<{ _id: string; roleName: string }>;
};

export type NetworkSettingTypesResponse = APISuccessResponse & {
  data: Array<{ label: string; value: string }>;
};

export type AddApprovalsPayload = ApprovalsPayload;

export type GetServiceCatalogItemResponseType = APISuccessResponse & {
  data: Array<{ id: string; name: string }>;
};

export type AddCatalogApprovalPayload = CatalogApprovalPayload;
export type ApprovalPayload = GetCatalogApprovalPayload;

export type CatalogPermissionspayload = CatalogPermissionpayload;

export type GetApprovalGroupResponseType = APISuccessResponse & {
  data: Array<{ _id: string; groupName: string }>;
};

export type GetApproverCountAndLevelType = APISuccessResponse & {
  data: {
    maxApproverCount: number;
    maxApproverLevel: number;
  };
};

export type CatalogOptions = Array<{
  id: string;
  name: string;
}>;

export type ServiceCatalogMetaDataType = {
  status: boolean;
  data: CatalogOptions;
};
export type TagsMetaDataType = {
  status: boolean;
  data: TagMetaData;
};

export type GetDbaasConfigResponse = {
  status: boolean;
  data: DbaasConfig;
};

export type GetCatalogLevel01ListResponse = APISuccessResponse & {
  data: CatalogLevel01Data[];
};

export type GetCatalogLevel02ListResponse = APISuccessResponse & {
  data: CatalogLevel02Data[];
};

export type GetCatalogLevel03ListResponse = APISuccessResponse & {
  data: CatalogLevel03Data[];
};

export type GetCatalogLevel04ListResponse = APISuccessResponse & {
  data: CatalogLevel04Data[];
};

export type GetCatalogLevel04DetailsResponse = APISuccessResponse & {
  data: CatalogLevel04Data;
};

export type AddCatalogLevel01Payload = CatalogLevel01Payload;

export type CatalogLevel01Response = APISuccessResponse & ServiceResponse;

export type AddCatalogLevel02Payload = CatalogLevel02Payload;

export type CatalogLevel02Response = APISuccessResponse & ServiceResponse;

export type AddCatalogLevel03Payload = CatalogLevel03Payload;

export type CatalogLevel03Response = APISuccessResponse & ServiceResponse;

export type AddCatalogLevel04Payload = CatalogLevel04Payload;

export type CatalogLevel04Response = APISuccessResponse & ServiceResponse;
export type ProvisionDBPayload = {
  name: string;
  projectName: string;
  datacenter: string;
  network?: {};
  clusterSize: string;
  appCode: string;
  dbSize: string;
  vmUserName: string;
  dbDetails: Array<{
    dbName: string;
    dbConfig: Array<{ dbUserName: string; permissions: string[] }>;
  }>;
};

export type ProvisionDBPayloadV2 = {
  shortName: string | undefined;
  projectName: string;
  projectId: string | undefined;
  datacenterDetails: Array<{
    datacenter: string;
    vmCount: number;
    primary: boolean;
    network: {
      description: string | null;
      displayName: string | null;
      id: number;
      name: string;
      subnetIpv4: string | null;
      subnetIpv6: string | null;
      zoneId: string;
      zoneName: string;
      morpheusNetworkName?: string;
      cloudId: number;
    };
  }>;
  clusterSize: string;
  appCode: string;
  dbSize: string;
  vmUserName: string;
  platformContext: {
    catalogId: string;
    envId: string;
    domainId: string;
  };
  dbDetails: Array<{
    dbName: string;
    dbConfig: Array<{ dbUserName: string; permissions: string[] }>;
  }>;
};

export type MetricApiServicePayload = {
  query: string;
};

export type ProvisionDBResponse = APISuccessResponse & ServiceResponse;

export type selectedCatalogs = {
  id: string;
  name: string;
  value: string;
  parentId: string;
  requestType?: string;
};

export type CatalogList = {
  id: string;
  name: string;
  value: string;
  parentId: string;
  subCatalogs: catalogs2[];
};
export type catalogs2 = {
  id: string;
  name: string;
  value: string;
  subCatalogs: catalogs3[];
};

export type catalogs3 = {
  id: string;
  name: string;
  value: string;
  subCatalogs: catalogs4[];
};
export type catalogs4 = {
  id: string;
  name: string;
  value: string;
  organizationnames: string[];
  requestType: string;
};

export type ResourceList = {
  id: string;
  name: string;
  value: string;
};

export type MetricApiServiceResponse = APISuccessResponse & {
  data: {
    data: {
      metricsData: MetricsData;
      catalogs: CatalogList[];
      resources: ResourceList[];
      getRequestList: GetRequestList;
      getRequestListByOrgName: GetRequestList;
    };
  };
};

export type RequestSummaryMonthlyData = {
  requestType: string;
  summary: {
    month: number;
    count: number;
  }[];
};

export type RequestSummaryApiServices = APISuccessResponse & {
  data: { data: { getRequestSummary: MetricsData } };
};

export type RequestSummaryByMonthResponse = APISuccessResponse & {
  data: {
    data: {
      getRequestSummaryByMonth: RequestSummaryMonthlyData[];
    };
  };
};

export interface getAllRequestStatusRes {
  _id: string;
  requests: requestStatusType[];
}

export interface requestStatusType {
  status: string;
  serviceRequestId: string;
  date: string;
}

export type GetCatelogAndResourceResponeType = APISuccessResponse & {
  data: { data: { catalogs: CatalogList[] } };
};

export type GetAllRequestStatusType = APISuccessResponse & {
  data: { data: { getLastestRequestStatus: getAllRequestStatusRes[] } };
};

export type GetServiceCatalogGroupsResponse = APISuccessResponse & {
  data: ServiceCatalogGroupsData[];
};

export type GetAllCatalogItemsResponse = APISuccessResponse & {
  data: { data: { catalogs: CatalogItem[] } };
};

export type GetLandingPageNblRequestSummaryResponse = APISuccessResponse & {
  data: {
    data: {
      getRequestSummary: {
        totalRequests: number;
        approvedRequests: number;
        failedRequests: number;
        processingRequests: number;
      };
    };
  };
};

export type GetLandingPageNblRequestStatsResponse = APISuccessResponse & {
  data: {
    data: {
      getRequestSummaryByMonth: {
        requestType: string;
        summary: {
          month: number;
          count: number;
        }[];
      }[];
    };
  };
};

export type GetMyRequestsGraphQLResponse = APISuccessResponse & {
  data: {
    data: MyRequestList;
  };
};

export type GetLandingPageFavouriteToolsResponse = APISuccessResponse & {
  data: {
    userId: string;
    favoriteTools: {
      _id: string;
      name: string;
      description: string;
      shortName: string;
      icon: string;
      component: string;
      enabled: boolean;
      formLink: string;
    }[];
  };
};

export type AddRoleResponse = APISuccessResponse & {
  data: { message: string; statusCode: number };
};

export type VMDeleteResponse = APISuccessResponse & {
  data: { message: string; successCode: number };
};

export type CancelSubrequestFirewallTicketResponse = APISuccessResponse & {
  data: {
    message: string;
  };
};

export type DeviceConfigLookupResponse = APISuccessResponse & {
  data: {
    ipAddress: string;
    aclConfig: string;
  };
};

export type CertificateRenewalResponse = APISuccessResponse & {
  status: boolean;
  message: string;
};

export interface ImpactedDevicesResponse {
  data: {
    serviceRequestId: string;
    subRequestId: string;
    impactedDevices: {
      target: [
        {
          name: string;
          type: string;
          vendor: string;
          isImpacted: boolean;
          owner: string;
          organization: string;
        }
      ];
      source: string;
      destination: string;
      ruleId: number;
    }[];
  };
}

export type GetCatalogLevel01ByShortNameResponse = APISuccessResponse & {
  data: CatalogLevel01Data;
};

export type GetCatalogLevel02ByShortNameResponse = APISuccessResponse & {
  data: CatalogLevel02Data;
};

export type GetCatalogLevel03ByShortNameResponse = APISuccessResponse & {
  data: CatalogLevel03Data;
};

export type ServiceCatalogListDataResponse = APISuccessResponse & {
  data: ServiceCatalogListData[];
};

export type MultiEnvServiceCatalogListDataResponse = APISuccessResponse & {
  data: MultiEnvServiceCatalogListData[];
};

export type CatalogLevel04ByShortNameResponse = APISuccessResponse & {
  data: CatalogLevel04ByShortName;
};

export type AddTagResponse = APISuccessResponse & ServiceResponse;

export type GetAdminTilePermissionsResponse = APISuccessResponse & {
  data: Array<AdminTileDetails>;
};

export type GetResources = APISuccessResponse & {
  data: Array<VropResource>;
};
export type GetLatestTimeStamp = APISuccessResponse & {
  data: Array<{ lasttimestamp: string; domainid: number; domainname: string }>;
};

export type GetFacilities = APISuccessResponse & {
  data: Array<Facility>;
};

export type GetFacility = APISuccessResponse & {
  data: Facility;
};

export type VmActivityLogs = {
  hostName: string;
  status: string;
  resourceId: string;
  ipAddress: {
    ipv4Address: string;
    ipv6Address: string;
  };
};

export type GetVmActivityLogs = APISuccessResponse & {
  data: VmActivityLogs[];
};
export interface PostFacilities {
  facilityname: string;
  streetaddress: string;
  city: string;
  statecode: string;
  zip: number;
  facilitytype: string;
  region: string;
  resources: Array<{
    resourceid: string;
    resourcelabel: string;
    latitude: number;
    longitude: number;
  }>;
}

export type GetActivityLogResponse = APISuccessResponse & {
  data: ActivityLogData;
};
export type GetFirewallSplittedRules = APISuccessResponse & {
  data: FirewallSplittedRules;
};

export type CancelFirewallTicketResponse = APISuccessResponse & {
  data: {
    message: string;
  };
};

export type CloseTuffinTicketResponse = APISuccessResponse & {
  data: {
    message: string;
  };
};

export interface ImpactedDevicesResponse {
  data: {
    serviceRequestId: string;
    subRequestId: string;
    impactedDevices: {
      target: [
        {
          name: string;
          type: string;
          vendor: string;
          isImpacted: boolean;
          owner: string;
          organization: string;
        }
      ];
      source: string;
      destination: string;
      ruleId: number;
    }[];
  };
}

export interface AccountData {
  accountId: string;
  accountName: string;
  accountDescription: string;
  accountQuota?: string;
}
export interface BucketData {
  bucketName: string;
  bucketQuota: string;
}

export interface GetStorageAccountResponse {
  status: boolean;
  data: { shortForm: string; matchingAccounts: AccountData[] };
}

export interface GetStorageBucketResponse {
  status: boolean;
  data: BucketData[];
}

export interface GetStorageFileSystemResponse {
  status: boolean;
  data: { shortForm: string; filteredNFS: FileSystemData[] };
}
export interface FileSystemData {
  fileSystemName: string;
}

export interface StorageS3Payload {
  project: {
    id: string;
    appId: string;
    appName: string;
    emailDistribution: string;
    projectName: string;
    projectShortName: string;
  };
  account?: string;
  accountName: string;
  accountQuota: string;
  dataCenter: string;
  accountDescription: string;
  bucketName: string;
  versioning: string;
  bucketQuota: string;
  bucketDescription: string;
  userDetails: any;
}

export type GetSubAccountDataResponse = APISuccessResponse & {
  data: Array<{ label: string; value: string }>;
};

export type createSubAccountPayload = SubAccountPayload;

export type createSubAccountGroupPayload = SubAccountGroupPayload;

export type createPermissionSetPayload = permissionSetPayload;

export type createSubAccountResponse = APISuccessResponse & ServiceResponse;

export type createSubAccountGroupResponse = APISuccessResponse & ServiceResponse;

export type createPermissionSetResponse = APISuccessResponse & ServiceResponse;

export type CIDRSPayload = VPCCIDRSPayload;

export type GetDAPDeviceConfigResponse = APISuccessResponse & {
  data: {
    builtConfig: string;
    resultDiff: string;
  };
};

export interface PathAnalysisPayload {
  sourceIp: string;
  destinationIp: string;
  sourcePort: string;
  destinationPort: string;
  protocol: string;
}

export type PathAnalysisImageResponse = APISuccessResponse & {
  data: any;
};

export type GetF5MetaData = APISuccessResponse & {
  data: F5MetaData;
};

export type SubmitF5RequestResponse = APISuccessResponse & ServiceResponse;

export type SubmitZTPSingleDeviceResponse = APISuccessResponse & SingleDeviceResponse;

export type ZTPMetaDataResponse = APISuccessResponse & SingleDeviceMetaDataResponse;

export type GetPermissionSetResponse = APISuccessResponse & {
  data: PermissionSetData[];
};

export type GetPoliciesDataResponse = APISuccessResponse & {
  data: Array<{ id: string; name: string; description: string; arn: string }>;
};

export type CreatePermissionPayload = CreatePermission;

export type RemovePermissionPayload = RemovePermission;

export type GetPolicyFoldersPayload = {
  externalCaConfigReference: {
    link: string;
  };
  policyFolderPath: string;
};

export type GetPolicyFoldersResponse = APISuccessResponse & {
  data: CertificatePolicyFolders;
};

export type createPermissionSetGroupPayload = {
  orgId: string;
  groupPermissionSet: {
    permissionId: string;
    groupId: string;
  }[];
};

export type SearchResponse = APISuccessResponse & {
  data: {
    projects: Array<{
      appName: string;
      createdAt: string;
      createdBy: string;
      description: string;
      projectname: string;
      projectShortname: string;
    }>;
    servicerequests: Array<{
      createdAt: string;
      createdBy: string;
      payload: { projectName: string };
      serviceRequestId: string;
      status: string;
    }>;
    resources: Array<{
      catalogType: string;
      createdAt: string;
      createdBy: string;
      projectId: string;
      requestId: string;
      resourceId: string;
      resourcesName: string;
      status: string;
    }>;
  };
};
export type CreateNamespacePayload = CreateNamespace;

export type OnboardNewProjectPayload = OnboardNewProject;
export type OnboardGroupPayload = OnboardGroup;

export type OnboardGroupResponse = APISuccessResponse & ServiceResponse;

export type GetAllOrgResponse = APISuccessResponse & {
  data: Array<{ label: string; value: string }>;
};

export type GetVerticalResponse = APISuccessResponse & {
  data: Array<{ label: string; value: string }>;
};

export type GetDepartmentResponse = APISuccessResponse & {
  data: Array<{ label: string; value: string }>;
};

export type MultiEnvCatalogLevel02Payload = MultiCatalogLevel02Payload;

export type GetMultiEnvCatalogLevel02ByShortNameResponse = APISuccessResponse & {
  data: MultiCatalogLevel02Data;
};

export type MultiEnvCatalogLevel03Payload = MultiCatalogLevel03Payload;

export type GetMultiEnvCatalogLevel03ByShortNameResponse = APISuccessResponse & {
  data: MultiCatalogLevel03Data;
};

export type CreateSpecS3Payload = {
  project: string;
  projectName: string;
  iacProjectName: string;
  namespaceId: number;
  bucket: string;
  versioning: string;
};

export type CreateSpecEC2Payload = {
  project: string;
  projectName: string;
  iacProjectName: string;
  namespaceId: number;
  ec2_name: string;
  subnet_id: string;
  ami_id: string;
  instance_type: string;
};

type Credentials = {
  name: string;
  path: string;
  provider: string;
};

export type CreateSpecEKSPayload = {
  project: string;
  projectName: string;
  iacProjectName: string;
  namespaceId: number;
  zone_id: string;
  cluster_name: string;
  cluster_version: string;
  ami_id: string;
  min_size: number;
  desired_size: number;
  max_size: number;
  disk_size: number;
  vpc_id: string;
  aws_region: string;
  control_plane_subnets: Array<string>;
  public_subnets: Array<string>;
  node_group_subnets: Array<string>;
  create_alb: boolean;
  alb_internal: boolean;
  waf_name: string;
  default_action: string;
  falcon_cid: string;
  falcon_client: string;
  falcon_secret: string;
  acm_domain_name: string;
  credentials: Array<Credentials>;
};

export type GetVmwareVmReferenceDataResponse = APISuccessResponse & {
  data: VmwareVmReferenceData;
};

export type GetVmwareVmResourcesResponse = APISuccessResponse & {
  data: Array<{ id: string; name: string }>;
};

export type CreateVmwareVmFormPayload = {
  name: string;
  size: string;
  targetLayout: { id: string; code: string; shortName: string };
  datacenter: string;
  cloud: { id: string; name: string };
  hostname: string;
  vmCount: number;
  resource?: { id: string; name: string };
  network?: {};
  ipv4: boolean;
  ipv6: boolean;
  securityTools: string[];
  complianceTools: string[];
  observabilityTools: string[];
  inventoryTools: string[];
  projectId: string;
  catalogId: string;
};

export type CreateVPCResponse = APISuccessResponse & ServiceResponse;

export type GetAllDropdownResponse = APISuccessResponse & {
  data: Array<{ label: string; value: string }>;
};

export type GetNameSpaceNameResponse = {
  status: boolean;
  data: {
    items: Array<{
      namespaceName: string;
      resourceId: string;
      status: string;
      catalogType: string;
      catalogLevel03: string;
      platformContext: {
        catalogId: string;
        envId: string;
        domainId: string;
      };
      requestType: RequestType.CREATE_NAMESPACE;
      resourcesDetails: {
        request_id: string;
        lease_id: string;
        renewable: boolean;
        lease_duration: number;
        data: {
          id: string;
          path: string;
        };
        wrap_info: null;
        warnings: null;
        auth: null;
        namespace: string;
        path: string;
      };
    }>;
  };
};

export type GetSecretDataResponse = APISuccessResponse & {
  data: SecretVersionData[];
};
export type ReconfigureVMwareVMResponse = {
  status: boolean;
  data: {
    serviceRequestId?: string;
    id?: string;
    message?: string;
  };
};

export type EditVMwareResponse = APISuccessResponse & {
  data: { message: string; statusCode: number };
};

export type ReconfigureVmPayload = {
  resourceId: string;
  volumes?: {
    size: string;
    diskFileSystem: string;
    diskName: string;
    action: VM_ACTION.add | VM_ACTION.delete | undefined;
  }[];
  memory?: number;
  coreCount?: number;
  coresPerSocket?: number;
  networks?: {
    id: string;
    network: string;
    ipMode: string | undefined;
    subnetIpv4: string;
    subnetIpv6: string;
    ipv4: boolean;
    ipv6: boolean;
    ipv4Address: string;
    ipv6Address: string;
    action: VM_ACTION.add | VM_ACTION.delete | undefined;
  }[];
};

export type EditVMWarePayload = {
  resourceId: string;
  resourceName: string;
  hostName: string;
  shortName: string;
  owner: string | null;
  description: string;
  layoutShortName: string;
  networks: {
    id: string;
    ipMode: string;
    network: string;
    subnetIpv4: string;
    subnetIpv6: string;
    oldIpv4Address: string;
    oldIpv6Address: string;
    newIpv4Address: string;
    newIpv6Address: string;
    action: VM_ACTION.add | VM_ACTION.delete | undefined;
  }[];

  tags: {
    tagName: string;
    tagValue: string;
    action: TAG_ACTION.add | undefined;
  }[];
  tools: {
    toolName: string;
    toolType: string;
    action: TOOLS_ACTION.install | TOOLS_ACTION.uninstall | undefined;
  }[];
};
export type GetVMWMKSConsoleConnectionDetailsPayload = {
  hostName: string;
  requestId: string;
  user: {
    email: string;
    groups: {
      id: string;
      name: string;
    }[];

    id: string;
    name: string;
  };
};

export type GetVMWMKSConsoleConnectionDetailsResponse = {
  status: boolean;
  data: {
    trackingId: string;
    hostName: string;
    webMksTicket: {
      ticket: string;
      cfgFile: string;
      host: string;
      port: number;
      sslThumbprint: string;
      certThumbprintList: [];
      url: string;
    };
  };
};

export type GetSecretDetails = {
  status: boolean;
  data: {
    vaultKey: string;
    vaultPassword: string;
    resourceId: string;
    namespace: string;
    vaultPath: string;
    type: SecretType;
    policyId: string;
    policyName: string;
    rotationType?: string;
    nextRotationDate?: string;
    userNameKey?: string;
    userNamePassword?: string;
    secretTTLInHours?: number;
    resourcesName: string;
    catalogId: string;
    envId: string;
    domainId: string;
    projectName: string;
    applicationId: string;
    applicationName: string;
    environmentName: string;
    domainName?: string;
  };
};

export type DeleteSecretResponse = APISuccessResponse & {
  data: { message: string; statusCode: number };
};

export type SecretDeviceAssociationResponse = APISuccessResponse & {
  data: SecretsDeviceData[];
};

export type DeviceResponse = APISuccessResponse & {
  data: DeviceList[];
};

export type SecretKeyResponse = APISuccessResponse & {
  data: SecretList[];
};

export type NamespaceListResponse = APISuccessResponse & {
  data: NamespaceList;
};
export type AdhocDetails = {
  secretId: string;
  [key: string]: any;
};

export type DeviceListResponse = APISuccessResponse & {
  data: {
    type: 'device';
    configuration: {
      _id: string;
      name: string;
      charterId: number;
      description: string;
      environment: string;
      asn: string;
      city: string;
      clliCode: string;
      market: string;
      siteName: string;
      state: string;
      vendorName: string;
      vendorModel: string;
      deviceRole: string[];
      sourceSystem: string;
      appId: string;
      createdAt: string;
      updatedAt: string;
      id: number;
    };
  }[];
};
