import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

export const validationSchema = yup.object().shape({
  isDevicesSelected: yup.boolean().test('is-device-selected', 'Device should be selected', function (value) {
    if (!value) {
      return this.createError({
        message: 'Atleast one device should be selected',
      });
    }
    return true;
  }),
  isDeviceInterfacesSelected: yup.boolean().test('is-device-interface-selected', 'Interfaces should be selected', function (value) {
    if (!value) {
      return this.createError({
        message: 'Atleast one interface should be selected for each device',
      });
    }
    return true;
  }),
  comment: yup
    .string()
    .trim()
    .required('Comment is required')
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});
