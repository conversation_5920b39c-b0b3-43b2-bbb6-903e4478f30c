import { act, render, screen } from '@testing-library/react';
import { MemoryRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import PermissionService from 'api/ApiService/PermissionService';
import AddApprovalForm from '../AddApprovalForm';
import ThemeProvider from 'mock/ThemeProvider';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';
import { GetGroups } from 'mock/Groups';

jest.mock('react-toastify');

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'approvals', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/approvals/add-approval'];

describe('Define/ Create approval Request form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  let getAdminCatalogItemsSpy: jest.SpyInstance;
  let getGroupsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);

    getGroupsSpy = jest.spyOn(PermissionService.prototype, 'getGroups');
    getGroupsSpy.mockResolvedValue(GetGroups);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Should render the form with all the fields', async () => {
    await act(async () => {
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AddApprovalForm permissions={{ canCreate: true }} title="Add Approval" onClose={handleClose} onSuccess={handleSuccess} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      );
    });

    expect(screen.getByText('Approval Name *')).toBeInTheDocument();
    expect(screen.getByText('Service Catalog *')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();

    const submitButton = screen.getByText('Submit');
    const cancelButton = screen.getByText('Cancel');
    expect(submitButton).toBeDisabled();
    expect(cancelButton).toBeEnabled();
  });
});
