import { act, render, waitFor, fireEvent } from '@testing-library/react';
import { MemoryRouter as Router, Routes, Route } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import ThemeProvider from 'mock/ThemeProvider';
import ViewGroup from '.';
import PermissionService from 'api/ApiService/PermissionService';
import { GroupRows } from 'mock/Groups';
import ViewGroups from 'pages/Administration/Groups/ViewGroups';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'groups', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/groups/manage-registered-ad-groups'];

describe('ViewGroups Component', () => {
  let getTeamsSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getTeamsSpy = jest.spyOn(PermissionService.prototype, 'getGroups');

    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);
  });

  afterEach(() => {
    getTeamsSpy.mockRestore();
  });

  test('Should display the No Record Found message', async () => {
    getTeamsSpy.mockResolvedValue({ status: false, data: [] });
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <Router initialEntries={BASE_ROUTE}>
            <ThemeProvider>
              <ViewGroup permissions={{ canRead: true }} />
            </ThemeProvider>
          </Router>
        </ReduxProvider>
      )
    );
    await waitFor(() => {
      expect(getTeamsSpy).toHaveBeenCalled();
    });
    expect(getByText('No Record Found')).toBeInTheDocument();
  });

  test('Should render the table with data ', async () => {
    getTeamsSpy.mockResolvedValue({ status: true, data: GroupRows.data });
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <Router initialEntries={BASE_ROUTE}>
            <ThemeProvider>
              <ViewGroups />
            </ThemeProvider>
          </Router>
        </ReduxProvider>
      )
    );
    await waitFor(() => {
      expect(getTeamsSpy).toHaveBeenCalled();
    });

    expect(getByText('Group Name')).toBeInTheDocument();
    expect(getByText('Description')).toBeInTheDocument();
    expect(getByText('Actions')).toBeInTheDocument();
    expect(getByText('Created Date')).toBeInTheDocument();
    expect(getByText('Updated Date')).toBeInTheDocument();
  });

  test('Clicking on edit icon should trigger the popup', async () => {
    getTeamsSpy.mockResolvedValue({ status: true, data: GroupRows.data });
    const { getAllByTestId, getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <Router initialEntries={BASE_ROUTE}>
              <Routes>
                <Route path="/administration/groups/manage-registered-ad-groups" element={<ViewGroup permissions={{ canRead: true }} />} />
                <Route path="/administration/groups/*" element={<div>Edit Registered AD group</div>} />
              </Routes>
            </Router>
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    await waitFor(() => {
      expect(getTeamsSpy).toHaveBeenCalled();
    });
    const editIcon = getAllByTestId('update-icon');
    fireEvent.click(editIcon[0]);

    await waitFor(() => {
      expect(getByText('Edit Registered AD group')).toBeInTheDocument();
    });
  });
});
