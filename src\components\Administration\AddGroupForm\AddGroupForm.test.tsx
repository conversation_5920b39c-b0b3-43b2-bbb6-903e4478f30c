import { act, render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter as Router } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import PermissionService from 'api/ApiService/PermissionService';
import AddGroupForm from '.';
import ThemeProvider from 'mock/ThemeProvider';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems, GetAdminTiles, GetAdminTileRoles } from 'mock/AdminTiles';

import { GetGroupsPermissions, AddGroupPermissionsData } from 'mock/Groups';

jest.mock('react-toastify');

const BASE_ROUTE = ['/administration/groups/register-ad-group'];

const mockStore = configureMockStore();

const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'groups', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

describe('Create AddGroup new request form', () => {
  let getGroupsPermissionsSpy: jest.SpyInstance;
  let addGroupPermissionsSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;
  let getAdminTilesSpy: jest.SpyInstance;
  let getAdminTileRolesSpy: jest.SpyInstance;

  beforeEach(async () => {
    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);

    getAdminTilesSpy = jest.spyOn(PermissionService.prototype, 'getAdminTiles');
    getAdminTilesSpy.mockResolvedValue(GetAdminTiles);

    getAdminTileRolesSpy = jest.spyOn(PermissionService.prototype, 'getAdminTileRoles');
    getAdminTileRolesSpy.mockResolvedValue(GetAdminTileRoles);

    getGroupsPermissionsSpy = jest.spyOn(PermissionService.prototype, 'getGroupsPermissions');
    getGroupsPermissionsSpy.mockResolvedValue(GetGroupsPermissions);

    addGroupPermissionsSpy = jest.spyOn(PermissionService.prototype, 'addGroupPermissions');
    addGroupPermissionsSpy.mockResolvedValue(AddGroupPermissionsData);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const handleClose = jest.fn();
  const handleSuccess = jest.fn();

  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AddGroupForm title="Register AD group" permissions={{ canCreate: true }} onClose={handleClose} onSuccess={handleSuccess} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    expect(screen.getByText('Register AD group')).toBeInTheDocument();
    expect(screen.getByText('Group Name *')).toBeInTheDocument();
    expect(screen.getByText('Email DL(s) *')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Project Permissions *')).toBeInTheDocument();
    expect(screen.getByText('Catalog Permissions *')).toBeInTheDocument();
    const submitButton = getByText('Submit');
    const cancelButton = getByText('Cancel');
    expect(submitButton).toBeDisabled();
    expect(cancelButton).toBeEnabled();
  });

  test('Should submit the request for adding the groups', async () => {
    const { getByText, getByLabelText, getByTestId } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AddGroupForm title="Register AD group" permissions={{ canCreate: true }} onClose={handleClose} onSuccess={handleSuccess} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );

    fireEvent.change(getByLabelText('Group Name *'), { target: { value: 'IPAM-ADMIN-GROUP' } });
    fireEvent.change(getByLabelText('Description'), { target: { value: 'Add permissions for Reserve IP Block, Release IP Block' } });

    fireEvent.click(getByTestId('add-project-permissions'));
    await waitFor(async () => {
      expect(getByText('Add Project Permissions')).toBeInTheDocument();
    });
  });
});
