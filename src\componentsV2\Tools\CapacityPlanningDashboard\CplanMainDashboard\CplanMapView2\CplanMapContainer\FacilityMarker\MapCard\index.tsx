import { Card, IconButton, useTheme } from '@mui/material';
import { PrevArrowIcon, NextArrowIcon } from 'assets/images/icons/custom-icons';
import { useState } from 'react';
import Carousel from 'react-material-ui-carousel';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblTypography from 'sharedComponents/NblTypography';
import ColorIndicatorIcon from '../../../../../components/ColorIndicationIcon';
// eslint-disable-next-line
import { NebulaTheme } from 'NebulaTheme/type';
import { getUsageColor } from '../../../../../utils/colors';
import { CalculatedFacility } from '../../../../../utils/types';
import { dispatch } from 'store';
import { setOverviewResource, setSelectedDomainIds } from 'store/reducers/capacityplanning';
import useNblNavigate from 'hooks/useNblNavigate';

interface CustomDotProps {
  onClick: () => void;
  active: boolean;
  index: number;
}

const CustomDot: React.FC<CustomDotProps> = ({ onClick, active }) => {
  return (
    <button
      onClick={onClick}
      style={{
        backgroundColor: active ? 'rgba(244,174,19,1)' : 'rgba(244,174,19,0.5)',
        border: 'none',
        width: '3px',
        height: '3px',
        borderRadius: '1px',
        cursor: 'pointer',
        margin: '0 1px',
      }}
    />
  );
};

interface MapCardProps {
  facility: CalculatedFacility;
}

const MapCard: React.FunctionComponent<MapCardProps> = ({ facility }) => {
  const theme: NebulaTheme = useTheme();
  const navigate = useNblNavigate();
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrevClick = () => {
    let index = currentIndex === 0 ? facility.resources.length - 1 : currentIndex - 1;
    const currentResource = facility.resources[index];
    dispatch(setOverviewResource(currentResource));
    setCurrentIndex(index);
  };
  const handleNextClick = () => {
    let index = currentIndex === facility.resources.length - 1 ? 0 : currentIndex + 1;
    const currentResource = facility.resources[index];
    dispatch(setOverviewResource(currentResource));
    setCurrentIndex(index);
  };

  return (
    <foreignObject
      x={10}
      y={10}
      width="200"
      height="100"
      transform={`scale(0.8)`}
      style={{ boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.2)', borderRadius: '10px' }}
    >
      <Card sx={{ height: '100%' }}>
        <Carousel
          index={currentIndex}
          onChange={(newIndex: any) => setCurrentIndex(newIndex)}
          indicators={false}
          animation="slide"
          autoPlay={false}
          navButtonsAlwaysInvisible
          navButtonsProps={{
            style: {
              backgroundColor: 'transparent',
            },
          }}
          PrevIcon={<PrevArrowIcon />}
          NextIcon={<NextArrowIcon />}
          sx={{ height: '100%' }}
        >
          {facility?.resources.map((calcRes: any, index: number) => (
            <NblFlexContainer key={index} direction="column" spacing={0} overflowY="visible" padding="0px 0px">
              <NblGridItem backgroundColor={`${theme.palette.primary.main}`}>
                <NblFlexContainer height="22px" justifyContent="center" alignItems="end">
                  <NblTypography variant="body3" weight="medium" color="shade4" textAlign="center">
                    {facility?.city} Facility
                  </NblTypography>
                </NblFlexContainer>
                <NblFlexContainer spacing={0} height="20px" justifyContent="space-between">
                  {facility?.resources.length > 1 && (
                    <NblFlexContainer flex={1} justifyContent="center">
                      <IconButton sx={{ p: 0 }} onClick={handlePrevClick}>
                        <PrevArrowIcon sx={{ color: `${theme.palette.primary.shade9}` }} />
                      </IconButton>
                    </NblFlexContainer>
                  )}
                  <NblFlexContainer
                    overflowX={'hidden'}
                    alignItems="start"
                    justifyContent="center"
                    flex={6}
                    onClick={() => {
                      navigate(calcRes.resourceid);
                      dispatch(setSelectedDomainIds([calcRes.domainid]));
                    }}
                  >
                    <NblTypography
                      variant="body3"
                      color="shade14"
                      weight="bold"
                      cursor="pointer"
                      wordBreak="break-all"
                      whiteSpace="nowrap"
                      showEllipsis={true}
                    >
                      {calcRes?.resourcelabel || calcRes?.resourcename}
                    </NblTypography>
                  </NblFlexContainer>
                  {facility?.resources.length > 1 && (
                    <NblFlexContainer flex={1} justifyContent="center">
                      <IconButton sx={{ p: 0 }} onClick={handleNextClick}>
                        <NextArrowIcon sx={{ color: `${theme.palette.primary.shade9}` }} />
                      </IconButton>
                    </NblFlexContainer>
                  )}
                </NblFlexContainer>
                <NblFlexContainer height="5px" justifyContent="center">
                  {facility?.resources.length > 1 &&
                    facility?.resources.map((_: any, dotIndex: number) => (
                      <CustomDot key={dotIndex} onClick={() => {}} active={index === dotIndex} index={dotIndex} />
                    ))}
                </NblFlexContainer>
              </NblGridItem>
              <NblGridItem width="auto">
                <NblFlexContainer height="11px" justifyContent="center">
                  <NblTypography variant="body4" color="shade2" width="auto" textAlign="center">
                    Utilization%
                  </NblTypography>
                </NblFlexContainer>
                <NblGridContainer columns={3} height="35px" alignContent="center" spacing={0} maxWidth="100%" margin="0px" padding="0px">
                  <NblGridItem colspan={1} textAlign="center">
                    <NblFlexContainer direction="column" spacing={0}>
                      <NblTypography variant="body3" color="shade6" textAlign="center">
                        CPU
                      </NblTypography>
                      <NblFlexContainer spacing={0} alignItems="center" justifyContent="center">
                        <ColorIndicatorIcon color={getUsageColor(calcRes?.cpuUtilized)} />
                        <NblTypography variant="subtitle2" color="shade1" textAlign="center">
                          {calcRes?.cpuUtilized}
                        </NblTypography>
                      </NblFlexContainer>
                    </NblFlexContainer>
                  </NblGridItem>
                  <NblGridItem colspan={1} alignSelf="center" width="100%" textAlign="center">
                    <NblFlexContainer direction="column" spacing={0}>
                      <NblTypography variant="body3" color="shade6" textAlign="center">
                        Memory
                      </NblTypography>
                      <NblFlexContainer spacing={0} alignItems="center" justifyContent="center">
                        <ColorIndicatorIcon color={getUsageColor(calcRes?.memoryUtilized)} />
                        <NblTypography variant="subtitle2" color="shade1" textAlign="center">
                          {calcRes?.memoryUtilized}
                        </NblTypography>
                      </NblFlexContainer>
                    </NblFlexContainer>
                  </NblGridItem>
                  <NblGridItem colspan={1} textAlign="center">
                    <NblFlexContainer direction="column" spacing={0}>
                      <NblTypography variant="body3" color="shade6" textAlign="center">
                        Storage
                      </NblTypography>
                      <NblFlexContainer spacing={0} alignItems="center" justifyContent="center">
                        <ColorIndicatorIcon color={getUsageColor(calcRes?.storageUtilized)} />
                        <NblTypography variant="subtitle2" color="shade1" textAlign="center">
                          {calcRes?.storageUtilized}
                        </NblTypography>
                      </NblFlexContainer>
                    </NblFlexContainer>
                  </NblGridItem>
                </NblGridContainer>
              </NblGridItem>
            </NblFlexContainer>
          ))}
        </Carousel>
      </Card>
    </foreignObject>
  );
};

export default MapCard;
