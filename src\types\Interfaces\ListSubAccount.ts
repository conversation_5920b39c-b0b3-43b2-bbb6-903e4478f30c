interface Contact {
  name: string;
  email: string;
  title: string;
  phone: string;
}

interface AccountDetails {
  description: string;
  name: string;
  owner: string;
  ownerEmail: string;
}

interface vPdetails {
  email: string;
  name: string;
}

interface vpcSettingss {
  cidrsBase: null | string;
  cidrsGua: null | string;
  description: string;
  eksSubnet: boolean;
  onPremConnectivity: boolean;
  requirements: string;
}

export default interface AccountsResponse {
  accountDetails: AccountDetails;
  accountId: string;
  billingContact: Contact;
  createdAt: string;
  environment: string;
  evpName: string;
  operationsContact: Contact;
  orgId: string;
  securityContact: Contact;
  updatedAt: string;
  vpDetails: vPdetails;
  vpcSettings: vpcSettingss;
}
