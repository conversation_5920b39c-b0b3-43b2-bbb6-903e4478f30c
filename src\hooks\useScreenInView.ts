// eslint-disable-next-line no-unused-vars
import { useState, useEffect, RefObject } from 'react';

const useScreenInView = (ref: RefObject<HTMLElement>, threshold: number = 0.5) => {
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(([entry]) => setIsInView(entry.isIntersecting), { threshold });

    observer.observe(ref.current);

    return () => {
      if (ref.current) observer.unobserve(ref.current);
      observer.disconnect();
    };
  }, [ref, threshold]);

  return isInView;
};

export default useScreenInView;
