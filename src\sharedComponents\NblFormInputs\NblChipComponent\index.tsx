import { useState } from 'react';
import ClearIcon from '@mui/icons-material/Clear';
import NblFieldWrapper from 'sharedComponents/NblFormInputs/NblFieldWrapper';
import { StyledAutoComplete, StyledChip, StyledTextField, StyledTooltip } from './styled';

interface NblChipComponentProps {
  value: string[];
  label: string;
  readOnly?: boolean;
  placeholder?: string;
  handleChange: (event: React.SyntheticEvent<Element, Event>, newValue: string[]) => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  handleBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  options: string[];
  name: string;
  mandatory?: boolean;
  disabled?: boolean;
  helperText?: string;
  error?: boolean;
  errorMessage?: { error: boolean; message: string }[];
  onKeyDown?: (e: React.KeyboardEvent<HTMLDivElement> & { defaultMuiPrevented?: boolean }) => void;
}

export default function NblChipComponent({
  label,
  placeholder = 'Type and press Enter to add...',
  readOnly,
  onMouseEnter,
  onMouseLeave,
  handleBlur,
  handleChange,
  onKeyDown,
  value,
  name,
  options,
  disabled = false,
  mandatory = false,
  errorMessage,
  helperText = '',
  error = false,
}: NblChipComponentProps) {
  const [inputValue, setInputValue] = useState('');
  const handleInputChange = (_: React.ChangeEvent<{}>, newInputValue: string) => {
    if (!disabled) {
      setInputValue(newInputValue);
    }
  };
  const handleInputBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    if (inputValue.trim() && !disabled) {
      handleChange(event, [...value, inputValue.trim()]);
      setInputValue('');
    }
    if (handleBlur) {
      handleBlur(event);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement> & { defaultMuiPrevented?: boolean }) => {
    if (e.key === 'Tab') {
      if (inputValue.trim() && !disabled) {
        handleChange(e, [...value, inputValue.trim()]);
        setInputValue('');
      }
    }
    onKeyDown?.(e);
  };

  return (
    <NblFieldWrapper label={label} name={name} mandatory={mandatory} disabled={disabled} error={error} helperText={helperText}>
      <StyledAutoComplete
        disableClearable={true}
        options={options}
        freeSolo
        multiple
        readOnly={readOnly || disabled}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        onBlur={handleInputBlur}
        value={value ?? []}
        inputValue={inputValue}
        onKeyDown={handleKeyDown}
        onInputChange={handleInputChange}
        onChange={(event, newValue: any) => {
          if (!disabled) {
            setInputValue('');
            handleChange(event, newValue);
          }
        }}
        renderTags={(value, getTagProps) =>
          value.map((option: any, index) => {
            const { key, ...tagProps } = getTagProps({ index });
            const error = errorMessage?.[index]?.error ? true : false;
            const tooltipMessage = readOnly ? '' : errorMessage?.[index]?.message;
            return (
              <StyledTooltip key={key} title={tooltipMessage} placement="top-end" arrow isError={error}>
                <StyledChip
                  key={key}
                  label={option}
                  onMouseEnter={onMouseEnter}
                  onMouseLeave={onMouseLeave}
                  isError={error}
                  variant="outlined"
                  deleteIcon={<ClearIcon />}
                  {...tagProps}
                />
              </StyledTooltip>
            );
          })
        }
        renderInput={(params) => (
          <StyledTextField
            {...params}
            placeholder={readOnly ? '' : placeholder}
            id={name}
            name={name}
            onBlur={handleInputBlur}
            disabled={disabled}
            error={error}
          />
        )}
      />
    </NblFieldWrapper>
  );
}
