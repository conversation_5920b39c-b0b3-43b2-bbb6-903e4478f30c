import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';
import { projectNameSchema } from './ManageMultiEnvProjects';

export const validationSchema = (projectList: string[]) =>
  yup.object().shape({
    projectName: projectNameSchema(projectList, ''),
    emailDistribution: yup
      .string()
      .email('Invalid email format')
      .matches(/@charter.com|@spectrum.com/, 'Please provide charter or spectrum email id')
      .required('Email is required'),
    description: yup
      .string()
      .max(500, 'Description cannot exceed 500 characters')
      .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage)
      .default('Max 500 characters'),
    projectAdminGroup: yup
      .array()
      .of(yup.string())
      .min(1, 'Atleast one group should be provided')
      .required('Project admin group is required'),
    organization: yup.string().required('Organisation Name is required'),
    vertical: yup.string().required('Vertical Name is required'),
    department: yup.string().required('Department Name is required'),
  });
