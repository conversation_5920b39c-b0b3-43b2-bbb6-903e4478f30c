import React, { useRef, useState } from 'react';
//eslint-disable-next-line no-unused-vars
import { ItemProps, SideBarMouseHandlerType } from '../interface';
import icons from 'assets/images/icons';
import { Handle, StyledItem, StyledTypography } from './styled';
import useMediaQuery from 'hooks/useMediaQuery';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

interface ItemComponentProps extends ItemProps {
  onClick: (item: ItemProps, position: number, handlerType: SideBarMouseHandlerType) => void;
  selectedLevel1: ItemProps | undefined;
  selectedLevel2: ItemProps | undefined;
  selectedMenu: ItemProps | undefined;
  sidebarExpanded: boolean | undefined;
  level: number;
}

const Item: React.FC<ItemComponentProps> = ({
  level,
  enabled,
  id,
  name,
  icon,
  label,
  onClick,
  sidebarExpanded,
  selectedLevel1,
  selectedLevel2,
  selectedMenu,
}) => {
  //Hooks
  const itemRef = useRef<HTMLButtonElement>(null);
  const iconFontSize = useMediaQuery('24px', '18px');

  //States
  const [timeout, setTime] = useState<NodeJS.Timeout | null>(null);

  // Utils
  function isSelectedItem() {
    return [selectedLevel1?.name, selectedMenu?.name, selectedLevel2?.name].includes(name);
  }

  function buildClassName() {
    let className = '';
    if (level !== 3 && isSelectedItem()) className += ' selectedItem ';
    if (level === 3) className += ' level3Item ';
    return className;
  }

  // Handlers
  const onMouseEnter = () => {
    let time = setTimeout(() => {
      const position = 70; /*itemRef?.current?.getBoundingClientRect().top || 0;*/
      onClick({ id, name, label, icon, enabled }, position - 70, 'expand'); // -70 to fit horizontally
      clearTimeout(time);
    }, 300);
    setTime(time);
  };
  function onMouseLeave() {
    if (timeout) clearTimeout(timeout);
  }

  const onClickHandler = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    onClick({ id, name, label, icon, enabled }, 0, 'navigate');
  };

  // Renders
  const renderIcon = (icon: string) => {
    /* @ts-ignore */
    const Icon = icons[icon];
    if (level === 2) {
      return null;
    } else if (!Icon) {
      return (
        <NblFlexContainer center width="20px" height="20px">
          {name?.[0]}
        </NblFlexContainer>
      );
    } else {
      return <Icon data-testid="mui-icon" style={{ fontSize: iconFontSize, fontWeight: 'bold' }} />;
    }
  };

  return (
    <StyledItem
      ref={itemRef}
      key={id}
      id={`sidebar-${name}-option`.replace(/\//,'')}
      name={name}
      className={buildClassName()}
      onMouseEnter={[1, 2].includes(level) && selectedLevel1?.name !== name && selectedLevel2?.name !== name ? onMouseEnter : undefined}
      onMouseLeave={[1, 2].includes(level) && selectedLevel1?.name !== name && selectedLevel2?.name !== name ? onMouseLeave : undefined}
      onClick={onClickHandler}
      expanded={sidebarExpanded}
      level={level}
      selectedLevel1={selectedLevel1}
      selectedLevel2={selectedLevel2}
      disabled={!enabled}
    >
      {icon && renderIcon(icon)}
      {sidebarExpanded && <StyledTypography variant="subtitle1">{label}</StyledTypography>}
      {level !== 3 && isSelectedItem() && <Handle expanded={sidebarExpanded || false} />}
    </StyledItem>
  );
};

export default Item;
