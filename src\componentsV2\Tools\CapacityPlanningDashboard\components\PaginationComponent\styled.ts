import { Pagination } from '@mui/material';
import { styled, useTheme } from '@mui/material/styles';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';

export const Styledpagination = styled(Pagination)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    '& .MuiPaginationItem-page': {
      backgroundColor: `${theme.palette.secondary.main}`,
      color: `${theme.palette.primary.main}`,
      borderRadius: '6px',
      border: `1px solid ${theme.palette.primary.main}`,
    },
    '& .MuiPaginationItem-root': {
      color: `${theme.palette.primary.main}`,
    },

    '& .Mui-selected': {
      backgroundColor: `${theme.palette.primary.main} !important`,
      color: `${theme.palette.secondary.main}`,
    },
  };
});
