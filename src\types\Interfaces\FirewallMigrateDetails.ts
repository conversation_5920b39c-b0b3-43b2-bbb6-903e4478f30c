interface FirewallRule {
  id?: number | null;
  ruleId?: number;
  source: {
    location: string;
    hostName: string;
    ipAddress: string;
    port: string;
  };
  destination: {
    location: string;
    hostName: string;
    ipAddress: string;
    port: string;
  };
  ipVersion?: string;
  ruleType?: string;
  protocol: string;
  notes: string;
  errors?: {
    source: {
      location: string;
      hostName: string;
      ipAddress: string;
      port: string;
    };
    destination: {
      location: string;
      hostName: string;
      ipAddress: string;
      port: string;
    };
    protocol: string;
  };
  valid?: boolean;
}

interface StorageInfo {
  bucketName: string;
  fileName: string;
  filePath: string;
}

type downstreamResponseJiraData = {
  key: string;
  href: string;
};

export interface downstreamResponseData {
  key: string;
  href: string;
}
export interface FirewallRequestMetaData {
  downstreamResponseData?: FirewallMigrateDetails['downstreamResponseData'] & {
    key?: string;
    href?: string;
  };
}
export interface systemUpdateDetails {
  FirewallV2ServiceRequestId: string;
  migrationStatus: string;
  jiraStatus?: {
    status: string;
  };
}
export default interface FirewallMigrateDetails {
  projectName: string;
  projectCreator: string;
  date: string;
  supportOrganization: string;
  region: string[];
  appId: string;
  jiraIssueLink: any[];
  netopsaskTicket: string;
  businessRequestDate: string;
  firewallRules: FirewallRule[];
  deeplinkUrl: string;
  summary: string;
  userName: string;
  description: string;
  storageInfo: StorageInfo;
  downstreamResponseData: downstreamResponseJiraData;
}
