import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblCheckBox from '.';

describe('NblCheckBox component', () => {
  const props = {
    label: 'IPv4',
    name: 'ipv4',
    disabled: false,
    checked: false,
    error: false,
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblCheckBox {...props} onChange={() => { }} onBlur={() => { }} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
