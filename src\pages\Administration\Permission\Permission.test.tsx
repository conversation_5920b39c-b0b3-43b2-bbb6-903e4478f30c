import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import * as api from 'api/static-data';
import Catalog from 'components/Catalog';
import Permissions from '.';

jest.mock('api/static-data', () => ({
  getPermissionCatalogItems: jest.fn(),
}));

jest.mock('components/Catalog', () => ({
  __esModule: true,
  default: jest.fn(() => <div>Mocked Catalog</div>),
}));

describe('Permissions', () => {
  it('renders a "No items to display" message when no data is fetched', async () => {
    (api.getPermissionCatalogItems as jest.Mock).mockResolvedValue([]);

    render(<Permissions />);

    await waitFor(() => expect(screen.getByText('No items to display')).toBeInTheDocument());
  });

  it('handles error case gracefully', async () => {
    (api.getPermissionCatalogItems as jest.Mock).mockRejectedValue(new Error('API error'));

    render(<Permissions />);

    await waitFor(() => expect(screen.getByText('No items to display')).toBeInTheDocument());
  });
});
