import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';

const TagValidationSchema = () => {
  return yup.object().shape({
    tagKey: yup.string().required('Tag Key is required'),
    tagValue: yup
      .string()
      .required('Tag Key is required')
      .trim()
      .matches(yupMatchesParams.tagValue.pattern, yupMatchesParams.tagValue.errorMessage),
  });
};
export default TagValidationSchema;
