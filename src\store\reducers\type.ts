import { Authorization } from './authorization';
import { User } from './user';
import { Common } from './common';
import { Catalogs } from './catalogs';
import { Menu } from './menu';
import { Spinner } from './spinner';
import { ProjectNetworks } from './projectNetworks';
import { ProjectTags } from './projectTags';
import { Breadcrumbs } from './breadcrumbs';
import { DNP } from './dnp';
import { UsageMetricsRequestType } from './usagemetricsrequest';
import { RequestFilterConfigType } from './filterDialogRequests';
import { CapacityPlanning } from './capacityplanning';
import { requestOverviewType } from './requestOverview';

export type State = {
  authorization: Authorization;
  user: User;
  common: Common;
  catalogs: Catalogs;
  menu: Menu;
  spinner: Spinner;
  projectNetworks: ProjectNetworks;
  projectTags: ProjectTags;
  breadcrumbs: Breadcrumbs;
  dnp: DNP;
  usageMetricsRequests: UsageMetricsRequestType;
  RequestFilterConfig: RequestFilterConfigType;
  capacityPlanning: CapacityPlanning;
  RequestOverviewSlice: requestOverviewType;
};
