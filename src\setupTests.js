// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// This config is added to render MUI DataGrid tables correctly while running the testcases
jest.mock('@mui/x-data-grid', () => {
  const { DataGrid } = jest.requireActual('@mui/x-data-grid');
  return {
    ...jest.requireActual('@mui/x-data-grid'),
    DataGrid: (props) => <DataGrid {...props} disableVirtualization />,
  };
});

jest.mock('@mui/material/styles', () => {
  const actualStyled = jest.requireActual('@mui/material/styles').styled;
  return {
    ...jest.requireActual('@mui/material/styles'),
    styled: (Component) => actualStyled(Component),
  };
});

//Mocking useNblForms globally
jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  useNblForms: jest.fn(),
}));
