import { Table, TableCell, TableContainer } from '@mui/material';
import { Box } from '@mui/material';
import { styled, useTheme } from '@mui/material/styles';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledTableContainer = styled(TableContainer)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    background: `${theme.palette.primary.main}`,
    borderRadius: '12px',
    maxHeight: '400px',
    overflow: 'auto',
    [theme.breakpoints.down('2K')]: {
      maxHeight: '150px',
      overflowY: 'scroll',
      overflowX: 'auto',
    },
  };
});

export const StyledTable = styled(Table)(({ theme }) => {
  return {
    [theme.breakpoints.down('2K')]: {
      tableLayout: 'fixed',
      width: '100%',
    },
  };
});

export const StyledTableCell = styled(TableCell)(() => {
  return {
    '&.MuiTableCell-root': {
      borderBottom: 'none !important',
    },
    paddingBottom: '4px',
  };
});

export const StyledTableData = styled(TableCell)(() => {
  return {
    '&.MuiTableCell-root': {
      borderBottom: 'none !important',
    },
    padding: '0px',
    paddingBottom: '4px',
    marginTop: '-2px',
  };
});

export const StyledBox = styled(Box)(() => {
  const theme: NebulaTheme = useTheme();
  return {
    '&.MuiTableCell-root': {
      borderBottom: 'none !important',
    },
    fontSize: '14px',
    color: `${theme.palette.secondary.shade3}`,
    textAlign: 'center',
    [theme.breakpoints.down('2K')]: {
      fontSize: '10px',
    },
  };
});
