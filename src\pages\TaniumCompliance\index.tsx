import ComplianceFindings from 'componentsV2/Resources/TaniumScreens/ComplianceFinding';
import useNblNavigate from 'hooks/useNblNavigate';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const TaniumCompliance = () => {
  //Hooks
  const [searchParams] = useSearchParams();
  const navigate = useNblNavigate();
  const [data, setData] = useState({});

  //Side Effects
  useEffect(() => {
    if (searchParams && !searchParams.get('reportId')) {
      navigate(-1);
    } else if (searchParams && searchParams.get('reportId')) {
      //Fetch details placeholder
      setData({});
    }
  }, [searchParams.get('reportId')]);

  //JSx
  return <ComplianceFindings data={data} />;
};

export default TaniumCompliance;
