import { render, screen, fireEvent } from '@testing-library/react';
import NblCounterField from './index';

jest.mock('sharedComponents/NblFormInputs/NblInputLabel', () => (props: any) => <label>{props.label}</label>);
jest.mock('sharedComponents/NblFormInputs/NblInputHelperText', () => (props: any) => <p>{props.helperText}</p>);
jest.mock('sharedComponents/NblContainers/NblFlexContainer', () => (props: any) => <div>{props.children}</div>);
jest.mock('./styled', () => ({
  StyledButton: (props: any) => <button {...props}>{props.children}</button>,
  StyledCounterOutlinedInput: (props: any) => <input data-testid="counter-input" {...props} />,
}));
jest.mock('assets/images/icons/custom-icons', () => ({
  DecreaseVMCountIcon: () => <span>-</span>,
  IncreaseVMCountIcon: () => <span>+</span>,
}));

describe('NblCounterField', () => {
  const mockHandleChange = jest.fn();

  const defaultProps = {
    type: 'number' as 'number',
    label: 'VM Count',
    name: 'vmCount',
    handleChange: mockHandleChange,
    initialValue: 2,
    maxValue: 5,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with label and initial value', () => {
    render(<NblCounterField {...defaultProps} />);
    expect(screen.getByText('VM Count')).toBeInTheDocument();
    expect(screen.getByTestId('counter-input')).toHaveValue(2);
  });

  it('increments value on click', () => {
    render(<NblCounterField {...defaultProps} />);
    const incButton = screen.getAllByRole('button')[1];
    fireEvent.click(incButton);
    expect(mockHandleChange).toHaveBeenCalledWith(3);
    expect(screen.getByTestId('counter-input')).toHaveValue(3);
  });

  it('does not increment beyond max value', () => {
    render(<NblCounterField {...defaultProps} initialValue={5} />);
    const incButton = screen.getAllByRole('button')[1];
    fireEvent.click(incButton);
    expect(mockHandleChange).not.toHaveBeenCalled();
  });

  it('decrements value on click', () => {
    render(<NblCounterField {...defaultProps} />);
    const decButton = screen.getAllByRole('button')[0];
    fireEvent.click(decButton);
    expect(mockHandleChange).toHaveBeenCalledWith(1);
    expect(screen.getByTestId('counter-input')).toHaveValue(1);
  });

  it('does not decrement below 1', () => {
    render(<NblCounterField {...defaultProps} initialValue={1} />);
    const decButton = screen.getAllByRole('button')[0];
    fireEvent.click(decButton);
    expect(mockHandleChange).not.toHaveBeenCalled();
  });

  it('updates value via manual input within bounds', () => {
    render(<NblCounterField {...defaultProps} />);
    const input = screen.getByTestId('counter-input');
    fireEvent.change(input, { target: { value: '4' } });
    expect(mockHandleChange).toHaveBeenCalledWith(4);
  });

  it('ignores out-of-bounds input', () => {
    render(<NblCounterField {...defaultProps} />);
    const input = screen.getByTestId('counter-input');
    fireEvent.change(input, { target: { value: '6' } });
    expect(mockHandleChange).not.toHaveBeenCalled();
  });

  it('disables input and buttons when disabled is true', () => {
    render(<NblCounterField {...defaultProps} disabled />);
    const input = screen.getByTestId('counter-input');
    const buttons = screen.getAllByRole('button');
    expect(input).toBeDisabled();
    expect(buttons[0]).toBeDisabled();
    expect(buttons[1]).toBeDisabled();
  });

  it('disables buttons and input in readOnly mode', () => {
    render(<NblCounterField {...defaultProps} readOnly />);
    const input = screen.getByTestId('counter-input');
    const buttons = screen.getAllByRole('button');
    expect(input).toHaveAttribute('readOnly');
    expect(buttons[0]).toBeDisabled();
    expect(buttons[1]).toBeDisabled();
  });
});
