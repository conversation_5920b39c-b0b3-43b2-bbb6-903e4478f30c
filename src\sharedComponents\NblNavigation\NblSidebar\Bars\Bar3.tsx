import { Typography } from '@mui/material';
import React from 'react';

//eslint-disable-next-line no-unused-vars
import { Bar3Props } from '../interface';
import ItemsList from '../ItemsList';
import { StyledBar, StyledBarTitle, StyledItemListContainer, StyledNestedItemListContainer } from './styled';
import NblTypography from 'sharedComponents/NblTypography';

const Bar3: React.FC<Bar3Props> = ({ level, bar3Items, onLevel4Click, selectedLevel2, sidebarExpanded, bar3ItemsPosition }) => {
  //Renders
  function render() {
    if (bar3Items.loading || bar3Items.items?.[0]?.level4Items?.[0]?.name) {
      return bar3Items.items?.map((bar3) => (
        <StyledNestedItemListContainer key={bar3.level3Item.label}>
          <NblTypography variant="h4" color={'shade10'} weight="bold">
            {bar3.level3Item.label}
          </NblTypography>
          <ItemsList
            loading={bar3Items.loading}
            itemsList={bar3.level4Items}
            level={level}
            onLevel4Click={(level4Item) => onLevel4Click(bar3.level3Item, level4Item)}
            selectedLevel2={selectedLevel2}
            sidebarExpanded={sidebarExpanded}
          />
          <br />
        </StyledNestedItemListContainer>
      ));
    } else {
      return <Typography variant="h5">No items found</Typography>;
    }
  }

  //JSX
  if (level === 3 && !selectedLevel2?.name) {
    return null;
  }

  return (
    <StyledBar expanded={sidebarExpanded} className={'bar2'}>
      <StyledBarTitle variant="h3">{selectedLevel2?.label}</StyledBarTitle>
      <StyledItemListContainer bar={3} listStartPosition={bar3ItemsPosition}>
        {render()}
      </StyledItemListContainer>
    </StyledBar>
  );
};
export default Bar3;
