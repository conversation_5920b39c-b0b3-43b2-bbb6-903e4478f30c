import { render, screen, fireEvent } from '@testing-library/react';
import DataStores from '.';
// eslint-disable-next-line no-unused-vars
import { DataStore } from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('DataStores Component', () => {
  const mockDatastores: DataStore[] = [
    {
      datastoreMor: 'ds-1',
      name: 'Datastore One',
      type: 'SSD',
      freeSpace: 5,
      capacity: 10,
      disabled: false,
    },
    {
      datastoreMor: 'ds-2',
      name: 'Datastore Two',
      type: 'HDD',
      freeSpace: 2,
      capacity: 8,
      disabled: true,
    },
  ];

  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders datastore rows correctly', () => {
    render(
      <NebulaThemeProvider>
        <DataStores datastores={mockDatastores} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.getByText('Datastore One')).toBeInTheDocument();
    expect(screen.getByText('Datastore Two')).toBeInTheDocument();
  });

  it('renders column headers', () => {
    render(
      <NebulaThemeProvider>
        <DataStores datastores={mockDatastores} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.getByText('Datastore ID')).toBeInTheDocument();
    expect(screen.getByText('Datastore Name')).toBeInTheDocument();
    expect(screen.getByText('Type')).toBeInTheDocument();
    expect(screen.getByText('Capacity (TB)')).toBeInTheDocument();
    expect(screen.getByText('Free Space (TB)')).toBeInTheDocument();
    expect(screen.getByText('Disable')).toBeInTheDocument();
  });

  it('toggles checkbox and calls onChange', () => {
    render(
      <NebulaThemeProvider>
        <DataStores datastores={mockDatastores} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes.length).toBe(2);

    fireEvent.click(checkboxes[0]);
    expect(mockOnChange).toHaveBeenCalledTimes(1);
    const updated = mockOnChange.mock.calls[0][0];
    expect(updated[0].disabled).toBe(true);
  });

  it('renders correctly with empty datastores list', () => {
    render(
      <NebulaThemeProvider>
        <DataStores datastores={[]} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.queryByText('Datastore One')).not.toBeInTheDocument();
    expect(screen.queryByText('Datastore Two')).not.toBeInTheDocument();
  });

  it('checkbox reflects initial disabled state', () => {
    render(
      <NebulaThemeProvider>
        <DataStores datastores={mockDatastores} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes[0]).not.toBeChecked();
    expect(checkboxes[1]).toBeChecked();
  });
});
