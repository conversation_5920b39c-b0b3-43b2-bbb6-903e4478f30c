//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblTypography from 'sharedComponents/NblTypography';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import masterPalette from 'NebulaTheme/bundle/light/master';
import typographyPalette from 'NebulaTheme/bundle/config/typography';

type StoryProps = ComponentProps<typeof NblTypography>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'NblTypography',
  component: NblTypography,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: { control: 'radio', options: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'body3', 'body4', 'button', 'textButton', 'caption'], type: 'string' },
    weight: { control: 'radio', options: ['bold', 'semiBold', 'medium', 'regular', 'light'], type: 'string' },
    color: { control: 'radio', options: Object.keys(typographyPalette(masterPalette).typography).map(variant => variant) }
  },
};

export default meta;

export const Typography: Story = {
  args: {
    variant: 'h1',
    color: 'shade1'
  },
  render: (args) => (
    <NebulaTheme>
      <NblFlexContainer margin='24px'>
        <NblTypography {...args} >Nebula</NblTypography>
      </NblFlexContainer>
    </NebulaTheme>
  ),
};
