import { render, screen } from '@testing-library/react';
import NblChip from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('NblChip Component', () => {
  it('renders the chip with the correct label', () => {
    render(<NebulaThemeProvider><NblChip id="test-chip" label="Test Label" /></NebulaThemeProvider>);
    const chip = screen.getByText('Test Label');
    expect(chip).toBeInTheDocument();
  });

  it('applies the correct default styles', () => {
    render(<NebulaThemeProvider><NblChip id="test-chip" label="Test Label" /></NebulaThemeProvider>);
    const chip = screen.getByText('Test Label').parentElement;
    expect(chip).toHaveStyle('border-radius: 6px');
  });

  it('applies the correct color styles based on the color prop', () => {
    render(<NebulaThemeProvider><NblChip id="success-chip" label="Success" color="success" /></NebulaThemeProvider>);
    const chip = screen.getByText('Success').parentElement;
    expect(chip).toHaveClass('MuiChip-filledSuccess');
  });

  it('handles ReactNode as a label', () => {
    render(<NebulaThemeProvider><NblChip id="node-chip" label={<strong>Strong Label</strong>} /></NebulaThemeProvider>);
    const chip = screen.getByText('Strong Label');
    expect(chip).toBeInTheDocument();
    expect(chip.tagName).toBe('STRONG');
  });
});
