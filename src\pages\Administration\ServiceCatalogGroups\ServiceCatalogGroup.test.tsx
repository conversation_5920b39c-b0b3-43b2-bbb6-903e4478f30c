import { act, render } from '@testing-library/react';
import ServiceCatalogGroup from '.';
import Catalog from 'components/Catalog';
import * as api from 'api/static-data';

jest.mock('components/Catalog');

describe('Service Catalog Group component', () => {
  const mockServiceCatalogGroupItems = [
    {
      name: 'mock',
      path: '/mock',
    },
    {
      name: 'mock2',
      path: '/mock2',
    },
  ];

  let mockServiceCatalogGroupAPI: jest.SpyInstance;

  beforeEach(async () => {
    mockServiceCatalogGroupAPI = jest.spyOn(api, 'getServiceCatalogData');
  });

  afterEach(() => {
    mockServiceCatalogGroupAPI.mockRestore();
  });

  test('should call api to fetch catalog data ', async () => {
    mockServiceCatalogGroupAPI.mockResolvedValueOnce(mockServiceCatalogGroupItems);
    await act(() => {
      render(<ServiceCatalogGroup />);
    });
    expect(mockServiceCatalogGroupAPI).toHaveBeenCalled();
  });

  test('should  render Catalog component', async () => {
    mockServiceCatalogGroupAPI.mockResolvedValueOnce(mockServiceCatalogGroupItems);
    await act(() => {
      render(<ServiceCatalogGroup />);
    });
    expect(Catalog).toHaveBeenCalled();
  });

  test('should pass correct props to Catalog component', async () => {
    mockServiceCatalogGroupAPI.mockResolvedValueOnce(mockServiceCatalogGroupItems);
    await act(() => {
      render(<ServiceCatalogGroup />);
    });
    expect(Catalog).toHaveBeenCalledWith(expect.objectContaining({ catalogItems: mockServiceCatalogGroupItems }), expect.anything());
  });

  test('Catalog component is not rendered if API fails', async () => {
    mockServiceCatalogGroupAPI.mockImplementationOnce(() => {
      throw new Error();
    });
    await act(() => {
      render(<ServiceCatalogGroup />);
    });
    expect(Catalog).not.toHaveBeenCalled();
  });
});
