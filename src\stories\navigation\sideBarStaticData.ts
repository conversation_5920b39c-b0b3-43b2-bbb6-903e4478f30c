export const bar1ItemsStatic = [
  {
    loading: false,
    items: [
      { id: '1', name: 'iaas', label: 'Iaa<PERSON>', icon: 'CloudOutlined', enabled: true },
      { id: '12', name: 'paas', label: 'Paas', icon: 'CloudSyncOutlined', enabled: true },
      { id: '123', name: 'tools', label: 'Tools', icon: 'BuildOutlined', enabled: true },
      { id: '1234', name: 'onBoarding', label: 'OnBoarding', icon: 'AWS', enabled: true },
    ],
  },
  {
    loading: false,
    items: [
      { id: '1', name: 'tool1', label: 'Tool1', icon: 'CloudOutlined', enabled: true },
      { id: '12', name: 'tool2', label: 'Tool2', icon: 'CloudOutlined', enabled: true },
    ],
  },
  {
    loading: false,
    items: [{ id: '1', name: 'tool3', label: 'Tool3', icon: 'CloudOutlined', enabled: false }],
  },
];

export const bar2ItemsStatic: { [key: string]: any } = {
  iaas: [
    { id: '1', name: 'network', label: 'Network', enabled: true },
    { id: '2', name: 'compute', label: 'Compute', enabled: true },
    { id: '3', name: 'security', label: 'Security', enabled: true },
    { id: '4', name: 'storage', label: 'Storage', enabled: true },
  ],
  paas: [
    { id: '1', name: 'paas1', label: 'Paas1', enabled: true },
    { id: '2', name: 'paas2', label: 'Paas2', enabled: true },
    { id: '3', name: 'paas3', label: 'Paas3', enabled: true },
    { id: '4', name: 'paas4', label: 'Paas4', enabled: true },
  ],
};

export const bar3ItemsStatic: { [key: string]: any } = {
  network: [
    {
      level3Item: { id: '1', name: 'network1', label: 'Network 1', icon: 'CloudOutlined', enabled: true },
      level4Items: [
        { id: '1', name: 'level4', label: 'Test1', icon: 'CloudOutlined', enabled: true },
        { id: '2', name: 'level4', label: 'Test2', icon: 'CloudOutlined', enabled: true },
      ],
    },
    {
      level3Item: { id: '1', name: 'network2', label: 'Network 2', icon: 'CloudOutlined', enabled: true },
      level4Items: [
        { id: '1', name: 'level4', label: 'Test3', icon: 'Ubuntu', enabled: true },
        { id: '2', name: 'level4', label: 'Test4', icon: 'Windows', enabled: true },
      ],
    },
  ],
  compute: [
    {
      level3Item: { id: '1', name: 'virtualmachines', label: 'Virtual Machines', icon: 'CloudOutlined', enabled: true },
      level4Items: [
        { id: '1', name: 'ubuntu', label: 'Ubuntu', icon: 'Ubuntu', enabled: true },
        { id: '2', name: 'windows', label: 'Windows', icon: 'Windows', enabled: true },
      ],
    },
  ],
};
