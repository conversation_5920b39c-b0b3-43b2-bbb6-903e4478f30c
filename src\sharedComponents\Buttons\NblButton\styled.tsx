import { Button } from '@mui/material';
import { styled } from '@mui/system';
//eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';

const StyledNblButton = styled(Button)<{ theme?: NebulaTheme; margin?: string; }>(({ theme, margin }) => {
  const { contained, outlined } = theme.palette.button;

  return {
    '&.MuiButtonBase-root': {
      borderRadius: '4px',
      ...theme.typography.body1,
      textTransform: 'capitalize',
      letterSpacing: 0,
      width: 'auto',
      minWidth: 0,
      minHeight: '40px',
      '.MuiSvgIcon-root': {
        fontSize: 'inherit',
      },
      ...(margin && {
        margin
      }),
    },
    '&.MuiButton-outlinedPrimary': {
      color: outlined.primary.textColor,
      border: `1px solid ${outlined.primary.borderColor}`,
    },
    '&.MuiButton-outlinedInfo': {
      color: outlined.info.textColor,
      border: `1px solid ${outlined.info.borderColor}`,
    },
    '&.MuiButton-outlinedError': {
      color: outlined.error.textColor,
      border: `1px solid ${outlined.error.borderColor}`,
    },
    '&.MuiButton-containedPrimary': {
      color: contained.primary.textColor,
      backgroundColor: contained.primary.bgColor,
    },
    '&.MuiButton-containedInfo': {
      backgroundColor: contained.info.bgColor,
    },
    '&.MuiButton-containedError': {
      color: contained.error.textColor,
      backgroundColor: contained.error.bgColor,
    },
    '&.MuiButton-contained.Mui-disabled': {
      backgroundColor: contained.disabled.bgColor,
    },
    '&.MuiButton-outlined.Mui-disabled': {
      color: outlined.disabled.textColor,
      border: `1px solid ${outlined.disabled.borderColor}`,
    },
    '&.MuiButton-textPrimary': {
      color: contained.primary.textColor,
      padding: 0,
      '&.Mui-disabled': {
        opacity: 0.1,
      },
    },
    '&.Mui-disabled': {
      pointerEvents: 'auto',
      cursor: 'not-allowed',
    },
  };
});

export default StyledNblButton;
