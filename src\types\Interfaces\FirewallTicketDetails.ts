import { ApprovalStatus, RequestStatus } from 'types/Enums';
import FirewallTicket from './FirewallTicket';
import FirewallTufinTicket from './FirewallTufinTicket';

export enum IpType {
  IPV4 = 'IPV4',
  IPV6 = 'IPV6',
}

export default interface FirewallTicketDetails {
  createdAt: string;
  updatedAt: string;
  serviceRequestId: string;
  subRequestId: string;
  status: RequestStatus;
  ipType: IpType;
  approvalStatus: ApprovalStatus;
  organizationName: string;
  ruleIds: number[];
  ticketDetails: {
    cherwell?: FirewallTicket;
    jira?: FirewallTicket;
    remedy?: FirewallTicket;
    tufin?: FirewallTufinTicket;
    [key: string]: FirewallTicket | undefined;
  };
  hasApproveAccess: boolean;
  isSubRequestInValidated?: boolean;
  tufinWorkflowError?: string;
  enableApproval?: boolean;
}
