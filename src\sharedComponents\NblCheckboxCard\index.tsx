import { useTheme } from '@mui/material';
import icons from 'assets/images/icons';
import { StyledDescription, StyledHeader, StyledLabel, StyledTitle } from './styled';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { NebulaTheme } from 'NebulaTheme/type';
import NblTypography from 'sharedComponents/NblTypography';

interface NblCheckboxCardProps {
  title?: string;
  description?: string;
  icon?: string;
  disabled?: boolean;
  width?: string;
  active?: boolean;
  checkBoxCardId?: string;
  name: string;
  onCardClickHandler: (e: React.MouseEvent | React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLButtonElement>) => void;
  error?: boolean;
  height?: string;
  backgroundColor?: string;
  isHover?: boolean;
  showDefaultChecked?: (name: string) => void;
}

const NblCheckboxCard: React.FC<NblCheckboxCardProps> = ({
  checkBoxCardId,
  onCardClickHandler,
  name,
  icon,
  title,
  description,
  disabled = false,
  width,
  active = false,
  onBlur,
  error,
  height,
  backgroundColor,
  isHover,
  showDefaultChecked,
}) => {
  const theme = useTheme<NebulaTheme>();

  //Renders
  const renderIcon = (icon: string) => {
    /* @ts-ignore */
    const Icon = icons[icon];
    if (!Icon) return null;
    return <Icon data-testid="mui-icon" />;
  };

  //Jsx
  return (
    <NblBorderContainer
      width={width}
      height={height}
      onClick={onCardClickHandler}
      aria-disabled={disabled}
      padding="0px 15px"
      border={active ? `1px solid ${theme.palette.border.highlightedColor}` : `1px solid ${theme.palette.border.color}`}
      backgroundColor={backgroundColor}
      isHover={isHover}
    >
      <StyledHeader>
        <StyledTitle>
          <NblCheckBox
            key={checkBoxCardId}
            name={name}
            label=""
            checked={active}
            onBlur={() => onBlur}
            onChange={onCardClickHandler}
            disabled={disabled}
            labelPlacement="end"
            type="regular"
            error={error}
            showDefaultChecked={showDefaultChecked}
          />
          <StyledLabel>
            {icon && renderIcon(icon)}
            {
              <NblTypography variant="caption" color={'shade1'} weight={'bold'}>
                {title}
              </NblTypography>
            }
          </StyledLabel>
        </StyledTitle>
      </StyledHeader>
      <StyledDescription>
        <NblTypography variant="caption" color={'shade1'} weight={'regular'}>
          {description}
        </NblTypography>
      </StyledDescription>
    </NblBorderContainer>
  );
};

export default NblCheckboxCard;
