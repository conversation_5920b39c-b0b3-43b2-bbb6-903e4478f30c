import React from 'react';

import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import { generateEnum } from 'utils/common';
import { FormValues } from '../..';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import NblCounterField from 'sharedComponents/NblFormInputs/NblCounterField';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblDatePicker from 'sharedComponents/NblFormInputs/NblDatePicker';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblInputAdornment from 'sharedComponents/NblFormInputs/NblInputAdorment';
import SecretValueField from '../SecretValueField';
import dayjs from 'dayjs';

export interface RotatingSecretFormValues {
  vaultKey: string;
  vaultPassword: string;
  isPasswordValid: boolean;
  userNameKey: string;
  userNamePassword: string;
  secretTTLInHours: number;
  nextRotationDate: any;
  notifyBeforeTokenExpiry: boolean;
  rotationType: string;
}

export const rotatingSecreInitialValues: RotatingSecretFormValues = {
  vaultKey: '',
  vaultPassword: '',
  isPasswordValid: false,
  userNameKey: '',
  userNamePassword: '',
  secretTTLInHours: 30,
  nextRotationDate: null,
  notifyBeforeTokenExpiry: true,
  rotationType: 'Manual',
};

interface RotatingSecretFieldsProps {
  policyDescription: string;
}

const RotatingSecretFields: React.FC<RotatingSecretFieldsProps> = ({ policyDescription }: RotatingSecretFieldsProps) => {
  //Hooks
  const { nblFormValues, nblFormProps } = useNblForms<FormValues & RotatingSecretFormValues>();
  const FIELD_NAMES = generateEnum(rotatingSecreInitialValues);
  //JSX
  return (
    <React.Fragment>
      <NblGridItem>
        <NblTextField
          mandatory
          type="text"
          label="Username Key"
          placeholder="Enter"
          name={FIELD_NAMES.userNameKey}
          value={nblFormValues.userNameKey}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          helperText={nblFormProps.touched.userNameKey ? nblFormProps.errors.userNameKey : ' '}
          error={Boolean(nblFormProps.errors.userNameKey && nblFormProps.touched.userNameKey)}
        />
      </NblGridItem>
      <NblGridItem>
        <NblTextField
          mandatory
          type="text"
          label="Username"
          placeholder="Enter"
          name={FIELD_NAMES.userNamePassword}
          value={nblFormValues.userNamePassword}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          helperText={nblFormProps.touched.userNamePassword ? nblFormProps.errors.userNamePassword : ' '}
          error={Boolean(nblFormProps.errors.userNamePassword && nblFormProps.touched.userNamePassword)}
        />
      </NblGridItem>
      <NblGridItem>
        <NblTextField
          mandatory
          type="text"
          label="Password Key"
          placeholder="Enter"
          name={FIELD_NAMES.vaultKey}
          value={nblFormValues.vaultKey}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          helperText={nblFormProps.touched.vaultKey ? nblFormProps.errors.vaultKey : ' '}
          error={Boolean(nblFormProps.errors.vaultKey && nblFormProps.touched.vaultKey)}
        />
      </NblGridItem>
      <NblGridItem colspan={2} width={'50%'}>
        <SecretValueField
          isPasswordPolicyRequired
          policyId={nblFormValues.policyId}
          label={'Password'}
          name={FIELD_NAMES.vaultPassword}
          value={nblFormValues.vaultPassword}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          setPasswordValidStatus={(isValid: boolean) => nblFormProps.setFieldValue('isPasswordValid', isValid)}
          helperText={(nblFormProps.touched.vaultPassword ? nblFormProps.errors.vaultPassword : ' ') || ''}
          error={Boolean(nblFormProps.errors.vaultPassword && nblFormProps.touched.vaultPassword)}
          policyDescription={policyDescription}
        />
      </NblGridItem>
      <NblGridItem colspan={1}>
        <NblCounterField
          width={'100%'}
          type="number"
          label="Rotation Interval"
          name={FIELD_NAMES.secretTTLInHours}
          initialValue={nblFormValues.secretTTLInHours}
          maxValue={1000}
          handleChange={(v) => nblFormProps.setFieldValue(FIELD_NAMES.secretTTLInHours, v)}
          handleBlur={nblFormProps.handleBlur}
          helperText={nblFormProps.touched.secretTTLInHours ? nblFormProps.errors.secretTTLInHours : ' '}
          error={Boolean(nblFormProps.errors.secretTTLInHours && nblFormProps.touched.secretTTLInHours)}
          endAdornment={<NblInputAdornment position="end" label={nblFormValues.secretTTLInHours > 1 ? 'Days' : 'Day'} />}
        />
      </NblGridItem>
      <NblGridItem>
        <NblDatePicker
          label={'First Run'}
          name={FIELD_NAMES.nextRotationDate}
          value={nblFormValues.nextRotationDate}
          handleChange={(newValue) => nblFormProps.setFieldValue(FIELD_NAMES.nextRotationDate, newValue)}
          helperText={typeof nblFormProps.errors.nextRotationDate === 'string' ? nblFormProps.errors.nextRotationDate || ' ' : ''}
          error={Boolean(nblFormProps.errors.nextRotationDate)}
          minDate={dayjs()}
          withTimePicker={true}
        />
      </NblGridItem>
      <NblGridItem colspan={2}>
        <NblFlexContainer height="auto" margin="20px 0 0 0">
          <NblCheckBox
            label="Notify before secret expiry/rotation"
            name={FIELD_NAMES.notifyBeforeTokenExpiry}
            checked={nblFormValues.notifyBeforeTokenExpiry}
            onChange={(_, v) => nblFormProps.setFieldValue(FIELD_NAMES.notifyBeforeTokenExpiry, v)}
            onBlur={nblFormProps.handleBlur}
            helperText={nblFormProps.touched.notifyBeforeTokenExpiry ? nblFormProps.errors.notifyBeforeTokenExpiry : ' '}
            error={Boolean(nblFormValues.notifyBeforeTokenExpiry && nblFormProps.errors.notifyBeforeTokenExpiry)}
          />
        </NblFlexContainer>
      </NblGridItem>
    </React.Fragment>
  );
};

export default RotatingSecretFields;
