import { <PERSON><PERSON><PERSON> } from '@mui/x-charts/PieChart';
import NblFlexContainer from '../../../../../sharedComponents/NblContainers/NblFlexContainer';
import { NblGridContainer, NblGridItem } from '../../../../../sharedComponents/NblContainers/NblGridContainer';
import NblDivider from '../../../../../sharedComponents/NblDivider';
import CplanDropdown from '../../components/CplanDropdown';
import TableExport from '../../components/TableExport';
import { exportFormats } from '../../utils/constant';
import { CalculatedVropsResource } from '../../utils/types';
import { sortLabels } from '../../utils/constant';
import { useEffect, useState } from 'react';
import { colorDeciderFunction } from '../../utils/utilization';
import { useUsageDetailsFunction } from '../../components/ResourceOverview';
import { MetricType } from '../../utils/statisticinfo';
import useNblNavigate from '../../../../../hooks/useNblNavigate';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from '../../../../../NebulaTheme/type';
import { useTheme } from '@mui/material';
import { useSelector } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import { State } from '../../../../../store/reducers/type';
import NblTypography from '../../../../../sharedComponents/NblTypography';
import useMediaQuery from '../../../../../hooks/useMediaQuery';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

interface CplanSunburstViewProps {
  inner: CalculatedVropsResource;
  outer: CalculatedVropsResource[];
  resourceId: string;
  resourceType: string;
}

const CplanSunburstView: React.FunctionComponent<CplanSunburstViewProps> = ({ outer, inner, resourceId, resourceType }) => {
  const navigate = useNblNavigate();
  const theme: NebulaTheme = useTheme();
  const sunburstLevel = useSelector((state: State) => state.capacityPlanning.sunburstLevel);
  const styles = useMediaQuery(
    {
      height: '600px',
      innerPieInnerRadius: 27,
      innerPieouterRadius: 90,
      outerPieInnerRadius: 105,
      outerPieOuterRadius: 180,
      columns: 12,
      colspan1: 9,
      colspan2: 3,
    },
    {
      height: '350px',
      innerPieInnerRadius: 13.5,
      innerPieouterRadius: 46,
      outerPieInnerRadius: 54,
      outerPieOuterRadius: 93,
      columns: 12,
      colspan1: 8,
      colspan2: 4,
    }
  );

  const chartMetricFilter = sortLabels.filter((label) => label !== 'Ascending Alphabetical');

  const metricOptions = chartMetricFilter.map((metric: string) => {
    return {
      label: metric,
      value: metric,
    };
  });
  const [selectedMetric, setSelectedMetric] = useState(chartMetricFilter[0]);
  const [utilizationData, setUtilizationData] = useState<CalculatedVropsResource>(inner);
  const tabs = [
    {
      label: 'Vcenter',
    },
    {
      label: 'Datacenter',
    },
    {
      label: 'Cluster',
    },
    {
      label: 'Hypervisor',
    },
  ];

  useEffect(() => {
    setUtilizationData(inner);
    if (sunburstLevel == 1) {
      setUtilizationData(outer[0]);
    }
  }, [inner, outer, sunburstLevel]);

  const innerPieData = [
    {
      id: inner?.resourceid,
      label: inner?.resourcelabel || inner?.resourcename,
      value: Number(inner?.cpu?.replace(/,/g, '')) ?? 0,
      color: colorDeciderFunction(selectedMetric, inner.cpuUtilized, inner.memoryUtilized, inner.storageUtilized),
    },
  ];
  const outerPieData = outer.map((resource: CalculatedVropsResource) => {
    return {
      id: resource.resourceid,
      label: resource.resourcelabel ? resource.resourcelabel : resource.resourcename,
      value: Number(resource.cpu.replace(/,/g, '')),
      color: colorDeciderFunction(selectedMetric, resource.cpuUtilized, resource.memoryUtilized, resource.storageUtilized),
    };
  });

  const handleHighLightChangeOnVcenter = (event: any) => {
    if (!event) return;
    const { seriesId, dataIndex } = event;
    if (seriesId === 'auto-generated-id-0') {
      setUtilizationData(inner);
    } else if (seriesId === 'auto-generated-id-1' || dataIndex !== undefined) {
      setUtilizationData(outer[dataIndex]);
    }
  };
  const handleOuterSliceClick = (event: any, data: any) => {
    if (data && data.seriesId === 'auto-generated-id-1' && sunburstLevel !== 4) {
      const clickedSliceId = outerPieData[data.dataIndex].id;
      navigate(clickedSliceId);
    }
  };

  return (
    <>
      <NblFlexContainer direction="column">
        <NblFlexContainer direction="row" height="auto">
          <CplanDropdown
            label="Metrics"
            options={metricOptions}
            value={selectedMetric}
            onChange={(metric: string) => {
              setSelectedMetric(metric);
            }}
          />
          <NblDivider orientation="vertical" color={theme.palette.primary.shade6} length="70%" />

          <TableExport
            downloadOptions={[exportFormats.Excel, exportFormats.CSV, exportFormats.JPEG, exportFormats.PDF]}
            exportSectionId="exportSection"
            resourceId={resourceId}
            resourceType={resourceType}
          ></TableExport>
        </NblFlexContainer>
        <NblGridContainer columns={6} justifyContent="center" maxHeight="530px" margin="3.75rem 0 0 0">
          <NblGridItem colspan={1}>
            <Tabs orientation="vertical" value={sunburstLevel - 1}>
              {tabs.slice(0, sunburstLevel).map((tab, index: number) => (
                <Tab
                  key={index}
                  label={tab.label}
                  onClick={() => {
                    const level = index + 1;
                    navigate(level - sunburstLevel);
                  }}
                  sx={{
                    '&.Mui-selected': {
                      color: theme.palette.primary.main,
                      backgroundColor: theme.palette.primary.shade7,
                    },
                    '&.Mui-Tabsindicator': {
                      backgroundColor: 'blue',
                    },
                  }}
                />
              ))}
            </Tabs>
          </NblGridItem>
          <NblGridItem colspan={5} overflowY="hidden">
            <NblGridContainer columns={styles.columns} overflowY="hidden">
              <NblGridItem colspan={styles.colspan1} height={styles.height}>
                <NblTypography variant="h4" textAlign="center" color="shade1" whiteSpace="nowrap">
                  {utilizationData?.resourcelabel ? utilizationData?.resourcelabel : utilizationData?.resourcename}
                </NblTypography>
                <PieChart
                  series={[
                    {
                      innerRadius: styles.innerPieInnerRadius,
                      outerRadius: styles.innerPieouterRadius,
                      data: innerPieData,
                      paddingAngle: 3,
                      cx: '63%',
                      cy: '42%',
                    },
                    {
                      innerRadius: styles.outerPieInnerRadius,
                      outerRadius: styles.outerPieOuterRadius,
                      data: outerPieData,
                      paddingAngle: 3,
                      cx: '63%',
                      cy: '42%',
                    },
                  ]}
                  slotProps={{
                    legend: { hidden: true },
                  }}
                  onHighlightChange={handleHighLightChangeOnVcenter}
                  onItemClick={handleOuterSliceClick}
                />
              </NblGridItem>
              <NblGridItem colspan={styles.colspan2} alignSelf="center" height="auto">
                <NblGridContainer rows={12}>
                  <NblGridItem rowspan={4}>
                    {useUsageDetailsFunction(
                      MetricType.CPU,
                      utilizationData?.cpu,
                      utilizationData?.cpuUtilized,
                      utilizationData?.cpuAllocated,
                      false,
                      true
                    )}
                  </NblGridItem>
                  <NblGridItem rowspan={4}>
                    {useUsageDetailsFunction(
                      MetricType.Memory,
                      utilizationData?.memory,
                      utilizationData?.memoryUtilized,
                      utilizationData?.memoryAllocated,
                      false,
                      true
                    )}
                  </NblGridItem>
                  <NblGridItem rowspan={4}>
                    {useUsageDetailsFunction(
                      MetricType.Storage,
                      utilizationData?.storage,
                      utilizationData?.storageUtilized,
                      utilizationData?.storageAllocated,
                      false,
                      true
                    )}
                  </NblGridItem>
                </NblGridContainer>
              </NblGridItem>
            </NblGridContainer>
          </NblGridItem>
        </NblGridContainer>
      </NblFlexContainer>
    </>
  );
};
export default CplanSunburstView;
