import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import { generateEnum } from 'utils/common';
import NblCounterField from 'sharedComponents/NblFormInputs/NblCounterField';
import { FormValues } from '..';
import NblDivider from 'sharedComponents/NblDivider';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
// eslint-disable-next-line no-unused-vars
import useSecretCommonFields from 'componentsV2/SecretsManagement/CreateNewSecret/CreateNewSecretForm/SecretCommonFields';
import NblFieldWrapper from 'sharedComponents/NblFormInputs/NblFieldWrapper';
import NblTypography from 'sharedComponents/NblTypography';

interface CreatePasswordPolicyFormProps {
  setFormInitialValues: React.Dispatch<React.SetStateAction<FormValues>>;
}

const CreatePasswordPolicyForm: React.FunctionComponent<CreatePasswordPolicyFormProps> = () => {
  const { nblFormProps, nblFormValues } = useNblForms<FormValues>();
  const FIELDNAMES = generateEnum<FormValues>(nblFormValues);
  const commonFields = useSecretCommonFields((namespace, namespacePath) => {
    {
      nblFormProps.setFieldValue(FIELDNAMES.namespace, namespace);
      nblFormProps.setFieldValue(FIELDNAMES.namespacePath, namespacePath);
    }
  });

  //JSX
  return (
    <>
      <NblFlexContainer direction={'column'}>
        <NblGridContainer columns={5} spacingX={5} spacingY={2}>
          {commonFields.map((field) => (
            <NblGridItem key={field.title}>
              <NblFieldWrapper mandatory name={field.title} disabled={false} label={field.title} error={false} helperText={''}>
                <NblTypography
                  variant="subtitle1"
                  padding={field.value ? '10px 0 5px 0' : '0'}
                  weight={'medium'}
                  color={'shade1'}
                  whiteSpace={'pre-line'}
                  wordBreak={'break-word'}
                >
                  {field.value}
                </NblTypography>
              </NblFieldWrapper>
            </NblGridItem>
          ))}
        </NblGridContainer>
        <NblDivider orientation="horizontal" length="100%" borderRadius={1} opacity={1} strokeWidth={0.3} mt="10px" mb="10px" />
        <NblGridContainer columns={4} spacingX={2} spacingY={2} overflowX="auto">
          <NblGridItem width="90%">
            <NblTextField
              mandatory
              type="text"
              label="Policy Name"
              placeholder="Enter"
              name={FIELDNAMES.policyname}
              value={nblFormValues.policyname}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.policyname ? nblFormProps.errors.policyname : ' '}
              error={Boolean(nblFormProps.errors.policyname && nblFormProps.touched.policyname)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblCounterField
              type="number"
              label="Password Length"
              name={FIELDNAMES.totalchars}
              initialValue={nblFormValues.totalchars}
              maxValue={30}
              handleChange={(v) => nblFormProps.setFieldValue(FIELDNAMES.totalchars, v)}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.totalchars ? nblFormProps.errors.totalchars : ' '}
              error={Boolean(nblFormProps.errors.totalchars && nblFormProps.touched.totalchars)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblCounterField
              type="number"
              label="Minimum Lower Case"
              name={FIELDNAMES.smallAlphabets}
              initialValue={nblFormValues.smallAlphabets}
              maxValue={30}
              handleChange={(v) => nblFormProps.setFieldValue(FIELDNAMES.smallAlphabets, v)}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.smallAlphabets ? nblFormProps.errors.smallAlphabets : ' '}
              error={Boolean(nblFormProps.errors.smallAlphabets && nblFormProps.touched.smallAlphabets)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblCounterField
              type="number"
              label="Minimum Upper Case"
              name={FIELDNAMES.bigAlphabets}
              initialValue={nblFormValues.bigAlphabets}
              maxValue={30}
              handleChange={(v) => nblFormProps.setFieldValue(FIELDNAMES.bigAlphabets, v)}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.bigAlphabets ? nblFormProps.errors.bigAlphabets : ' '}
              error={Boolean(nblFormProps.errors.bigAlphabets && nblFormProps.touched.bigAlphabets)}
            />
          </NblGridItem>
          <NblGridItem width="90%">
            <NblTextField
              mandatory
              type="text"
              label="Allowed Special Characters"
              placeholder="Enter"
              name={FIELDNAMES.splChars}
              value={nblFormValues.splChars}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.splChars ? nblFormProps.errors.splChars : ' '}
              error={Boolean(nblFormProps.errors.splChars && nblFormProps.touched.splChars)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblCounterField
              type="number"
              label="Minimum Special Characters"
              name={FIELDNAMES.noOfSplChars}
              initialValue={nblFormValues.noOfSplChars}
              maxValue={30}
              handleChange={(v) => nblFormProps.setFieldValue(FIELDNAMES.noOfSplChars, v)}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.noOfSplChars ? nblFormProps.errors.noOfSplChars : ' '}
              error={Boolean(nblFormProps.errors.noOfSplChars && nblFormProps.touched.noOfSplChars)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblCounterField
              type="number"
              label="Minimum Numerals"
              name={FIELDNAMES.numbers}
              initialValue={nblFormValues.numbers}
              maxValue={30}
              handleChange={(v) => nblFormProps.setFieldValue(FIELDNAMES.numbers, v)}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.numbers ? nblFormProps.errors.numbers : ' '}
              error={Boolean(nblFormProps.errors.numbers && nblFormProps.touched.numbers)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              mandatory
              type="text"
              label="Policy Description"
              placeholder="Enter"
              name={FIELDNAMES.description}
              value={nblFormValues.description}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.description ? nblFormProps.errors.description : ' '}
              error={Boolean(nblFormProps.errors.description && nblFormProps.touched.description)}
            />
          </NblGridItem>
        </NblGridContainer>
      </NblFlexContainer>
    </>
  );
};
export default CreatePasswordPolicyForm;
