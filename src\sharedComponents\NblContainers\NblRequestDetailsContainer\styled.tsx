import { Typography } from '@mui/material';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledTypography = styled(Typography)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography, palette } = theme;
  return {
    '&.MuiTypography-h2': {
      ...typography.h2,
      fontSize: '1.5rem',
      fontWeight: typography.fontWeightBold,
      color: palette.typography.shade1,
    },
    '&.MuiTypography-subtitle2': {
      ...typography.subtitle2,
      color: palette.typography.shade1,
    },
    '&.MuiTypography-subtitle1': {
      ...typography.subtitle2,
      fontWeight: typography.fontWeightLight,
      color: palette.typography.shade1,
    },
  };
});
