import { styled } from '@mui/system';
import { IconButton } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { StyledOutlinedInput } from '../NblTextField/styled';

export const StyledCounterOutlinedInput = styled(StyledOutlinedInput)<{ theme?: NebulaTheme; width?: string | number }>(
  ({ theme, width = '25%' }) => {
    return {
      '&.MuiOutlinedInput-root': {
        width: width,
        backgroundColor: theme.palette.secondary.main,
      },
    };
  }
);

export const StyledButton = styled(IconButton)(() => {
  return {
    boxSizing: 'border-box',
    '&.MuiIconButton-root': {
      padding: '0px',
    },
  };
});
