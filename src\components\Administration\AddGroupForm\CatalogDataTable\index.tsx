import React, { useState } from 'react';
import { Icon<PERSON><PERSON>on, Stack, useTheme, Typography, Box } from '@mui/material';
import { EditOutlined, DeleteOutlineOutlined } from '@mui/icons-material';
// eslint-disable-next-line no-unused-vars
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import DataGridTable from 'components/DataGridTable';
// eslint-disable-next-line
import { CatalogPermissions, SelectedGroupPermissions, Roles } from 'types';
import CatalogPermissionForm from '../CatalogPermission';

interface CatalogDataTableProps {
  data: SelectedGroupPermissions['catalogPermissions'];
  setSelectedCatalogPermissions: (rules: SelectedGroupPermissions['catalogPermissions']) => void;
  catalogPermissions: CatalogPermissions;
}

const CatalogDataTable: React.FunctionComponent<CatalogDataTableProps> = ({ data, catalogPermissions, setSelectedCatalogPermissions }) => {
  const [catalogPermissionData, setCatalogPermissionData] = useState({
    open: false,
    updateCatalogPermissions: {
      id: null,
      catalogId: '',
      roles: [],
    },
  });
  const theme: NebulaTheme = useTheme();
  const {
    palette: { table },
  } = theme;

  const handleEdit = (row: any) => {
    setCatalogPermissionData({
      open: true,
      updateCatalogPermissions: {
        id: row.id,
        catalogId: row.catalogId,
        roles: row.roles.map((role: { _id: string }) => role._id),
      },
    });
  };

  const handleDelete = (rowId: number) => {
    const deletedRow = data.filter((item) => item.id !== rowId);
    setSelectedCatalogPermissions(deletedRow);
  };

  const columns: GridColDef[] = [
    {
      field: 'catalogName',
      headerName: 'Catalogs',
      flex: 1,
      width: 500,
    },
    {
      field: 'roles',
      headerName: 'Catalog Roles',
      flex: 1,
      width: 700,
      sortable: true,
      valueGetter: (params: GridRenderCellParams) => {
        const {
          row: { roles },
        } = params;
        return roles.map((role: { roleName: string }) => role.roleName).join(', ');
      },
      renderCell: (params: GridRenderCellParams) => {
        const {
          row: { roles },
        } = params;
        const roleNames = roles.map((role: { roleName: string }) => role.roleName).join(', ');
        return (
          <Typography title={roleNames} sx={{ width: '100%', textOverflow: 'ellipsis', overflow: 'hidden' }}>
            {roleNames}
          </Typography>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      flex: 0.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        return (
          <Stack spacing={0} direction="row" alignItems={'center'}>
            <IconButton
              data-testid={`edit-icon-${params.id}`}
              onClick={() => {
                handleEdit(params.row);
              }}
            >
              <EditOutlined style={{ color: table.editIconColor }} />
            </IconButton>
            <IconButton
              data-testid={`delete-icon-${params.id}`}
              onClick={() => {
                handleDelete(params.row.id);
              }}
            >
              <DeleteOutlineOutlined style={{ color: table.deleteIconColor }} />
            </IconButton>
          </Stack>
        );
      },
    },
  ];

  const handleCloseDialog = () => {
    setCatalogPermissionData({
      ...catalogPermissionData,
      open: false,
    });
  };

  const setCatalogData = (values: { catalogId: string; catalogName: string; roles: Roles[] }) => {
    setCatalogPermissionData({
      ...catalogPermissionData,
      open: false,
    });
    setSelectedCatalogPermissions(
      data.map((currRow, id: number) => (id !== catalogPermissionData.updateCatalogPermissions.id ? currRow : values))
    );
  };

  const renderForm = () => {
    return (
      <CatalogPermissionForm
        selectedCatalogPermissions={data}
        open={catalogPermissionData.open}
        catalogPermissions={catalogPermissions}
        onClose={handleCloseDialog}
        setCatalogData={setCatalogData}
        catalogDetails={catalogPermissionData.updateCatalogPermissions}
      />
    );
  };

  return (
    <Box sx={{ mt: 2 }}>
      {data.length ? <DataGridTable columns={columns} rows={data} pageSize={5} showResetFilter={false} /> : null}
      {renderForm()}
    </Box>
  );
};

export default CatalogDataTable;
