import { Grid, Typography, IconButton, useTheme } from '@mui/material';

import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';

interface FormNavigationProps {
  isPreviousBtnDisabled: boolean;
  isNextBtnDisabled: boolean;
  selectedTabIndex: number;
  setSelectedTabIndex: (selectedTabIndex: number) => void;
}

const FormNavigation: React.FunctionComponent<FormNavigationProps> = ({
  isPreviousBtnDisabled,
  isNextBtnDisabled,
  selectedTabIndex,
  setSelectedTabIndex,
}) => {
  const theme: NebulaTheme = useTheme();
  const {
    palette: {
      forms: { navigation },
    },
  } = theme;

  return (
    <Grid display={'flex'} justifyContent={'center'} item xs={12} columnGap={5} sx={{ mt: 4 }}>
      <IconButton
        sx={{ width: 100, padding: 0, justifyContent: 'flex-end', color: navigation.color, ...(isPreviousBtnDisabled && { opacity: 0.4 }) }}
        disableRipple
        aria-label="left"
        size="large"
        disabled={isPreviousBtnDisabled}
        onClick={() => setSelectedTabIndex(selectedTabIndex - 1)}
      >
        <ChevronLeftIcon />
        <Typography id={'formNavigation-previous-btn'}>Previous</Typography>
      </IconButton>
      <IconButton
        sx={{ width: 100, padding: 0, justifyContent: 'flex-start', color: navigation.color, ...(isNextBtnDisabled && { opacity: 0.4 }) }}
        disableRipple
        aria-label="right"
        size="large"
        disabled={isNextBtnDisabled}
        onClick={() => setSelectedTabIndex(selectedTabIndex + 1)}
      >
        <Typography id={'formNavigation-next-btn'}>Next</Typography>
        <ChevronRightIcon />
      </IconButton>
    </Grid>
  );
};

export default FormNavigation;
