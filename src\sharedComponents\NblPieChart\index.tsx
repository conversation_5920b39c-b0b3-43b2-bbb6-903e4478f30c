import * as React from 'react';
import { <PERSON><PERSON><PERSON> } from '@mui/x-charts/PieChart';
import { useTheme } from '@mui/material';
import { StyledChartContainer, StyledContainer, StyledHeader, StyledLegend, StyledLegendCircle, StyledLegendList } from './styled';
import { NebulaTheme } from 'NebulaTheme/type';
import useWindowSize from 'hooks/useWindowSize';
import NblTypography from 'sharedComponents/NblTypography';

interface NblPieChartProps {
  data: { value: number; label: string; percentageOverride?: string | number }[];
  title: string;
  subtitle?: string;
  loading: boolean;
}

const NblPieChart: React.FC<NblPieChartProps> = ({ data, title, subtitle, loading }) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();
  const { width } = useWindowSize();

  //Local
  const chartWidth = 160;

  //Memoization
  const chartRatio = React.useMemo(() => width / theme.breakpoints.values['2K'], [width, theme]);
  const alteredData = React.useMemo(() => data.sort((obj1, obj2) => obj1.value - obj2.value), [data]);

  //JSX
  return (
    <StyledContainer>
      <StyledHeader>
        <NblTypography variant={'subtitle1'} weight={'bold'} color={'shade1'}>
          {title}
        </NblTypography>
        {subtitle && <span>-</span>}
        {subtitle && (
          <NblTypography variant="subtitle2" color={'shade1'} opacity={0.5}>
            {subtitle}
          </NblTypography>
        )}
      </StyledHeader>
      <StyledChartContainer>
        {!loading && (
          <>
            <PieChart
              series={[{ data: alteredData, innerRadius: '50%', outerRadius: '80%' }]}
              colors={theme.palette.pieChart}
              slotProps={{
                legend: { hidden: true },
              }}
              margin={{ left: 0, right: 0 }}
              sx={{ padding: 0, margin: 0 }}
              width={chartWidth * chartRatio}
              height={chartWidth * chartRatio}
            />
            <StyledLegendList circleWidth={chartWidth * chartRatio}>
              {alteredData.map((obj, index) => (
                <StyledLegend key={obj.label}>
                  <StyledLegendCircle index={index} />
                  <NblTypography variant="subtitle2" color={'shade1'} opacity={0.5}>
                    {obj.label}
                  </NblTypography>
                  <NblTypography variant={'subtitle1'} weight={'bold'} color={'shade1'}>
                    {obj?.percentageOverride ?? obj.value + '%'}
                  </NblTypography>
                </StyledLegend>
              ))}
            </StyledLegendList>
          </>
        )}
      </StyledChartContainer>
    </StyledContainer>
  );
};

export default NblPieChart;
