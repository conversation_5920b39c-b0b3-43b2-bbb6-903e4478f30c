import { act, render, screen } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import AddTeamForm from '.';
import ThemeProvider from 'mock/ThemeProvider';

jest.mock('react-toastify');

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});

describe('Create AddTeam new request form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();

  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <AddTeamForm title="Add Team" onClose={handleClose} onSuccess={handleSuccess} />
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(screen.getByText('Team Name *')).toBeInTheDocument();
    expect(screen.getByText('Groups *')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    const submitButton = getByText('Submit');
    const cancelButton = getByText('Cancel');
    expect(submitButton).toBeEnabled();
    expect(cancelButton).toBeEnabled();
  });
});
