import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

const StyledContainer = styled('div')(() => {
  return {
    display: 'flex',
    flexDirection: 'column',
    gap: '15px',
    width: '100%',
    height: '100%',
    padding: '10px',
  };
});

const StyledChartContainer = styled('div')(({ theme }) => {
  return {
    position: 'relative',
    display: 'flex',
    width: '100%',
    height: '100%',
    justifyContent: 'flex-start',
    alignItems: 'center',
    borderRadius: '10px',
    backgroundColor: theme.palette.secondary.shade1,
    boxSizing: 'border-box',
    [theme.breakpoints.down('2K')]: {
      height: '80%',
    },
  };
});

const StyledLegendList = styled('div')<{ circleWidth: number }>(({ circleWidth }) => {
  return {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: '10px',
    alignSelf: 'center',
    height: '90%',
    width: `calc(100% - ${circleWidth}px)`,
    marginRight: '20px',
    boxSizing: 'border-box',
  };
});

const StyledLegend = styled('div')(() => {
  return {
    display: 'flex',
    gap: '10px',
    alignItems: 'center',
    width: '100%',
    paddingRight: '50px',
    '.MuiTypography-subtitle1': {
      position: 'absolute',
      right: 0,
    },
  };
});

const StyledLegendCircle = styled('div')<{ theme?: NebulaTheme; index: number }>(({ theme, index }) => {
  return {
    width: '12px',
    height: '12px',
    borderRadius: '100%',
    backgroundColor: theme.palette.pieChart[index],
  };
});

const StyledHeader = styled('div')(() => {
  return {
    display: 'flex',
    alignItems: 'center',
    gap: '5px',
  };
});

export { StyledContainer, StyledLegendCircle, StyledChartContainer, StyledLegendList, StyledLegend, StyledHeader };
