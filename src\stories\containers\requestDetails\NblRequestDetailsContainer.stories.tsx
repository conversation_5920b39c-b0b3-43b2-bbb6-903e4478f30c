//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NebulaTheme from 'NebulaTheme';
import { ComponentProps } from 'react';
import NblRequestDetailsContainer from 'sharedComponents/NblContainers/NblRequestDetailsContainer';

type StoryProps = ComponentProps<typeof NblRequestDetailsContainer>;

export default {
  title: 'Containers/RequestDetails/NblRequest Container',
  tags: ['autodocs'],
  component: NblRequestDetailsContainer,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    title: { type: 'string' },
    componentName: { type: 'string' },
  },
} as Meta<StoryProps>;

type Story = StoryObj<StoryProps>;

const RequestDetails = (args: StoryProps) => {
  //JSX
  return <NblRequestDetailsContainer {...args} />;
};

export const Default: Story = {
  args: {
    title: 'RequestDetails',
    componentName: 'CreateVirtualMachine',
    data: {
      requestId: 'IaaS-NEB-REQ-23456',
      catalogItem: 'IaaS-compute-Virtual-Server',
      createdBy: 'P3236788',
      startDate: '2025-01-14T09:23:19.498Z',
      completedAt: '2025-01-14T09:30:52.314Z',
      status: 'failed',
      approvalStatus: 'approved',
      resourceId: 'NEB-RES-VM-2343'
    },
    approvalDetails: [
      {
        id: 1,
        level: 1,
        approvalGroup: 'AP-Nebula-APS-ReliabilityEng-Catalog-Admin',
        approvedOrRejectedBy: 'Sec-nebula-test-admn',
        approvedOrRejectedAt: '06/01/2024 08:24 PM',
        approvalStatus: 'approved',
        comments: 'Placeholder',
      },
      {
        id: 2,
        level: 2,
        approvalGroup: 'AP-Nebula-APS-ReliabilityEng-Catalog-Admin',
        approvedOrRejectedBy: 'Sec-nebula-test-admn',
        approvedOrRejectedAt: '06/01/2024 08:24 PM',
        approvalStatus: 'pending',
        comments: 'Placeholder',
      },
      {
        id: 3,
        level: 3,
        approvalGroup: 'AP-Nebula-APS-ReliabilityEng-Catalog-Admin',
        approvedOrRejectedBy: 'Sec-nebula-test-admn',
        approvedOrRejectedAt: '06/01/2024 08:24 PM',
        approvalStatus: 'pending',
        comments: 'Placeholder',
      },
      {
        id: 4,
        level: 4,
        approvalGroup: 'AP-Nebula-APS-ReliabilityEng-Catalog-Admin',
        approvedOrRejectedBy: 'Sec-nebula-test-admn',
        approvedOrRejectedAt: '06/01/2024 08:24 PM',
        approvalStatus: 'pending',
        comments: 'Placeholder',
      },
    ],
  },
  render: (args) => {
    return (
      <NebulaTheme>
        <RequestDetails {...args} />
      </NebulaTheme>
    );
  },
};
