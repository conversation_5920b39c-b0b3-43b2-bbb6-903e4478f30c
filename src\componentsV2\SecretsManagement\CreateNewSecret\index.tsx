import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import dayjs from 'dayjs';
import { useDispatch } from 'react-redux';
import { useSearchParams } from 'react-router-dom';

// eslint-disable-next-line no-unused-vars
import NblFormContainer, { NblFormHelpers } from 'sharedComponents/NblContainers/NblFormContainer';
import CreateNewSecretForm from './CreateNewSecretForm';
import { SecretType } from 'types/Enums/SecretType';
import { CreateNewSecretSchema } from 'yupSchema/CreateNewSecretSchema';
import { RotatingSecretFormValues } from './CreateNewSecretForm/RotatingSecretFields';
import EditSecretDetails from './EditSecretDetails';
import { decryptUrlState } from 'utils/common';
import { useApiService } from 'api/ApiService/context';
import useNblNavigate from 'hooks/useNblNavigate';
import { showSpinner } from 'store/reducers/spinner';
import { SuccessPageProps } from 'sharedComponents/NblSuccessfulPage';
import NblButton from 'sharedComponents/Buttons/NblButton';
import { CreateSecretIcon } from 'assets/images/icons/custom-icons';
import { decryptPassword, encryptPassword } from '../common';
import { useNblRefresh } from 'sharedComponents/NblUtils/NblRefreshProvider';

export type FormValues = {
  type: SecretType | '';
  path: string;
  policyId: string;
  vaultKey: string;
  vaultPassword: string;
  isPasswordValid: boolean;
  namespaceName?: string;
  namespacePath?: string;
  lastDeviceSyncStatus?: string;
  active?: boolean;
} & Partial<RotatingSecretFormValues>;

export const initialValues: FormValues = {
  type: '',
  policyId: '',
  vaultKey: '',
  vaultPassword: '',
  isPasswordValid: false,
  namespaceName: '',
  namespacePath: '',
  path: '',
};

interface CreateNewSecretProps {
  editMode?: boolean;
}

export default function CreateNewSecret({ editMode }: CreateNewSecretProps) {
  //Hooks
  const { apiSecretsManagement } = useApiService();
  const dispatch = useDispatch();
  const navigate = useNblNavigate();
  const { refreshData } = useNblRefresh();

  //States
  const [formInitialValues, setFormInitialValues] = useState(initialValues);
  const [searchParams] = useSearchParams();
  const [responseData, setResponseData] = useState<SuccessPageProps>({
    requestId: '',
    title: 'Secret Created Successfully',
  } as SuccessPageProps);
  const [editsecrenId, setEditSecreId] = useState(1);

  //Memoization
  const getSecretDetails = useCallback(() => {
    const secretId = searchParams.get('secretId');
    if (secretId) {
      dispatch(showSpinner({ message: 'Fetching secret details....', id: 'getSecret', status: true }));
      apiSecretsManagement
        .getSecretDetails(secretId)
        .then(async (res) => {
          let decryptedPassword = await decryptPassword(res.data.vaultPassword, apiSecretsManagement);
          let decryptedUserNamePassword =
            res.data.userNamePassword && (await decryptPassword(res.data.userNamePassword, apiSecretsManagement));

          if (res.data) {
            setFormInitialValues({
              path: res.data.vaultPath,
              ...res.data,
              vaultPassword: decryptedPassword,
              isPasswordValid: true,
              userNamePassword: decryptedUserNamePassword,
              ...(res.data.secretTTLInHours && { secretTTLInHours: res.data.secretTTLInHours / 24 }),
              nextRotationDate: res.data.nextRotationDate ? dayjs(res.data.nextRotationDate) : undefined,
            });
          }
        })
        .finally(() => {
          dispatch(showSpinner({ message: 'Fetching secret details....', id: 'getSecret', status: false }));
        });
    }
  }, [searchParams.get('secretId')]);

  //Side Effects
  useEffect(() => {
    if (editMode) {
      getSecretDetails();
    }
  }, [editMode, getSecretDetails]);

  useEffect(() => {
    const metadata = searchParams.get('metadata');
    if (metadata) {
      const decryptedData = decryptUrlState(metadata);
      if (decryptedData?.path) {
        setFormInitialValues((prev) => ({ ...prev, path: decryptedData.path }));
      }
    }
  }, [searchParams.get('metadata')]);

  //Handlers
  //Post Form
  async function handleSubmit(values: FormValues, nblFormHelpers: NblFormHelpers<FormValues>) {
    if (values.namespacePath) {
      dispatch(showSpinner({ message: 'Creating Secret....', id: 'CreateNewSecret', status: true }));
      const encryptedResponse = await encryptPassword(values.vaultPassword, apiSecretsManagement);
      const encryptedUserResponse = values?.userNamePassword && (await encryptPassword(values.userNamePassword, apiSecretsManagement));

      apiSecretsManagement
        .createNewSecret(values.namespacePath, values.path, {
          ...values,
          vaultPassword: encryptedResponse,
          userNamePassword: values.userNamePassword ? encryptedUserResponse : undefined,
          ...(values.secretTTLInHours && { secretTTLInHours: values.secretTTLInHours * 24 }),
          nextRotationDate: values.nextRotationDate ? dayjs(values.nextRotationDate).toISOString() : undefined,
        })
        .then((res) => {
          if (res.status) {
            toast.success('Secret created successfully', {
              position: toast.POSITION.BOTTOM_CENTER,
            });
            if (values.type === SecretType.RotatingSecret) {
              setResponseData((prev) => ({
                ...prev,
                content: 'Rotatable secret is created, perfrom a secret device association',
                renderElement: (
                  <NblButton buttonID="navigate" color="primary" onClick={() => navigate('../secret-device-association')}>
                    Go to secret-device association
                  </NblButton>
                ),
              }));
            } else {
              setResponseData((prev) => ({
                ...prev,
                content: 'You can go back to view the secrets',
                renderElement: (
                  <NblButton buttonID="navigate" color="primary" onClick={() => navigate(-1)}>
                    Go back
                  </NblButton>
                ),
              }));
            }
          }
        })
        .finally(() => {
          dispatch(showSpinner({ message: 'Creating Secret....', id: 'CreateNewSecret', status: false }));
          nblFormHelpers.setSubmitting(false);
        });
    }
  }

  //Update form
  async function handleUpdate(values: FormValues, nblFormHelpers: NblFormHelpers<FormValues>) {
    const secretId = searchParams.get('secretId');
    if (secretId) {
      dispatch(showSpinner({ message: 'Updating Secret....', id: 'CreateNewSecret', status: true }));
      const encryptedResponse = await encryptPassword(values.vaultPassword, apiSecretsManagement);
      const encryptedUserResponse = values?.userNamePassword && (await encryptPassword(values.userNamePassword, apiSecretsManagement));
      apiSecretsManagement
        .updateSecret(secretId, {
          ...values,
          vaultPassword: encryptedResponse,
          userNamePassword: values.userNamePassword ? encryptedUserResponse : undefined,
          ...(values.secretTTLInHours && { secretTTLInHours: values.secretTTLInHours * 24 }),
          nextRotationDate: values.nextRotationDate ? dayjs(values.nextRotationDate).toISOString() : undefined,
        })
        .then((res) => {
          if (res.status) {
            toast.success('Secret updated successfully', {
              position: toast.POSITION.BOTTOM_CENTER,
            });
            setEditSecreId((prev) => prev + 1);
            getSecretDetails();
            refreshData();
          }
        })
        .finally(() => {
          dispatch(showSpinner({ message: 'Updating Secret....', id: 'CreateNewSecret', status: false }));
          nblFormHelpers.setSubmitting(false);
        });
    }
  }

  //JSX
  return (
    <NblFormContainer
      title={responseData.content ? '' : 'Create New Secret'}
      Icon={CreateSecretIcon}
      caption={'Fill the necessary details to create new Secret'}
      formInitialValues={formInitialValues}
      formValidationSchema={CreateNewSecretSchema}
      onSubmit={editMode ? handleUpdate : handleSubmit}
      responseData={editMode ? undefined : responseData}
      steps={[
        {
          caption: '',
          icon: '',
          status: 'current',
          title: '',
          errorFields: [],
        },
      ]}
      formType={editMode ? 'subform' : 'simple'}
    >
      {editMode ? <EditSecretDetails key={editsecrenId} /> : <CreateNewSecretForm setFormInitialValues={setFormInitialValues} />}
    </NblFormContainer>
  );
}
