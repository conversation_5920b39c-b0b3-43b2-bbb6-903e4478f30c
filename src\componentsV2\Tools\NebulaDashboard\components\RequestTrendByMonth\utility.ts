// eslint-disable-next-line
import { CatalogWithSummary } from '../../utils/types';

export function getPieSeriesData(filteredCatalogs: CatalogWithSummary[]) {
  let sum: number;
  const data = filteredCatalogs.map((item) => {
    sum = 0;
    item.summary?.forEach((i) => {
      sum = sum + i.count;
    });

    return { id: item.id, label: item.value, value: sum };
  });
  const allzeroes = data.every((item) => item.value === 0);
  return allzeroes ? [] : data;
}
