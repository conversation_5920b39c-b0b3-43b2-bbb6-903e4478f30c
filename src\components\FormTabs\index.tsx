import { Box, Tabs as MuiTabs, Tab, SvgIcon, useTheme } from '@mui/material';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';

interface TabsProps {
  currentTab: number;
  onTabChangeHandler: (param: number) => void;
  tabsList: Array<{ id: string; label: string; errorFields: string[]; disabled?: boolean; icon: null | typeof SvgIcon }>;
}

const FormTabs: React.FunctionComponent<TabsProps> = ({ currentTab, onTabChangeHandler, tabsList }: TabsProps) => {
  const theme: NebulaTheme = useTheme();
  const {
    typography,
    palette: {
      forms: { tabs },
      error,
    },
  } = theme;

  const renderIcon = (Icon: typeof SvgIcon) => {
    return <Icon style={{ fontSize: '2rem', color: error.main }} />;
  };

  return (
    <Box
      sx={{
        borderColor: 'divider',
        marginBottom: 5,
        '& .MuiTabs-indicator': { height: 0 },
        '& .MuiTab-root': {
          margin: 0,
          color: tabs.color,
          bgcolor: tabs.bgcolor,
          fontWeight: 400,
          borderBottom: `2px solid ${tabs.borderColor}`,
          '&.Mui-selected': {
            fontWeight: 'bold',
            color: tabs.bgcolor,
            bgcolor: tabs.color,
          },
          '&:not(:last-child)': {
            borderRight: `2px solid ${tabs.borderColor}`,
          },
        },
      }}
    >
      <MuiTabs value={currentTab < 0 ? 0 : currentTab} aria-label="form-tabs" variant="fullWidth">
        {tabsList.map((tab, index) => (
          <Tab
            onClick={() => onTabChangeHandler(index)}
            sx={{ fontSize: '1rem', fontFamily: typography.primaryFontFamily, ...(tab.disabled && { opacity: 0.3 }) }}
            key={tab.id}
            label={tab.label}
            {...(tab.icon && { icon: renderIcon(tab.icon) })}
            iconPosition="end"
            disabled={tab.disabled}
          />
        ))}
      </MuiTabs>
    </Box>
  );
};

export default FormTabs;
