import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblDataViewer from '.';

describe('NblDataViewer component', () => {
  const props = {
    data: [
      { name: 'Project', value: 'Placeholder' },
      { name: 'VM Size', value: 'Placeholder' },
    ],
    title: 'VM Details',
    isEdit: true,
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblDataViewer {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
