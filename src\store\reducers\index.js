// third-party
import { combineReducers } from 'redux';

// project import
import authorization from './authorization';
import common from './common';
import catalogs from './catalogs';
import projectNetworks from './projectNetworks';
import projectTags from './projectTags';
import menu from './menu';
import user from './user';
import spinner from './spinner';
import breadcrumbs from './breadcrumbs';
import dnp from './dnp';
import usageMetricsRequests from './usagemetricsrequest';
import RequestFilterConfig from './filterDialogRequests';
import capacityPlanning from './capacityplanning';
import RequestOverviewSlice from './requestOverview'

// ==============================|| COMBINE REDUCERS ||============================== //
const reducers = combineReducers({
  authorization,
  projectNetworks,
  projectTags,
  common,
  menu,
  spinner,
  user,
  catalogs,
  breadcrumbs,
  dnp,
  usageMetricsRequests,
  RequestFilterConfig,
  capacityPlanning,
  RequestOverviewSlice
});

export default reducers;
