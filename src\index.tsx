import { StrictMode, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { OidcProvider } from '@axa-fr/react-oidc';
import { configuration, oidcDefault, token } from './oidc';
import AuthenticationInProgress from './components/Authentication/AuthenticationInProgress';
import AuthenticationFailed from './components/Authentication/AuthenticationFailed';
import { ToastContainer } from 'react-toastify';
import { styled } from '@mui/material/styles';
import { constructRoute } from 'utils/namespace-routing'

// scroll bar
import 'simplebar/src/simplebar.css';

// third-party
import { Provider as ReduxProvider } from 'react-redux';

import './assets/css/style.css';

// project import
import NebulaApp from './NebulaApp';

import ErrorBoundary from './ErrorBoundary';
import { store } from './store';

const StyledToastContainer = styled(ToastContainer)(() => ({
  minWidth: 350,
}));

// ==============================|| MAIN - REACT DOM RENDER  ||============================== //

const container = document.getElementById('root');
const root = createRoot(container!); // createRoot(container!) if you use TypeScript

const RootElement = () => {
  let tokenRefreshPopup: Window | null = null;

  const refreshToken = () => {
    //deleting
    //The popup will get the session data. Just mark it as stale to be cleaned-up
    //before attempting user re-authentication

    window.sessionStorage.setItem(token, 'true');

    if (!tokenRefreshPopup || tokenRefreshPopup?.closed)
      tokenRefreshPopup = window.open(constructRoute('/reauth'), 'popUpWindow', 'width=1,height=1,left=-1000,top=-1000');
  };

  //Close the refresh pop-up if the main application window is closed
  window.addEventListener('beforeunload', () => {
    if (tokenRefreshPopup) {
      tokenRefreshPopup.close();
    }
  });

  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.storageArea === localStorage && event.key === oidcDefault && event.newValue) {
        console.log('User JTW refreshed.');
        window.sessionStorage.setItem(oidcDefault, `${event.newValue}`);
        window.localStorage.removeItem(oidcDefault);

        //To use react live server and avoid Nebula theme mess up after user token refresh
        if (process.env.NODE_ENV === 'production') {
          tokenRefreshPopup?.close();
        }
      }
    };
    window.addEventListener('storage', handleStorageChange);
  }, []);

  async function onEvent(oidcEvent: string, name: string, data: any) {
    // console.log(`Event: ${oidcEvent} name: ${name}`)
    // console.log('timeleft is :', data.timeLeft)

    if (data.timeLeft < 50 && data.timeLeft % 5 == 0) {
      refreshToken();
    }
  }

  // const basename = `${process.env.REACT_APP_ROUTE_BASE_NAME || ''}`;
  return (
    <OidcProvider
      configuration={configuration}
      onEvent={onEvent}
      authenticatingErrorComponent={AuthenticationFailed}
      loadingComponent={AuthenticationInProgress}
      authenticatingComponent={AuthenticationInProgress}
    >
      <ReduxProvider store={store}>
        <BrowserRouter>
          <ErrorBoundary>
            <NebulaApp />
            <StyledToastContainer data-testid="notification-bar" />
          </ErrorBoundary>
        </BrowserRouter>
      </ReduxProvider>
    </OidcProvider>
  );
};

const isStrictMode = process.env.REACT_APP_USE_STRICT_MODE;

if (isStrictMode === 'enabled') {
  root.render(
    <StrictMode>
      <RootElement />
    </StrictMode>
  );
} else {
  root.render(<RootElement />);
}
