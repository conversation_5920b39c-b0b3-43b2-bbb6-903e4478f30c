import { useTheme } from '@mui/material';
/* eslint-disable no-unused-vars */
import { ApexOptions } from 'apexcharts';
import { NebulaTheme } from 'NebulaTheme/type';
import React from 'react';
import { StyledNblStackedBarGraph } from './styled';
import useMediaQuery from 'hooks/useMediaQuery';

interface ISeriesData {
  name: string;
  data: number[];
}

interface INblStackedBarGraphProps {
  title: string;
  labels: string[];
  dataset: ISeriesData[];
}

export const NblStackedBarGraph: React.FunctionComponent<INblStackedBarGraphProps> = ({ title, labels, dataset }) => {
  //Hooks
  const theme: NebulaTheme = useTheme();
  const { barTitle, xaxis, grid, colorsVariants, legendStrokeColor } = theme.palette.stackedBarGraph;

  //Local
  const highestNumber = Math.max(...dataset.flatMap((obj) => obj.data));
  const yaxisMax = Math.max(5, highestNumber * 2);

  const options: ApexOptions = {
    chart: {
      stacked: true,
      stackType: 'normal',
      toolbar: {
        show: false,
      },
    },
    title: {
      text: title, // Title of the chart
      align: 'left', // Title alignment
      margin: 10,
      offsetX: 20,
      offsetY: 18,
      style: {
        fontSize: useMediaQuery('1rem', '0.75rem'),
        fontWeight: 'bold',
        fontFamily: 'Gotham',
        color: `${barTitle.color}`,
      },
    },
    dataLabels: {
      enabled: false,
    },

    xaxis: {
      categories: labels,
      axisTicks: {
        show: false, // Hide vertical lines on x-axis
      },
      labels: {
        style: {
          colors: `${xaxis.labels.colors}`,
          fontSize: '10px',
          fontFamily: 'Gotham',
          fontWeight: 'bold',
        },
      },
    },
    yaxis: {
      min: 0, // Set the minimum value for the Y-axis
      max: yaxisMax, // Set the maximum value for the Y-axis
      tickAmount: 4, // Define the number of ticks on the Y-axis
      labels: {
        padding: 29,
        style: {
          colors: `${xaxis.labels.colors}`,
          fontSize: '10px',
          fontFamily: 'Gotham',
          fontWeight: 'bold',
        },
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '35%',
        borderRadius: 8,
        distributed: false,
        borderRadiusWhenStacked: 'last',
        borderRadiusApplication: 'end',
      },
    },
    legend: {
      show: true,
      position: 'top',
      horizontalAlign: 'right',
      floating: false,
      onItemClick: {
        toggleDataSeries: false,
      },
      markers: {
        offsetY: 2,
        width: 12,
        height: 12,
        strokeColor: `${legendStrokeColor.color}`,
        strokeWidth: 1,
        fillColors: [`${colorsVariants.color1}`, `${colorsVariants.color2}`, `${colorsVariants.color3}`], // Set the fill colors for the markers
        radius: 6,
      },
      labels: {
        colors: [`${colorsVariants.color1}`, `${colorsVariants.color2}`, `${colorsVariants.color3}`],
      },
    },

    grid: {
      show: true,
      borderColor: `${grid.borderColor}`, // Color of the grid lines
      row: {
        colors: [`${grid.row.colors}`],
      },
      column: {
        colors: [`${grid.column.colors}`],
      },
      padding: {
        right: 22,
        bottom: 22,
      },
    },
    colors: [`${colorsVariants.color1}`, `${colorsVariants.color2}`, `${colorsVariants.color3}`],
  };

  return <StyledNblStackedBarGraph options={options} series={dataset} type="bar" width="100%" height="100%" />;
};
