import { render, screen } from '@testing-library/react';
import NblInputAdornment from './index';

// Mock the styled component
jest.mock('./styled', () => ({
  StyledAdornmentBox: ({ children }: any) => <div data-testid="styled-adornment-box">{children}</div>,
}));

// Mock NblTypography
jest.mock('sharedComponents/NblTypography', () => ({ children, ...props }: any) => (
  <span data-testid="nbl-typography" {...props}>{children}</span>
));

describe('NblInputAdornment', () => {
  it('renders with correct label and start position', () => {
    render(<NblInputAdornment position="start" label="USD" />);

    expect(screen.getByText('USD')).toBeInTheDocument();

    const typography = screen.getByTestId('nbl-typography');
    expect(typography).toHaveTextContent('USD');

    const adornmentBox = screen.getByTestId('styled-adornment-box');
    expect(adornmentBox).toBeInTheDocument();
  });

  it('renders with end position', () => {
    const { container } = render(<NblInputAdornment position="end" label="GB" />);

    const inputAdornment = container.querySelector('[class*="MuiInputAdornment"]');
    expect(inputAdornment).toBeInTheDocument();
    expect(screen.getByText('GB')).toBeInTheDocument();
  });
});
