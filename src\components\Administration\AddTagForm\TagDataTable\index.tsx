import React, { useState } from 'react';
import { Box, IconButton, Stack, useTheme } from '@mui/material';
import { EditOutlined, DeleteOutlineOutlined } from '@mui/icons-material';
// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import DataGridTable from 'components/DataGridTable';
import { TagMappingData } from 'types';
import TagMappingForm from '../TagMappingForm';
// eslint-disable-next-line
import { GridColDef } from '@mui/x-data-grid';

interface TagDataTableProps {
  data: TagMappingData[];
  setTagData: (rules: TagMappingData[]) => void;
  tagKeys: { id: string; name: string }[];
  tagValues: { id: string; name: string }[];
}

const TagDataTable: React.FunctionComponent<TagDataTableProps> = ({ data, setTagData, tagKeys, tagValues }) => {
  const [tagMappingData, setTagMappingData] = useState({
    open: false,
    updatedTagData: {
      tagKey: '',
      tagKeyId: '',
      tagValue: '',
      tagValueName: '',
      description: '',
      id: null,
      dynamic: false,
      staticTagValue: '',
    },
  });
  const theme: NebulaTheme = useTheme();
  const {
    palette: { table },
  } = theme;

  const handleEdit = (row: any) => {
    setTagMappingData({
      open: true,
      updatedTagData: {
        id: row.id,
        tagKey: row.tagKey,
        tagKeyId: row.tagKeyId,
        tagValue: row.tagValue,
        tagValueName: row.tagValueName,
        description: row.description,
        dynamic: row.dynamic,
        staticTagValue: row.staticTagValue,
      },
    });
  };

  const handleDelete = (rowId: number) => {
    const deletedRow = data.filter((item: TagMappingData) => item.id !== rowId);
    setTagData(deletedRow);
  };

  const columns: GridColDef[] = [
    {
      field: 'tagKey',
      headerName: 'Label',
      flex: 1,
      width: 500,
    },
    {
      field: 'tagValueName',
      headerName: 'Tag Value',
      flex: 1,
      width: 700,
      valueGetter: (params) => {
        if (params.row.tagValueName && params.row.dynamic) {
          return params.row.tagValueName;
        } else {
          return params.row.staticTagValue;
        }
      },
    },

    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
      width: 700,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      flex: 0.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: any) => {
        return (
          <Stack spacing={0} direction="row" alignItems={'center'}>
            <IconButton
              data-testid={'edit-icon-tag'}
              onClick={() => {
                handleEdit(params.row);
              }}
            >
              <EditOutlined style={{ color: table.editIconColor }} />
            </IconButton>
            <IconButton
              data-testid={'delete-icon-tag'}
              onClick={() => {
                handleDelete(params.row.id);
              }}
            >
              <DeleteOutlineOutlined style={{ color: table.deleteIconColor }} />
            </IconButton>
          </Stack>
        );
      },
    },
  ];

  const handleCloseDialog = () => {
    setTagMappingData({
      ...tagMappingData,
      open: false,
    });
  };

  const setSavedData = (values: TagMappingData) => {
    setTagMappingData({
      ...tagMappingData,
      open: false,
    });
    setTagData(data.map((currRow, id: number) => (id !== tagMappingData.updatedTagData.id ? currRow : values)));
  };

  return (
    <Box sx={{ mt: 2 }}>
      {data.length ? <DataGridTable columns={columns} rows={data} /> : null}
      <TagMappingForm
        onClose={handleCloseDialog}
        setTagData={setSavedData}
        tagDetails={tagMappingData.updatedTagData}
        open={tagMappingData.open}
        tagKeys={tagKeys}
        tagValues={tagValues}
      />
    </Box>
  );
};

export default TagDataTable;
