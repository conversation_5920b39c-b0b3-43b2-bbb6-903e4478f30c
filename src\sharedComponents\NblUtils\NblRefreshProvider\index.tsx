import React, { createContext, useContext } from 'react';

interface NblRefreshProviderProps {
  refreshData: () => any;
  children?: any;
}

interface NblRefreshContextProps {
  refreshData: () => any;
}

const NblRefreshContext = createContext<NblRefreshContextProps>({ refreshData: () => {} });

export const useNblRefresh = () => {
  const context = useContext(NblRefreshContext);
  return context;
};

const NblRefreshProvider: React.FC<NblRefreshProviderProps> = ({ refreshData, children }) => {
  return <NblRefreshContext.Provider value={{ refreshData }}>{children}</NblRefreshContext.Provider>;
};

export default NblRefreshProvider;
