import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblDropdown from 'sharedComponents/NblDropdown';
import NblTypography from 'sharedComponents/NblTypography';

interface CplanDropdownProps {
  label: string;
  options: Array<{ label: string; value: string | number }>;
  value: Array<string | number> | string | number;
  onChange?: (option: any) => void;
  Icon?: React.ElementType;
  multiple?: boolean;
}

const CplanDropdown: React.FC<CplanDropdownProps> = ({ label, options, value, onChange, Icon, multiple }) => {
  return (
    <NblFlexContainer alignItems="center" width="auto" height="2.5rem" alignSelf="start">
      <NblTypography variant="subtitle2" color="shade1">
        {label}
      </NblTypography>
      <NblDropdown options={options} value={value} onChange={onChange} Icon={Icon} multiple={multiple}></NblDropdown>
    </NblFlexContainer>
  );
};

export default CplanDropdown;
