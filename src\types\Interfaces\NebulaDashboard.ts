export interface catalogResourceType {
  catalog1: string[] | [];
  catalog2?: string[] | [];
  catalog3: string[] | [];
  catalog4: string[] | [];
  multiYearData?: any; //need to add the interface
  allRequest?: any[];
  status?: string[];
  year?: number;
  quarters?: number[];
  month?: string;
  totalCreatedRequests?: boolean;
}
export interface ResourceType {
  [key: string]: number;
}
export interface CatalogSummaryData {
  allRequestMonthlyData: any;
  allRequests: any[];
  catalogName: string;
  catalogOneName: string;
  catalogThreeName: string;
  catalog1: string[];
  catalog2?: string[];
  catalog3: string[];
  catalog4: string[];
}
export interface QuartersData {
  [key: number]: number[];
}
