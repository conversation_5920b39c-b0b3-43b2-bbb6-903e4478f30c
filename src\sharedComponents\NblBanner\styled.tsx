import { styled } from '@mui/material/styles';
import { Alert } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledBanner = styled(Alert)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography, palette } = theme;
  const { alert } = palette;
  return {
    ...typography.h6,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: alert.color,
    paddingTop: '0',
    paddingBottom: '0',
    textWrap: 'nowrap',
    marginTop: '8px',
    '&.MuiAlert-colorWarning': {
      backgroundColor: theme.palette.tertiary.shade4.light,
    },

    '& .MuiAlert-message': {
      overflow: 'hidden',
      whiteSpace: 'break-spaces'
    },

    '& .MuiAlert-icon': {
      // Default icon color
      color: alert.warningColor,
    },
  };
});
