import { styled } from '@mui/material/styles';
// eslint-disable-next-line
import MainCard from 'mantis/components/MainCard';
// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';

interface CatalogCardProps {
  theme: NebulaTheme;
}

const CatalogCard = styled(MainCard)(
  ({
    theme: {
      palette: { contentCard },
    },
  }: CatalogCardProps) => ({
    minHeight: 191,
    minWidth: 268,
    borderRadius: '0',
    height: '100%',
    width: 268,
    borderColor: contentCard.borderColor,
    backgroundColor: contentCard.backgroundColor,
    backdropFilter: contentCard.backdropFilter,
    cursor: 'pointer',
    '& .hexagon-svg-icon-children': {
      color: contentCard.iconColor,
    },
    '&.isDisabled:hover': {
      cursor: 'not-allowed',
    },
    '&:not(.isDisabled):hover': {
      background: contentCard.textPrimaryColor,
      '& .hexagon-svg-icon': {
        color: contentCard.hoveringColor,
      },
      '& .hexagon-svg-icon-children': {
        color: contentCard.textPrimaryColor,
      },
      '& .MuiTypography-root': {
        color: contentCard.hoveringColor,
      },
    },
  })
);
export default CatalogCard;
