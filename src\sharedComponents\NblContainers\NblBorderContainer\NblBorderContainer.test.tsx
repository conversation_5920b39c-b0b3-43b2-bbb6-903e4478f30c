import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblBorderContainer from '.';

describe('NblBorderContainer component', () => {
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblBorderContainer>
              Layout Wrapper
            </NblBorderContainer>
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
