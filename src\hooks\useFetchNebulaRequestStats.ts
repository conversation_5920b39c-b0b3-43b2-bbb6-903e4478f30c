import { useState } from 'react';

import { useApiService } from 'api/ApiService/context';
import { formatMetricQueryArray } from 'utils/common';
import { CatalogItem } from 'types';

export interface NebulaRequestStats {
  getRequestSummaryByMonth: {
    requestType: string;
    summary: {
      month: number;
      count: number;
    }[];
  }[];
}

interface CatalogItemsQuery {
  year: number;
  quarter: number[];
  month?: number;
  level1: CatalogItem[];
  level3: CatalogItem[];
  level4: CatalogItem[];
  createdBy: string;
}

const getNebulaRequestStatsQuery = ({ createdBy, year, quarter, month, level1, level3, level4 }: CatalogItemsQuery) => {
  return {
    query: `{
    getRequestSummaryByMonth(
        year: ${year}
        quarter: [${quarter}]
        ${month ? 'month: ' + month : ''}
        createdBy:"${createdBy}"
        catalogs1: [${formatMetricQueryArray(level1)}]
        catalogs3: [${formatMetricQueryArray(level3)}]
        catalogs4: [${formatMetricQueryArray(level4)}])
        {
        requestType
        summary{
            month
            count
        }
      }
  }
    `,
  };
};

const useFetchNebulaRequestStats = () => {
  const { apiUsageMetricsService } = useApiService();
  const [loadingRequestStats, setLoadingRequestStats] = useState(true);
  const [nebulaRequestStats, setNebulaRequestStats] = useState<NebulaRequestStats>({
    getRequestSummaryByMonth: [
      {
        requestType: '',
        summary: [
          {
            month: 1,
            count: 0,
          },
        ],
      },
    ],
  });

  const fetchNebulaRequestStats = async ({ year, quarter, month, level1, level3, level4, createdBy }: CatalogItemsQuery) => {
    const queryPayload = {
      year,
      quarter,
      month,
      level1,
      level3,
      level4,
      createdBy,
    };

    setLoadingRequestStats(true);

    const nebulaRequestStatsResponse = await apiUsageMetricsService.getLandingPageNblRequestStats(getNebulaRequestStatsQuery(queryPayload));
    if (nebulaRequestStatsResponse.status) {
      setNebulaRequestStats(nebulaRequestStatsResponse.data.data);
      setLoadingRequestStats(false);
    }
  };

  return {
    nebulaRequestStats,
    fetchNebulaRequestStats,
    loadingRequestStats,
  };
};

export default useFetchNebulaRequestStats;
