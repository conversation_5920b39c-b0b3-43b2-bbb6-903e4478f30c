import * as yup from 'yup';

export const validationSchema = yup.object().shape({
  projectName: yup.string(),
  namespaceId: yup.number(),
  bucket: yup.string(),
  versioning: yup.string(),
  app_id: yup.string(),
  cost_code: yup.string(),
  owner: yup.string(),
  team: yup.string(),
  app: yup.string(),
  data_priv: yup.string(),
  group: yup.string(),
  vp: yup.string(),
  org: yup.string(),
  ops_owner: yup.string(),
  sec_owner: yup.string(),
  dev_owner: yup.string(),
});
