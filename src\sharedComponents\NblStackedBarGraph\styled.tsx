import { styled } from '@mui/system';
import ReactApexChart from 'react-apexcharts';

export const StyledNblStackedBarGraph = styled(ReactApexChart)(() => ({
  '.apexcharts-legend': {
    display: 'flex !important',
    justifyContent: 'flex-end',
    width: 'auto !important',
    position: 'absolute !important',
    right: '0 !important',
    top: '10px !important',
    left: '70px !important',
  },
  '.apexcharts-legend-series': {
    width: ' auto !important' /* Override the width to auto */,
    '.apexcharts-legend-text': {
      gap: '8px',
    },
  },
  '.apexcharts-bar-area': {
    paddingRight: '15px' /* Add padding to create a gap between columns */,
  },
}));

export default StyledNblStackedBarGraph;
