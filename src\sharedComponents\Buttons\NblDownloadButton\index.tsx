import SaveAltIcon from '@mui/icons-material/SaveAlt';

import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblButton from '../NblButton';
import NblTypography from '../../NblTypography';

interface NblDownloadButtonProps {
  buttonID: string;
  onClick?: () => void;
  disabled?: boolean;
  description?: string;
}

const NblDownloadButton: React.FunctionComponent<NblDownloadButtonProps> = ({
  buttonID,
  onClick = () => { },
  disabled,
  description
}) => {
  //Jsx
  return (
    <NblFlexContainer direction={'column'} width={'auto'}>
      <NblButton variant={'contained'} buttonID={buttonID} disabled={disabled} onClick={onClick} endIcon={<SaveAltIcon sx={{ fontSize: '1.5rem' }} />}>Download</NblButton>
      {description && <NblTypography variant='caption' color={'shade1'} weight={'regular'}>{description}</NblTypography>}
    </NblFlexContainer>
  );
};

export default NblDownloadButton;
