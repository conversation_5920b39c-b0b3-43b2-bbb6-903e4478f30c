//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblAutoComplete from 'sharedComponents/NblFormInputs/NblAutoComplete';

type StoryProps = ComponentProps<typeof NblAutoComplete>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblAutoComplete',
  component: NblAutoComplete,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { control: 'text' },
    name: { control: 'text' },
    placeholder: { control: 'text' },
    value: { control: 'text' },
    options: { control: 'object' },
    helperText: { control: 'text' },
  },
};

export default meta;

export const Autocomplete: Story = {
  args: {
    label: 'Label',
    name: 'name',
    placeholder: 'Select an option',
    options: [
      { label: 'John', value: '1' },
      { label: '<PERSON>', value: '2' },
      { label: 'Alex', value: '3' },
      { label: 'Jack', value: '4' },
      { label: 'Tom', value: '5' },
      { label: 'Jane', value: '6' },
    ],
    helperText: 'helper text',
    error: false,
    disabled: false,
  },
  render: (args) => {
    const WrapperComponent = () => {
      const [value, setValue] = useState<string>('');

      return (
        <NebulaTheme>
          <NblFlexContainer width="300px">
            <NblAutoComplete {...args} onChange={(newVal: any) => setValue(newVal)} value={value} />
          </NblFlexContainer>
        </NebulaTheme>
      );
    };

    return <WrapperComponent />;
  },
};
