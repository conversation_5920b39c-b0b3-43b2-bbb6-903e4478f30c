import { PaginationItem, SxProps } from '@mui/material';
import { Styledpagination } from './styled';

interface PaginationProps {
  pageLength: number;
  rowsPerPage: number;
  handlePageChange: (event: any, value: number) => void;
  page: number;
  sx?: SxProps;
}
const PaginationComponent: React.FunctionComponent<PaginationProps> = ({ pageLength, rowsPerPage, handlePageChange, page, sx }) => {
  return (
    <Styledpagination
      count={Math.ceil(pageLength / rowsPerPage)}
      page={page}
      onChange={handlePageChange}
      color="primary"
      sx={sx}
      renderItem={(item) => (
        <PaginationItem
          {...item}
          components={{
            previous: () => <span style={{ border: 'none !important' }}>Prev</span>,
            next: () => <span>Next</span>,
          }}
          sx={{
            ...(item.type === 'previous' || item.type === 'next'
              ? {
                  border: 'none !important',
                  backgroundColor: 'transparent !important',
                }
              : {}),
          }}
        />
      )}
    />
  );
};
export default PaginationComponent;
