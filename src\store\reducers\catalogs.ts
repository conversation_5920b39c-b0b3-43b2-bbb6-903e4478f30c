// types
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
// eslint-disable-next-line no-unused-vars
import UsageMetricsService from 'api/ApiService/UsageMetricsService';
import { getCatalogItemsQuery, sortCatalogItems } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { CatalogLevel04Data, CatalogItem } from 'types';

export type CatalogsItem = {
  description: string;
  id: string;
  name: string;
  icon: string;
  shortName: string;
  enabled?: boolean;
  requestType?: string;
  component?: string;
  sequenceNumber: number;
};

export type CatalogLevel = {
  [key: string]: Array<CatalogsItem>;
};

export type CatalogLevels = {
  level1: Array<CatalogsItem>;
  level2: CatalogLevel;
  level3: CatalogLevel;
  level4: CatalogLevel;
  allCatalogItems: CatalogItem[];
};

export type Catalogs = CatalogLevels & {
  isCatalogsLoading: boolean;
  catalogItemDetails: { [key: string]: CatalogLevel04Data };
};

const initialState: Catalogs = {
  isCatalogsLoading: false,
  level1: [],
  level2: {},
  level3: {},
  level4: {},
  allCatalogItems: [],
  catalogItemDetails: {},
};

function transformCatalogData2(catalogs: CatalogItem[]): CatalogLevels {
  let result: CatalogLevels = {
    level1: [],
    level2: {},
    level3: {},
    level4: {},
    allCatalogItems: catalogs,
  };

  function processCatalog(catalog: CatalogItem, level: number, parentShortname: string) {
    const catalogEntry = {
      id: catalog.id,
      name: catalog.name,
      description: catalog.description,
      shortName: catalog.value ? catalog.value : '',
      icon: catalog.icon || '',
      enabled: catalog.enabled,
      ...(level === 4 && {
        requestType: catalog.requestType,
        component: catalog.component,
        enabled: catalog.enabled,
      }),
      sequenceNumber: catalog.sequenceNumber,
    };

    // Add to level1 if it's the first level
    if (level === 1) {
      result.level1.push(catalogEntry);
    }

    // Add to the level2, level3, level4
    if (level === 2) {
      result.level2[parentShortname] = result.level2[parentShortname] || [];
      result.level2[parentShortname].push(catalogEntry);
    } else if (level === 3) {
      result.level3[parentShortname] = result.level3[parentShortname] || [];
      result.level3[parentShortname].push(catalogEntry);
    } else if (level === 4) {
      result.level4[parentShortname] = result.level4[parentShortname] || [];
      result.level4[parentShortname].push(catalogEntry);
    }

    if (catalog.subCatalogs && catalog.subCatalogs.length > 0) {
      sortCatalogItems(catalog.subCatalogs).forEach((subCatalog) => {
        processCatalog(subCatalog, level + 1, catalog.value);
      });
    }
  }

  sortCatalogItems(catalogs).forEach((level1) => {
    processCatalog(level1, 1, ''); // Start from level 1
  });

  return result;
}
// ==============================|| SLICE - Catalogs ||============================== //

const catalogs = createSlice({
  name: 'catalogs',
  initialState,
  reducers: {
    setCatalogLevel1(state, action) {
      state.level1 = action.payload;
    },
    setCatalogLevel2(state, action) {
      state.level2 = { ...state.level2, ...action.payload };
    },
    setCatalogLevel3(state, action) {
      state.level3 = { ...state.level3, ...action.payload };
    },
    setCatalogLevel4(state, action) {
      state.level4 = { ...state.level4, ...action.payload };
    },
    setCatalogItemDetails(state, action) {
      state.catalogItemDetails = { ...state.catalogItemDetails, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(initCatalogDataAsyncThunk.pending, (state) => {
        state.isCatalogsLoading = true;
      })
      .addCase(initCatalogDataAsyncThunk.fulfilled, (state, action) => {
        state.isCatalogsLoading = false;
        if (action.payload.status && action.payload.data.data.catalogs) {
          const { level1, level2, level3, level4, allCatalogItems } = transformCatalogData2(action.payload.data.data.catalogs);
          state.level1 = level1;
          state.level2 = level2;
          state.level3 = level3;
          state.level4 = level4;
          state.allCatalogItems = allCatalogItems;
        }
      })
      .addCase(initCatalogDataAsyncThunk.rejected, (state) => {
        state.isCatalogsLoading = false;
      });
  },
});

export const initCatalogDataAsyncThunk = createAsyncThunk(
  'catalogs/setInitCatalogData',
  async (apiUsageMetricsService: UsageMetricsService) => {
    return await apiUsageMetricsService.getAllCatalogItems(getCatalogItemsQuery());
  }
);

export default catalogs.reducer;

export const { setCatalogLevel1, setCatalogLevel2, setCatalogLevel3, setCatalogLevel4, setCatalogItemDetails } = catalogs.actions;
