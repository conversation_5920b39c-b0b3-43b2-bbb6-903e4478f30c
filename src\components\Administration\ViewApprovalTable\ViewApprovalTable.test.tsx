import { act, render, waitFor } from '@testing-library/react';
import { MemoryRouter as Router } from 'react-router-dom';
import ThemeProvider from 'mock/ThemeProvider';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import ViewApprovalTable from '.';
import AdministrationService from 'api/ApiService/AdministrationService';
import { ViewApprovalRows } from 'mock/viewApprovals';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'approvals', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/approvals/view-approvals'];

describe('ViewApprovals Component', () => {
  let getApprovalSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getApprovalSpy = jest.spyOn(AdministrationService.prototype, 'getApprovals');

    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);
  });

  afterEach(() => {
    getApprovalSpy.mockRestore();
  });

  test('Should display the No Record Found message', async () => {
    getApprovalSpy.mockResolvedValue({ status: false, data: [] });
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <ViewApprovalTable permissions={{ canRead: true }} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    await waitFor(() => {
      expect(getApprovalSpy).toHaveBeenCalled();
    });
    expect(getByText('No Record Found')).toBeInTheDocument();
  });
  test('Should render the table with data ', async () => {
    getApprovalSpy.mockResolvedValue({ status: false, data: ViewApprovalRows.data });
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <ViewApprovalTable permissions={{ canRead: true }} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    await waitFor(() => {
      expect(getApprovalSpy).toHaveBeenCalled();
    });

    expect(getByText('Approval Name')).toBeInTheDocument();
    expect(getByText('Service Catalog Name')).toBeInTheDocument();
    expect(getByText('Description')).toBeInTheDocument();
    expect(getByText('Created Date')).toBeInTheDocument();
    expect(getByText('Created By')).toBeInTheDocument();
    expect(getByText('Updated Date')).toBeInTheDocument();
    expect(getByText('Updated By')).toBeInTheDocument();
  });
});
