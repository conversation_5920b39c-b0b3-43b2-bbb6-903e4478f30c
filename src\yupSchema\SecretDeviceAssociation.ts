import * as yup from 'yup';

const secretSchema = yup.object().shape({
  secretId: yup.string().trim().required('Secret Key is required'),
  deviceId: yup.string().trim().required('Device is required'),
});

export const secretDeviceAssociationSchema = () =>
  yup.object().shape({
    projectName: yup.string().required('Project is required'),
    application: yup.string().required('Application is required'),
    environment: yup.string().required('Environment is required'),
    path: yup.string().required('Path is required'),
    namespace: yup.string().required('Namespace is required'),
    secretAssociation: yup.array().of(secretSchema).min(1, 'At least one secret-device association is required'),
  });
