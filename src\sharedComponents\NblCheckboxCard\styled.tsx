import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

interface StyledHeader {
  theme?: NebulaTheme;
}

export const StyledHeader = styled('div')<StyledHeader>(({ theme }) => ({
  position: 'relative',
  gap: '8px',
  margin: '10px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  '&.MuiTypography-root': {
    color: theme.palette.typography.shade1,
  },
}));

export const StyledTitle = styled('span')<StyledHeader>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  color: theme.palette.card.titleTextColor,
  marginLeft: '-10px',
  '& .MuiTypography-root': {
    display: 'inline',
    width: 'auto',
    ...theme.typography.subtitle2,
  },
}));

export const StyledLabel = styled('span')<StyledHeader>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  color: theme.palette.primary.main,
  height: '39px',
  marginLeft: '-220px',
  '& .MuiTypography-root': {
    display: 'inline',
    width: 'auto',
    ...theme.typography.subtitle2,
  },
}));

export const StyledDescription = styled('span')<StyledHeader>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  color: theme.palette.card.titleTextColor,
  marginLeft: '25px',
  marginTop: '-30px',
  padding: '10px',
  '& .MuiTypography-root': {
    display: 'inline',
    width: 'auto',
  },
}));
