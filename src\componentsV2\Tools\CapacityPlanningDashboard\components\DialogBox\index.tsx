import { <PERSON>rid, Box, IconButton, Divider, Tooltip, useMediaQuery, useTheme } from '@mui/material';
import UtilizationStatus from '../../utilizationStatus';
import { CapacityPlanningCloseIcon, CapacityPlanningDropDown } from 'assets/images/icons/custom-icons';
import Table from './Table';
import { VropResource, VropResourceStatistic } from '../../utils/types';
import { CalculateVropsResource, extractStatsAndFormat, processUtilization } from '../../utils/utilization';
import { MetricType, StatisticTypes } from '../../utils/statisticinfo';
import { useEffect, useState } from 'react';
import CapacityPlanningService from 'api/ApiService/CapacityPlanningService';
import { calculateStatusLevel } from '../../utils/status';
import DividerComponent from '../Divider';

import {
  StyledAccordion,
  StyledAccordionDetail,
  StyledAccordionDetails,
  StyledAccordionSummary,
  StyledDialog,
  StyledUtilizationStatus,
} from '../../styled';
import React from 'react';
import TableExport from '../TableExport';
import { exportFormats } from '../../utils/constant';
import { useSelector } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import NblTypography from 'sharedComponents/NblTypography';
import { CplanResourceType } from '../../utils/constant';

interface DialogBoxProps {
  onClose: () => void;
  open: boolean;
  clusterData: VropResource;
}

export function getStatusLabel(level: number) {
  switch (level) {
    case 0:
      return 'Low';
    case 1:
      return 'Normal';
    case 2:
      return 'Attention';
    case 3:
      return 'Warning';
    case 4:
      return 'Danger';
    default:
      return 'Attention';
  }
}

const DialogBox: React.FunctionComponent<DialogBoxProps> = ({ onClose, open, clusterData }: DialogBoxProps) => {
  const capacityPlanningService = new CapacityPlanningService();
  const hours = useSelector((state: State) => state.capacityPlanning.hours);
  const theme: NebulaTheme = useTheme();
  const isMatchDown = useMediaQuery(theme.breakpoints.down('2K'));
  const utilizationObjectForClsuter = CalculateVropsResource(clusterData);
  let hostsCount = Math.round(clusterData?.lateststats?.find((data) => data.statname == 'summary|total_number_hosts')?.statvalue || 0);
  const clusterInfo = {
    RAM: extractStatsAndFormat(
      clusterData.lateststats as VropResourceStatistic[],
      StatisticTypes.mem_haTotalCapacity_average,
      1024 * 1024 * 10
    ),
    totalCores: utilizationObjectForClsuter.cpu,
    hostsCount: hostsCount,
    maintenanceMode: hostsCount ? Math.ceil(hostsCount * 0.1) : 0,
  };
  const utilization = processUtilization(clusterInfo.RAM);
  const [data, setData] = useState<any[]>([]);
  useEffect(() => {
    const getHostList: () => Promise<void> = async () => {
      const ApiresponseByResourceId = await capacityPlanningService.getResourcesById(clusterData.resourceid, hours);
      var hostSystems: any[] = [];
      if (ApiresponseByResourceId.status) {
        ApiresponseByResourceId.data
          .filter((item) => item.resourcetype === CplanResourceType.HOST)
          .forEach((item) => {
            var utilizationObjectHostSystem: any = CalculateVropsResource(item);
            let truncatedText;
            const label = utilizationObjectHostSystem.resourcelabel
              ? utilizationObjectHostSystem.resourcelabel
              : utilizationObjectHostSystem.resourcename;
            if (label.length > 10 && isMatchDown) {
              truncatedText = label.substring(0, 5) + '...';
            } else if (label.length > 10 && !isMatchDown) {
              truncatedText = label.substring(0, 18) + '...';
            } else {
              truncatedText = label;
            }

            utilizationObjectHostSystem.Hypervisor = (
              <Tooltip title={label}>
                <Box
                  component={'span'}
                  style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', display: 'inline-block' }}
                >
                  {truncatedText}
                </Box>
              </Tooltip>
            );
            utilizationObjectHostSystem.CPU = utilizationObjectHostSystem.cpuUtilized;
            utilizationObjectHostSystem.Memory = utilizationObjectHostSystem.memoryUtilized;
            utilizationObjectHostSystem.Storage = utilizationObjectHostSystem.storageUtilized;
            let cpuStatusLevel = calculateStatusLevel(utilizationObjectHostSystem.cpuUtilized);
            let memoryStatusLevel = calculateStatusLevel(utilizationObjectHostSystem.memoryUtilized);
            let storageStatusLevel = calculateStatusLevel(utilizationObjectHostSystem.storageUtilized);
            if (cpuStatusLevel > memoryStatusLevel) {
              if (cpuStatusLevel > storageStatusLevel) {
                utilizationObjectHostSystem.Status = getStatusLabel(cpuStatusLevel);
                utilizationObjectHostSystem.StatusColor = utilizationObjectHostSystem.cpuUtilizedColor;
              } else {
                utilizationObjectHostSystem.Status = getStatusLabel(storageStatusLevel);
                utilizationObjectHostSystem.StatusColor = utilizationObjectHostSystem.storageUtilizedColor;
              }
            } else if (memoryStatusLevel > storageStatusLevel) {
              utilizationObjectHostSystem.Status = getStatusLabel(memoryStatusLevel);
              utilizationObjectHostSystem.StatusColor = utilizationObjectHostSystem.memoryUtilizedColor;
            } else {
              utilizationObjectHostSystem.Status = getStatusLabel(storageStatusLevel);
              utilizationObjectHostSystem.StatusColor = utilizationObjectHostSystem.storageUtilizedColor;
            }
            hostSystems.push(utilizationObjectHostSystem);
          });
      }
      setData(hostSystems);
    };
    if (clusterInfo.hostsCount) getHostList();
    // eslint-disable-next-line
  }, [isMatchDown, hours]);
  const columns = ['Hypervisor', 'Status', 'CPU', 'Memory', 'Storage'];
  const [expanded, setExpanded] = React.useState<string | false>('panel1');

  const handleChange = (panel: string) => (event: React.SyntheticEvent, newExpanded: boolean) => {
    setExpanded(newExpanded ? panel : false);
  };

  const gridComponent = (utilizationNumber: string, name: string, utilizedPercentage: number, allocatedPercentage: number | undefined) => {
    const utilization = processUtilization(utilizationNumber);
    return (
      <>
        <Grid item xs={12}>
          <Box display={'flex'} justifyContent={'flex-start'}>
            {
              <DividerComponent
                orientation="vertical"
                sx={{ border: `3px solid ${theme.palette.primary.shade3}`, mr: 0.8, borderRadius: '8px 8px 0 0' }}
              />
            }
            <NblTypography variant="h3" color="shade4">
              {utilization?.numericPart}
              <span>
                <NblTypography variant="h5" color="shade4" display="inline">
                  {utilization?.unitPart}
                </NblTypography>
              </span>
            </NblTypography>
          </Box>
        </Grid>
        <Grid item xs={12}>
          <Box display={'flex'} justifyContent={'flex-start'}>
            {
              <DividerComponent
                orientation="vertical"
                sx={{ border: `3px solid ${theme.palette.primary.shade3}`, mr: 0.8, borderRadius: '0 0 8px 8px' }}
              />
            }
            <NblTypography variant="body1" textAlign="left" color="shade4">
              {name}
            </NblTypography>
          </Box>
        </Grid>
        <Grid item xs={12} sx={{ ml: '11px' }}>
          <NblTypography variant="body2" textAlign="left" color="shade4">
            Utilized :{`${utilizedPercentage}%`}
          </NblTypography>
        </Grid>
        <Grid item xs={12} sx={{ ml: '11px' }}>
          <NblTypography variant="body2" textAlign="left" color="shade4">
            Allocated:{`${allocatedPercentage}%`}
          </NblTypography>
        </Grid>
      </>
    );
  };

  const renderCloseIcon = () => {
    return (
      <IconButton
        aria-label="close"
        onClick={onClose}
        sx={{
          position: 'absolute',
          left: -16,
          top: 14,
          zIndex: 10,
          height: '49px',
          width: '38px',
        }}
      >
        <CapacityPlanningCloseIcon />
      </IconButton>
    );
  };
  return (
    <StyledDialog
      PaperProps={{
        sx: {
          position: 'absolute',
          display: 'flex',
          top: '40px',
          right: -23,
          bottom: 0,
          overflow: 'visible',
          maxHeight: '100vh',
          maxWidth: '40vw',
        },
      }}
      open={open}
      onClose={onClose}
    >
      {renderCloseIcon()}
      <Grid container alignItems={'center'} justifyContent={'flex-start'} sx={{ mt: '9px', ml: '33px' }}>
        <Grid container>
          <Grid item xs={6} sx={{ mt: '23px' }}>
            <NblTypography variant="body1" color="shade4" opacity={0.7}>
              CLUSTER
            </NblTypography>
          </Grid>
          <Grid item xs={6} sx={{ textAlign: 'right', pt: '11px', pr: '47px', color: `${theme.palette.secondary.main}` }}>
            <Grid sx={{ visibility: data.length != 0 && expanded === 'panel2' ? 'visible' : 'hidden', height: 'auto' }}>
              <TableExport
                downloadOptions={[exportFormats.Excel, exportFormats.CSV, exportFormats.JPEG, exportFormats.PDF]}
                resourceId={clusterData.resourceid}
                exportSectionId="exportSectionDialog"
              />
            </Grid>
          </Grid>
          <Grid item xs={12} sx={{ [theme.breakpoints.down('xl')]: { mt: '-18px' } }}>
            <NblTypography variant="h2" color="shade4">{`${
              utilizationObjectForClsuter.resourcelabel
                ? utilizationObjectForClsuter.resourcelabel
                : utilizationObjectForClsuter.resourcename
            }`}</NblTypography>
          </Grid>
        </Grid>
      </Grid>
      <Grid container>
        <Grid item xs={12}>
          <StyledAccordion
            expanded={expanded === 'panel1'}
            onChange={handleChange('panel1')}
            sx={{ [theme.breakpoints.down('xl')]: { '& .Mui-expanded': { mt: '-7px' } } }}
          >
            <StyledAccordionSummary expandIcon={<CapacityPlanningDropDown />}>
              <Grid sx={{ flexGrow: 1 }}>
                <NblTypography variant="body1" color="shade4" opacity={0.7}>
                  SPECIFICATION
                </NblTypography>
              </Grid>

              <Divider orientation="horizontal" sx={{ flexGrow: 22, height: '0.5px', mr: '6px', mt: '5px' }} flexItem color="FFFFFF" />
            </StyledAccordionSummary>
            <StyledAccordionDetails>
              <Grid container spacing={2} sx={{ mt: -10 }}>
                <Grid
                  container
                  sx={{
                    mt: 10,
                    borderRadius: '12px',
                    backgroundColor: '#0a3d65',
                    ml: '15px',
                    opacity: '0.9',
                    padding: '15px 0px 0px 36px',
                  }}
                >
                  <Grid item xs={12} sm={4} justifyContent={'center'} sx={{ mb: 2, textAlign: ' left' }}>
                    {gridComponent(
                      utilizationObjectForClsuter.cpu,
                      MetricType.CPU,
                      utilizationObjectForClsuter.cpuUtilized,
                      utilizationObjectForClsuter?.cpuAllocated
                    )}
                  </Grid>
                  <Grid item xs={12} sm={4} sx={{ mb: 2, textAlign: 'left' }}>
                    {gridComponent(
                      utilizationObjectForClsuter.memory,
                      MetricType.Memory,
                      utilizationObjectForClsuter.memoryUtilized,
                      utilizationObjectForClsuter?.memoryAllocated
                    )}
                  </Grid>
                  <Grid item xs={12} sm={4} justifyContent={'center'} sx={{ mb: 2, textAlign: 'left' }}>
                    {gridComponent(
                      utilizationObjectForClsuter.storage,
                      MetricType.Storage,
                      utilizationObjectForClsuter.storageUtilized,
                      utilizationObjectForClsuter?.storageAllocated
                    )}
                  </Grid>
                </Grid>
                <Grid container alignItems={'center'} alignContent={'center'} marginTop={2}>
                  <Grid container justifyContent={'center'} alignContent={'center'} sx={{ mb: '5px' }}>
                    <Grid item xs={6} justifyContent={'flex-start'} alignItems={'flex-start'}>
                      <NblTypography variant="body1" opacity={0.7} color="shade4">
                        CPU Cores
                      </NblTypography>
                      <NblTypography variant="h1" color="shade4">
                        {clusterInfo.totalCores}
                      </NblTypography>
                    </Grid>
                    <Grid item xs={4} justifyContent={'flex-start'}>
                      <NblTypography variant="body1" opacity={0.7} color="shade4">
                        Hypervisors
                      </NblTypography>
                      <NblTypography variant="h1" color="shade4">
                        {clusterInfo.hostsCount}
                      </NblTypography>
                    </Grid>
                  </Grid>
                  <Grid container justifyContent={'center'} alignContent={'center'}>
                    <Grid item xs={6} justifyContent={'flex-start'} alignItems={'flex-start'}>
                      <NblTypography variant="body1" opacity={0.7} color="shade4">
                        RAM
                      </NblTypography>
                      <NblTypography variant="h1" color="shade4">
                        {utilization?.numericPart}
                        <span>
                          <NblTypography variant="h3" color="shade4" display="inline">
                            {utilization?.unitPart}
                          </NblTypography>
                        </span>
                      </NblTypography>
                    </Grid>
                    <Grid item xs={4} justifyContent={'flex-start'}>
                      <NblTypography variant="body1" opacity={0.7} color="shade4">
                        Maintenance Mode
                      </NblTypography>
                      <NblTypography variant="h1" color="shade4">
                        {clusterInfo.maintenanceMode}
                      </NblTypography>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </StyledAccordionDetails>
          </StyledAccordion>
        </Grid>
        <Grid item xs={12}>
          <StyledAccordion sx={{ backgroundColor: 'transparent' }} expanded={expanded === 'panel2'} onChange={handleChange('panel2')}>
            <StyledAccordionSummary sx={{ backgroundColor: 'transparent !important' }} expandIcon={<CapacityPlanningDropDown />}>
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <Grid sx={{ flexGrow: 1 }}>
                  <NblTypography variant="body1" color="shade4" opacity={0.7}>
                    HYPERVISOR LIST ({clusterInfo.hostsCount})
                  </NblTypography>
                </Grid>
                <Divider orientation="horizontal" sx={{ flexGrow: 22, height: '0.5px', mr: '6px', mt: '5px' }} flexItem color="FFFFFF" />
              </Box>
            </StyledAccordionSummary>
            <StyledAccordionDetail>
              <Grid item xs={12} sx={{ textAlign: 'center', textColor: 'FFFFFF' }} id="exportSectionDialog">
                <Table columns={columns} data={data} dataList={data} />
              </Grid>
            </StyledAccordionDetail>
          </StyledAccordion>
        </Grid>
      </Grid>
      <StyledUtilizationStatus
        sx={{
          position: 'fixed',
          bottom: '0px',
          ml: 30,
          mb: 1,
        }}
      >
        <UtilizationStatus
          textColor={theme.palette.secondary.main}
          typographySx={{
            marginRight: '84px',
            [theme.breakpoints.down('xl')]: {
              mb: '-11px',
            },
          }}
        />
      </StyledUtilizationStatus>
    </StyledDialog>
  );
};
export default DialogBox;
