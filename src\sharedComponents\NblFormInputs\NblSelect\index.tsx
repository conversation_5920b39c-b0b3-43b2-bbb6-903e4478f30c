import React, { useEffect } from 'react';
import { useTheme } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { StyledTypography, StyledMenuItem, StyledListItemIcon, StyledSelect, StyledSelectWrapper } from './styled';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblInputLabel from 'sharedComponents/NblFormInputs/NblInputLabel';
import NblInputHelperText from 'sharedComponents/NblFormInputs/NblInputHelperText';
import { NebulaTheme } from 'NebulaTheme/type';
import { getMenulistPropsStyles, getPaperPropStyles, truncateLabel } from '../common';
import { CustomScrollbarStyles } from '../NblMultiSelect/styled';
import NblTypography from 'sharedComponents/NblTypography';
import { formatId } from 'utils/common';

interface Option {
  value: string | number;
  label: string;
  icon?: React.ComponentType;
  disabled?: boolean;
}

interface NblSelectProps {
  disabled?: boolean;
  error?: boolean;
  label?: string;
  name: string;
  options: Option[] | undefined;
  placeholder: string;
  value: string | number;
  maxLength?: number;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  handleBlur?: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleChange: (event: any) => void;
  datatestid?: any;
  helperText?: string;
  mandatory?: boolean;
  variant?: 'outlined' | 'contained';
  center?: boolean;
}

const NblSelect: React.FunctionComponent<NblSelectProps> = ({
  disabled,
  error,
  label,
  name,
  placeholder,
  value,
  options = [],
  maxLength,
  onMouseEnter,
  onMouseLeave,
  handleBlur = () => {},
  handleChange,
  datatestid,
  helperText = '',
  mandatory = false,
  variant = 'outlined',
  center = false,
}: NblSelectProps) => {
  // Hooks
  const theme = useTheme<NebulaTheme>();

  //to show default value if only one option in dropdown
  useEffect(() => {
    if (mandatory && options?.length === 1 && (!value || value === '')) {
      handleChange({ target: { value: options[0].value, name } });
    }
  }, [mandatory, options.length, value]);

  // JSX
  return (
    <NblFlexContainer direction="column" position="relative" {...(center && { justifyContent: 'center' })}>
      {label && <NblInputLabel label={label} name={name} mandatory={mandatory} disabled={disabled} />}
      <StyledSelect
        disabled={disabled}
        id={name}
        value={value}
        name={name}
        onChange={handleChange}
        onBlur={handleBlur}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        displayEmpty
        type={variant}
        error={Boolean(error)}
        IconComponent={KeyboardArrowDownIcon}
        inputProps={{ 'data-testid': `${datatestid}` }}
        onKeyDown={(e) => e.stopPropagation()}
        onDoubleClick={(e) => e.stopPropagation()}
        MenuProps={{
          disableAutoFocusItem: true,
          PaperProps: {
            sx: getPaperPropStyles(theme),
            component: CustomScrollbarStyles,
          },
          MenuListProps: {
            sx: getMenulistPropsStyles,
          },
        }}
        renderValue={(selected: unknown) => {
          if (!selected) {
            return <StyledTypography>{placeholder}</StyledTypography>;
          }
          const selectedOption = options.find((option: { value: string | number }) => option.value === selected);
          if (!selectedOption) return '';
          return (
            <StyledSelectWrapper>
              {selectedOption.icon && (
                <StyledListItemIcon>
                  <selectedOption.icon />
                </StyledListItemIcon>
              )}
              {selectedOption?.label}
            </StyledSelectWrapper>
          );
        }}
      >
        {options?.length === 0 ? (
          <StyledMenuItem disabled>
            <NblTypography variant="subtitle1" color="shade1">
              No records found
            </NblTypography>
          </StyledMenuItem>
        ) : (
          options.map((option: Option, index: number) => (
            <StyledMenuItem
              key={`${name}-option-${index}`}
              id={formatId(`select-${option.value}-option`)}
              value={option.value}
              disabled={Boolean(option.disabled)}
              className="menuItem"
            >
              {option.icon && (
                <StyledListItemIcon>
                  <option.icon />
                </StyledListItemIcon>
              )}
              {maxLength ? truncateLabel(option.label, maxLength) : option.label}
            </StyledMenuItem>
          ))
        )}
      </StyledSelect>
      {helperText && <NblInputHelperText error={Boolean(error)} helperText={helperText} />}
    </NblFlexContainer>
  );
};

export default NblSelect;
