// eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NebulaTheme from 'NebulaTheme';
import { ComponentProps } from 'react';
import { NblCircledGraph } from 'sharedComponents/NblCircledGraph';

type StoryProps = ComponentProps<typeof NblCircledGraph>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'CircledGraph/NblCircledGraph',
  component: NblCircledGraph,
  parameters: {
    layout: 'left',
  },
  tags: ['autodocs'],
  argTypes: {
    data: {
      control: {
        type: 'object', // The control type will be array
      },
      description: 'Dataset for the chart, should be an array of data arrays',
    },
    loading: { type: 'boolean' },
  },
};

export default meta;

export const CircledGraph: Story = {
  args: {
    loading: false,
    data: [
      { width: 100, value: 70, label: 'IaaS' },
      { width: 88, value: 30, label: 'PaaS' },
      { width: 59, value: 23, label: 'Tools' },
    ], // Default dataset
  },
  render: (args: any) => (
    <NebulaTheme>
      <div style={{ width: '100%', height: '100%' }}>
        <NblCircledGraph {...args} />
      </div>
    </NebulaTheme>
  ),
};
