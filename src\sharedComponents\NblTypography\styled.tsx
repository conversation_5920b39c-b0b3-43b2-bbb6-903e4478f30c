import { Typography, styled } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { TypographyPalette } from 'NebulaTheme/type/typographyPalette';

export type TypographyVariant =
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'h6'
  | 'subtitle1'
  | 'subtitle2'
  | 'body1'
  | 'body2'
  | 'body3'
  | 'body4'
  | 'button'
  | 'textButton'
  | 'caption';
export type TypographyWeight = 'bold' | 'semiBold' | 'medium' | 'regular' | 'light';

interface StyledTypographyProps {
  theme?: NebulaTheme;
  variant: TypographyVariant;
  weight?: TypographyWeight;
  color?: keyof TypographyPalette['typography'];
  margin?: string;
  padding?: string;
  textAlign?: React.CSSProperties['textAlign'];
  textTransform?: React.CSSProperties['textTransform'];
  display?: React.CSSProperties['display'];
  whiteSpace?: React.CSSProperties['whiteSpace'];
  wordBreak?: React.CSSProperties['wordBreak'];
  showEllipsis?: boolean;
  opacity?: number;
  width?: string;
  cursor?: React.CSSProperties['cursor'];
  textDecoration?: React.CSSProperties['textDecoration'];
}

export const StyledTypography = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'color' && prop !== 'themeColor' && prop !== 'showEllipsis' && prop !== 'wordBreak',
})<StyledTypographyProps>(
  ({
    theme,
    weight,
    color,
    margin,
    padding,
    textAlign,
    display,
    whiteSpace,
    showEllipsis,
    opacity,
    textTransform,
    width,
    cursor,
    wordBreak,
    textDecoration,
  }) => {
    const { typography } = theme;
    return {
      ...(weight && {
        ...typography[weight],
      }),
      ...(color && {
        color: theme.palette.typography[color as keyof TypographyPalette['typography']],
      }),
      ...(margin && {
        margin,
      }),
      ...(padding && {
        padding,
      }),
      ...(textAlign && {
        textAlign,
      }),
      ...(textTransform && {
        textTransform,
      }),
      ...(display && {
        display,
      }),
      ...(whiteSpace && {
        whiteSpace,
      }),
      ...(opacity && {
        opacity,
      }),
      ...(width && {
        width,
      }),
      ...(cursor && {
        cursor,
      }),
      ...(wordBreak && {
        wordBreak,
      }),
      ...(textDecoration && {
        textDecoration,
      }),
      ...(showEllipsis && {
        textOverflow: 'ellipsis',
        overflow: 'hidden',
      }),
    };
  }
);
