import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useParams } from 'react-router-dom';

import { getAdministrationCatalogItems } from 'api/static-data';
import NonAuthorizedPage from 'components/Authentication/NonAuthorizedPage';
import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
// eslint-disable-next-line no-unused-vars
import { AdminComponent, AdminTileDetails } from 'types';
import { AdminEditRouteParams, Permissions } from 'types/Enums';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { AdminCatalogdetails } from 'types/Interfaces/AdminTileDetails';

type Data = {
  [key: string]: Array<{ path: string; editPath?: string; shortName: string }>;
};

function withAdminPermissions<P>(WrappedComponent: AdminComponent<P>) {
  const WithAdminPermissions: React.FC<P> = (props) => {
    const {
      [AdminEditRouteParams.approvals]: approvalsShortName,
      [AdminEditRouteParams.groups]: groupName,
      [AdminEditRouteParams.projects]: projectName,
      [AdminEditRouteParams.roles]: roleName,
      [AdminEditRouteParams.serviceCatalogGroup]: serviceCatalogGroupName,
      [AdminEditRouteParams.serviceCatalogItem]: serviceCatalogItem,
      [AdminEditRouteParams.tags]: tagsShortName,
    } = useParams();
    const { pathname } = useLocation();
    const [isUnAuthorized, setIsUnAuthorized] = useState<boolean>(false);
    const [permissions, setPermissions] = useState<AdminTileDetails | null>(null);
    const [catalogDetails, setCatalogDetails] = useState<AdminCatalogdetails>();

    const { adminPermissions } = useSelector((state: State) => state.authorization);
    const isEditForm =
      approvalsShortName || groupName || projectName || roleName || serviceCatalogGroupName || serviceCatalogItem || tagsShortName;

    const hasformPermissions = (selectedPermission: AdminTileDetails) => {
      if (isEditForm) {
        return selectedPermission[Permissions.canUpdate] || selectedPermission[Permissions.canRead];
      } else {
        return selectedPermission[Permissions.canCreate];
      }
    };

    const hasGridPermissions = (selectedPermission: AdminTileDetails) => {
      return (
        selectedPermission[Permissions.canUpdate] || selectedPermission[Permissions.canRead] || selectedPermission[Permissions.canDelete]
      );
    };

    const hasAnyPermission = (selectedPermission: AdminTileDetails) => {
      switch (WrappedComponent.type) {
        case ADMIN_TILE_PERMISSION_TYPE.form:
          return hasformPermissions(selectedPermission);
        case ADMIN_TILE_PERMISSION_TYPE.grid:
          return hasGridPermissions(selectedPermission);
        default:
          return false;
      }
    };

    useEffect(() => {
      getAdministrationCatalogItems()
        .then((data: Data) => {
          const selectedCatalogItem = Object.values(data)
            .flat()
            .find((item) => item.path === pathname || (item.editPath && pathname.includes(item.editPath)));
          setCatalogDetails(selectedCatalogItem as AdminCatalogdetails);
          let selectedCatalogItemPermissions =
            adminPermissions.find((permission) => permission.shortName === selectedCatalogItem?.shortName) || null;
          const hasPermission = selectedCatalogItemPermissions && hasAnyPermission(selectedCatalogItemPermissions);
          if (hasPermission) {
            // @ts-ignore
            const { canCreate, ...restPermissions } = selectedCatalogItemPermissions;
            setPermissions({ ...restPermissions, canCreate: isEditForm ? false : canCreate });
          } else {
            setIsUnAuthorized(true);
          }
        })
        .catch((error) => {
          setIsUnAuthorized(true);
          console.log('withAdminPermissions::', error);
        });
    }, []);

    return permissions?.shortName ? (
      <WrappedComponent {...props} permissions={permissions} metaData={catalogDetails} />
    ) : isUnAuthorized ? (
      <NonAuthorizedPage />
    ) : null;
  };

  return WithAdminPermissions;
}

export default withAdminPermissions;
