import { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid, Button, Typography } from '@mui/material';
import { hasFormikErrors, yupMatchesParams } from 'utils/common';
import { toast } from 'react-toastify';
/* eslint-disable no-unused-vars */
import FormWrapper from 'components/FormWrapper';
import TextField from 'components/TextField';
import Select from 'components/Select';
import icons from 'assets/images/icons';
import {
  FormProps,
  ServiceCatalogItem,
  ApprovalDetails,
  ApprovalsPayload,
  EditApprovalDetails,
  ApprovalGroups,
  AddApprovalResponse,
  AdminComponent,
} from 'types';

import withAdminPermissions from 'hoc/withAdminPermissions';
import useExposureParams from 'hooks/useExposureParams';
import AddApprovalDetailsDialog from './AddApprovalDetailsDialog';
import AddApprovalDetailsTable from './AddApprovalDetailsTable';
import PermissionService from 'api/ApiService/PermissionService';
import CatalogService from 'api/ApiService/CatalogService';
import AdministrationService from 'api/ApiService/AdministrationService';
import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import useNblNavigate from 'hooks/useNblNavigate';

interface AddApprovalFormProps extends FormProps {
  editApprovalDetails?: EditApprovalDetails;
}

const validationSchema = yup.object().shape({
  approvalName: yup
    .string()
    .trim()
    .required('Approval name is required field')
    .matches(yupMatchesParams.approvalName.pattern, yupMatchesParams.approvalName.errorMessage),
  serviceCatalogName: yup.string().required('Service Catalog is required'),
  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});

const AddApprovalForm: AdminComponent<AddApprovalFormProps> = ({
  title,
  onClose,
  editApprovalDetails,
  permissions: { canUpdate, canCreate },
}) => {
  const { exposureParamsEnabled } = useExposureParams();
  const apiAdministrationService = new AdministrationService();
  const permissionService = new PermissionService();
  const catalogService = new CatalogService();
  const [serviceCatalogItem, setServiceCatalogItem] = useState<ServiceCatalogItem[]>([]);
  const [approvalDialog, setApprovalDialog] = useState<boolean>(false);
  const [approvalDetails, setApprovalDetails] = useState<ApprovalDetails[]>([]);
  const [groupLength, setGroupLength] = useState<Number>(0);
  const [isFormSubmitting, setIsFormSubmitting] = useState<boolean>(false);
  const [approvalGroupId, setApprovalGroupId] = useState<ApprovalGroups[]>([]);

  const navigate = useNblNavigate();

  const getApprovalDetailsData = async () => {
    permissionService.getGroups().then((res) => {
      if (res.status) {
        setApprovalGroupId(res.data);
      }
    });
  };

  const getServiceCatalogItem = async () => {
    catalogService.getServiceCatalogItems().then((res) => {
      if (res.status) {
        setServiceCatalogItem(res.data);
      }
    });
  };
  useEffect(() => {
    getServiceCatalogItem();
    getApprovalDetailsData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const editApprovalHandler = (payload: ApprovalsPayload) => {
    if (editApprovalDetails) {
      const updatedPayload = { ...payload, id: editApprovalDetails.id };
      setIsFormSubmitting(true);
      apiAdministrationService
        .editApprovalCreation(updatedPayload, editApprovalDetails.shortName)
        .then((res) => {
          if (res.status) {
            toast.success(res.data?.message || 'Request submitted successfully', {
              position: toast.POSITION.BOTTOM_CENTER,
            });
            navigate('/administration/approvals/view-approvals');
          } // Don't need the else block as the default error handling is provided by API Service class
        })
        .finally(() => {
          setIsFormSubmitting(false);
        });
    }
  };

  const addApprovalHandler = (payload: ApprovalsPayload) => {
    setIsFormSubmitting(true);
    permissionService
      .addApprovals(payload)
      .then((res) => {
        if (res.status) {
          toast.success(res.data?.message || 'Approval added successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
          navigate('/administration/approvals/view-approvals');
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        setIsFormSubmitting(false);
      });
  };

  const formik = useFormik({
    initialValues: {
      approvalName: '',
      serviceCatalogName: '',
      description: '',
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const { approvalName, serviceCatalogName, description } = values;
      const payload = {
        name: approvalName,
        serviceCatalogItem: serviceCatalogName,
        description,
        approvalDetails: approvalDetails.map((value) => ({
          level: value.level,
          approverCount: value.approverCount,
          groupId: value.groupId,
        })),
      };
      editApprovalDetails ? editApprovalHandler(payload) : addApprovalHandler(payload);
    },
  });

  useEffect(() => {
    if (editApprovalDetails) {
      formik.setValues({
        serviceCatalogName: editApprovalDetails?.serviceCatalogItem,
        approvalName: editApprovalDetails.name,
        description: editApprovalDetails.description,
      });
      const updatedApprovalDetails = editApprovalDetails.approvalDetails.map((tableValue: any) => {
        const selectedGroup = approvalGroupId.find((project) => project._id === tableValue.groupId);
        if (selectedGroup) {
          tableValue.approvalGroupId = selectedGroup?._id;
          tableValue.approvalGroup = selectedGroup?._id;
          tableValue.approvalGroupName = selectedGroup?.groupName;
        }
        return tableValue;
      });
      setApprovalDetails([...updatedApprovalDetails]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editApprovalDetails, approvalGroupId]);

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const handleApprovalData = () => {
    setApprovalDialog(true);
  };

  const dialogCloseHandler = () => {
    setApprovalDialog(false);
  };

  const fetchApprovalData = (value: ApprovalDetails) => {
    setApprovalDetails([...approvalDetails, value]);
  };

  const handleGroupLength = (length: number) => {
    setGroupLength(length);
  };
  const renderDialog = () => {
    return (
      <AddApprovalDetailsDialog
        onClose={dialogCloseHandler}
        setApprovalData={fetchApprovalData}
        selectedGroupData={approvalDetails}
        groupLength={handleGroupLength}
        open={approvalDialog}
      />
    );
  };

  const renderTable = () => {
    return (
      <Grid container item justifyContent={'center'} spacing={3}>
        <Grid item xs={12}>
          <AddApprovalDetailsTable
            data={approvalDetails.map((item, index) => ({
              ...item,
              id: index,
              approverCount: item.approverCount,
              approvalGroupName: item.approvalGroupName,
            }))}
            setApprovalDetails={setApprovalDetails}
          />
        </Grid>
      </Grid>
    );
  };

  const disableFormSubmission = true; 

  /**
   * while editing the request we have added submit behind the exposure controll
   * because from BE side we need to handle one use case https://jira.charter.com/browse/APSRE-2892
   */
  return (
    <>
      <FormWrapper
        title={title}
        isSubmitting={disableFormSubmission || isFormSubmitting || !approvalDetails.length || (editApprovalDetails && !exposureParamsEnabled('editApprovalSubmit'))}
        errors={hasFormikErrors(formik)}
        submitText={'Submit'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        Icon={icons.ApprovalOutlined}
        canCreate={canCreate}
        canUpdate={canUpdate}
      >
        <>
          <Grid container spacing={1}>
            <Grid container item justifyContent={'center'} spacing={3}>
              <Grid item xs={6}>
                <TextField
                  type="text"
                  name="approvalName"
                  label="Approval Name *"
                  placeholder="Please Enter"
                  value={formik.values.approvalName}
                  handleChange={formik.handleChange}
                  handleBlur={formik.handleBlur}
                  error={formik.touched.approvalName && formik.errors.approvalName}
                />
              </Grid>
              <Grid item xs={6}>
                <Select
                  label="Service Catalog *"
                  placeholder="Select"
                  name="serviceCatalogName"
                  value={formik.values.serviceCatalogName}
                  handleChange={formik.handleChange}
                  error={formik.touched.serviceCatalogName && formik.errors.serviceCatalogName}
                  options={serviceCatalogItem?.map((data) => ({ value: data.id, label: data.name }))}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  type="text"
                  value={formik.values.description}
                  label="Description"
                  name="description"
                  handleChange={formik.handleChange}
                  handleBlur={formik.handleBlur}
                  error={formik.touched.description && formik.errors.description}
                />
              </Grid>
              <Grid item xs={6}>
                <Typography mb={1} variant="h4">
                  Add Approval *
                </Typography>
                <Button
                  variant="contained"
                  disabled={approvalDetails.length !== 0 && groupLength === approvalDetails?.length}
                  onClick={handleApprovalData}
                >
                  Add
                </Button>
              </Grid>
            </Grid>
          </Grid>
          {renderTable()}
        </>
      </FormWrapper>
      {renderDialog()}
    </>
  );
};

AddApprovalForm.type = ADMIN_TILE_PERMISSION_TYPE.form;

export default withAdminPermissions(AddApprovalForm);
