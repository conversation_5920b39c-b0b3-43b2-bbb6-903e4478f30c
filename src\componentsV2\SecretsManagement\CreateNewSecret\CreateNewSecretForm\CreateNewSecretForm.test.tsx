import { render, screen } from '@testing-library/react';
import CreateNewSecretForm from '.';
import { MemoryRouter } from 'react-router';
import ReduxProvider from 'mock/ReduxProvider';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { useApiService } from 'api/ApiService/context';

jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  useNblForms: () => ({
    nblFormValues: {
      type: '',
      policyId: '',
      vaultKey: '',
      vaultPassword: '',
      namespaceName: '',
      path: '',
    },
    nblFormProps: {
      setFieldValue: jest.fn(),
      handleBlur: jest.fn(),
      handleChange: jest.fn(),
      setValues: jest.fn(),
      touched: {},
      errors: {},
    },
  }),
}));

jest.mock('./SecretCommonFields', () => () => [
  { title: 'Namespace Name', value: 'test-demo' },
  { title: 'Namespace Path', value: 'nebula-stamp/test-demo' },
]);

jest.mock('./SecretCommonFields', () => () => [
  {
    title: 'Test Field',
    value: 'Test Value',
  },
]);

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

jest.mock('./NormalSecretFields', () => () => <div>NormalSecretFields</div>);
jest.mock('./RotatingSecretFields', () => ({
  __esModule: true,
  default: () => <div>RotatingSecretFields</div>,
  rotatingSecreInitialValues: {
    vaultKey: '',
    vaultPassword: '',
    userNameKey: '',
    userNamePassword: '',
    secretTTLInHours: 1,
    nextRotationDate: null,
    notifyBeforeTokenExpiry: true,
    rotationType: 'Auto',
  },
}));

describe('CreateNewSecretForm', () => {
  beforeEach(() => {
    (useApiService as jest.Mock).mockReturnValue({
      apiSecretsManagement: {
        getPasswordPolicies: [
          {
            policyId: 'Test',
            policyName: 'Test',
            description: 'Test',
          },
        ],
      },
    });
  });
  it('renders Secret Type select', () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <CreateNewSecretForm setFormInitialValues={() => { }} />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );
    expect(screen.getByText('Secret Type')).toBeInTheDocument();
  });
});
