type CreateNamespace = {
  name: string;
  projectName: string;
  projectDL: string;
  projectId: string;
  catalogId: string;
  namespaceName: string;
  platformContext: {
    catalogId: string;
    domainId: string;
    envId: string;
  };
  vaultPolicies: {
    enable: boolean;
    policyName: string;
    role: string;
  }[];
  vaultAppRoles: {
    policyName: string;
    appRoleName: string;
  }[];
  tokenTTL: number;
  autoRenewTokenOnExpiry: boolean;
  notifyBeforeTokenExpiry: boolean;
  addDisks: string[];

  deeplinkUrl: string;
};

export default CreateNamespace;
