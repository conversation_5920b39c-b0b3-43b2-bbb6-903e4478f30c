
import { render, screen } from '@testing-library/react';
import { ViewDetailsFields } from '..';
import NblViewDetailsOverview from './index';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

const mockFields: ViewDetailsFields[] = [
  {
    title: 'Request ID',
    value: 'NEB-VAULT-NAMESPACE-30032',
    subValue: 'Created By',
    reason: 'P312122',
  },
  {
    title: 'Resource ID',
    value: 'NEB-RES-VAULT-NAMESPACE-8718',
  },
];

describe('NblViewDetailsOverview', () => {
  it('renders nothing when viewDetailsFields is undefined', () => {
    const { container } = render(<NebulaThemeProvider><NblViewDetailsOverview /></NebulaThemeProvider>);
    expect(container.firstChild).toBeNull();
  });

  it('renders fields with title, value, subValue, and reason', () => {
    render(<NebulaThemeProvider><NblViewDetailsOverview viewDetailsFields={mockFields} /></NebulaThemeProvider>);
    expect(screen.getByText('Request ID')).toBeInTheDocument();
    expect(screen.getByText('NEB-VAULT-NAMESPACE-30032')).toBeInTheDocument();
    expect(screen.getByText('Created By')).toBeInTheDocument();
    expect(screen.getByText('Resource ID')).toBeInTheDocument();
    expect(screen.getByText('NEB-RES-VAULT-NAMESPACE-8718')).toBeInTheDocument();
  });

  it('renders skeletons when loading is true', () => {
    const { container } = render(<NebulaThemeProvider><NblViewDetailsOverview viewDetailsFields={mockFields} loading /></NebulaThemeProvider>);
    const skeletons = container.querySelectorAll('.MuiSkeleton-root');
    expect(skeletons.length).toBe(mockFields.length);
  });

  it('renders "-" when value is missing', () => {
    const fieldsWithMissingValue: ViewDetailsFields[] = [
      { title: 'Status', value: '' },
    ];
    render(<NebulaThemeProvider><NblViewDetailsOverview viewDetailsFields={fieldsWithMissingValue} /></NebulaThemeProvider>);
    expect(screen.getByText('-')).toBeInTheDocument();
  });

  it('does not render subValue if not provided', () => {
    const fieldsWithoutSubValue: ViewDetailsFields[] = [
      { title: 'Request Status', value: 'COMPLETED' },
    ];
    render(<NebulaThemeProvider><NblViewDetailsOverview viewDetailsFields={fieldsWithoutSubValue} /></NebulaThemeProvider>);
    expect(screen.queryByText('Request Status')).toBeInTheDocument();
    expect(screen.queryByText('body4')).not.toBeInTheDocument(); // Assuming body4 is not rendered
  });
});
