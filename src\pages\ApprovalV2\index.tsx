import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';

// eslint-disable-next-line no-unused-vars
import { dateFormatter } from 'utils/common';
import MyApprovalsGrid from 'componentsV2/Approvals/index';
import { setRefreshMyApprovalsGrid } from 'store/reducers/common';
// eslint-disable-next-line no-unused-vars
import { AssetPayload, DownStreamError } from 'types';
import { useApiService } from 'api/ApiService/context';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import { NblTabBar } from 'sharedComponents/NblNavigation/NblTabBar';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { RequestStatus } from 'types/Enums';

interface ApprovalsProps {}

const Approvals: React.FunctionComponent<ApprovalsProps> = () => {
  const dispatch = useDispatch();
  const [approvalData, setApprovalData] = useState<AssetPayload[]>([]);
  const [isApprovalLoading, setIsApprovalLoading] = useState<boolean>(true);

  const { refreshMyApprovalsGrid } = useSelector((state: State) => state.common);
  const { apiAssetService } = useApiService();

  const theme = useTheme<NebulaTheme>();
  const { palette } = theme;

  const getError = (downstreamError: DownStreamError[]) => {
    return downstreamError.map((error: DownStreamError) => error?.data?.message || '').join(', ');
  };

  const fetchApprovalList = () => {
    setIsApprovalLoading(true);
    apiAssetService
      .getPendingApprovals()
      .then((res) => {
        if (res.status) {
          setApprovalData(
            res.data.map((asset, index) => {
              const latestApprovalData = asset?.approvalDetails?.[asset?.approvalDetails.length - 1];
              return {
                catalogItem: asset?.metadata?.serviceCatalog?.catalogName,
                id: asset?.serviceRequestId || asset?.id,
                sNo: index + 1,
                rowId: asset?.id,
                requestId: asset.serviceRequestId,
                createdAt: asset?.createdAt ? dateFormatter(asset.createdAt) : '',
                catalogLevel03: asset?.metadata?.serviceCatalog?.catalogType,
                createdBy: asset?.createdBy,
                approvalGroup: latestApprovalData?.approvalGroup,
                approvalRejectedBy: latestApprovalData?.approvedOrRejectedBy,
                approvalRejectedDate: latestApprovalData?.approvedOrRejectedAt
                  ? dateFormatter(latestApprovalData?.approvedOrRejectedAt)
                  : '',
                approvalStatus: asset?.approvalStatus,
                evaluationResult: asset?.evaluationResult,
                multiLevelApprovals: asset?.multiLevelApprovals,
                status: asset?.status,
                requesterEmail: asset?.requesterEmail,
                topsTicket: asset?.netopsaskTicket || '-',
                error:
                  (asset?.status === RequestStatus.FAILED ||
                    asset?.status === RequestStatus.PARTIAL ||
                    asset?.status === RequestStatus.TIMED_OUT) &&
                  asset?.downstreamError?.length
                    ? getError(asset.downstreamError)
                    : '',
              };
            })
          );
        } else {
          setApprovalData([]);
        }
      })
      .finally(() => {
        setIsApprovalLoading(false);
      });
  };

  useEffect(() => {
    fetchApprovalList();
  }, []);

  useEffect(() => {
    if (refreshMyApprovalsGrid) {
      fetchApprovalList();
      dispatch(setRefreshMyApprovalsGrid(false));
    }
  }, [refreshMyApprovalsGrid]);

  const handleRefresh = () => {
    fetchApprovalList();
  };

  return (
    <>
      <NblBorderContainer padding="10px" backgroundColor={palette.background.paper} height="auto">
        <NblTabBar
          tabBarIndicator={true}
          tabs={[
            {
              chip: approvalData.length ?? 0,
              label: 'My Approvals',
            },
          ]}
        />
        <NblGridItem colspan={12} width="100%" position="relative" overflowY="scroll">
          <MyApprovalsGrid
            pageSizeOptions={['10', '20', '30', '40']}
            loading={isApprovalLoading}
            rows={approvalData}
            onRefreshHandler={handleRefresh}
          />
        </NblGridItem>
      </NblBorderContainer>
    </>
  );
};

export default Approvals;
