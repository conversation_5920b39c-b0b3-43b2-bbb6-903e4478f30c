import Chart from 'react-apexcharts';
// eslint-disable-next-line no-unused-vars
import { ApexOptions } from 'apexcharts';
interface AreaChartProps {
  dataSet: number[];
  resourceName: string;
  monthName: string[];
}
const RequestSummaryChart = (props: AreaChartProps) => {
  const options: ApexOptions = {
    xaxis: {
      categories: props.monthName,
      labels: { show: false },
      tickPlacement: 'off',
      tooltip: {
        enabled: false,
      },
    },
    stroke: {
      curve: 'straight',
      width: 1,
      colors: ['#0E99D8'],
    },
    dataLabels: {
      enabled: false,
    },
    yaxis: {
      labels: { show: false },
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val.toFixed(0);
        },
      },
    },
    grid: { show: false },
    chart: {
      toolbar: {
        show: false,
      },
      height: '100',
      width: 'auto',
    },
    colors: ['#0099D8', '#0099D800'],
    noData: {
      text: 'No Data Found',
      align: 'center',
      verticalAlign: 'middle',
    },
  };
  const series = () => {
    return [
      {
        name: props.resourceName,
        data: props.dataSet,
      },
    ];
  };
  return <Chart options={options} series={series()} type="area"></Chart>;
};
export default RequestSummaryChart;
