import { useEffect, useState } from 'react';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { NblTabBar } from 'sharedComponents/NblNavigation/NblTabBar';
import NblAccordion from 'sharedComponents/Accordion/NblAccordion';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import { useTheme } from '@mui/material';
import Clusters from './Clusters';
import DataStores from './DataStores';
import Hosts from './Hosts';
import Networks from './Networks';
import OperatingSystems from './OperatingSystems';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { FormValues } from '..';

enum Tabs {
  clusters = 'Clusters',
  hosts = 'Hosts',
  datastores = 'Datastores',
  networks = 'Networks',
  operatingSystem = 'Operating Systems',
}

interface CloudDetailsProps {
  activeCloudIndex: number;
}

const CloudDetails: React.FC<CloudDetailsProps> = ({ activeCloudIndex }) => {
  const { nblFormValues, nblFormProps } = useNblForms<FormValues>();
  const [activeTab, setActiveTab] = useState<Tabs>(Tabs.clusters);

  const {
    palette: { secondary },
  }: NebulaTheme = useTheme();

  const cloudDetailsArray = nblFormValues.cloudDetails || [];

  const currentCloud = cloudDetailsArray[activeCloudIndex] || {
    clusters: [],
    hosts: [],
    datastore: [],
    networks: [],
    osLayouts: [],
  };

  const updateCloudField = (field: keyof typeof currentCloud, value: any) => {
    const updatedClouds = [...cloudDetailsArray];
    updatedClouds[activeCloudIndex] = {
      ...updatedClouds[activeCloudIndex],
      [field]: value,
    };
    nblFormProps.setFieldValue('cloudDetails', updatedClouds);
  };

  useEffect(() => {
    setActiveTab(Tabs.clusters);
  }, [activeCloudIndex]);

  return (
    <NblAccordion
      border={`1px solid ${secondary.shade4}`}
      summary="Cloud Details"
      hasDivider
      bgColor={secondary.main}
      expandIconSize="large"
    >
      <NblFlexContainer direction="column" spacing={2}>
        <NblTabBar
          tabs={[
            { label: Tabs.clusters },
            { label: Tabs.hosts },
            { label: Tabs.datastores },
            { label: Tabs.networks },
            { label: Tabs.operatingSystem },
          ]}
          onTabChange={(tab) => setActiveTab(tab as Tabs)}
        />

        {activeTab === Tabs.clusters && (
          <Clusters clusters={currentCloud.clusters} hosts={currentCloud.hosts} onChange={(value) => updateCloudField('clusters', value)} />
        )}
        {activeTab === Tabs.hosts && <Hosts hosts={currentCloud.hosts} onChange={(value) => updateCloudField('hosts', value)} />}
        {activeTab === Tabs.datastores && (
          <DataStores datastores={currentCloud.datastore} onChange={(value) => updateCloudField('datastore', value)} />
        )}
        {activeTab === Tabs.networks && (
          <Networks networks={currentCloud.networks} onChange={(value) => updateCloudField('networks', value)} />
        )}
        {activeTab === Tabs.operatingSystem && (
          <OperatingSystems osLayouts={currentCloud.osLayouts} onChange={(value) => updateCloudField('osLayouts', value)} />
        )}
      </NblFlexContainer>
    </NblAccordion>
  );
};

export default CloudDetails;
