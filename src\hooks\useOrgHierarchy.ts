import { useApiService } from 'api/ApiService/context';
import { useEffect, useState } from 'react';

interface OrgProps {
  organizationId: string;
  verticalId: string;
  departmentId: string;
}

const useOrgHierarchy = ({ organizationId, verticalId, departmentId }: OrgProps) => {
  const { apiOnBoardingService } = useApiService();
  const [organization, setOrganization] = useState<string>('');
  const [vertical, setVertical] = useState<string>('');
  const [department, setDepartment] = useState<string>('');

  useEffect(() => {
    apiOnBoardingService.getAllOrg().then((res) => {
      if (res.status) {
        setOrganization(res.data.find((item) => item.value === organizationId)?.label ?? '');
      } else {
        setOrganization('');
      }
    });
  }, []);

  useEffect(() => {
    apiOnBoardingService.getVerticals(organizationId).then((res) => {
      if (res.status) {
        setVertical(res.data.find((item) => item.value === verticalId)?.label ?? '');
      } else {
        setVertical('');
      }
    });
  }, [organizationId]);

  useEffect(() => {
    apiOnBoardingService.getDepartments(verticalId).then((res) => {
      if (res.status) {
        setDepartment(res.data.find((val: { value: string; label: string }) => val.value === departmentId)?.label ?? '');
      } else {
        setDepartment('');
      }
    });
  }, [verticalId]);
  return { organization, vertical, department };
};

export default useOrgHierarchy;
