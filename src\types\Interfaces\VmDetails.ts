import VirtualMachineData from './VirtualMachineData';

type VmDetails = {
  domain: string;
  vmDetails?: VirtualMachineData | null;
  name: string;
  size: string;
  projectName: string;
  catalogStepsId: string;
  targetLayout: { name: string };
  network: { name: string; displayName?: string };
  groupId?: string;
  group: { id: number; name: string };
  datacenter: string;
  ipModes: string;
  cloudId: number;
  hostname: string;
  vmCount: number;
  description: string;
  resourceId?: string;
  resource: { id: number; name: string };
  ipv4: boolean;
  ipv6: boolean;
  dirrtAction: string;
  securityTools: string[];
  complianceTools: string[];
  observabilityTools: string[];
  inventoryTools: string[];
  projectId: string;
  catalogId: string;
  addDisks?: { diskName: string; diskValue: string }[];
  diskFileSystem?: string;
  ipv4Addresses: string[];
  ipv6Addresses: string[];
  templateConfig: { [key: string]: string | number };
  resourceTags: { tagKey: string; tagValue: string }[];
  rubrikSLA: string;
  sshpubkey: string;
  shortName: string;
  swap: number;
  appId: string;
  appInstance: { name: string; value: string };
  appRefId: string;
  deeplinkUrl?: string;
  sequenceNumber?: number;
  application: string;
  platformContext: { domainId: string; envId: string; domainName: string; environmentName: string; applicationName: string };
  networkDomain?: { dnsDomainName: string };
};

export default VmDetails;
