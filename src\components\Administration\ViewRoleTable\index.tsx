import React, { useState, useEffect } from 'react';
// eslint-disable-next-line no-unused-vars
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Box, useMediaQuery, useTheme } from '@mui/material';

// eslint-disable-next-line no-unused-vars
import { AdminComponent, ViewRolePayload, AdminGridProps } from 'types';

import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import withAdminPermissions from 'hoc/withAdminPermissions';
import AddRoleForm from '../AddRoleForm';
import DialogBox from 'components/DialogBox/Dialog';
import ActionsColumn from '../ActionsColumn';
import DataGridTable from 'components/DataGridTable';
import RoleService from 'api/ApiService/RoleService';
import { dateFormatter, getAdminColumnWidth } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'mantis/themes/type';

interface ViewServiceCatalogProps extends AdminGridProps {}

const ViewRoleTable: AdminComponent<ViewServiceCatalogProps> = ({ permissions }: ViewServiceCatalogProps) => {
  const roleService = new RoleService();
  const [viewRoleListData, setRoleData] = useState<ViewRolePayload[]>([]);
  const theme: NebulaTheme = useTheme();
  const isSmaller = useMediaQuery(theme.breakpoints.down('xl'));
  const [roleData, setEditRoleData] = useState({
    open: false,
    updateRole: { roleName: '', permissions: [{ permissionKey: '' }], roleType: '', description: '' },
  });

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchRoles = () => {
    setIsLoading(true);
    roleService
      .getRoles()
      .then((res) => {
        if (res.status) {
          setRoleData(
            res.data.map((viewData: any) => ({
              roleName: viewData?.roleName,
              shortName: viewData?.shortName,
              permissionName: viewData?.permissions.map((permission: any) => permission.permissionKey).join(','),
              id: viewData?._id,
              description: viewData?.description,
              createdBy: viewData?.createdBy,
              createdAt: viewData?.createdAt ? dateFormatter(viewData.createdAt) : '',
              updatedAt: viewData?.updatedAt ? dateFormatter(viewData.updatedAt) : '',
              updatedBy: viewData?.updatedBy,
            }))
          );
        } else {
          setRoleData([]);
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  useEffect(() => {
    fetchRoles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: GridColDef[] = [
    {
      field: 'roleName',
      headerName: 'Role Name',
      flex: 1,
    },
    {
      field: 'permissionName',
      headerName: 'Permission Name',
      flex: 1,
    },
    { field: 'description', headerName: 'Description', flex: 1 },
    { field: 'createdAt', headerName: 'Created Date', flex: 1 },
    { field: 'createdBy', headerName: 'Created By', flex: 1 },
    { field: 'updatedAt', headerName: 'Updated Date', flex: 1 },
    { field: 'updatedBy', headerName: 'Updated By', flex: 1 },
    {
      field: 'actions',
      headerName: 'Actions',
      filterable: false,
      sortable: false,
      width: getAdminColumnWidth(isSmaller),
      headerAlign: 'center',
      align: 'center',

      renderCell: (params: GridRenderCellParams) => {
        const {
          row: { shortName },
        } = params;
        return (
          <ActionsColumn
            permissions={permissions}
            disableDelete
            editUrl={`/administration/roles/${shortName}`}
            onDeleteHandler={() => {}}
          />
        );
      },
    },
  ];

  const handleCloseDialog = () => {
    setEditRoleData({
      ...roleData,
      open: false,
    });
  };

  return (
    <>
      <Box sx={{ mx: 'auto', mb: 3 }}>
        <DataGridTable loading={isLoading} rows={viewRoleListData} columns={columns} refreshHandler={fetchRoles} />
      </Box>

      {roleData.open && (
        <DialogBox fullWidth maxWidth={'lg'} open={roleData.open} onClose={handleCloseDialog}>
          <AddRoleForm title="Edit Role" permissions={permissions} onClose={handleCloseDialog} editRoleDetails={roleData.updateRole} />
        </DialogBox>
      )}
    </>
  );
};

ViewRoleTable.type = ADMIN_TILE_PERMISSION_TYPE.grid;

export default withAdminPermissions(ViewRoleTable);
