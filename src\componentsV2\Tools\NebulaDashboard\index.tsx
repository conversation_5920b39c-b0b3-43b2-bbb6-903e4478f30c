import { StyledUMCard } from './styled';
import './NebulaDashboard.css';
import RequestByMonthChart from './components/RequestByMonthChart/RequestByMonthChart';
import RequestWrapper from './components/RequestWrapper/RequestWrapper';
import RequestTrendByMonth from './components/RequestTrendByMonth/RequestTrendByMonthChart';
import RequestSummary from './components/RequestSummary/RequestSummary';
import UsageMetricsDashboardHeader from './components/FilterDialogBox';
import RequestOverview from './components/RequestsOverview';
// eslint-disable-next-line
import { State } from 'store/reducers/type';
import { Route, Routes } from 'react-router';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblTypography from 'sharedComponents/NblTypography';
import NblBox from 'sharedComponents/NblContainers/NblBox';

const NebulaDashboard = () => {
  return (
    <>
      <NblGridContainer columns={12} spacing={2}>
        {/* HEADER */}
        <NblGridItem colspan={12}>
          <UsageMetricsDashboardHeader />
        </NblGridItem>
        {/* REQUEST WRAPPER */}
        <NblGridItem colspan={12}>
          <RequestWrapper />
        </NblGridItem>
        {/* REQUEST SUMMARY */}
        <NblGridItem colspan={6}>
          <RequestSummary />
        </NblGridItem>
        {/* REQUEST CATALOG */}
        <NblGridItem colspan={1}>
          <StyledUMCard sx={{ maxHeight: '100vh', position: 'relative' }}>
            {/* <StyledUMCardTitle>Request By Catalog</StyledUMCardTitle> */}
            <NblTypography variant="subtitle1" weight={'bold'} color={'shade1'}>
              Request By Catalog
            </NblTypography>
            <NblBox margin="30px 0 0 0">
              <RequestTrendByMonth />
            </NblBox>
          </StyledUMCard>
        </NblGridItem>
        {/* REQUEST BY MONTH */}
        <NblGridItem colspan={5}>
          <StyledUMCard sx={{ maxHeight: '100vh', position: 'relative' }}>
            <NblTypography variant="subtitle1" weight={'bold'} color={'shade1'}>
              Request By Month
            </NblTypography>
            <NblBox margin="1rem 0 0 0">
              <RequestByMonthChart />
            </NblBox>
          </StyledUMCard>
        </NblGridItem>
      </NblGridContainer>
    </>
  );
};

const NebulaDashboardRoutes = () => {
  return (
    <Routes>
      <Route path={'*'} element={<NebulaDashboard />} />
      <Route path={'requestoverview'} element={<RequestOverview />} />
    </Routes>
  );
};

export default NebulaDashboardRoutes;
