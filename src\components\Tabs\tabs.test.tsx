import { act, render, fireEvent } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import Tabs from './index';

describe('Tabs component', () => {
  test('Should render all the tabs', async () => {
    const onChangeHandler = jest.fn();
    const TABS = [
      { label: 'Network', id: 'Network' },
      { label: 'Compute', id: 'Compute' },
      { label: 'Storage', id: 'Storage' },
      { label: 'Security', id: 'Security' },
    ];

    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <Tabs currentTab={0} tabsList={TABS} onTabChangeHandler={onChangeHandler} />
        </ThemeProvider>
      )
    );

    TABS.map((tab) => {
      expect(getByText(tab.label)).toBeInTheDocument();
    });

    fireEvent.click(getByText(TABS[1]['label']));
    expect(onChangeHandler).toHaveBeenCalled();
  });
});
