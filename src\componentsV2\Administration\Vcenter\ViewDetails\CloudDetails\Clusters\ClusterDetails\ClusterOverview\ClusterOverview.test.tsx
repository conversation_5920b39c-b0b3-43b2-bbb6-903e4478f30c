import React from 'react';
import { render, fireEvent, screen, waitFor } from '@testing-library/react';
import ClusterOverview from './index';
import { Cluster } from '../..';
import { Provider as ReduxProvider } from 'react-redux';
import { store } from 'store';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { MemoryRouter } from 'react-router-dom';

jest.mock('api/ApiService/context', () => ({
  useApiService: () => ({
    apiComputeService: {
      getMultiENVProjectDetails: jest.fn().mockResolvedValue({
        status: true,
        data: [
          { id: 'p1', name: 'Project One' },
          { id: 'p2', name: 'Project Two' },
        ],
      }),
    },
  }),
}));

jest.mock('componentsV2/Administration/ActionsColumn', () => ({
  __esModule: true,
  default: () => <div>Mocked ActionsColumn</div>,
}));

const mockCluster: Cluster = {
  name: 'TestCluster',
  clusterMor: 'mor-1234',
  disabled: false,
  restricted: false,
  haEnabled: true,
  drsEnabled: true,
  projects: ['p1'],
  hosts: [],
  hostCount: 0,
  clusterStatus: null,
  vmCount: 0,
};

describe('ClusterOverview', () => {
  const renderComponent = (overrides: Partial<Cluster> = {}, onClusterChange = jest.fn()) =>
    render(
      <MemoryRouter>
        <ReduxProvider store={store}>
          <NebulaThemeProvider>
            <ClusterOverview selectedCluster={{ ...mockCluster, ...overrides }} onClusterChange={onClusterChange} />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

  it('renders the cluster name', () => {
    renderComponent();
    expect(screen.getByText(/VMware: TestCluster/i)).toBeInTheDocument();
  });

  it('calls onClusterChange when "Disabled" checkbox is toggled', () => {
    const onClusterChange = jest.fn();
    renderComponent({ disabled: false }, onClusterChange);

    const disabledCheckbox = screen.getByRole('checkbox', { name: /Disabled/i });
    fireEvent.click(disabledCheckbox);

    expect(onClusterChange).toHaveBeenCalledWith(expect.objectContaining({ disabled: true }));
  });

  it('calls onClusterChange when "Restricted" checkbox is toggled on', async () => {
    const onClusterChange = jest.fn();
    renderComponent({ restricted: false }, onClusterChange);

    const restrictedCheckbox = screen.getByRole('checkbox', { name: /Restricted/i });
    fireEvent.click(restrictedCheckbox);

    await waitFor(() =>
      expect(onClusterChange).toHaveBeenCalledWith(
        expect.objectContaining({
          restricted: true,
          projects: expect.any(Array),
        })
      )
    );
  });

  it('calls onClusterChange when "Restricted" checkbox is toggled off', async () => {
    const onClusterChange = jest.fn();
    renderComponent({ restricted: true, projects: ['p1'] }, onClusterChange);

    const restrictedCheckbox = screen.getByRole('checkbox', { name: /Restricted/i });
    fireEvent.click(restrictedCheckbox);

    await waitFor(() =>
      expect(onClusterChange).toHaveBeenCalledWith(
        expect.objectContaining({
          restricted: false,
        })
      )
    );
  });
});
