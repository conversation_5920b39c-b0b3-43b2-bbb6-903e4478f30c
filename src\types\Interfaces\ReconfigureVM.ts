type Volume = {
  diskName: string;
  size: string;
  diskFileSystem: string;
  action: string;
};

type Network = {
  id: string;
  network: string,
  ipMode: string;
  subnetIpv4: string;
  subnetIpv6: string;
  ipv4: boolean;
  ipv6: boolean;
  ipv4Address: string;
  ipv6Address: string;
  action: string;
};

type ReconfigureVM = {
  resourceId: string;
  volumes: Volume[];
  memory: number;
  coreCount: number;
  coresPerSocket: number;
  networks: Network[];
  domain: string;
  datacenter: string;
  requestorPID: string;
  requestorEmail: string;
};

export default ReconfigureVM;