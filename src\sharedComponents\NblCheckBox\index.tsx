import React, { useEffect } from 'react';
import NblFieldWrapper from '../NblFormInputs/NblFieldWrapper';
import { StyledFormControlLabel, StyledCheckbox } from './styled';

interface NblCheckboxProps {
  disabled?: boolean;
  checked: boolean;
  type?: 'indeterminate' | 'regular';
  label: string;
  name: string;
  labelPlacement?: 'start' | 'end';
  onChange: (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => void;
  onBlur: (event: React.FocusEvent<HTMLButtonElement>) => void;
  error?: boolean;
  helperText?: string;
  mandatory?: boolean;
  onMouseEnter?: (event: React.MouseEvent<HTMLLabelElement>) => void;
  onMouseLeave?: (event: React.MouseEvent<HTMLLabelElement>) => void;
  showDefaultChecked?: (name: string) => void;
}

const NblCheckBox: React.FunctionComponent<NblCheckboxProps> = ({
  disabled,
  label,
  onChange,
  onBlur,
  error,
  helperText = '',
  mandatory = false,
  onMouseEnter,
  onMouseLeave,
  labelPlacement = 'end',
  checked = false,
  type = 'regular',
  name,
  showDefaultChecked,
}) => {
  useEffect(() => {
    if (!disabled && !checked && showDefaultChecked) {
      showDefaultChecked(name);
    }
  }, []);
  // JSX
  return (
    <NblFieldWrapper label={''} name={name} mandatory={mandatory} disabled={Boolean(disabled)} error={Boolean(error)} helperText={helperText}>
      <StyledFormControlLabel
        disabled={disabled}
        control={
          <StyledCheckbox
            checked={checked}
            onChange={onChange}
            id={name}
            name={name}
            onBlur={onBlur}
            indeterminate={type === 'indeterminate'}
            color={error ? 'error' : 'primary'}
            disableRipple
          />
        }
        color={error ? 'error' : 'primary'}
        label={label}
        labelPlacement={labelPlacement}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      />
    </NblFieldWrapper>
  );
};

export default NblCheckBox;
