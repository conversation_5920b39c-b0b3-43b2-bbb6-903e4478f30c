import React from 'react';
import StyledDivider from './styled';

interface NblDividerProps {
  children?: React.ReactNode;
  length: string;
  strokeWidth?: number;
  orientation?: 'vertical' | 'horizontal';
  positionAbsolute?: boolean;
  borderRadius?: number;
  mt?: string | number;
  mb?: string | number;
  color?: string;
  opacity?: number;
  visibility?: 'visible' | 'hidden';
}

const NblDivider: React.FC<NblDividerProps> = ({
  children,
  length,
  strokeWidth = 1,
  orientation = 'horizontal',
  positionAbsolute = false,
  borderRadius = 0,
  mt = '5x',
  mb = '5px',
  color,
  opacity = 0.2,
  visibility = 'visible',
}) => {
  //JSX
  return (
    <StyledDivider
      radius={borderRadius}
      strokeWidth={strokeWidth}
      mt={mt}
      mb={mb}
      length={length}
      color={color}
      orientation={orientation}
      absolute={positionAbsolute}
      opacity={opacity}
      style={{ visibility }}
    >
      {children}
    </StyledDivider>
  );
};

export default NblDivider;
