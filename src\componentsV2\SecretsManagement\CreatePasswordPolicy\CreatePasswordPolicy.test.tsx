import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useApiService } from 'api/ApiService/context';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import CreatePasswordPolicy from './index';
import { toast } from 'react-toastify';
import { useDispatch } from 'react-redux';
import { MemoryRouter } from 'react-router';

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));

jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    POSITION: {
      BOTTOM_CENTER: 'bottom-center',
    },
  },
}));

jest.mock('../CreateNewSecret/CreateNewSecretForm/SecretCommonFields', () => () => [
  { title: 'Namespace Name', value: 'test-demo' },
  { title: 'Namespace Path', value: 'nebula-stamp/test-demo' },
]);

jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  __esModule: true,
  default: ({ children, onSubmit }: { children: React.ReactNode; onSubmit: (values: any, nblFormHelpers: any) => void }) => {
    const mockValues = {
      policyName: 'Test Policy',
      totalchars: 12,
      smallAlphabets: 4,
      bigAlphabets: 4,
      specialCharacters: '@#',
      noOfSpecialCharacters: 2,
      numbers: 2,
      description: 'Test description',
    };

    const mockHelpers = {
      setSubmitting: jest.fn(),
      setFieldValue: jest.fn(),
    };

    return (
      <div>
        <div>Mocked NblFormContainer</div>
        <button type="button" onClick={() => onSubmit(mockValues, mockHelpers)}>
          Submit
        </button>
        {children}
      </div>
    );
  },
}));

jest.mock('./CreatePasswordPolicyForm', () => () => {
  return <div>Mocked CreatePasswordPolicyForm</div>;
});

describe('CreatePasswordPolicy', () => {
  const mockDispatch = jest.fn();
  const mockCreatePasswordPolicy = jest.fn();

  beforeEach(() => {
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    (useApiService as jest.Mock).mockReturnValue({
      apiSecretsManagement: {
        createPasswordPolicy: mockCreatePasswordPolicy,
      },
    });
  });

  test('renders the form container and form', () => {
    render(
      <MemoryRouter>
        <NebulaThemeProvider>
          <CreatePasswordPolicy />
        </NebulaThemeProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('Mocked NblFormContainer')).toBeInTheDocument();
    expect(screen.getByText('Mocked CreatePasswordPolicyForm')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
  });

  test('submits the form and handles API response with success', async () => {
    mockCreatePasswordPolicy.mockResolvedValueOnce({
      status: true,
      data: { message: 'Created!' },
    });

    render(
      <MemoryRouter>
        <NebulaThemeProvider>
          <CreatePasswordPolicy />
        </NebulaThemeProvider>
      </MemoryRouter>
    );

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith({
        payload: {
          id: 'create-password-policy',
          message: 'Creating Secret....',
          status: true,
        },
        type: 'spinner/showSpinner',
      });

      expect(mockDispatch).toHaveBeenCalledWith({
        payload: {
          id: 'create-password-policy',
          message: '',
          status: false,
        },
        type: 'spinner/showSpinner',
      });
    });
  });

  test('handles API response with no success status', async () => {
    mockCreatePasswordPolicy.mockResolvedValueOnce({
      status: false,
    });

    render(
      <MemoryRouter>
        <NebulaThemeProvider>
          <CreatePasswordPolicy />
        </NebulaThemeProvider>
      </MemoryRouter>
    );

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(toast.success).not.toHaveBeenCalled();
    });
  });
});
