import React from 'react';
import Nbl<PERSON>ieldWrapper from '../NblFieldWrapper';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblRadioButton from 'sharedComponents/NblFormInputs/NblRadioButton';

interface RadioOption {
  name: string;
  label: string;
  value: string;
  checked: boolean;
  disabled?: boolean;
}

interface NblRadioGroupProps {
  label: string;
  name: string;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>, index: number, value: string) => void;
  onMouseEnter?: (event: React.MouseEvent<HTMLLabelElement>, index: number) => void;
  onMouseLeave?: (event: React.MouseEvent<HTMLLabelElement>, index: number) => void;
  onBlur?: (event: React.FocusEvent<HTMLButtonElement>, index: number) => void;
  options: RadioOption[];
  mandatory?: boolean;
}

const NblRadioGroup: React.FC<NblRadioGroupProps> = ({
  label,
  name,
  error,
  disabled,
  onChange,
  onMouseEnter,
  onMouseLeave,
  onBlur,
  options,
  helperText = '',
  mandatory = false,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    onChange?.(event, index, event.target.value);
  };

  const handleBlur = (event: React.FocusEvent<HTMLButtonElement>, index: number) => {
    onBlur?.(event, index);
  };

  const handleMouseEnter = (event: React.MouseEvent<HTMLLabelElement>, index: number) => {
    onMouseEnter?.(event, index);
  };

  const handleMouseLeave = (event: React.MouseEvent<HTMLLabelElement>, index: number) => {
    onMouseLeave?.(event, index);
  };

  return (
    <NblFieldWrapper
      label={label}
      name={name}
      mandatory={mandatory}
      disabled={Boolean(disabled)}
      error={Boolean(error)}
      helperText={helperText}
    >
      <NblFlexContainer alignItems="center">
        {options.map((option, index) => (
          <NblRadioButton
            key={option.value}
            name={name}
            value={option.value}
            checked={option.checked}
            label={option.label}
            disabled={disabled || option.disabled}
            error={Boolean(error)}
            onChange={(e) => handleChange(e, index)}
            onBlur={(e) => handleBlur(e, index)}
            onMouseEnter={(e) => handleMouseEnter(e, index)}
            onMouseLeave={(e) => handleMouseLeave(e, index)}
          />
        ))}
      </NblFlexContainer>
    </NblFieldWrapper>
  );
};

export default NblRadioGroup;
