import { requestStatusType } from '../../../../api/ApiService/type';

export interface MonthlyCount {
  month: number;
  count: number;
}

export interface CatalogWithSummary {
  id: string;
  name: string;
  value: string;
  parentId: string;
  requestType?: string;
  summary?: MonthlyCount[];
  requestListByRequestType?: (requestStatusType | undefined)[];
}

export interface TableData {
  catalogName: string;
  allRequestMonthlyData: {
    [month: number]: number;
  };
  id: string;
  allRequests: {
    status: string;
    serviceRequestId: string;
    date: any;
  }[];
}
