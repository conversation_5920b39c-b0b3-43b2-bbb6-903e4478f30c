import React, { useState } from 'react';
// eslint-disable-next-line no-unused-vars
import NblFormContainer, { NblFormHelpers } from 'sharedComponents/NblContainers/NblFormContainer';
import CreatePasswordPolicyForm from './CreatePasswordPolicyForm';
import { CreatePasswordPolicySchema } from 'yupSchema/CreatePasswordPolicySchema';
import { useApiService } from 'api/ApiService/context';
import { toast } from 'react-toastify';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { useDispatch } from 'react-redux';
import useNblNavigate from 'hooks/useNblNavigate';

export type FormValues = {
  policyname: string;
  totalchars: number;
  smallAlphabets: number;
  bigAlphabets: number;
  splChars: string;
  noOfSplChars: number;
  numbers: number;
  description: string;
  namespace: string;
  namespacePath: string;
};

export type CreatePasswordPolicyPayload = {
  policyname: string;
  totalchars: number;
  smallAlphabets: number;
  bigAlphabets: number;
  splChars: string;
  noOfSplChars: number;
  numbers: number;
  description: string;
  namespace: string;
};

export const initialValues: FormValues = {
  policyname: '',
  totalchars: 0,
  smallAlphabets: 0,
  bigAlphabets: 0,
  splChars: '',
  noOfSplChars: 0,
  numbers: 0,
  description: '',
  namespace: '',
  namespacePath: '',
};

const CreatePasswordPolicy: React.FunctionComponent = () => {
  //Hooks
  const { apiSecretsManagement } = useApiService();
  const dispatch = useDispatch();
  const navigate = useNblNavigate();

  //States
  const [formInitialValues, setFormInitialValues] = useState(initialValues);

  //Handlers
  const handleSubmitForm = (values: FormValues, nblFormHelpers: NblFormHelpers<FormValues>) => {
    dispatch(showSpinner({ message: 'Creating Secret....', id: SPINNER_IDS.passwordPolicy, status: true }));
    nblFormHelpers.setSubmitting(true);

    const requestBody = {
      policyname: values.policyname,
      totalchars: values.totalchars,
      smallAlphabets: values.smallAlphabets,
      bigAlphabets: values.bigAlphabets,
      splChars: values.splChars,
      noOfSplChars: values.noOfSplChars,
      numbers: values.numbers,
      description: values.description,
      namespace: values.namespacePath,
    };

    apiSecretsManagement
      .createPasswordPolicy(requestBody)
      .then((res) => {
        if (res.status) {
          navigate(-1);
          toast.success(res.data.message || `Password Policy is created successfully`, {
            position: toast.POSITION.BOTTOM_CENTER,
          });
        }
      })
      .finally(() => {
        dispatch(showSpinner({ message: '', id: SPINNER_IDS.passwordPolicy, status: false }));
        nblFormHelpers.setSubmitting(false);
      });
  };

  //JSx
  return (
    <>
      <NblFormContainer
        title={'Create Password Policy'}
        caption={'Fill the necessary details to Create Password Policy'}
        formInitialValues={formInitialValues}
        formValidationSchema={CreatePasswordPolicySchema}
        steps={[
          {
            caption: '',
            icon: '',
            status: 'current',
            title: '',
            errorFields: [],
          },
        ]}
        formType="simple"
        onSubmit={handleSubmitForm}
      >
        <CreatePasswordPolicyForm setFormInitialValues={setFormInitialValues} />
      </NblFormContainer>
    </>
  );
};

export default CreatePasswordPolicy;
