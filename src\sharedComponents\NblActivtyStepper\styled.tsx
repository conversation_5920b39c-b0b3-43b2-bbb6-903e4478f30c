import { Typography } from '@mui/material';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';
import { textOverflowStyles } from 'utils/common';

export const StyledTypography = styled(Typography)<{ theme?: NebulaTheme; color: string }>(({ theme, color }) => {
  const { typography } = theme;
  return {
    '&.MuiTypography-body3': {
      ...typography.body3,
      fontWeight: theme.typography.medium.fontWeight,
      textTransform: 'capitalize',
      color,
    },
    '&.MuiTypography-subtitle2': {
      ...typography.body2,
      fontWeight: theme.typography.bold.fontWeight,
      textAlign: 'center',
      textTransform: 'capitalize',
      color,
      ...textOverflowStyles('100%', 1),
    },
  };
});
