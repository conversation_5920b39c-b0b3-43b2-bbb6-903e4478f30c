import { IconButton } from '@mui/material';
import { CapacityPlanningCloseIcon } from 'assets/images/icons/custom-icons';
import { StyledFilterDialog } from 'componentsV2/Tools/NebulaDashboard/styled';
import { ReactElement } from 'react';

interface DialogBoxProps {
  onClose: () => void;
  open: boolean;
  children: ReactElement;
  placement?: 'right' | 'left';
}
const DialogBox: React.FunctionComponent<DialogBoxProps> = ({ onClose, open, children, placement = 'right' }: DialogBoxProps) => {
  const placementStyles = placement === 'right' ? { right: -30 } : { left: -23 };
  const renderCloseIcon = () => {
    return (
      <IconButton
        aria-label="close"
        onClick={onClose}
        sx={{
          position: 'absolute',
          left: -10,
          top: 14,
          zIndex: 10,
          height: '49px',
          width: '38px',
        }}
      >
        <CapacityPlanningCloseIcon />
      </IconButton>
    );
  };

  return (
    <StyledFilterDialog
      PaperProps={{
        sx: {
          position: 'absolute',
          display: 'flex',
          top: '40px',
          bottom: 0,
          overflow: 'visible',
          maxHeight: '100vh',
          minWidth: '26vw',
          paddingInline: 5,
          paddingBlock: 2,
          overflowX: 'hidden',
          ...placementStyles,
        },
      }}
      open={open}
      onClose={onClose}
    >
      {renderCloseIcon()}
      {children}
    </StyledFilterDialog>
  );
};
export default DialogBox;
