// eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NblRadioButton from 'sharedComponents/NblFormInputs/NblRadioButton';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';

type StoryProps = ComponentProps<typeof NblRadioButton>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblRadioButton',
  component: NblRadioButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

const Template = (args: StoryProps) => {
  const [selectedValue, setSelectedValue] = useState(args.checked ? args.value : '');

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSelectedValue(value);
    args.onChange?.(event, true);
  };

  return <NblRadioButton {...args} checked={selectedValue === args.value} onChange={handleChange} />;
};

export const NblRadio: Story = {
  args: {
    label: 'Nbl Radio Button',
    disabled: false,
    checked: true,
    value: 'option1',
    name: 'radioGroup1',
    error: false,
    onBlur: () => {},
  },
  render: (args) => (
    <NebulaTheme>
      <Template {...args} />
    </NebulaTheme>
  ),
};
