import { render, act, fireEvent } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import { MemoryRouter as Router, Routes, Route } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import AddTag from './index';
import { ServiceCatalogMetaData } from 'mock/ServiceCatalogMetaData';
import AdministrationService from 'api/ApiService/AdministrationService';
import { GetTagKeysData } from 'mock/GetTagKeysData';
import { GetTagValuesData } from 'mock/GetTagValuesData';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'tags', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/tags/add-tag'];

describe('AddTag component', () => {
  let getLevel4ItemsSpy: jest.SpyInstance;
  let getTagKeysSpy: jest.SpyInstance;
  let getTagValuesSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);

    getLevel4ItemsSpy = jest.spyOn(AdministrationService.prototype, 'getLevel4Items');
    getLevel4ItemsSpy.mockResolvedValue(ServiceCatalogMetaData.data);

    getTagKeysSpy = jest.spyOn(AdministrationService.prototype, 'getTagKeys');
    getTagKeysSpy.mockResolvedValue(GetTagKeysData.data.tagKeys);

    getTagValuesSpy = jest.spyOn(AdministrationService.prototype, 'getTagValues');
    getTagValuesSpy.mockResolvedValue(GetTagValuesData.data.tagValues);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Should render the Add Tag form', async () => {
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AddTag />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    expect(getByText('Service Catalog Item *')).toBeInTheDocument();
    expect(getByText('Description')).toBeInTheDocument();
  });

  test('Should navigate to adminstration tags page when user clicks on  Cancel button', async () => {
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <Routes>
                <Route path="/administration/tags/add-tag" element={<AddTag />}></Route>
                <Route path="/administration/tags" element={<div>Adminstration Tags</div>} />
              </Routes>
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    fireEvent.click(getByText('Cancel'));
    expect(getByText('Adminstration Tags')).toBeInTheDocument();
  });
});
