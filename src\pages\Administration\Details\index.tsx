import React, { useEffect, useState } from 'react';
import { getAllAdministrationData } from 'api/static-data';

import CatalogTiles from 'components/CatalogTiles';
import { CatalogTilesData } from 'types';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';

const AdministrationDetails: React.FunctionComponent = () => {
  const [catalogTilesItems, setCatalogGroupItems] = useState<{ content: CatalogTilesData[] }>({ content: [] });

  useEffect(() => {
    const get_AdministrationData = async () => {
      try {
        let data = await getAllAdministrationData();
        setCatalogGroupItems(data);
      } catch (error) {
        console.log(error);
      }
    };
    get_AdministrationData();
  }, []);

  return (
    <NblBorderContainer padding="20px" backgroundColor="#FFFFFF">
      <CatalogTiles name="adminstration" items={catalogTilesItems.content} />
    </NblBorderContainer>
  );
};

export default AdministrationDetails;
