import { Chip } from '@mui/material';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

interface StyledNblChipProps {
  theme?: NebulaTheme;
  radius: 'sm' | 'lg';
  extracolor?: {
    color: string;
    backgroundColor: string;
  };
  borderColor?: `#${string}` | string;
}

const StyledNblChip = styled(Chip, {
  shouldForwardProp: (prop) => prop !== 'borderColor' && prop !== 'extracolor' && prop !== 'radius',
})<StyledNblChipProps>(({ theme, radius, extracolor, borderColor }) => ({
  '&.MuiChip-root': {
    margin: '0 8px 0px 0px',
    borderRadius: radius === 'sm' ? '6px' : 'auto',
    ...(extracolor && { color: `${extracolor.color} !important` }),
    ...(extracolor && { backgroundColor: `${extracolor.backgroundColor} !important` }),
    ...(borderColor && { border: `1px solid ${borderColor}` }),

    ...theme.typography.subtitle2,
    fontWeight: theme.typography.bold.fontWeight,
    textTransform: 'capitalize',
    '&.Mui-disabled': {
      background: `${theme.palette.chip.disabled.bgColor} 0% 0% no-repeat padding-box`,
      color: `${theme.palette.chip.disabled.textColor}`,
      opacity: `1 !important`,
      pointerEvents: 'auto',
      cursor: 'not-allowed',
    },
  },
  '&.MuiChip-filledPrimary': {
    color: theme.palette.chip.primary.textColor,
    backgroundColor: theme.palette.chip.primary.bgColor,
  },
  '&.MuiChip-filledInfo': {
    color: theme.palette.chip.info.textColor,
    backgroundColor: theme.palette.chip.info.bgColor,
  },
  '&.MuiChip-filledSuccess': {
    color: theme.palette.chip.success.textColor,
    backgroundColor: theme.palette.chip.success.bgColor,
  },
  '&.MuiChip-filledWarning': {
    color: theme.palette.chip.warning.textColor,
    backgroundColor: theme.palette.chip.warning.bgColor,
  },
  '&.MuiChip-filledError': {
    color: theme.palette.chip.error.textColor,
    backgroundColor: theme.palette.chip.error.bgColor,
  },
  '&.MuiChip-filledSecondary': {
    color: theme.palette.chip.secondary.textColor,
    backgroundColor: theme.palette.chip.secondary.bgColor,
  },
  '&.MuiChip-filledDefault': {
    color: theme.palette.chip.default.textColor,
    backgroundColor: theme.palette.chip.default.bgColor,
  },
}));

export default StyledNblChip;
