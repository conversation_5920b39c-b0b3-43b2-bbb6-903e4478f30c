import { createSlice } from '@reduxjs/toolkit';
import { CalculatedVropsResource } from 'componentsV2/Tools/CapacityPlanningDashboard/utils/types';

export interface CapacityPlanning {
  hours: number;
  clickedResourceId: string | null;
  overviewResource: CalculatedVropsResource;
  sunburstLevel: number;
  selectedDomainIds: number[];
}
// initial state
const initialState: CapacityPlanning = {
  hours: 1,
  clickedResourceId: null,
  overviewResource: {
    resourceid: '',
    resourcename: '',
    resourcelabel: '',
    cpu: '',
    memory: '',
    storage: '',
    cpuUtilized: 0,
    memoryUtilized: 0,
    storageUtilized: 0,
    cpuAllocated: 0,
    memoryAllocated: 0,
    storageAllocated: 0,
    status: '',
    domainid: 1,
    domainname: 'CUSTOMERNET',
  },
  sunburstLevel: 1,
  selectedDomainIds: [1, 2],
};

// ==============================|| SLICE - CapacityPlanning ||============================== //

const capacityPlanning = createSlice({
  name: 'CapacityPlanning',
  initialState,
  reducers: {
    setSelectedHour(state, action) {
      state.hours = action.payload;
    },
    setClickedResourceId(state, action) {
      state.clickedResourceId = action.payload;
    },
    setOverviewResource(state, action) {
      state.overviewResource = action.payload;
    },
    setSunburstLevel(state, action) {
      state.sunburstLevel = action.payload;
    },
    setSelectedDomainIds(state, action) {
      state.selectedDomainIds = action.payload;
    },
  },
});

export default capacityPlanning.reducer;

export const { setSelectedHour, setClickedResourceId, setOverviewResource, setSunburstLevel, setSelectedDomainIds } =
  capacityPlanning.actions;
