import React from 'react';
import { render, screen } from '@testing-library/react';
import NormalSecretFields from '.';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

jest.mock('sharedComponents/NblContainers/NblFormContainer');

jest.mock('../SecretValueField', () => () => {
  return <input data-testid="secret-value-field" />
});

describe('NormalSecretFields Component', () => {
  const mockNblFormValues = {
    vaultKey: '',
    vaultPassword: '',
  };

  const mockNblFormProps = {
    values: mockNblFormValues,
    touched: {},
    errors: {},
    handleChange: jest.fn(),
    handleBlur: jest.fn(),
    handleSubmit: jest.fn(),
    resetForm: jest.fn(),
    setFieldValue: jest.fn(),
  };

  beforeEach(() => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormValues: mockNblFormValues,
      nblFormProps: mockNblFormProps,
    });
    mockNblFormProps.handleChange.mockClear();
  });

  it('renders the key and value text fields', () => {
    render(
      <NebulaThemeProvider>
        <NormalSecretFields policyDescription={''} />
      </NebulaThemeProvider>
    );

    expect(screen.getByText('Key')).toBeInTheDocument();
    expect(screen.getByTestId('secret-value-field')).toBeInTheDocument();
  });
});
