import { render, screen } from '@testing-library/react';
import CreateNewSecret from '.';
import { useApiService } from 'api/ApiService/context';
import { MemoryRouter } from 'react-router-dom';
import ReduxProvider from 'mock/ReduxProvider';

// Mocks

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

jest.mock('./CreateNewSecretForm', () => () => <div>CreateNewSecretForm Rendered</div>);
jest.mock('./EditSecretDetails', () => () => <div>Edit Secret Details Rendered</div>);

describe('CreateNewSecret Component', () => {
  beforeEach(() => {
    (useApiService as jest.Mock).mockReturnValue({
      apiAssetService: {
        getMyResourcesv2: jest.fn().mockResolvedValue({ status: true, data: { items: [] } }),
      },
    });
  });

  it('renders correctly in non-edit mode and shows the form', () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <CreateNewSecret editMode={false} />
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('CreateNewSecretForm Rendered')).toBeInTheDocument();
  });

  it('renders correctly in edit mode and shows the form', () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <CreateNewSecret editMode={true} />
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('Edit Secret Details Rendered')).toBeInTheDocument();
  });
});
