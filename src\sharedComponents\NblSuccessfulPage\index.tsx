import React, { useState } from 'react';
import { IconButton } from '@mui/material';

import NblButton from 'sharedComponents/Buttons/NblButton';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import copy from '../../assets/images/icons/copy.svg';
import success from '../../assets/images/icons/success.svg';
import NblTooltip from 'sharedComponents/NblTooltip';
import useNblNavigate from 'hooks/useNblNavigate';

export interface SuccessPageProps {
  title: string;
  requestId: string;
  buttonTitle: string;
  content?: string;
  showTrackbtn?: boolean;
  renderElement?: React.ReactElement;
}
const NblSuccessfulPage: React.FunctionComponent<SuccessPageProps> = ({
  title,
  requestId,
  buttonTitle,
  content = 'Request Submitted Successfully',
  showTrackbtn = true,
  renderElement,
}) => {
  const [tooltipOpen, setTooltipOpen] = useState(false);
  const navigate = useNblNavigate();

  const handleCopy = () => {
    navigator.clipboard.writeText(requestId);
    setTooltipOpen(true);
    setTimeout(() => setTooltipOpen(false), 2000); // Hide tooltip after 2 seconds
  };

  return (
    <NblFlexContainer direction="column" center>
      <img src={success} alt={success} width={'90px'} height={'86px'} />
      <NblTypography variant="h1" weight={'regular'} color={'shade1'} margin={'0 0 24px'}>
        {title}
      </NblTypography>
      <NblTypography variant="h1" weight={'bold'} color={'shade1'} margin={'0 0 24px'}>
        {content}
      </NblTypography>
      {requestId && (
        <>
          <NblTypography variant="h4" weight={'regular'} color={'shade1'} margin={'0 0 24px'}>
            Req ID : {requestId}{' '}
            <NblTooltip tooltipMessage="Copied!" open={tooltipOpen}>
              <IconButton onClick={handleCopy}>
                <img src={copy} alt={requestId} width={'20px'} height={'20px'} />
              </IconButton>
            </NblTooltip>
          </NblTypography>
          {showTrackbtn && (
            <NblButton
              buttonID={`success-${buttonTitle}-btn`}
              color="primary"
              variant="outlined"
              onClick={() => {
                navigate(`/requests/${requestId}`);
              }}
            >
              {buttonTitle}
            </NblButton>
          )}
        </>
      )}
      {renderElement}
    </NblFlexContainer>
  );
};

export default NblSuccessfulPage;
