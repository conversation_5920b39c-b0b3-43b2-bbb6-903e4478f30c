import React from 'react';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblAutoComplete from 'sharedComponents/NblFormInputs/NblAutoComplete';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
// eslint-disable-next-line no-unused-vars
import { ColumnData, NblTable } from 'sharedComponents/NblTable';
import { Network } from 'types/Interfaces/LookUpVCenterResponse';

interface NetworksProps {
  networks: Network[];
  onChange: (updatedNetworks: Network[]) => void;
}

const Networks: React.FC<NetworksProps> = ({ networks, onChange }) => {
  const handleFieldChange = (networkMor: string, fieldName: keyof Network) => (value: any) => {
    const updated = networks.map((n) => (n.networkMor === networkMor ? { ...n, [fieldName]: value } : n));
    onChange(updated);
  };

  const columns: ColumnData[] = [
    { field: 'networkMor', headerName: 'Network ID', flex: 1, minWidth: 200 },
    { field: 'name', headerName: 'Network Name', flex: 1, minWidth: 250 },
    { field: 'type', headerName: 'Type', flex: 1, minWidth: 250 },
    {
      field: 'ipv4Enabled',
      headerName: 'IPV4',
      flex: 1,
      minWidth: 100,
      renderCell: (params) => {
        const { ipv4Subnet, ipv4Enabled, networkMor } = params.row;
        const isEnabled = !!ipv4Subnet;

        return (
          <NblFlexContainer center>
            <NblCheckBox
              checked={isEnabled ? true : !!ipv4Enabled}
              disabled={isEnabled}
              label=""
              name=""
              onChange={(e) => handleFieldChange(networkMor, 'ipv4Enabled')(e.target.checked)}
              onBlur={() => {}}
            />
          </NblFlexContainer>
        );
      },
    },
    {
      field: 'ipv6Enabled',
      headerName: 'IPV6',
      flex: 1,
      minWidth: 100,
      renderCell: (params) => {
        const { ipv6Subnet, ipv6Enabled, networkMor } = params.row;
        const isEnabled = !!ipv6Subnet;

        return (
          <NblFlexContainer center>
            <NblCheckBox
              checked={isEnabled ? true : !!ipv6Enabled}
              disabled={isEnabled}
              label=""
              name=""
              onChange={(e) => handleFieldChange(networkMor, 'ipv6Enabled')(e.target.checked)}
              onBlur={() => {}}
            />
          </NblFlexContainer>
        );
      },
    },
    {
      field: 'dhcp',
      headerName: 'DHCP',
      flex: 1,
      minWidth: 100,
      renderCell: (params) => (
        <NblFlexContainer center>
          <NblCheckBox
            checked={!!params.row.dhcp}
            label=""
            name=""
            onChange={(e) => handleFieldChange(params.row.networkMor, 'dhcp')(e.target.checked)}
            onBlur={() => {}}
          />
        </NblFlexContainer>
      ),
    },
    {
      field: 'ipv4Subnet',
      headerName: 'IPv4 CIDR',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => {
        const { ipv4Enabled, ipv4Subnet, networkMor } = params.row;
        const isEditable = ipv4Enabled || !!ipv4Subnet;

        return (
          <NblTextField
            type="text"
            name="ipv4Subnet"
            value={ipv4Subnet || ''}
            handleChange={(e) => handleFieldChange(networkMor, 'ipv4Subnet')(e.target.value)}
            handleBlur={() => {}}
            placeholder="Enter IPv4 CIDR"
            disabled={!isEditable}
          />
        );
      },
    },
    {
      field: 'ipv6Subnet',
      headerName: 'IPv6 CIDR',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => {
        const { ipv6Enabled, ipv6Subnet, networkMor } = params.row;
        const isEditable = ipv6Enabled || !!ipv6Subnet;

        return (
          <NblTextField
            type="text"
            name="ipv6Subnet"
            value={ipv6Subnet || ''}
            handleChange={(e) => handleFieldChange(networkMor, 'ipv6Subnet')(e.target.value)}
            handleBlur={() => {}}
            placeholder="Enter IPv6 CIDR"
            disabled={!isEditable}
          />
        );
      },
    },
    { field: 'ipv4Gateway', headerName: 'IPv4 Gateway', flex: 1, minWidth: 200 },
    { field: 'ipv6Gateway', headerName: 'IPv6 Gateway', flex: 1, minWidth: 200 },
    {
      field: 'dnsDomain',
      headerName: 'DNS Domain',
      flex: 1,
      minWidth: 250,
      renderCell: (params) => {
        const value = params.row.dnsDomain ?? '';
        const options = [{ label: value, value }];
        return (
          <NblFlexContainer center>
            <NblAutoComplete
              value={value}
              options={options}
              placeholder="Select"
              label=""
              name=""
              onChange={(selected) => handleFieldChange(params.row.networkMor, 'dnsDomain')(selected?.value)}
              handleBlur={() => {}}
            />
          </NblFlexContainer>
        );
      },
    },
    {
      field: 'disabled',
      headerName: 'Disable',
      flex: 1,
      minWidth: 100,
      renderCell: (params) => (
        <NblFlexContainer center>
          <NblCheckBox
            checked={!!params.row.disabled}
            label=""
            name=""
            onChange={(e) => handleFieldChange(params.row.networkMor, 'disabled')(e.target.checked)}
            onBlur={() => {}}
          />
        </NblFlexContainer>
      ),
    },
  ];

  const rows = networks.map((network) => ({
    ...network,
    id: network.networkMor,
  }));

  return (
    <NblFlexContainer>
      <NblTable columns={columns} rows={rows} />
    </NblFlexContainer>
  );
};

export default Networks;
