//eslint-disable-next-line no-unused-vars
import { GridRowId, GridRowSelectionModel } from '@mui/x-data-grid';
import { useState } from 'react';

const useDataGridUtils = () => {
  const [isEditMode, setEditMode] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([]);
  const canEditRow = (selectedRows: GridRowSelectionModel, rowIndex: GridRowId, isEditMode: boolean): boolean => {
    if ((selectedRows.length > 0 && selectedRows.includes(rowIndex) && isEditMode) || (selectedRows.length === 0 && isEditMode)) {
      return true;
    }
    return false;
  };
  return {
    isEditMode,
    setEditMode,
    selectedRows,
    setSelectedRows,
    canEditRow,
  };
};

export default useDataGridUtils;
