import { Address4, Address6 } from 'ip-address';
import { IPv4CidrRange, IPv6CidrRange } from 'ip-num/IPRange';
import * as yup from 'yup';
import { isVmTypeWindows, yupMatchesParams } from 'utils/common';
import { IP_MODES } from 'componentsV2/IaaS/CreateVirtualMachine/CreateVirtualMachineForm';

const CreateVMSchema = (
  vmType: string,
  cidrValue: { cidr: string; cidrIPv6: string },
  secondaryCidrValue: { secondaryCidr: string; secondaryCidrIPv6: string },
  projectTags: { [key: string]: string },
  validateGroupId?: boolean,
  validateResourceId?: boolean,
  validateIPv6?: boolean,
  validateDomainName?: boolean,
  dnsDomain?: any
) =>
  yup.object().shape({
    projectName: yup.string().required('Project is required'),
    application: yup.string().required('Application is required'),
    environment: yup
      .string()
      .required('Environment is required')
      .test('check-tags', 'Some tags are missing appid field', async (value, context) => {
        if (!value) return true; // Skip validation if projectName is not selected

        // Fetch tags for the selected project
        if (Object.keys(projectTags)?.length) {
          const isValidAppId = projectTags['appid'];
          const isValidOpsOwner = projectTags['opsowner'];
          const isValidAppRefId = projectTags['apprefid'];
          if (!isValidAppId && !isValidOpsOwner && !isValidAppRefId) {
            return context.createError({
              message:
                'App ID, AppRefID and OpsOwner tag are missing for the selected application / environment. Please choose different application / environment.',
            });
          } else if (!isValidOpsOwner && !isValidAppRefId) {
            return context.createError({
              message:
                'OpsOwner and AppRefId tags are missing for the selected application / environment. Please choose different application / environment.',
            });
          } else if (!isValidAppId && !isValidOpsOwner) {
            return context.createError({
              message: 'App ID and OpsOwner tags are missing for the selected application. Please choose different application.',
            });
          } else if (!isValidAppId && !isValidAppRefId) {
            return context.createError({
              message: 'App ID and AppRefId tags are missing for the selected application. Please choose different application.',
            });
          } else if (!isValidAppId) {
            return context.createError({
              message:
                'App ID  tag is missing in the selected application. Please choose a different application or add app ID tag to the current application.',
            });
          } else if (!isValidAppRefId) {
            return context.createError({
              message:
                'AppRefId tag is missing in the selected environment. Please choose a different environment or add appRefId tag to the current environemnet.',
            });
          } else if (!isValidOpsOwner) {
            return context.createError({
              message:
                'OpsOwner tag is missing in the selected applciation. Please choose a different application or add OpsOwner tag to the current application.',
            });
          }
        }
        return true; // If validation passes
      }),
    swapSpace: yup.number().typeError('The field must be a number').min(4, 'Swap Space must be at least 4'),
    hostname: yup
      .string()
      .trim()
      .required('Hostname is required')
      .matches(yupMatchesParams.hostname.pattern, yupMatchesParams.hostname.errorMessage)
      .test('toCheckTypeofVM', 'Hostname should be less than 12 characters', function (value) {
        if (value && vmType && isVmTypeWindows(vmType)) {
          return value.length <= 12;
        }
        return true;
      }),
    isHostNameValid: yup.boolean().oneOf([true]),
    vmCount: yup.number().required('VM Count is required'),
    sshpubkey: yup
      .string()
      .nullable()
      .notRequired()
      .matches(yupMatchesParams.sshKey.pattern, yupMatchesParams.sshKey.errorMessage)
      .test('start with ssh-rsa', 'The field must start with "ssh-rsa".', (value) => !value || (!!value && value.includes('ssh-rsa'))),

    sequenceNumber: yup
      .number()
      .typeError('The field must be a number')
      .positive('The field must be a positive number')
      .test('default-password', 'starting sequence', async function (value) {
        const { vmCount } = this.parent;
        if (vmCount > 1) {
          if (!value) {
            return this.createError({ message: 'Starting Sequence is required' });
          }
        }
        return true;
      }),

    description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
    targetLayout: yup.string().required('OS Version is required').notOneOf(['0'], 'OS Version is required'),
    password: yup
      .string()
      .trim()
      .required('Password is required')
      .matches(yupMatchesParams.vmPassword.pattern, yupMatchesParams.vmPassword.errorMessage)
      .test('default-password', 'Password', function (value) {
        if (value === process.env.REACT_APP_VM_DEFAULT_PASSWORD) {
          return this.createError({ message: yupMatchesParams.vmPassword.errorMessage });
        }
        return true;
      }),
    datacenter: yup.string().required('Datacenter is required'),
    ipModes: yup.string().required('IP Modes is required'),
    groupId: yup.string().test('CMP Group is required', 'CMP Group', function (value) {
      if (!value && validateGroupId) {
        return this.createError({ message: 'CMP Group is required' });
      }
      return true;
    }),
    resourceId: yup.string().test('Cluster is required', 'Cluster', function (value) {
      if (!value && validateResourceId) {
        return this.createError({ message: 'Cluster is required' });
      }
      return true;
    }),
    customDomain: yup.string().test('custom-domain-validation', 'Custom Domain must end with .com', function (value) {
      if (!dnsDomain?.dnsDomainName && !value && validateDomainName) {
        return this.createError({ message: 'Custom Domain is required as network does not have a dnsDomain' });
      }
      if (value && validateDomainName && !yupMatchesParams.dnsDomainName.pattern.test(value)) {
        return this.createError({ message: 'Custom Domain must end with .com' });
      }
      return true;
    }),
    network: yup.string().required('Network is required'),
    ipv4: yup.boolean().test('atleast-one-selected', 'IPV4', function (value) {
      const { ipv6, ipModes } = this.parent;
      if (!value && !ipv6 && ipModes !== 'dhcp') {
        return this.createError({ message: 'Please select IPv4 or Ipv6 or both IPv4 and Ipv6 to continue' });
      }
      return true;
    }),
    ipv6: yup.boolean().test('atleast-one-selected', 'IPV6', function (value) {
      const disableIpv6Only = Boolean(process.env.REACT_APP_DISABLE_IPV6_ONLY_VM);
      const { ipv4, ipModes } = this.parent;
      if (!value && !ipv4 && ipModes !== 'dhcp') {
        return this.createError({ message: 'Please select IPv4 or Ipv6 or both IPv4 and Ipv6 to continue' });
      }
      if (value && !ipv4 && validateIPv6 && disableIpv6Only) {
        return this.createError({ message: 'Please select IPv4 or both IPv4 and Ipv6 to continue' });
      }
      return true;
    }),

    ipv4Addresses: yup.array().of(
      yup
        .string()
        .test('valid-ipv4-address', 'Invalid IPv4 address', function (value: any) {
          const ipv4 = this.options.context?.ipv4;
          const ipModes = this.options.context?.ipModes;
          if (ipv4 && ipModes === IP_MODES.static) {
            if (!value) {
              return this.createError({ message: 'IPv4 address is required' });
            } else if (Address4.isValid(value) && cidrValue) {
              const subnetIP = new Address4(cidrValue.cidr);
              const addressIPv4 = new Address4(value);
              const ipv4Range = IPv4CidrRange.fromCidr(cidrValue.cidr);
              if (!addressIPv4.isInSubnet(subnetIP)) {
                return this.createError({
                  message: `IP address should be within the range of selected network: ${cidrValue.cidr}`,
                });
              } else if (ipv4Range.getFirst().toString() === value || ipv4Range.getLast().toString() === value) {
                return this.createError({ message: "IP address shouldn't be network or broadcast address" });
              }
              return true;
            } else {
              return false;
            }
          }
          return true;
        })
        .matches(yupMatchesParams.validIPAddress.pattern, yupMatchesParams.validIPAddress.errorMessage)
        .test('unique-ipv4', 'Duplicate IPv4 address found', function (value) {
          const addresses = this.parent;
          if (value) {
            const duplicates = addresses.filter((address: any) => address === value);
            return duplicates.length <= 1;
          }
          return true;
        })
    ),
    ipv6Addresses: yup.array().of(
      yup
        .string()
        .test('valid-ipv6-address', 'Invalid IPv6 address', function (value: any) {
          const ipv6 = this.options.context?.ipv6;
          const ipModes = this.options.context?.ipModes;
          if (ipv6 && ipModes === IP_MODES.static) {
            if (!value) {
              return this.createError({ message: 'IPv6 address is required' });
            } else if (Address6.isValid(value) && cidrValue) {
              const subnetIP = new Address6(cidrValue.cidrIPv6);
              const addressIPv6 = new Address6(value);
              const ipv6Range = IPv6CidrRange.fromCidr(cidrValue.cidrIPv6);
              if (!addressIPv6.isInSubnet(subnetIP)) {
                return this.createError({
                  message: `IP address should be within the range of selected network: ${cidrValue.cidrIPv6}`,
                });
              } else if (ipv6Range.getFirst().toString() === value) {
                return this.createError({ message: "IP address shouldn't be network address" });
              }
              return true;
            } else {
              return false;
            }
          }
          return true;
        })
        .matches(yupMatchesParams.ipv6Field.pattern, yupMatchesParams.ipv6Field.errorMessage)
        .matches(yupMatchesParams.validIPAddress.pattern, yupMatchesParams.validIPAddress.errorMessage)
        .test('unique-ipv6', 'Duplicate IPv6 address found', function (value) {
          const addresses = this.parent;
          if (value) {
            const duplicates = addresses.filter((address: any) => address === value);
            return duplicates.length <= 1;
          }
          return true;
        })
    ),
    secondaryIpModes: yup.string().test('ipMode-required', 'IP Mode is required when Network is provided', function (value) {
      const { secondaryNetwork } = this.parent;
      if (secondaryNetwork) {
        return value ? true : this.createError({ message: 'IP Mode is required when Network is provided' });
      }
      return true;
    }),

    secondaryIpv4: yup.boolean().test('atleast-one-selected', 'IPV4', function (value) {
      const { secondaryIpv6, secondaryNetwork } = this.parent;

      if (secondaryNetwork && !value && !secondaryIpv6) {
        return this.createError({ message: 'Please select either IPv4 or IPv6' });
      }
      return true;
    }),
    secondaryIpv6: yup.boolean().test('atleast-one-selected', 'IPV6', function (value) {
      const { secondaryIpv4, secondaryNetwork } = this.parent;
      if (secondaryNetwork && !value && !secondaryIpv4) {
        return this.createError({ message: 'Please select either IPv4 or IPv6' });
      }
      return true;
    }),

    secondaryIpv4Addresses: yup.array().of(
      yup
        .string()
        .test('valid-ipv4-address', 'Invalid IPv4 address', function (value: any) {
          const ipv4 = this.options.context?.secondaryIpv4;
          const ipModes = this.options.context?.secondaryIpModes;
          if (ipv4 && ipModes === IP_MODES.static) {
            if (!value) {
              return this.createError({ message: 'IPv4 address is required' });
            } else if (Address4.isValid(value) && secondaryCidrValue) {
              const subnetIP = new Address4(secondaryCidrValue.secondaryCidr);
              const addressIPv4 = new Address4(value);
              const ipv4Range = IPv4CidrRange.fromCidr(secondaryCidrValue.secondaryCidr);
              if (!addressIPv4.isInSubnet(subnetIP)) {
                return this.createError({
                  message: `IP address should be within the range of selected network: ${secondaryCidrValue.secondaryCidr}`,
                });
              } else if (ipv4Range.getFirst().toString() === value || ipv4Range.getLast().toString() === value) {
                return this.createError({ message: "IP address shouldn't be network or broadcast address" });
              }
              return true;
            } else {
              return false;
            }
          }
          return true;
        })
        .matches(yupMatchesParams.validIPAddress.pattern, yupMatchesParams.validIPAddress.errorMessage)
        .test('unique-secondary-ipv4', 'Duplicate IPv4 address found', function (value) {
          const addresses = this.parent;
          if (value) {
            const duplicates = addresses.filter((address: any) => address === value);
            return duplicates.length <= 1;
          }
          return true;
        })
    ),
    secondaryIpv6Addresses: yup.array().of(
      yup
        .string()
        .test('valid-ipv6-address', 'Invalid IPv6 address', function (value: any) {
          const ipv6 = this.options.context?.secondaryIpv6;
          const ipModes = this.options.context?.secondaryIpModes;
          if (ipv6 && ipModes === IP_MODES.static) {
            if (!value) {
              return this.createError({ message: 'IPv6 address is required' });
            } else if (Address6.isValid(value) && secondaryCidrValue) {
              const subnetIP = new Address6(secondaryCidrValue.secondaryCidrIPv6);
              const addressIPv6 = new Address6(value);
              const ipv6Range = IPv6CidrRange.fromCidr(secondaryCidrValue.secondaryCidrIPv6);
              if (!addressIPv6.isInSubnet(subnetIP)) {
                return this.createError({
                  message: `IP address should be within the range of selected network: ${secondaryCidrValue.secondaryCidrIPv6}`,
                });
              } else if (ipv6Range.getFirst().toString() === value) {
                return this.createError({ message: "IP address shouldn't be network address" });
              }
              return true;
            } else {
              return false;
            }
          }
          return true;
        })
        .matches(yupMatchesParams.validIPAddress.pattern, yupMatchesParams.validIPAddress.errorMessage)
        .test('unique-secondary-ipv6', 'Duplicate IPv6 address found', function (value) {
          const addresses = this.parent;
          if (value) {
            const duplicates = addresses.filter((address: any) => address === value);
            return duplicates.length <= 1;
          }
          return true;
        })
    ),
    isValidSize: yup.boolean().oneOf([true]),
    appId: yup.string().required('App ID is required'),
    appRefId: yup.string().required('AppRefId is required'),
    isValidDisk: yup.boolean().oneOf([true]),
  });
export default CreateVMSchema;
