import React, { useEffect } from 'react';

import useGetCatalogLevel4Details from 'hooks/useGetCatalogLevel4Details';
import withFetchFormDetails from 'hoc/withFetchFormDetails';
import useLoadComponentDetails from 'hooks/useLoadComponentDetails';
import WorkInProgress from 'components/WorkInProgress';
import RequestDetailsType, { CatalogRequestType } from 'types/Interfaces/RequestDetails';
import { RequestStatus } from 'types/Enums';
import { useDispatch } from 'react-redux';
import { DefaultAccordions, setAccordions } from 'store/reducers/common';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

interface FormDetailsProps<T extends CatalogRequestType> {
  children: React.ReactElement;
  formDetails?: RequestDetailsType<T> | null;
  isFormDetailsLoading?: boolean;
  refetchServiceRequestDetails: () => void;
}

const FormDetails = <T extends CatalogRequestType>({ children, formDetails }: FormDetailsProps<T>) => {
  //Hooks
  const { catalogDetails } = useGetCatalogLevel4Details(formDetails?.requestType || '');
  const { Component: RequestDetailsComponent, comingSoon } = useLoadComponentDetails(catalogDetails?.component || '');
  const dispatch = useDispatch();
  const theme = useTheme<NebulaTheme>();
  const { palette } = theme;

  // Helper function to format form names
  const getFormattedFormName = (catalogName: string): string => {
    const nameMapping: { [key: string]: string } = {
      'donotpage': 'Do Not Page',
      // Add more mappings as needed
    };

    const displayName = nameMapping[catalogName] || catalogName;
    return `${displayName} Details`;
  };

  useEffect(() => {
    dispatch(setAccordions(DefaultAccordions.ALL));
  }, []);

  const requestMetaData = {
    id: formDetails?.id || '',
    serviceRequestId: formDetails?.serviceRequestId || '',
    status: formDetails?.status || ('' as RequestStatus),
    requesterEmail: formDetails?.requesterEmail || '',
    canApprove: false,
    downstreamResponseData: formDetails?.downstreamResponseData,
    systemUpdate: formDetails?.systemUpdate,
  };

  const renderContent = () => {
    return RequestDetailsComponent ? (
      <NblBorderContainer padding="20px" height="auto" backgroundColor={palette.secondary.main}>
        <RequestDetailsComponent
          formName={getFormattedFormName(catalogDetails.name)}
          requestPayload={formDetails?.payload}
          requestMetaData={requestMetaData}
        />
      </NblBorderContainer>
    ) : comingSoon ? (
      <WorkInProgress />
    ) : null;
  };

  return <>{formDetails ? renderContent() : children}</>;
};

export default withFetchFormDetails(FormDetails);
