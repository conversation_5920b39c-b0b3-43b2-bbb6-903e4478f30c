import { IconButton, styled, Typography } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import CloseIcon from '@mui/icons-material/Close';

export const RemoveFileButton = styled(IconButton)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { palette } = theme;
  return {
    color: palette.grey[600],
    '&:hover': {
      color: palette.grey[900],
    },
  };
});

export const ErrorText = styled(Typography)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { palette, typography } = theme;
  return {
    color: palette.error.main,
    fontSize: typography.pxToRem(14),
  };
});

export const StyledCloseIcon = styled(CloseIcon)(({ theme }) => ({
  color: 'black',
  cursor: 'pointer',
  '&:hover': {
    color: theme.palette.error.main,
  },
}));
