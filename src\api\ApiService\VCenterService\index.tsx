/* eslint-disable no-unused-vars */
import { AxiosRequestConfig } from 'axios';
import ApiService from '../';
import { LookUpVCenterpayload, LookUpResponse } from '../type';
import { FormValues } from 'componentsV2/Administration/Vcenter/ViewDetails';

class AddVCenterService extends ApiService {
  private baseUserContextUrl = 'vmware-admin/vCenter/lookup';

  lookUpVCenter(payload: LookUpVCenterpayload): Promise<LookUpResponse> {
    return this.post(`${this.baseUserContextUrl}`, payload);
  }

  addVCenter(payload: any): Promise<any> {
    return this.post(`${this.baseUserContextUrl}/save`, payload);
  }
}

export default AddVCenterService;
