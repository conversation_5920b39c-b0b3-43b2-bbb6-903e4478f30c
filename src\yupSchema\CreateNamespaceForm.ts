import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';

const MIN_TOKEN_TTL = Number(process.env.REACT_APP_CREATE_NAMESPACE_MIN_TOKEN_TTL || 10);
const MAX_TOKEN_TTL = Number(process.env.REACT_APP_CREATE_NAMESPACE_MAX_TOKEN_TTL || 31);

export const validationSchema = yup.object().shape({
  domain: yup.string().required('Domain is required'),
  projectName: yup.string().required('Project is required'),
  application: yup.string().required('Application is required'),
  environment: yup.string().required('Environment is required'),
  namespaceName: yup
    .string()
    .matches(yupMatchesParams.createNamespaceName.pattern, yupMatchesParams.createNamespaceName.errorMessage)
    .required('Namespace is required'),
  tokenTTL: yup
    .number()
    .typeError('Vault token TTL must be a number')
    .min(MIN_TOKEN_TTL, `Vault token TTL must be at least ${MIN_TOKEN_TTL} days`)
    .max(MAX_TOKEN_TTL, `Vault token TTL cannot be greater than ${MAX_TOKEN_TTL} days`)
    .required('Vault token TTL is required'),
  autoRenewTokenOnExpiry: yup.boolean().required('Auto renew Token on Expiry selection is required'),
  notifyBeforeTokenExpiry: yup.boolean().required('Notify before Token Expiry selection is required'),
  vaultPolicies: yup
    .array()
    .of(
      yup.object().shape({
        enable: yup.boolean(),
        role: yup.string().required('Role is required'),
        policyName: yup.string().required('Policy name is required'),
      })
    )
    .test('at-least-one-enabled', 'At least one policy must be enabled', (vaultPolicies) => {
      if (!vaultPolicies) return false;
      return vaultPolicies.some((policy) => policy.enable === true);
    }),
});
