import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { toast } from 'react-toastify';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { useApiService } from 'api/ApiService/context';
import DeleteSecret from '.';

const mockStore = configureMockStore();
const secretId = 'api_key_abc';
const confirmDeleteMessage = `Do you want to delete the secret api_key_abc secret?`;

// Mocks
jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    POSITION: {
      BOTTOM_CENTER: 'bottom-center',
    },
  },
}));

jest.mock('hooks/useMediaQuery', () => () => ({ size: '52px' }));

jest.mock('sharedComponents/NblConfirmPopUp', () => {
  return ({ onSubmit, open }: any) => open ? (
    <div>
      <h1>Confirm Delete</h1>
      <p>{confirmDeleteMessage}</p>
      <button onClick={onSubmit} data-testid="mock-confirm-submit">
        Mock Confirm Submit
      </button>
    </div>
  ) : null;
});

const store = mockStore({
  common: {
    isDialogMaximized: false,
  },
});

describe('DeleteSecret Component', () => {

  const mockDeleteSecretDetails = jest.fn().mockResolvedValue({
    status: true,
    data: { message: 'Deleted successfully' },
  });

  beforeEach(() => {
    (useApiService as jest.Mock).mockReturnValue({
      apiSecretsManagement: {
        deleteSecretDetails: mockDeleteSecretDetails,
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders delete button when type is "button"', () => {
    render(<ReduxProvider store={store}><NebulaThemeProvider><DeleteSecret secretId={secretId} type="button" disabled={!secretId} /></NebulaThemeProvider></ReduxProvider>);
    expect(screen.getByText('Delete')).toBeInTheDocument();
  });

  it('renders icon button when type is "icon"', () => {
    render(<ReduxProvider store={store}><NebulaThemeProvider><DeleteSecret secretId={secretId} type="icon" disabled={!secretId} /></NebulaThemeProvider></ReduxProvider>);
    expect(screen.getByTestId(`btnDeleteSecretIcon-${secretId}`)).toBeInTheDocument();
  });

  it('opens confirmation popup when icon is clicked', () => {
    render(<ReduxProvider store={store}><NebulaThemeProvider><DeleteSecret secretId={secretId} type="icon" disabled={!secretId} /></NebulaThemeProvider></ReduxProvider>);
    fireEvent.click(screen.getByTestId(`btnDeleteSecretIcon-${secretId}`));

    waitFor(() => {
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
      expect(screen.getByText(confirmDeleteMessage)).toBeInTheDocument();
    });
  });

  it('should delete a single secret and show success toast', async () => {
    const mockDeleteSecretDetails = jest.fn().mockResolvedValue({
      status: true,
      data: { message: 'Deleted successfully' },
    });

    (useApiService as jest.Mock).mockReturnValue({
      apiSecretsManagement: {
        deleteSecretDetails: mockDeleteSecretDetails,
      },
    });

    const { getByText } = render(
      <ReduxProvider store={store}>
        <NebulaThemeProvider>
          <DeleteSecret secretId={secretId} type="button" disabled={false} />
        </NebulaThemeProvider>
      </ReduxProvider>
    );

    fireEvent.mouseDown(getByText('Delete'));

    await waitFor(() => {
      expect(screen.getByTestId('mock-confirm-submit')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('mock-confirm-submit'));

    await waitFor(() => {
      expect(mockDeleteSecretDetails).toHaveBeenCalledWith([secretId]);
      expect(toast.success).toHaveBeenCalledWith(
        'Deleted successfully',
        expect.any(Object)
      );
    });
  });

  it('calls deleteSecretDetails with multiple IDs as array and shows toast', async () => {
    const secretIds = ['secretKey1', 'secretKey2', 'secretKey3'];
    const mockDeleteSecretDetails = jest.fn().mockResolvedValue({
      status: true,
      data: { message: 'Deleted successfully' },
    });

    (useApiService as jest.Mock).mockReturnValue({
      apiSecretsManagement: {
        deleteSecretDetails: mockDeleteSecretDetails,
      },
    });

    const { getByText } = render(
      <ReduxProvider store={store}>
        <NebulaThemeProvider>
          <DeleteSecret secretId={secretIds} type="button" disabled={false} />
        </NebulaThemeProvider>
      </ReduxProvider>
    );

    fireEvent.mouseDown(getByText('Delete'));

    await waitFor(() => {
      expect(screen.getByTestId('mock-confirm-submit')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('mock-confirm-submit'));

    await waitFor(() => {
      expect(mockDeleteSecretDetails).toHaveBeenCalledWith(secretIds);
      expect(toast.success).toHaveBeenCalledWith(
        'Deleted successfully',
        expect.any(Object)
      );
    });
  });

  it('does not call delete API if secretId is null', () => {
    render(<ReduxProvider store={store}><NebulaThemeProvider><DeleteSecret secretId={null} type="button" disabled={true} /></NebulaThemeProvider></ReduxProvider>);
    const deleteButton = screen.getByText('Delete');
    expect(deleteButton).toBeDisabled();
  });

  it('does not open dialog if icon button is disabled', () => {
    render(<ReduxProvider store={store}><NebulaThemeProvider><DeleteSecret secretId={null} type="icon" disabled={true} /></NebulaThemeProvider></ReduxProvider>);

    const iconButton = screen.getByTestId('btnDeleteSecretIcon-null');
    expect(iconButton).toBeDisabled();

    fireEvent.click(iconButton);

    const dialog = screen.queryByText(/confirm delete/i);
    expect(dialog).not.toBeInTheDocument();
  });
});
