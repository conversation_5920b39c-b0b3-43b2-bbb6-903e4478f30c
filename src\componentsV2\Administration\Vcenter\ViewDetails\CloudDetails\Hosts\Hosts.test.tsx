import { render, screen, fireEvent } from '@testing-library/react';
import Hosts from '.';
// eslint-disable-next-line no-unused-vars
import { Host } from '../Clusters/ClusterDetails/ClusterHostsDetails';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('Hosts Component', () => {
  const mockHosts: Host[] = [
    {
      hostMor: 'host-1',
      name: 'Host One',
      connectionState: 'https',
      powerState: 'power',
      disabled: false,
      location: 'london',
      vendor: 'horizon',
      model: 'new',
      cpu: 10,
      core: 100,
      memory: 25,
    },
    {
      hostMor: 'host-2',
      name: 'Host Two',
      connectionState: 'http',
      powerState: 'power',
      disabled: false,
      location: 'london',
      vendor: 'horizon',
      model: 'new',
      cpu: 10,
      core: 100,
      memory: 25,
    },
  ];

  const mockOnChange = jest.fn();

  it('renders host rows correctly', () => {
    render(
      <NebulaThemeProvider>
        <Hosts hosts={mockHosts} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.getByText('Host One')).toBeInTheDocument();
    expect(screen.getByText('Host Two')).toBeInTheDocument();
  });

  it('renders all columns correctly', () => {
    render(
      <NebulaThemeProvider>
        <Hosts hosts={mockHosts} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Type')).toBeInTheDocument();
    expect(screen.getByText('Disable')).toBeInTheDocument();
    expect(screen.getByText('Location')).toBeInTheDocument();
    expect(screen.getByText('Vendor')).toBeInTheDocument();
    expect(screen.getByText('Model')).toBeInTheDocument();
    expect(screen.getByText('CPU')).toBeInTheDocument();
    expect(screen.getByText('Cores')).toBeInTheDocument();
    expect(screen.getByText('Memory')).toBeInTheDocument();
  });

  it('toggles checkbox and calls onChange', () => {
    render(
      <NebulaThemeProvider>
        <Hosts hosts={mockHosts} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes.length).toBe(2);

    fireEvent.click(checkboxes[0]);
    expect(mockOnChange).toHaveBeenCalledTimes(1);
    expect(mockOnChange.mock.calls[0][0][0].disabled).toBe(true);
  });

  it('handles empty hosts list', () => {
    render(
      <NebulaThemeProvider>
        <Hosts hosts={[]} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.queryByText('Host One')).not.toBeInTheDocument();
    expect(screen.queryByText('Host Two')).not.toBeInTheDocument();
  });

  it('checkbox reflects initial state', () => {
    render(
      <NebulaThemeProvider>
        <Hosts hosts={mockHosts} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes[0]).not.toBeChecked();
    expect(checkboxes[1]).not.toBeChecked();
  });
});
