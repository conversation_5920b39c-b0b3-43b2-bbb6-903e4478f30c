import { useMemo } from 'react';

// eslint-disable-next-line no-unused-vars
import { CssBaseline, StyledEngineProvider, ThemeOptions, PaletteMode } from '@mui/material';
import { createTheme, ThemeProvider } from '@mui/material/styles';

// project import
import Typography from 'NebulaTheme/typography';
import { THEMES } from 'NebulaTheme/bundle';
import { BreakPoints } from 'NebulaTheme';

// ==============================|| Mocked THEME ||============================== //

type NebulaThemeProviderProps = {
  children: React.ReactElement;
};

const Palette = (mode: PaletteMode | undefined) => {
  return createTheme({
    mode,
    palette: {
      ...THEMES.light.palette,
    },
  } as ThemeOptions);
};

export default function NebulaThemeProvider({ children }: NebulaThemeProviderProps) {
  const theme = Palette('light');

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const themeTypography = Typography(`'Public Sans', sans-serif`);

  const themeOptions = useMemo(
    () => ({
      breakpoints: {
        values: BreakPoints
      },
      direction: 'ltr',
      palette: theme.palette,
      typography: themeTypography,
    }),
    [theme, themeTypography]
  );

  const themes = createTheme(themeOptions as ThemeOptions);

  return (
    <StyledEngineProvider injectFirst>
      <ThemeProvider theme={themes}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </StyledEngineProvider>
  );
}
