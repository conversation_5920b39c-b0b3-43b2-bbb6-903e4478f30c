import { styled } from '@mui/material/styles';

interface StyledSideBarProps {
  expanded: boolean;
}

const StyledSideBar = styled('div')<StyledSideBarProps>(({ theme, expanded }) => ({
  position: 'relative',
  display: 'flex',
  width: expanded ? 'fit-content' : '80px',
  height: '100%',
  borderRadius: expanded ? '40px' : '68px',
  cursor: 'pointer',
  backgroundColor: '#062844',
  overflow: 'hidden',
  zIndex: 10,
  boxSizing: 'border-box',
  '*': {
    boxSizing: 'border-box',
  },
  [theme.breakpoints.down('2K')]: {
    width: expanded ? 'fit-content' : '70px',
    borderRadius: expanded ? '35px' : '64px',
  },
}));

export { StyledSideBar };
