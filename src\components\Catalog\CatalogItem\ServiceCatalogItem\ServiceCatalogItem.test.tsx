import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import ServiceCatalogItem from '.';
import Theme<PERSON>rovider from 'mock/ThemeProvider';

describe('ServiceCatalogItem component', () => {
  test('Should render the catalog item', async () => {
    const item = {
      name: 'Virtual Machine',
      description: 'Provision virtual machine',
      button: 'start',
      path: '/IaaS/compute/virtualMachine',
      icon: 'FoundationOutlined',
      canAccess: true,
      disabled: false,
      id: 'virtualMachine',
    };

    const mockStore = configureMockStore();
    const store = mockStore({
      authorization: {
        permissions: {
          iaas: { virtualMachine: { Permissions: ['CREATE'] } },
        },
      },
      common: {
        exposureParams: [],
      },
    });

    const { getByText } = render(
      <BrowserRouter>
        <ReduxProvider store={store}>
          <ThemeProvider>
            <ServiceCatalogItem {...item} />
          </ThemeProvider>
        </ReduxProvider>
      </BrowserRouter>
    );
    expect(getByText(item.name)).toBeInTheDocument();
  });
});
