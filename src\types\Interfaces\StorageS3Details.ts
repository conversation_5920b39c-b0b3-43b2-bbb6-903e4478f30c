type StorageS3Details = {
  domain: string;
  name: string;
  project: {
    appName: string;
    projectName: string;
  };
  dataCenter: string;
  account: {
    id: string;
    name: string;
  };
  platformContext: {
    catalogId: string;
    domainId: string;
    envId: string;
    domainName: string;
    environmentName: string;
  };
  createNewAccount: boolean;
  accountName: string;
  accountQuota: string;
  accountDescription: string;
  bucketName: string;
  versioning: boolean;
  bucketQuota: string;
  bucketDescription: string;
  userDetails: Array<{
    userName: string;
    permissions: {
      read: boolean;
      write: boolean;
      admin: boolean;
    };
  }>;
};

export default StorageS3Details;
