import { ExpandLess, ExpandMore } from '@mui/icons-material';
import { TableHead, TableRow, TableCell, TableBody, Button, Box, useTheme, Grid } from '@mui/material';
import { useState } from 'react';
import PaginationComponent from '../../PaginationComponent';
import { StyledTable, StyledTableCell, StyledTableContainer, StyledTableData } from './tableStyles';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import NblTypography from 'sharedComponents/NblTypography';
import { getUsageColor } from '../../../utils/colors';

interface TableProps {
  columns: Array<string>;
  data: Array<any>;
  rowPerPageOptions?: Array<number>;
  dataList: any[];
}

const CommonTable: React.FC<TableProps> = ({ columns, data, dataList, rowPerPageOptions = [5, 10, 15] }) => {
  const [rowPerPage, setRowsPerPage] = useState(rowPerPageOptions[0]);
  const [showMore, setShowMore] = useState(false);
  const [page, setPage] = useState(1);
  const theme: NebulaTheme = useTheme();

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const getColor = (column: any, data: any) => {
    const maxUtilization = Math.max(data.cpuUtilized, data.memoryUtilized, data.storageUtilized);
    switch (column) {
      case 'Status':
        return getUsageColor(maxUtilization);
      case 'CPU':
        return getUsageColor(data.cpuUtilized);
      case 'Memory':
        return getUsageColor(data.memoryUtilized);
      case 'Storage':
        return getUsageColor(data.storageUtilized);
      default:
        return 'none';
    }
  };

  const getColumnWidth = (column: string) => {
    switch (column) {
      case 'Status':
        return '400px';
      case 'CPU':
        return '10%';
      case 'Memory':
        return '10%';
      case 'Storage':
        return '10%';
      default:
        return 'none';
    }
  };

  const handleToggleViewMore = () => {
    setShowMore(!showMore);
    setRowsPerPage(showMore ? rowPerPageOptions[0] : rowPerPageOptions[1]);
    setPage(1);
  };

  return (
    <>
      <StyledTableContainer>
        <StyledTable>
          <TableHead>
            <TableRow>
              {columns.map((column, index) => (
                <StyledTableCell key={index}>
                  <NblTypography variant="subtitle1" color="shade4">
                    {column}
                  </NblTypography>
                </StyledTableCell>
              ))}
            </TableRow>
            {/* New Row for Utilization % Labels */}
            <TableRow sx={{ marginTop: '18px' }}>
              {columns.map((column, index) => (
                <StyledTableData key={index}>
                  {(column === 'CPU' || column === 'Memory' || column === 'Storage') && (
                    <NblTypography variant="subtitle1" color="shade6">
                      Utilization%
                    </NblTypography>
                  )}
                </StyledTableData>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.slice((page - 1) * rowPerPage, page * rowPerPage).map((row, rowIndex, arr) => (
              <TableRow key={rowIndex}>
                {columns.map((column: any, colIndex) => (
                  <TableCell
                    sx={{
                      '&.MuiTableCell-root': {
                        borderBottom:
                          rowIndex === arr.length - 1 ? 'none !important' : `1px solid ${theme.palette.secondary.shade4} !important`,
                        color: `${theme.palette.secondary.main}`,
                        alignItems: 'center',
                      },
                      ...(column === 'Hypervisor'
                        ? {
                            font: 'normal normal medium 16px/18px Gotham',
                            lineHeight: 1.14,
                            fontWeight: 500,
                          }
                        : {
                            font: 'normal normal normal 16px/18px Gotham; ',
                            lineHeight: 1.14,
                          }),
                      width: getColumnWidth(column),
                    }}
                    key={colIndex}
                  >
                    {column === 'Hypervisor' ? (
                      row[column]
                    ) : (
                      <>
                        <Grid container direction="column" alignItems="flex-start">
                          <Grid item>
                            <Grid container>
                              <Grid item>
                                <svg
                                  width="15"
                                  height="15"
                                  viewBox="0 0 15 15"
                                  preserveAspectRatio="xMidYMid meet"
                                  style={{ marginRight: '2px', marginBottom: '-4px' }}
                                >
                                  <rect x="1.5" y="4" width="12" height="3" fill={getColor(column, row)} rx="2" ry="2" />
                                </svg>
                              </Grid>
                              <Grid item>{row[column]}</Grid>
                            </Grid>
                          </Grid>

                          {/* Check if the column is CPU, Memory or Storage and display utilization in the next line */}
                          {(column === 'CPU' || column === 'Memory' || column === 'Storage') && (
                            <Grid item>
                              <Grid container direction="column" alignItems="center">
                                <Grid item>
                                  <svg
                                    width="15"
                                    height="15"
                                    viewBox="0 0 15 15"
                                    preserveAspectRatio="xMidYMid meet"
                                    style={{ marginRight: 'px', marginBottom: '-4px' }}
                                  >
                                    <rect x="1.5" y="4" width="12" height="3" fill={getColor(`${column}Utilization`, row)} rx="2" ry="2" />
                                  </svg>
                                </Grid>
                                {/* Center Utilization % */}
                                <Grid item sx={{ fontSize: '12px', color: `${theme.palette.secondary.shade4}`, textAlign: 'center' }}>
                                  {row[`${column}Utilization`]}
                                </Grid>
                              </Grid>
                            </Grid>
                          )}
                        </Grid>
                      </>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </StyledTable>
        {data.length > rowPerPage && (
          <Button onClick={handleToggleViewMore} sx={{ justifyContent: 'center', justifyItems: 'center', alignItems: 'center' }}>
            {showMore ? 'View Less' : 'View More'}
            {showMore ? <ExpandLess /> : <ExpandMore />}
          </Button>
        )}
      </StyledTableContainer>
      <Box display="flex" justifyContent="center" alignItems="center" mt={2}>
        <PaginationComponent
          pageLength={dataList.length}
          rowsPerPage={rowPerPage}
          handlePageChange={handlePageChange}
          page={page}
          sx={{
            '& .MuiPaginationItem-page': {
              backgroundColor: `${theme.palette.primary.main}`,
              color: `${theme.palette.secondary.main}`,
              borderRadius: '6px',
            },
            '& .MuiPaginationItem-root': {
              color: `${theme.palette.secondary.main}`,
            },
            '& .Mui-selected': {
              backgroundColor: `${theme.palette.primary.shade3}`,
              color: `${theme.palette.secondary.main}`,
            },
            mt: 3,
            [theme.breakpoints.down('2K')]: {
              mt: '-6px',
            },
          }}
        />
      </Box>
    </>
  );
};

export default CommonTable;
