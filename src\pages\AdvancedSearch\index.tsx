import React, { useC<PERSON>back, useEffect, useMemo, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { Chip, IconButton, InputAdornment, useTheme } from "@mui/material";
import dayjs, { Dayjs } from "dayjs";
import { NebulaTheme } from "NebulaTheme/type";
import SearchService from "api/ApiService/SearchService";
import NblBorderContainer from "sharedComponents/NblContainers/NblBorderContainer";
import { NblGridContainer, NblGridItem } from "sharedComponents/NblContainers/NblGridContainer";
import NblFlexContainer from "sharedComponents/NblContainers/NblFlexContainer";
import NblAccordion from "sharedComponents/Accordion/NblAccordion";
import NblButton from "sharedComponents/Buttons/NblButton";
import NblTextField from "sharedComponents/NblFormInputs/NblTextField";
import NblTypography from "sharedComponents/NblTypography";
import { StyledCloseIcon } from "sharedComponents/NblFileUpload/styled";
import NblDatePicker from "sharedComponents/NblFormInputs/NblDatePicker";
import NblSearchResults from "sharedComponents/NblSearchResults";

interface FilterMetaData {
  [key: string]: {displayName: string};
}

interface ResultsData {
  [key: string]: {[key: string]: any}
}

type FormValues = {
  [key: string]: string | Dayjs | null;
}

const filterMetaData: FilterMetaData = {
  'projectName': {displayName: 'Project Name'},
  'resourcesName': {displayName: 'Resource Name'},
  'requestId': {displayName: 'Service Request Id'},
  'resourceId': {displayName: 'Resource Id'},
  'status': {displayName: 'Status'},
  'createdBy': {displayName: 'Created By'},
  'createdStart': {displayName: 'Created Start Date'},
  'createdEnd': {displayName: 'Created End Date'},
  'approvedBy': {displayName: 'Approved By'},
  'appId': {displayName: 'AppId'},
}

const AdvancedSearch = () => {
  const searchService = new SearchService();
  const theme = useTheme<NebulaTheme>();
  const [filters, setFilters] = useState<FormValues>({});
  const [results, setResults] = useState<ResultsData>({});
  const [isLoading, setIsLoading] = useState<Boolean>(false);
  const [searchTriggered, setSearchTriggered] = useState<Boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    if (Object.fromEntries(searchParams.entries())) {
      const allParams = Object.fromEntries(searchParams.entries())
      setFilters({...allParams})
    }
  },[])

  const handleToggleFilter = (filter: string) => {
    setFilters(prevFilters => {
      const newFilters = {...prevFilters};
      if (Object.hasOwn(newFilters, filter)) {
        delete newFilters[filter]
        setSearchParams((params: URLSearchParams) => {
          const newParams = new URLSearchParams(params);
          newParams.delete(filter);
          return newParams;
        })
      } else {
        if (filter === 'createdStart' || filter === 'createdEnd') {
          newFilters[filter] = null;
        } else {
          newFilters[filter] = '';
        }
      } 
      return newFilters;
    })
  }

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prevFilters => {
      return ({
        ...prevFilters,
        [e.target.name]: e.target.value,
      })
    })

    setSearchParams(prevParams => ({
      ...Object.fromEntries(prevParams.entries()),
      [e.target.name]: e.target.value
    }))
  }

  const handleDateChange = (field: string, newDate: Dayjs) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      [field]: newDate.toISOString()
    }))

    setSearchParams(prevParams => {
      return ({
        ...Object.fromEntries(prevParams.entries()),
        [field]: newDate.toISOString()
      })
    })
  }

  const handleClearSearch = () => {
    setFilters({});
    setResults({});
    setSearchParams({});
    setSearchTriggered(false);
  }

  const removeFilter = (filter: string) => {
    setFilters(prevFilters => {
      const newFilters = {...prevFilters}
      delete newFilters[filter];
      return newFilters;
    })

    setSearchParams((params: URLSearchParams) => {
      const newParams = new URLSearchParams(params);
      if (newParams.has(filter)) {
        newParams.delete(filter);
      }
      return newParams;
    })
  }

  const handleSubmitSearch = async () => {
    setIsLoading(true);
    setSearchTriggered(true);
    const response: any = await searchService.advancedSearch(filters)
    const formattedData = response.data.reduce((acc:any, curr:any) => {
      for (let key in curr) {
        if (Array.isArray(curr[key]) && curr[key].length === 0) {
          continue; // skip empty arrays
        }
        acc[key] = curr[key];
      }
      return acc;
    }, {});
    setIsLoading(false);
    setResults(formattedData)
  }

  const renderFilters = useCallback(() => {
    return Object.keys(filterMetaData).map(filter => (
      <Chip
        key={filter}
        onClick={() => handleToggleFilter(filter)}
        label={filterMetaData[filter].displayName}
        sx={{ 
          backgroundColor: Object.hasOwn(filters, filter) ? theme.palette.primary.shade3 : theme.palette.secondary.shade1,
          border: '1px solid', 
          color: Object.hasOwn(filters, filter) ? theme.palette.common.white : theme.palette.primary.main, 
          margin: '0 .25rem .5rem',
          '&:hover': {
            backgroundColor: Object.hasOwn(filters, filter) ? theme.palette.primary.shade4 : theme.palette.secondary.dark,
          }
        }}
      />
    ))
  },[filters])

  const renderFields = useCallback(() => {
    return Object.keys(filters).length > 0 
      ? Object.keys(filters).map((item) => {
          if (item === 'createdStart' || item === 'createdEnd') {
            return (
              <NblGridItem key={item} maxWidth="33.3rem">
                <NblDatePicker
                  label={filterMetaData[item].displayName}
                  name={item}
                  value={filters[item] != null ? dayjs(filters[item]) : null}
                  handleChange={(newValue: Dayjs) => handleDateChange(item, newValue)}
                  error={false}
                />
              </NblGridItem>
            )
          } else {
            return (
              <NblGridItem key={item} maxWidth="33.3rem">
                <NblTextField
                  key={item}
                  type="text"
                  name={item}
                  label={filterMetaData[item].displayName}
                  placeholder={filterMetaData[item].displayName}
                  handleChange={handleFilterChange}
                  value={filters[item]}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton onClick={() => removeFilter((item))} onMouseDown={(event) => event.preventDefault()} edge="end">
                        <StyledCloseIcon sx={{fontSize: '.875rem'}} />
                      </IconButton>
                    </InputAdornment>
                  }
                />
              </NblGridItem>
            )
          }
        })
      : <NblGridItem colspan={3}>
          <NblTypography textAlign="center" color='shade1' variant="body1">Select one or more filters to begin searching Nebula.</NblTypography>
        </NblGridItem>
  }, [filters])

  const renderResults = useMemo(
    () => <NblSearchResults results={results} isLoading={isLoading} searchTriggered={searchTriggered} />, 
    [results, isLoading, searchTriggered]
  )

  return (
    <>
      <NblBorderContainer backgroundColor={theme.palette.common.white} height="auto" margin="0 0 1rem">
        <NblAccordion
          summary="Advanced Search"
          hasDivider
          defaultExpanded
          bgColor={theme.palette.common.white}
        >
          <NblFlexContainer direction="row" wrap="wrap" alignItems="center" justifyContent="center" spacing={.25} margin="1rem 0 0">
            {renderFilters()}
          </NblFlexContainer>
          <NblGridContainer 
            columns={3}
            justifyContent="center" 
            alignItems="center"
            alignContent="center"
            margin="1rem 0"
          >
            {renderFields()}
          </NblGridContainer>
          <NblFlexContainer direction="row" justifyContent="center" alignItems="center" spacing={2} margin="0 0 1rem">
            <NblButton
              type="button"
              buttonID="clear filters"
              color="primary"
              variant="outlined"
              onClick={handleClearSearch}
            >
              Clear
            </NblButton>
            <NblButton
              type="button"
              disabled={Object.values(filters).every(value => value === '')}
              buttonID="apply filters"
              color="primary"
              variant="contained"
              onClick={handleSubmitSearch}
            >
              Search
            </NblButton>
          </NblFlexContainer>
        </NblAccordion>
      </NblBorderContainer>
      {renderResults}
    </>
  )
}

export default AdvancedSearch;