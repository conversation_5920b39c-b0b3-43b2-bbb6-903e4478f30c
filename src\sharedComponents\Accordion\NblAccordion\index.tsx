import React from 'react';
import { useTheme } from '@mui/material';

import NblDivider from 'sharedComponents/NblDivider';
import { StyledAccordion, StyledAccordionSummary, StyledAccordionDetails } from './styled';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography, { NblTypographyProps } from 'sharedComponents/NblTypography';
import { AccordionExpandIcon } from 'assets/images/icons/custom-icons';
import { NebulaTheme } from 'NebulaTheme/type';

export type ExpandIconSize = 'regular' | 'large';

export interface NblAccordionProps {
  summary: string;
  subSummary?: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  hasDivider?: boolean;
  bgColor?: string;
  border?: string;
  expanded?: boolean;
  expandIconSize?: ExpandIconSize;
  icon?: React.ReactElement;
  onChange?: () => void;
  margin?: string;
  renderElement?: React.ReactNode;
  summaryFontSize?: NblTypographyProps['variant'];
}

const NblAccordion: React.FunctionComponent<NblAccordionProps> = ({
  summary,
  subSummary,
  children,
  defaultExpanded = false,
  hasDivider,
  bgColor,
  border,
  expanded,
  expandIconSize = 'regular',
  icon,
  onChange,
  margin = '0px',
  renderElement,
  summaryFontSize = 'h6',
}) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();

  //JSX
  return (
    <StyledAccordion
      bgColor={bgColor}
      defaultExpanded={defaultExpanded}
      expanded={expanded}
      expandIconSize={expandIconSize}
      onChange={onChange}
      border={border}
    >
      <StyledAccordionSummary expandIcon={<AccordionExpandIcon />} margin={margin}>
        <NblFlexContainer alignItems={'center'} spacing={2}>
          {icon && icon}
          <NblFlexContainer direction={'column'} spacing={0.5} width="auto">
            <NblTypography variant={summaryFontSize} color={'shade1'} weight="bold">
              {summary}
            </NblTypography>
            {subSummary && (
              <NblTypography variant="body3" color={'shade1'}>
                {subSummary}
              </NblTypography>
            )}
          </NblFlexContainer>
          {renderElement}
        </NblFlexContainer>
      </StyledAccordionSummary>
      {hasDivider && (
        <NblFlexContainer justifyContent="center" spacing={0}>
          <NblDivider
            orientation="horizontal"
            length={margin ? `calc(100% - 32px - ${margin} - ${margin})` : `calc(100% - 32px - 60px)`}
            borderRadius={1}
            color={theme.palette.nbldivider.variant3}
            opacity={0.4}
            strokeWidth={0.3}
          />{' '}
        </NblFlexContainer>
      )}
      <StyledAccordionDetails margin={margin}>{children} </StyledAccordionDetails>
    </StyledAccordion>
  );
};

export default NblAccordion;
