import { render, fireEvent, act, waitFor } from '@testing-library/react';
import Select from './index';
import ThemeProvider from 'mock/ThemeProvider';

const OPTIONS = [
  { label: 'Male', value: 'Male' },
  { label: 'Female', value: 'Female' },
];

describe('Select Component', () => {
  test('Should render gender dropdown with the options', async () => {
    const onChange = jest.fn();
    const { getByText, container } = await act(async () =>
      render(
        <ThemeProvider>
          <Select value={''} label="Gender" options={OPTIONS} name="gender" handleChange={onChange} />
        </ThemeProvider>
      )
    );

    let vatSelectTextField = container.querySelector('#gender') as HTMLDivElement;

    fireEvent.mouseDown(vatSelectTextField);

    await waitFor(() => {
      expect(getByText('Male')).toBeInTheDocument();
      expect(getByText('Female')).toBeInTheDocument();
    });

    fireEvent.click(getByText('Male'));
    await waitFor(() => {
      expect(onChange).toHaveBeenCalledTimes(1);
    });
  });
});
