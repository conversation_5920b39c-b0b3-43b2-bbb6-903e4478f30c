// eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps, useState } from 'react';
import NblRadioGroup from 'sharedComponents/NblFormInputs/NblRadioGroup';
import NebulaTheme from 'NebulaTheme';

type StoryProps = ComponentProps<typeof NblRadioGroup>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblRadioGroup',
  component: NblRadioGroup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

const Template = (args: StoryProps) => {
  const [options, setOptions] = useState(args.options);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>, index: number, value: string) => {
    const updatedOptions = options.map((opt, i) => ({
      ...opt,
      checked: i === index,
    }));
    setOptions(updatedOptions);

    args.onChange?.(event, index, value);
  };

  const handleBlur = (event: React.FocusEvent<HTMLButtonElement>, index: number) => {
    args.onBlur?.(event, index);
  };

  const handleMouseEnter = (event: React.MouseEvent<HTMLLabelElement>, index: number) => {
    args.onMouseEnter?.(event, index);
  };

  const handleMouseLeave = (event: React.MouseEvent<HTMLLabelElement>, index: number) => {
    args.onMouseLeave?.(event, index);
  };

  return (
    <NblRadioGroup
      {...args}
      options={options}
      onChange={handleChange}
      onBlur={handleBlur}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    />
  );
};

export const NblRadioGroupStory: Story = {
  args: {
    label: 'Choose an option',
    name: 'exampleRadioGroup',
    error: false,
    helperText: 'Select one of the options below.',
    mandatory: true,
    options: [
      { label: 'Option 1', value: 'option1', name: 'exampleRadioGroup', checked: true },
      { label: 'Option 2', value: 'option2', name: 'exampleRadioGroup', checked: false },
      { label: 'Option 3', value: 'option3', name: 'exampleRadioGroup', checked: false, disabled: true },
    ],
  },
  render: (args) => (
    <NebulaTheme>
      <Template {...args} />
    </NebulaTheme>
  ),
};
