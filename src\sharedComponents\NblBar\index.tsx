import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { BarPalette } from 'NebulaTheme/type/barPalette';
import React, { useMemo } from 'react';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

export interface NblBarProps {
  orientation: 'vertical' | 'horizontal';
  color: keyof BarPalette['bar'];
  length: string;
  strokeWidth?: string;
}

const NblBar: React.FC<NblBarProps> = ({ orientation, color, length, strokeWidth = '5px' }) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();

  //Memoization
  const bgColor = useMemo(() => theme.palette.bar[color], [theme, color]);
  const orientationStyles = useMemo(
    () => (orientation === 'vertical' ? { width: strokeWidth, height: length } : { width: length, height: strokeWidth }),
    [orientation]
  );

  //JSX
  return <NblFlexContainer backgroundColor={bgColor} {...orientationStyles} borderRadius="10px" />;
};

export default NblBar;
