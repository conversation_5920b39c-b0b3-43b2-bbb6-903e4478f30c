{"content": {"serviceCatalog": [{"name": "Add Service Catalog Groups", "path": "/administration/service-catalog/add-service-catalog-group", "icon": "AddOutlined", "id": "add-service-catalog-group", "disabled": false, "description": "Add catalog level 1, level 2 and level 3 groups", "shortName": "servicecatalogs", "permissions": ["canCreate"]}, {"name": "Manage Service Catalog Groups", "path": "/administration/service-catalog/manage-service-catalog-groups", "editPath": "service-catalog", "icon": "RemoveRedEyeOutlined", "id": "manage-service-catalog-groups", "disabled": false, "description": "View, edit and delete catalog level 1, level 2 and level 3 groups", "shortName": "servicecatalogs", "permissions": ["canRead", "canUpdate", "canDelete"]}, {"name": "Add Service Catalog List", "path": "/administration/service-catalog/add-service-catalog-list", "icon": "AddOutlined", "id": "add-service-cataloglist", "disabled": false, "description": "Add catalog level 4 item", "shortName": "servicecatalogs", "permissions": ["canCreate"]}, {"name": "Manage Service Catalog Lists", "path": "/administration/service-catalog/manage-service-catalog-lists", "editPath": "service-catalog-list", "icon": "RemoveRedEyeOutlined", "id": "manage-service-cataloglists", "disabled": false, "description": "View, edit and delete catalog level 4 item", "shortName": "servicecatalogs", "permissions": ["canRead", "canUpdate", "canDelete"]}], "permission": [{"name": "Add Permission", "path": "/administration/permissions/add-permission", "icon": "AddOutlined", "id": "add-permission-catalog", "description": "Add specific permissions that can be assigned to various roles to control access within system", "disabled": false, "shortName": "permissions", "permissions": ["canCreate"]}, {"name": "Manage Permissions", "path": "/administration/permissions/view-permissions", "icon": "VisibilityOutlined", "id": "view-permissions-catalog", "description": "View, edit or delete existing permissions to ensure correct access for roles", "disabled": false, "shortName": "permissions", "permissions": ["canRead", "canUpdate", "canDelete"]}], "groups": [{"name": "Register AD group", "path": "/administration/groups/register-ad-group", "icon": "AddOutlined", "description": "Register a AD group or DL with Nebula", "id": "register-ad-group", "shortName": "groups", "permissions": ["canCreate"]}, {"name": "Manage Registered AD groups", "path": "/administration/groups/manage-registered-ad-groups", "editPath": "groups", "icon": "RemoveRedEyeOutlined", "description": "Manage catalog, project and administration permissions for registered AD groups", "id": "manage-registered-ad-groups", "shortName": "groups", "permissions": ["canRead", "canUpdate", "canDelete"]}], "role": [{"name": "Add Role", "path": "add-role", "icon": "AddOutlined", "id": "add-role", "description": "Define new role to grant permissions for catalogs, projects and administration", "disabled": false, "shortName": "roles", "permissions": ["canCreate"]}, {"name": "Manage Roles", "path": "view-roles", "editPath": "roles", "icon": "VisibilityOutlined", "id": "view-roles", "description": "View, edit, or delete roles", "disabled": false, "shortName": "roles", "permissions": ["canRead", "canUpdate", "canDelete"]}], "team": [{"name": "Add Team", "path": "add-team", "icon": "AddOutlined", "id": "add-team", "description": "Assign team member to existing groups", "disabled": false, "shortName": "teams", "permissions": ["canCreate"]}, {"name": "View Teams", "path": "view-teams", "icon": "VisibilityOutlined", "id": "view-teams", "description": "View, edit, or delete existing team members assigned to specific group", "disabled": false, "shortName": "teams", "permissions": ["canRead", "canUpdate", "canDelete"]}], "project": [{"name": "Add Project", "path": "add-project", "icon": "AddOutlined", "id": "add-project", "description": "Set up projects with customizable network and tag settings for resources", "disabled": false, "shortName": "projects", "permissions": ["canCreate"]}, {"name": "Manage Projects", "path": "view-projects", "editPath": "project", "icon": "VisibilityOutlined", "id": "view-projects", "description": "View, edit, or delete projects and adjust network and tag settings as needed", "disabled": false, "shortName": "projects", "permissions": ["canRead", "canUpdate", "canDelete"]}], "vmsizing": [{"name": "Add VM Sizes", "path": "/administration/VM-sizing/add-VM-sizing", "icon": "AddOutlined", "description": "Add the sizes for Virtual Machines", "id": "add-vm-sizing", "shortName": "vmsizing", "permissions": ["canCreate"]}, {"name": "View VM Sizes", "path": "/administration/VM-sizing/view-VM-sizing", "editPath": "VM-sizing", "icon": "RemoveRedEyeOutlined", "description": "View the sizes for Virtual Machines", "id": "view-vm-sizing", "shortName": "vmsizing", "permissions": ["canRead", "canUpdate", "canDelete"]}]}}