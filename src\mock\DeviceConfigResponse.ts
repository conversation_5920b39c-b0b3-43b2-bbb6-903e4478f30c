export const deviceConfigresponse = {
  ipAddress: '***********',
  aclConfig:
    'ipAddress: ***********\nHostName: mla01milnhixd.netops.charter.com\nlocation: MILNHIXD - 200 Akamainui St, <PERSON><PERSON><PERSON>, HI 96789\ninterface_name: em0\ninterface_address: ***********/24\nManagement Address: ***********\ndescription: undefined\n\nDevice Configuration: \nset version 21.4R3.15\nset groups RESERVED_GROUP\nset groups BGP_IPV4\nset groups INTERFACE_ALL_GROUP interfaces <ae*> aggregated-ether-options lacp active\nset groups INTERFACE_ALL_GROUP interfaces <ae*> aggregated-ether-options lacp periodic fast\nset groups INTERFACE_ALL_GROUP interfaces <*> hold-time up 500\nset groups INTERFACE_ALL_GROUP interfaces <*> hold-time down 0\nset groups INTERFACE_ALL_GROUP interfaces <et*> hold-time up 500\nset groups INTERFACE_ALL_GROUP interfaces <et*> hold-time down 0\nset groups INTERFACE_ALL_GROUP interfaces <xe*> hold-time up 500\nset groups INTERFACE_ALL_GROUP interfaces <xe*> hold-time down 0\nset groups INTERFACE_ALL_GROUP interfaces <ge*> hold-time up 500\nset groups INTERFACE_ALL_GROUP interfaces <ge*> hold-time down 0\nset groups ISIS_L1_INTERFACE_GROUP protocols isis interface <*> level 1 ipv6-unicast-metric 1234567\nset groups ISIS_L1_INTERFACE_GROUP protocols isis interface <*> level 1 hello-authentication-key "$9$9gMHpBEM8X7-wXxHmTz6/"\nset groups ISIS_L1_INTERFACE_GROUP protocols isis interface <*> level 1 hello-authentication-type md5\nset groups ISIS_L1_INTERFACE_GROUP protocols isis interface <*> level 1 hello-interval 1\nset groups ISIS_L1_INTERFACE_GROUP protocols isis interface <*> level 2 disable\nset groups ISIS_L1_INTERFACE_GROUP protocols isis interface <*> hello-padding disable\nset groups ISIS_L1_INTERFACE_GROUP protocols isis interface <*> point-to-point\nset groups IBGP_IPV4_GROUP protocols bgp group <*> type internal\nset groups IBGP_IPV4_GROUP protocols bgp group <*> local-address ***********\nset groups IBGP_IPV4_GROUP protocols bgp group <*> family inet unicast\nset groups IBGP_IPV4_GROUP protocols bgp group <*> authentication-key "$9$8bK7VYDik.PQkqBEylMW"\nset groups IBGP_IPV4_GROUP protocols bgp group <*> graceful-restart\nset groups MARKET_RR_IPV4_NEIGHBOR_GROUP protocols bgp group <*> type internal\nset groups MARKET_RR_IPV4_NEIGHBOR_GROUP protocols bgp group <*> local-address ***********\nset groups MARKET_RR_IPV4_NEIGHBOR_GROUP protocols bgp group <*> family inet unicast\nset groups MARKET_RR_IPV4_NEIGHBOR_GROUP protocols bgp group <*> authentication-key "$9$JBUkmTz3CtOzFlMWLN-UjHmPQn/Ctp0"\nset groups MARKET_RR_IPV4_NEIGHBOR_GROUP protocols bgp group <*> peer-as 20001\nset groups MARKET_RR_IPV4_NEIGHBOR_GROUP protocols bgp group <*> graceful-restart\nset groups IBGP_IPV6_GROUP protocols bgp group <*> type internal\nset groups IBGP_IPV6_GROUP protocols bgp group <*> local-address 2602:107:4e1e::5\nset groups IBGP_IPV6_GROUP protocols bgp group <*> family inet6 unicast\nset groups IBGP_IPV6_GROUP protocols bgp group <*> authentication-key "$9$JBUkmTz3CtOzFlMWLN-UjHmPQn/Ctp0"\nset groups IBGP_IPV6_GROUP protocols bgp group <*> graceful-restart\nset groups INTERFACE_MPLS_GROUP interfaces <*> mtu 9192\nset groups INTERFACE_MPLS_GROUP interfaces <*> unit 0 family iso\nset groups INTERFACE_MPLS_GROUP interfaces <*> unit 0 family inet6 filter input NDP_IN_IPV6_FILTER\nset groups INTERFACE_MPLS_GROUP interfaces <*> unit 0 family mpls\nset groups INTERFACE_MPLS_GROUP interfaces ae25 mtu 9178\nset groups INTERFACE_NOMPLS_GROUP interfaces <*> mtu 9192\nset groups INTERFACE_NOMPLS_GROUP interfaces <*> unit 0 family iso\nset groups INTERFACE_NOMPLS_GROUP interfaces <*> unit 0 family inet6 filter input NDP_IN_IPV6_FILTER\nset groups UNTAGGED_INTERFACE_GROUP interfaces <*> mtu 1514\nset groups UNTAGGED_INTERFACE_GROUP interfaces <*> encapsulation ethernet-bridge\nset groups UNTAGGED_INTERFACE_GROUP interfaces <*> ether-options ethernet-switch-profile storm-control BUM_LIMITER_POLICY\nset groups EPON_TEST\nset groups OSPF_LDP_INTERFACE_GROUP protocols ospf area <*> interface <*> interface-type p2p\nset groups OSPF_LDP_INTERFACE_GROUP protocols ospf area <*> interface <*> metric 65000\nset groups OSPF_LDP_INTERFACE_GROUP protocols ospf area <*> interface <*> hello-interval 1\nset groups OSPF_LDP_INTERFACE_GROUP protocols ospf area <*> interface <*> authentication md5 1 key "$9$B69EyK-VwYgJwsT39A0O"\nset groups OSPF_LDP_INTERFACE_GROUP protocols ospf area <*> interface <*> ldp-synchronization\nset groups OSPF_DOWNSTREAM_INTERFACE_GROUP protocols ospf area <*> interface <*> interface-type p2p\nset groups OSPF_DOWNSTREAM_INTERFACE_GROUP protocols ospf area <*> interface <*> metric 65000\nset groups OSPF_DOWNSTREAM_INTERFACE_GROUP protocols ospf area <*> interface <*> hello-interval 1\nset groups OSPF_DOWNSTREAM_INTERFACE_GROUP protocols ospf area <*> interface <*> authentication md5 1 key "$9$B69EyK-VwYgJwsT39A0O"\nset groups TAGGED_INTERFACE_GROUP interfaces <*> flexible-vlan-tagging\nset groups TAGGED_INTERFACE_GROUP interfaces <*> mtu 1514\nset groups TAGGED_INTERFACE_GROUP interfaces <*> encapsulation flexible-ethernet-services\nset groups TAGGED_INTERFACE_GROUP interfaces <*> ether-options ethernet-switch-profile storm-control BUM_LIMITER_POLICY\nset groups OSPF_EDGE_GROUP routing-instances <*> protocols ospf area <*> interface <*> interface-type p2p\nset groups OSPF_EDGE_GROUP routing-instances <*> protocols ospf area <*> interface <*> metric 65000\nset groups OSPF_EDGE_GROUP routing-instances <*> protocols ospf area <*> interface <*> hello-interval 4\nset groups OSPF_EDGE_GROUP routing-instances <*> protocols ospf area <*> interface <*> authentication md5 1 key "$9$RvTElMX7-YgJX7JDjH5Tcyr"\nset system host-name mla01milnhixd\nset system root-authentication encrypted-password "$1$Z33h4ZIX$BsIDpeUpRGNx42P.88jIK/"\nset system login idle-timeout 15\nset system login class ADMIN_CLASS idle-timeout 15\nset system login class ADMIN_CLASS permissions all\nset system login class ENG_CLASS idle-timeout 15\nset system login class ENG_CLASS permissions all\nset system login class NOC_CLASS idle-timeout 15\nset system login class NOC_CLASS permissions secret\nset system login class NOC_CLASS permissions view\nset system login class NOC_CLASS permissions view-configuration\nset system login user ENG uid 2002\nset system login user ENG class ENG_CLASS\nset system login user NOC uid 2001\nset system login user NOC class NOC_CLASS\nset system login user admin uid 2000\nset system login user admin class ADMIN_CLASS\nset system login user admin authentication encrypted-password "$1$vfCuUz1Q$bSFd/fNKBXOqlN.ALoQIF/"\nset system login announcement "************************\\nmla01milnhixd\\nACX5448\\nMILNHIXD - 200 Akamainui St, Mililani, HI 96789\\n************************\\n"\nset system login message "***********************************WARNING*************************************\\n*                                                                             *\\n*  This is a private computer system. It is for authorized use only. Users    *\\n*  (authorized or unauthorized) have no explicit or implicit expectation of   *\\n*  privacy.                                                                   *\\n*                                                                             *\\n*  Any or all uses of this system and all files on this system may be         *\\n*  intercepted, monitored, recorded, copied, audited, inspected, and          *\\n*  disclosed to authorized site, law enforcement personnel, as well as        *\\n*  authorized officials of other agencies.  By using this system, the user    *\\n*  consents to such interception, monitoring, recording, copying, auditing,   *\\n*  inspection, and disclosure at the discretion of authorized site personnel. *\\n*                                                                             *\\n*  Unauthorized or improper use of this system may result in administrative   *\\n*  disciplinary action and civil and criminal penalties. By continuing to     *\\n*  use this system you indicate your awareness of and consent to these terms  *\\n*  and conditions of use. LOG OFF IMMEDIATELY if you do not agree to the      *\\n*  conditions stated in this warning.                                         *\\n*                                                                             *\\n******************Network Operations Center ***********************************\\n"\nset system services ftp\nset system services ssh root-login deny-password\nset system services ssh protocol-version v2\nset system services ssh max-sessions-per-connection 15\nset system services ssh connection-limit 20\nset system services telnet connection-limit 10\nset system services telnet rate-limit 5\nset system services netconf ssh\nset system domain-name netops.charter.com\nset system time-zone UTC\nset system default-address-selection\nset system no-redirects\nset system authentication-order tacplus\nset system ports console log-out-on-disconnect\nset system ports console type vt100\nset system name-server ************\nset system name-server ************\nset system tacplus-server ************ port 49\nset system tacplus-server ************ secret "$9$jDH.5n/A1ESqmTF36u0xNdwaGHqm3/t"\nset system tacplus-server ************ timeout 3\nset system tacplus-server ************ source-address ***********\nset system tacplus-server ************* port 49\nset system tacplus-server ************* secret "$9$bysgJikmF6C24ZDjH5ThSrKx-s24jkP"\nset system tacplus-server ************* timeout 3\nset system tacplus-server ************* source-address ***********\nset system accounting events interactive-commands\nset system accounting destination tacplus server ************ secret "$9$L5nx-woJU.fQNdsg4aiHuO1EeMxNd4JD"\nset system accounting destination tacplus server ************ source-address ***********\nset system accounting destination tacplus server ************* secret "$9$GeDHmz3/01EikPTQFAt8X7dgaDikQ39"\nset system accounting destination tacplus server ************* source-address ***********\nset system syslog user * any emergency\nset system syslog host *********** any info\nset system syslog host *********** facility-override local6\nset system syslog host *********** log-prefix mla01milnhixd.netops.charter.com\nset system syslog host *********** any info\nset system syslog host *********** facility-override local6\nset system syslog host *********** log-prefix mla01milnhixd.netops.charter.com\nset system syslog file cli_log interactive-commands any\nset system syslog file cli_log archive size 1m\nset system syslog file cli_log archive files 5\nset system syslog file firewall_filter firewall any\nset system syslog file firewall_filter archive size 1m\nset system syslog file firewall_filter archive files 5\nset system syslog file messages any notice\nset system syslog file messages authorization info\nset system syslog file messages archive size 10m\nset system syslog file messages archive files 5\nset system syslog time-format year\nset system processes web-management disable\nset system processes dhcp-service traceoptions file dhcp_logfile\nset system processes dhcp-service traceoptions file size 10m\nset system processes dhcp-service traceoptions level all\nset system processes dhcp-service traceoptions flag packet\nset system ntp boot-server *************\nset system ntp server *************\nset system ntp source-address ***********\nset chassis dump-on-panic\nset chassis aggregated-devices ethernet device-count 64\nset chassis aggregated-devices maximum-links 64\nset chassis fpc 0 pic 1 port 0 speed 40g\nset chassis fpc 0 pic 1 port 1 speed 40g\nset chassis fpc 0 pic 1 port 46 speed 10g\nset chassis fpc 0 pic 1 port 47 speed 10g\nset chassis alarm management-ethernet link-down ignore\nset interfaces apply-groups INTERFACE_ALL_GROUP\nset interfaces xe-0/0/0 description "[TYPE=MGMT][BW=1G][ZLOC=milnhixd-gia-tsv01][ZPRT=FE0/0][IP=**************?]"\nset interfaces xe-0/0/0 mtu 1500\nset interfaces xe-0/0/0 unit 0 family inet address **************/31\nset interfaces xe-0/0/1 description "[TYPE=CORE][BW=1G][ZLOC=mili4900_RRBB_vrf][ZPRT=g3/3][IP=************?]"\nset interfaces xe-0/0/1 mtu 1500\nset interfaces xe-0/0/1 unit 0 family inet address ************/30\nset interfaces xe-0/0/2 description "[TYPE=VIDEO][BW=10G][GRNT=PLHD][ZLOC=RSN_SW_MILNHIXD][ZPRT=1/52][IP=*********]"\nset interfaces xe-0/0/2 speed auto\nset interfaces xe-0/0/2 mtu 1514\nset interfaces xe-0/0/2 ether-options auto-negotiation\nset interfaces xe-0/0/2 ether-options link-mode full-duplex\nset interfaces xe-0/0/2 unit 0 family inet no-redirects\nset interfaces xe-0/0/2 unit 0 family inet address *************/25\nset interfaces xe-0/0/3 description "[STATUS=DECOM][DATE=02/19/2024][TICKET=AUTO][OWNER=CBOTOOL.AUTH][TYPE=VIDEO][BW=1G][ZLOC=mili-hosp-rr-sws1][ZPRT=45]"\nset interfaces xe-0/0/3 speed auto\nset interfaces xe-0/0/3 mtu 1500\nset interfaces xe-0/0/3 ether-options auto-negotiation\nset interfaces xe-0/0/3 ether-options link-mode full-duplex\nset interfaces xe-0/0/3 unit 0 family inet no-redirects\nset interfaces xe-0/0/3 unit 0 family inet address *************/25\ndeactivate interfaces xe-0/0/3\nset interfaces xe-0/0/4 description "[TYPE=AVAIL][DATE=01/16/2024][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/5 description "[TYPE=IT][ZLOC=dtvVodCore4948][ZPRT=G1/40][IP=************]"\nset interfaces xe-0/0/5 mtu 1500\nset interfaces xe-0/0/5 unit 0 family inet address ************/24\nset interfaces xe-0/0/6 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/6 disable\nset interfaces xe-0/0/7 apply-groups TAGGED_INTERFACE_GROUP\nset interfaces xe-0/0/7 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/7 disable\nset interfaces xe-0/0/8 description "[TYPE=P2P][BW=10G][ZLOC=MILI-DMZ-SWITCH1][ZPRT=XE-1/51]"\nset interfaces xe-0/0/8 speed 10g\nset interfaces xe-0/0/8 mtu 1500\nset interfaces xe-0/0/8 unit 0 family inet address ***********/24\nset interfaces xe-0/0/9 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/9 disable\nset interfaces xe-0/0/9 mtu 9178\nset interfaces xe-0/0/9 unit 0 family inet address ************/30\nset interfaces xe-0/0/10 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/10 disable\nset interfaces xe-0/0/11 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/11 disable\nset interfaces xe-0/0/12 description "[TYPE=CORE][BW=10G][ZLOC=sdv7609][ZPRT=TE9/2][IP=************]"\nset interfaces xe-0/0/12 speed 10g\nset interfaces xe-0/0/12 mtu 1500\nset interfaces xe-0/0/12 unit 0 family inet address *************/30\nset interfaces xe-0/0/13 description "[TYPE=ACCESS][BW=1G][ZLOC=BRIX NETWORK PERFORMANCE TOOL][ZPRT=SFP][IP=************]"\nset interfaces xe-0/0/13 unit 0 family inet filter output PROTECT_BRIX_SOURCE\nset interfaces xe-0/0/13 unit 0 family inet address ************/30\nset interfaces xe-0/0/14 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/14 disable\nset interfaces xe-0/0/15 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/15 disable\nset interfaces xe-0/0/16 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/16 disable\nset interfaces xe-0/0/16 speed 1g\nset interfaces xe-0/0/16 mtu 1514\nset interfaces xe-0/0/16 link-mode full-duplex\nset interfaces xe-0/0/17 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/17 disable\nset interfaces xe-0/0/17 speed 1g\nset interfaces xe-0/0/17 mtu 1500\nset interfaces xe-0/0/17 unit 0 family inet no-redirects\nset interfaces xe-0/0/17 unit 0 family inet address *************/30\nset interfaces xe-0/0/18 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/18 disable\nset interfaces xe-0/0/18 speed 1g\nset interfaces xe-0/0/18 mtu 1500\nset interfaces xe-0/0/19 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/19 disable\nset interfaces xe-0/0/20 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/20 disable\nset interfaces xe-0/0/21 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/21 disable\nset interfaces xe-0/0/22 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/22 disable\nset interfaces xe-0/0/23 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/23 disable\nset interfaces xe-0/0/24 description "[TYPE=CORE][BW=100M][ZLOC=milnhixd02x][ZPRT=Fa0/0][IP=*************]"\nset interfaces xe-0/0/24 ether-options auto-negotiation\nset interfaces xe-0/0/24 unit 0 family inet address *************/30\nset interfaces xe-0/0/25 description "[TYPE=AVAIL][DATE=01/18/2024][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/26 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/26 disable\nset interfaces xe-0/0/27 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/27 disable\nset interfaces xe-0/0/28 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/28 disable\nset interfaces xe-0/0/29 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/29 disable\nset interfaces xe-0/0/30 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/30 disable\nset interfaces xe-0/0/31 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/31 disable\nset interfaces xe-0/0/32 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/32 disable\nset interfaces xe-0/0/33 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/33 disable\nset interfaces xe-0/0/34 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/34 disable\nset interfaces xe-0/0/35 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/35 disable\nset interfaces xe-0/0/36 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/36 disable\nset interfaces xe-0/0/37 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/37 disable\nset interfaces xe-0/0/38 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/38 disable\nset interfaces xe-0/0/39 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/39 disable\nset interfaces xe-0/0/40 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/40 disable\nset interfaces xe-0/0/41 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/41 disable\nset interfaces xe-0/0/42 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/42 disable\nset interfaces xe-0/0/43 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/43 disable\nset interfaces xe-0/0/44 description "[TYPE=AVAIL][DATE=07/21/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/44 disable\nset interfaces xe-0/0/45 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/45 disable\nset interfaces xe-0/0/46 description "[TYPE=AVAIL][DATE=10/27/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/46 disable\nset interfaces xe-0/0/46 speed 10g\nset interfaces xe-0/0/47 description "[TYPE=AVAIL][DATE=11/06/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces xe-0/0/47 disable\nset interfaces xe-0/0/47 speed 10g\nset interfaces et-0/1/0 description "[TYPE=CORE][BW=40G][LAG=1][GRNT=PLHD][ZLOC=MILNHIXD01H][ZPRT=0/0/103][IP=*************]"\nset interfaces et-0/1/0 gigether-options 802.3ad ae1\nset interfaces et-0/1/1 description "[TYPE=CORE][BW=40G][LAG=2][GRNT=PLHD][ZLOC=MILNHIXD02H][ZPRT=0/0/103][IP=*************]"\nset interfaces et-0/1/1 gigether-options 802.3ad ae2\nset interfaces et-0/1/2 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces et-0/1/2 disable\nset interfaces et-0/1/3 description "[TYPE=AVAIL][DATE=11/03/2023][OWNER=CBOTOOL.AUTH]"\nset interfaces et-0/1/3 disable\nset interfaces ae0 disable\nset interfaces ae1 apply-groups INTERFACE_MPLS_GROUP\nset interfaces ae1 description "[TYPE=CORE][BW=40g][ZLOC=milnhixd01h][ZPRT=ae25][IP=*************]"\nset interfaces ae1 unit 0 family inet address *************/31\nset interfaces ae1 unit 0 family inet6 address 2602:107:4e1d:17::2/64\nset interfaces ae2 apply-groups INTERFACE_MPLS_GROUP\nset interfaces ae2 description "[TYPE=CORE][BW=40g][ZLOC=milnhixd02h][ZPRT=ae25][IP=**********]"\nset interfaces ae2 unit 0 family inet address *************/31\nset interfaces ae2 unit 0 family inet6 address 2602:107:4e1d:16::2/64\nset interfaces ae3 disable\nset interfaces ae4 disable\nset interfaces ae5 disable\nset interfaces ae6 disable\nset interfaces ae7 disable\nset interfaces ae8 disable\nset interfaces ae9 disable\nset interfaces ae10 disable\nset interfaces ae11 disable\nset interfaces ae12 disable\nset interfaces ae13 disable\nset interfaces ae14 disable\nset interfaces ae15 disable\nset interfaces ae16 disable\nset interfaces ae17 disable\nset interfaces ae18 disable\nset interfaces ae21 disable\nset interfaces ae22 disable\nset interfaces ae23 disable\nset interfaces ae24 disable\nset interfaces ae25 disable\nset interfaces ae26 disable\nset interfaces ae27 disable\nset interfaces ae28 disable\nset interfaces ae29 disable\nset interfaces ae30 disable\nset interfaces ae31 disable\nset interfaces ae32 disable\nset interfaces ae33 disable\nset interfaces ae34 disable\nset interfaces ae35 disable\nset interfaces ae36 disable\nset interfaces ae37 disable\nset interfaces ae38 disable\nset interfaces ae39 disable\nset interfaces ae40 disable\nset interfaces ae41 disable\nset interfaces ae42 disable\nset interfaces ae43 disable\nset interfaces ae44 disable\nset interfaces ae45 disable\nset interfaces ae46 disable\nset interfaces ae47 disable\nset interfaces ae48 disable\nset interfaces ae49 disable\nset interfaces ae50 disable\nset interfaces ae51 disable\nset interfaces ae52 disable\nset interfaces ae53 disable\nset interfaces ae54 disable\nset interfaces ae55 disable\nset interfaces ae56 disable\nset interfaces ae57 disable\nset interfaces ae58 disable\nset interfaces ae59 disable\nset interfaces ae60 disable\nset interfaces ae61 disable\nset interfaces ae62 disable\nset interfaces ae63 disable\nset interfaces em0 unit 0 family inet address ***********/24\nset interfaces em0 unit 0 family inet address ***********/24\nset interfaces irb unit 900 family inet address *************/30\nset interfaces lo0 description "[TYPE=LOOPBACK][NAME=mla01milnhixd][FUNC=EDGE][CLLI=MILNHIXD][SITE=TWC][AS=20001]"\nset interfaces lo0 unit 0 family inet filter input PROTECT_RE_IPV4_FILTER\nset interfaces lo0 unit 0 family inet address ***********/32 primary\nset interfaces lo0 unit 0 family iso address 49.1013.0721.2904.4003.00\nset interfaces lo0 unit 0 family inet6 filter input PROTECT_RE_IPV6_FILTER\nset interfaces lo0 unit 0 family inet6 address 2602:107:4e1e::5/128\nset snmp name mla01milnhixd.netops.charter.com\nset snmp location "MILNHIXD - 200 Akamainui St, Mililani, HI 96789"\nset snmp contact CBO-Core-IP-West\nset snmp filter-interfaces all-internal-interfaces\nset snmp client-list SNMP_CLIENTS **********/24\nset snmp client-list SNMP_CLIENTS ************/24\nset snmp client-list SNMP_CLIENTS ************/26\nset snmp client-list SNMP_CLIENTS **************/26\nset snmp client-list SNMP_CLIENTS ************/23\nset snmp client-list SNMP_CLIENTS ************/23\nset snmp client-list SNMP_CLIENTS 2001:1998:880:216::/64\nset snmp client-list SNMP_CLIENTS 2001:1998:8c0:216::/64\nset snmp client-list SNMP_CLIENTS 2607:f428:9330:1108::/64\nset snmp client-list SNMP_CLIENTS 2607:f428:9340:1108::/64\nset snmp client-list CBO_TOOLS_CLIENTS ***********/24\nset snmp client-list CBO_TOOLS_CLIENTS ************/27\nset snmp client-list CBO_TOOLS_CLIENTS **********/23\nset snmp client-list CBO_TOOLS_CLIENTS ***********/26\nset snmp client-list CBO_TOOLS_CLIENTS **********/24\nset snmp client-list CBO_TOOLS_CLIENTS **********/22\nset snmp client-list CBO_TOOLS_CLIENTS ***********/26\nset snmp client-list CBO_TOOLS_CLIENTS **********/23\nset snmp client-list CBO_TOOLS_CLIENTS **********/24\nset snmp client-list CBO_TOOLS_CLIENTS **********/22\nset snmp client-list CBO_TOOLS_CLIENTS *********/23\nset snmp client-list CBO_TOOLS_CLIENTS **********/23\nset snmp client-list CBO_TOOLS_CLIENTS 2600:6c7f:9330:5::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2600:6c7f:9330:49::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2600:6c7f:9330:108::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2600:6c7f:9330:1501::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2600:6c7f:9330:1502::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2600:6c7f:9340:208::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2600:6c7f:9340:801::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2600:6c7f:9340:1501::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2600:6c7f:9340:1502::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2607:f428:9330:108::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2607:f428:9340:208::/64\nset snmp client-list CBO_TOOLS_CLIENTS 2602:107:4c10:19a::/64\nset snmp community tFXe9N6jd authorization read-only\nset snmp community tFXe9N6jd client-list-name CBO_TOOLS_CLIENTS\nset snmp community Fm6A83qw authorization read-only\nset snmp community Fm6A83qw client-list-name SNMP_CLIENTS\nset snmp community "$pA*4#sH7y!" authorization read-only\nset snmp community "$pA*4#sH7y!" client-list-name SNMP_CLIENTS\nset snmp trap-options source-address ***********\nset snmp trap-group Fm6A83qw version v2\nset snmp trap-group Fm6A83qw targets ************\nset snmp trap-group Fm6A83qw targets 2607:f428:ffff:ffff::23\nset forwarding-options storm-control-profiles BUM_LIMITER_POLICY all bandwidth-percentage 75\nset forwarding-options storm-control-profiles BUM_LIMITER_POLICY all no-multicast\nset forwarding-options hash-key family inet layer-3\nset forwarding-options hash-key family inet layer-4\nset forwarding-options hash-key family inet6 layer-3\nset forwarding-options hash-key family inet6 layer-4\nset forwarding-options family inet6 route-accounting\nset forwarding-options dhcp-relay server-group DHCP_SERVER_GROUP_EPON **************\nset forwarding-options dhcp-relay group DHCP_SERVER_GROUP_EPON active-server-group DHCP_SERVER_GROUP_EPON\nset forwarding-options dhcp-relay group DHCP_SERVER_GROUP_EPON overrides bootp-support\nset forwarding-options dhcp-relay group DHCP_SERVER_GROUP_EPON relay-option option-60 equals hexadecimal ff forward-only\nset forwarding-options dhcp-relay group DHCP_SERVER_GROUP_EPON interface xe-0/0/7.0\nset forwarding-options dhcp-relay group DHCP_SERVER_GROUP_EPON interface xe-0/0/7.1\nset forwarding-options dhcp-relay group DHCP_INTERFACE_GROUP_EPON active-server-group DHCP_SERVER_GROUP_EPON\nset forwarding-options dhcp-relay group DHCP_INTERFACE_GROUP_EPON overrides bootp-support\nset forwarding-options dhcp-relay group DHCP_INTERFACE_GROUP_EPON relay-option option-60 equals hexadecimal ff forward-only\nset forwarding-options dhcp-relay group DHCP_INTERFACE_GROUP_EPON interface irb.138\nset forwarding-options dhcp-relay group DHCP_INTERFACE_GROUP_EPON interface irb.139\nset event-options policy FPC_POLICY events SYSTEM\nset event-options policy FPC_POLICY attributes-match SYSTEM.message matches "Alarm set: FPC color="\nset event-options policy FPC_POLICY then raise-trap\nset policy-options prefix-list TACACS_NETS ***********/32\nset policy-options prefix-list TACACS_NETS 2001:db8:ffff:ffff:ffff:ffff:ffff:ffff/128\nset policy-options prefix-list TACACS_NETS apply-path "system tacplus-server <*>"\nset policy-options prefix-list SNMP_IPV4_NETS apply-path "snmp client-list <*> <*.*>"\nset policy-options prefix-list SNMP_IPV6_NETS apply-path "snmp client-list <*> <*:*>"\nset policy-options prefix-list FTP_IPV4_NETS **********/32\nset policy-options prefix-list FTP_IPV4_NETS **********/32\nset policy-options prefix-list BGP_NETS ***********/32\nset policy-options prefix-list BGP_NETS 2001:db8:ffff:ffff:ffff:ffff:ffff:ffff/128\nset policy-options prefix-list BGP_NETS apply-path "protocols bgp group <*> neighbor <*>"\nset policy-options prefix-list OAHU_VIDEO_SOURCES *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES **************/30\nset policy-options prefix-list DHCP_NETS apply-path "forwarding-options dhcp-relay server-group <*> <*>"\nset policy-options prefix-list HAWAII_DIVISION_AS6534_NETS ************/22\nset policy-options prefix-list DEFAULT_IPV4_NETS 0.0.0.0/0\nset policy-options prefix-list DEFAULT_IPV6_NETS ::/0\nset policy-options prefix-list DOMAIN_NETS apply-path "system name-server <*>"\nset policy-options prefix-list NTP_NETS apply-path "system ntp server <*>"\nset policy-options prefix-list LOOPBACK_IPV4_NETS apply-path "interfaces lo0 unit 0 family inet address <*>"\nset policy-options prefix-list LOOPBACK_IPV6_NETS apply-path "interfaces lo0 unit 0 family inet6 address <*>"\nset policy-options prefix-list FTP_IPV6_NETS 2600:6c7f:9330:1502::156:3/128\nset policy-options prefix-list FTP_IPV6_NETS 2600:6c7f:9340:1502::188:3/128\nset policy-options prefix-list RPKI_NETS apply-path "routing-options validation group RPKI_VALIDATOR_GROUP session <*>"\nset policy-options prefix-list OAHU_VIDEO_SOURCES_IPV4_NETS *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES_IPV4_NETS *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES_IPV4_NETS *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES_IPV4_NETS *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES_IPV4_NETS *************/30\nset policy-options prefix-list OAHU_VIDEO_SOURCES_IPV4_NETS **************/30\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS **********/24\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ************/24\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/24\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/28\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/28\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/30\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/28\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/28\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/30\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ************/26\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS **************/26\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS **********/23\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/26\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS **********/24\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS **********/22\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/26\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS **********/23\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS **********/24\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS **********/22\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS *********/23\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/27\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ***********/27\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS **********/23\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ************/23\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS ************/23\nset policy-options prefix-list REMOTE_ACCESS_IPV4_NETS *************/26\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2001:1998:0:4::200/120\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2001:1998:880:216::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2001:1998:8c0:216::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9330:49::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9330:108::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9330:941::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9330:1501::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9330:1502::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9340:208::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9340:801::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9340:941::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9340:1501::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2600:6c7f:9340:1502::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2602:107:4c10:19a::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2607:f428:9330:108::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2607:f428:9330:1108::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2607:f428:9340:208::/64\nset policy-options prefix-list REMOTE_ACCESS_IPV6_NETS 2607:f428:9340:1108::/64\nset policy-options route-filter-list INTERFACE_TEST_IPV4_NETS ***********/16 orlonger\nset policy-options route-filter-list INTERFACE_TEST_IPV6_NETS fc00::/56 orlonger\nset policy-options policy-statement CORE_TO_EDGE term IPV4_DEFAULT from instance master\nset policy-options policy-statement CORE_TO_EDGE term IPV4_DEFAULT from route-filter 0.0.0.0/0 exact\nset policy-options policy-statement CORE_TO_EDGE term IPV4_DEFAULT then accept\nset policy-options policy-statement CORE_TO_EDGE term DENY_ALL then reject\nset policy-options policy-statement DEFAULT_ONLY term DEFAULT from route-filter 0.0.0.0/0 exact\nset policy-options policy-statement DEFAULT_ONLY term DEFAULT then accept\nset policy-options policy-statement DEFAULT_ONLY term DENY then reject\nset policy-options policy-statement ECMP_POLICY term ECMP_IPV4 from rib inet.0\nset policy-options policy-statement ECMP_POLICY term ECMP_IPV4 then load-balance per-packet\nset policy-options policy-statement ECMP_POLICY term ECMP_IPV6 from rib inet6.0\nset policy-options policy-statement ECMP_POLICY term ECMP_IPV6 then load-balance per-packet\nset policy-options policy-statement ECMP_POLICY term NO_ECMP then accept\nset policy-options policy-statement EDGE_TO_BGP term DENY_DEFAULT from route-filter 0.0.0.0/0 exact\nset policy-options policy-statement EDGE_TO_BGP term DENY_DEFAULT then reject\nset policy-options policy-statement EDGE_TO_BGP term ALLOW_EDGE from instance EDGE_INSTANCE\nset policy-options policy-statement EDGE_TO_BGP term ALLOW_EDGE then origin igp\nset policy-options policy-statement EDGE_TO_BGP term ALLOW_EDGE then next-hop self\nset policy-options policy-statement EDGE_TO_BGP term ALLOW_EDGE then accept\nset policy-options policy-statement EDGE_TO_CORE term IPV4_DENY_DEFAULT from route-filter 0.0.0.0/0 exact\nset policy-options policy-statement EDGE_TO_CORE term IPV4_DENY_DEFAULT then reject\nset policy-options policy-statement EDGE_TO_CORE term ALLOW_EDGE from instance EDGE_INSTANCE\nset policy-options policy-statement EDGE_TO_CORE term ALLOW_EDGE then accept\nset policy-options policy-statement ISIS_EXPORT_POLICY term ALLOW_LOOPBACK from interface lo0.0\nset policy-options policy-statement ISIS_EXPORT_POLICY term ALLOW_LOOPBACK then accept\nset policy-options policy-statement ISIS_EXPORT_POLICY term DENY_CONNECTED from protocol direct\nset policy-options policy-statement ISIS_EXPORT_POLICY term DENY_CONNECTED then reject\nset policy-options policy-statement ISIS_EXPORT_POLICY term ALLOW_ISIS from protocol isis\nset policy-options policy-statement ISIS_EXPORT_POLICY term ALLOW_ISIS then accept\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term DENY_DEFAULT from prefix-list-filter DEFAULT_IPV4_NETS exact\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term DENY_DEFAULT then reject\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term DENY_TEST_NETS from protocol direct\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term DENY_TEST_NETS from rib inet.0\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term DENY_TEST_NETS from route-filter-list INTERFACE_TEST_IPV4_NETS\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term DENY_TEST_NETS then reject\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term STATIC from protocol static\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term STATIC from rib inet.0\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term STATIC then community add AREA_COMMUNITY\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term STATIC then next-hop ***********\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term STATIC then accept\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term CONNECTED from protocol direct\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term CONNECTED from rib inet.0\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term CONNECTED then community add AREA_COMMUNITY\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term CONNECTED then next-hop ***********\nset policy-options policy-statement MARKET_RR_OUT_IPV4_POLICY term CONNECTED then accept\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term DENY_DEFAULT from prefix-list-filter DEFAULT_IPV6_NETS exact\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term DENY_DEFAULT then reject\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term DENY_TEST_NETS from protocol direct\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term DENY_TEST_NETS from rib inet6.0\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term DENY_TEST_NETS from route-filter-list INTERFACE_TEST_IPV6_NETS\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term DENY_TEST_NETS then reject\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term STATIC from protocol static\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term STATIC from rib inet6.0\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term STATIC then community add AREA_COMMUNITY\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term STATIC then next-hop 2602:107:4e1e::5\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term STATIC then accept\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term CONNECTED from protocol direct\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term CONNECTED from rib inet6.0\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term CONNECTED then community add AREA_COMMUNITY\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term CONNECTED then next-hop 2602:107:4e1e::5\nset policy-options policy-statement MARKET_RR_OUT_IPV6_POLICY term CONNECTED then accept\nset policy-options policy-statement METRO_RR_OUT_IPV4_POLICY term DENY_DEFAULT from route-filter 0.0.0.0/0 exact\nset policy-options policy-statement METRO_RR_OUT_IPV4_POLICY term DENY_DEFAULT then reject\nset policy-options policy-statement METRO_RR_OUT_IPV4_POLICY term CONNECTED from protocol direct\nset policy-options policy-statement METRO_RR_OUT_IPV4_POLICY term CONNECTED from rib inet.0\nset policy-options policy-statement METRO_RR_OUT_IPV4_POLICY term CONNECTED then community add ORIGINATION_COMMUNITY\nset policy-options policy-statement METRO_RR_OUT_IPV4_POLICY term CONNECTED then next-hop ***********\nset policy-options policy-statement METRO_RR_OUT_IPV4_POLICY term CONNECTED then accept\nset policy-options policy-statement SDV7609_IN_IPV4_POLICY term ACCEPT_ROUTES from prefix-list-filter OAHU_VIDEO_SOURCES_IPV4_NETS exact\nset policy-options policy-statement SDV7609_IN_IPV4_POLICY term ACCEPT_ROUTES then accept\nset policy-options policy-statement SDV7609_IN_IPV4_POLICY term DENY_ALL then reject\nset policy-options policy-statement SDV7609_OUT_IPV4_POLICY term DENY_ALL then reject\nset policy-options community AREA_COMMUNITY members 20001:1013\nset policy-options community ORIGINATION_COMMUNITY members 20001:1013\nset class-of-service interfaces xe-0/0/2 unit 0 rewrite-rules dscp default protocol mpls\nset firewall family inet filter PROTECT_BRIX_SOURCE interface-specific\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ***********/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ***********/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address *************/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address **********/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/32\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address *************/32\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address *************/32\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address **********/27\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/27\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/32\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ***********/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED from source-address ************/30\nset firewall family inet filter PROTECT_BRIX_SOURCE term TRUSTED then accept\nset firewall family inet filter PROTECT_BRIX_SOURCE term DENY then count DROP\nset firewall family inet filter PROTECT_BRIX_SOURCE term DENY then discard\nset firewall family inet filter PROTECT_RE_IPV4_FILTER interface-specific\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SNMP from source-prefix-list SNMP_IPV4_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SNMP from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SNMP from destination-port snmp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SNMP then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP_TRUSTED from source-prefix-list REMOTE_ACCESS_IPV4_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP_TRUSTED from protocol icmp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP_TRUSTED then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SSH from source-prefix-list REMOTE_ACCESS_IPV4_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SSH from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SSH from destination-port ssh\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SSH then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SSH_OUTBOUND from source-prefix-list REMOTE_ACCESS_IPV4_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SSH_OUTBOUND from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SSH_OUTBOUND from source-port ssh\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term SSH_OUTBOUND then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_SERVER from source-prefix-list DHCP_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_SERVER from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_SERVER from destination-port bootps\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_SERVER from destination-port bootpc\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_SERVER then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_CLIENT from source-address 0.0.0.0/32\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_CLIENT from destination-address ***************/32\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_CLIENT from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_CLIENT from source-port bootpc\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_CLIENT from destination-port bootps\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_CLIENT from destination-port bootpc\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BOOTP_CLIENT then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP from protocol icmp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP from icmp-type echo-request\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP from icmp-type echo-reply\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP from icmp-type unreachable\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP from icmp-type time-exceeded\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP then policer SMALL_BW_POLICER\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term ICMP then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term TRACEROUTE from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term TRACEROUTE from destination-port 33408-33535\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term TRACEROUTE then policer SMALL_BW_POLICER\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term TRACEROUTE then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BGP_SOURCE from source-prefix-list BGP_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BGP_SOURCE from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BGP_SOURCE from source-port bgp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BGP_SOURCE then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BGP_DESTINATION from source-prefix-list BGP_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BGP_DESTINATION from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BGP_DESTINATION from destination-port bgp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BGP_DESTINATION then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term OSPF from precedence internet-control\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term OSPF from protocol ospf\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term OSPF then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_SOURCE from precedence internet-control\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_SOURCE from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_SOURCE from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_SOURCE from source-port ldp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_SOURCE then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_DESTINATION from precedence internet-control\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_DESTINATION from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_DESTINATION from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_DESTINATION from destination-port ldp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term LDP_DESTINATION then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term PIM_ALLOW from destination-address **********/32\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term PIM_ALLOW from protocol pim\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term PIM_ALLOW then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term IGMP from destination-address *********/29\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term IGMP from destination-address **********/32\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term IGMP from destination-address **********/32\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term IGMP from protocol igmp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term IGMP then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_SOURCE from precedence internet-control\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_SOURCE from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_SOURCE from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_SOURCE from source-port 3784-3785\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_SOURCE from source-port 6784\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_SOURCE then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_DESTINATION from precedence internet-control\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_DESTINATION from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_DESTINATION from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_DESTINATION from destination-port 3784-3785\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_DESTINATION from destination-port 6784\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term BFD_DESTINATION then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP from source-prefix-list NTP_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP from source-port ntp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_SOURCE from source-prefix-list LOOPBACK_IPV4_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_SOURCE from destination-prefix-list LOOPBACK_IPV4_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_SOURCE from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_SOURCE from source-port ntp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_SOURCE then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_DESTINATION from source-prefix-list LOOPBACK_IPV4_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_DESTINATION from destination-prefix-list LOOPBACK_IPV4_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_DESTINATION from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_DESTINATION from destination-port ntp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term NTP_LOCAL_DESTINATION then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term DOMAIN from source-prefix-list DOMAIN_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term DOMAIN from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term DOMAIN from source-port domain\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term DOMAIN then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term TACACS from source-prefix-list TACACS_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term TACACS from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term TACACS from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term TACACS from source-port 49\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term TACACS then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term FTP from source-prefix-list FTP_IPV4_NETS\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term FTP from protocol tcp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term FTP from source-port 21\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term FTP from source-port 20\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term FTP then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term RSVP from precedence internet-control\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term RSVP from protocol rsvp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term RSVP then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term MPLS_PING from precedence internet-control\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term MPLS_PING from protocol udp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term MPLS_PING from source-port 3503\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term MPLS_PING then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term VRRP from precedence internet-control\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term VRRP from protocol vrrp\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term VRRP then accept\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term OTHER then count DROP\nset firewall family inet filter PROTECT_RE_IPV4_FILTER term OTHER then discard\nset firewall family inet filter VIDEO_OUT_IPV4_FILTER term NTP from source-address *************/32\nset firewall family inet filter VIDEO_OUT_IPV4_FILTER term TRUSTED from source-address *************/32\nset firewall family inet6 filter NDP_IN_IPV6_FILTER interface-specific\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term PERMIT_VALID_ICMP from next-header icmpv6\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term PERMIT_VALID_ICMP from hop-limit 255\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term PERMIT_VALID_ICMP then count PERMIT_VALID_ICMP\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term PERMIT_VALID_ICMP then accept\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term REJECT_INVALID_ICMP from next-header icmpv6\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term REJECT_INVALID_ICMP from icmp-type neighbor-advertisement\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term REJECT_INVALID_ICMP from icmp-type neighbor-solicit\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term REJECT_INVALID_ICMP from icmp-type router-solicit\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term REJECT_INVALID_ICMP from icmp-type router-advertisement\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term REJECT_INVALID_ICMP from icmp-type redirect\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term REJECT_INVALID_ICMP then count REJECT_INVALID_ICMP\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term REJECT_INVALID_ICMP then discard\nset firewall family inet6 filter NDP_IN_IPV6_FILTER term ALLOW then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER interface-specific\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SNMP from source-prefix-list SNMP_IPV6_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SNMP from next-header udp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SNMP from destination-port snmp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SNMP then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6_TRUSTED from source-prefix-list REMOTE_ACCESS_IPV6_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6_TRUSTED from next-header icmpv6\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6_TRUSTED then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term IPV6_ND from next-header icmpv6\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term IPV6_ND from hop-limit 255\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term IPV6_ND then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6 from next-header icmpv6\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6 from icmp-type echo-request\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6 from icmp-type echo-reply\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6 from icmp-type time-exceeded\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6 from icmp-type destination-unreachable\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6 from icmp-type packet-too-big\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6 from icmp-type parameter-problem\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6 then policer SMALL_BW_POLICER\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term ICMPV6 then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term TRACEROUTE from next-header udp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term TRACEROUTE from destination-port 33408-33535\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term TRACEROUTE then policer SMALL_BW_POLICER\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term TRACEROUTE then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SSH from source-prefix-list REMOTE_ACCESS_IPV6_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SSH from next-header tcp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SSH from destination-port ssh\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SSH then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SSH_OUTBOUND from source-prefix-list REMOTE_ACCESS_IPV6_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SSH_OUTBOUND from next-header tcp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SSH_OUTBOUND from source-port ssh\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term SSH_OUTBOUND then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term BGP_SOURCE from source-prefix-list BGP_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term BGP_SOURCE from next-header tcp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term BGP_SOURCE from source-port bgp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term BGP_SOURCE then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term BGP_DESTINATION from source-prefix-list BGP_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term BGP_DESTINATION from next-header tcp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term BGP_DESTINATION from destination-port bgp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term BGP_DESTINATION then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term OSPF from next-header ospf\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term OSPF from traffic-class cs6\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term OSPF then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP from source-prefix-list NTP_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP from next-header udp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP from source-port ntp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_SOURCE from source-prefix-list LOOPBACK_IPV6_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_SOURCE from destination-prefix-list LOOPBACK_IPV6_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_SOURCE from next-header udp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_SOURCE from source-port ntp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_SOURCE then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_DESTINATION from source-prefix-list LOOPBACK_IPV6_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_DESTINATION from destination-prefix-list LOOPBACK_IPV6_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_DESTINATION from next-header udp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_DESTINATION from destination-port ntp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term NTP_LOCAL_DESTINATION then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term DOMAIN from source-prefix-list DOMAIN_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term DOMAIN from next-header udp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term DOMAIN from source-port domain\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term DOMAIN then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term TACACS from source-prefix-list TACACS_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term TACACS from next-header udp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term TACACS from next-header tcp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term TACACS from source-port 49\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term TACACS then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term FTP from source-prefix-list FTP_IPV6_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term FTP from next-header tcp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term FTP from source-port 21\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term FTP from source-port 20\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term FTP then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term VRRP from next-header vrrp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term VRRP from traffic-class cs6\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term VRRP then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term RPKI_VALIDATOR from source-prefix-list RPKI_NETS\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term RPKI_VALIDATOR from next-header tcp\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term RPKI_VALIDATOR from source-port 8323\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term RPKI_VALIDATOR then accept\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term OTHER then count DROP\nset firewall family inet6 filter PROTECT_RE_IPV6_FILTER term OTHER then discard\nset firewall policer SMALL_BW_POLICER if-exceeding bandwidth-limit 256k\nset firewall policer SMALL_BW_POLICER if-exceeding burst-size-limit 20k\nset firewall policer SMALL_BW_POLICER then discard\nset routing-instances EDGE_INSTANCE instance-type virtual-router\nset routing-instances EDGE_INSTANCE routing-options router-id ***********\nset routing-instances EDGE_INSTANCE routing-options instance-import CORE_TO_EDGE\nset routing-instances EDGE_INSTANCE protocols ospf area 0.0.0.0 interface xe-0/0/0.0\nset routing-instances EDGE_INSTANCE protocols ospf export DEFAULT_ONLY\nset routing-instances EDGE_INSTANCE interface xe-0/0/0.0\nset routing-options router-id ***********\nset routing-options autonomous-system 20001\nset routing-options graceful-restart\nset routing-options static route ***********/24 next-hop *************\nset routing-options static route **************/26 next-hop *************\nset routing-options static route ************/28 next-hop ************\nset routing-options instance-import EDGE_TO_CORE\nset routing-options forwarding-table export ECMP_POLICY\nset routing-options multicast ssm-groups *********/8\nset routing-options multicast ssm-groups *********/8\nset protocols bgp group METRO_RR_IPV4_NEIGHBOR_GROUP apply-groups MARKET_RR_IPV4_NEIGHBOR_GROUP\nset protocols bgp group METRO_RR_IPV4_NEIGHBOR_GROUP export METRO_RR_OUT_IPV4_POLICY\nset protocols bgp group METRO_RR_IPV4_NEIGHBOR_GROUP export EDGE_TO_BGP\nset protocols bgp group MARKET_RR_IPV4_NEIGHBOR_GROUP apply-groups IBGP_IPV4_GROUP\nset protocols bgp group MARKET_RR_IPV4_NEIGHBOR_GROUP export MARKET_RR_OUT_IPV4_POLICY\nset protocols bgp group MARKET_RR_IPV4_NEIGHBOR_GROUP neighbor ************* description kmlahi0701r\nset protocols bgp group MARKET_RR_IPV4_NEIGHBOR_GROUP neighbor ************* description milnhixd01r\nset protocols bgp group MARKET_RR_IPV6_NEIGHBOR_GROUP apply-groups IBGP_IPV6_GROUP\nset protocols bgp group MARKET_RR_IPV6_NEIGHBOR_GROUP export MARKET_RR_OUT_IPV6_POLICY\nset protocols bgp group MARKET_RR_IPV6_NEIGHBOR_GROUP neighbor 2605:e000::b:2 description kmlahi0701r\nset protocols bgp group MARKET_RR_IPV6_NEIGHBOR_GROUP neighbor 2605:e000::b:1 description milnhixd01r\nset protocols bgp group SDV7609_neighbor type external\nset protocols bgp group SDV7609_neighbor local-address *************\nset protocols bgp group SDV7609_neighbor import SDV7609_IN_IPV4_POLICY\nset protocols bgp group SDV7609_neighbor export SDV7609_OUT_IPV4_POLICY\nset protocols bgp group SDV7609_neighbor peer-as 65200\nset protocols bgp group SDV7609_neighbor neighbor *************\nset protocols bgp traceoptions file bgp_log\nset protocols bgp traceoptions file size 1m\nset protocols bgp traceoptions file files 5\nset protocols bgp traceoptions flag open\nset protocols bgp traceoptions flag state\nset protocols bgp mtu-discovery\nset protocols bgp log-updown\nset protocols bgp bgp-error-tolerance\nset protocols bgp graceful-restart stale-routes-time 360\nset protocols igmp traceoptions file igmp_log\nset protocols igmp traceoptions file size 1m\nset protocols igmp traceoptions file files 5\nset protocols igmp traceoptions flag general\nset protocols igmp interface xe-0/0/2.0 version 3\nset protocols igmp interface xe-0/0/2.0 static group ************* source *************\nset protocols igmp interface ae1.0 version 3\nset protocols igmp interface ae2.0 version 3\nset protocols isis interface ae1.0 apply-groups ISIS_L1_INTERFACE_GROUP\nset protocols isis interface ae1.0 level 1 metric 65000\nset protocols isis interface ae2.0 apply-groups ISIS_L1_INTERFACE_GROUP\nset protocols isis interface ae2.0 level 1 metric 65000\nset protocols isis interface lo0.0 passive\nset protocols isis level 1 wide-metrics-only\nset protocols isis level 2 disable\nset protocols isis traceoptions flag state\nset protocols isis traceoptions flag error\nset protocols isis traceoptions file isis_log\nset protocols isis traceoptions file size 1m\nset protocols isis traceoptions file files 5\nset protocols isis no-ipv4-routing\nset protocols isis ignore-attached-bit\nset protocols isis topologies ipv6-unicast\nset protocols isis overload timeout 180\nset protocols isis overload advertise-high-metrics\nset protocols l2circuit traceoptions file l2circuit_log\nset protocols l2circuit traceoptions flag connections\nset protocols l2circuit traceoptions flag error\nset protocols l2circuit traceoptions flag state\nset protocols l2circuit traceoptions flag fec\nset protocols l2circuit traceoptions flag route\nset protocols ldp traceoptions file ldp_log\nset protocols ldp traceoptions file size 1m\nset protocols ldp traceoptions file files 5\nset protocols ldp traceoptions flag error\nset protocols ldp traceoptions flag state\nset protocols ldp track-igp-metric\nset protocols ldp transport-address router-id\nset protocols ldp interface ae1.0\nset protocols ldp interface ae2.0\nset protocols ldp interface lo0.0\nset protocols ldp igp-synchronization holddown-interval 10\nset protocols ldp log-updown trap enable\nset protocols mpls traceoptions file mpls_log\nset protocols mpls traceoptions file size 1m\nset protocols mpls traceoptions file files 5\nset protocols mpls traceoptions flag error\nset protocols mpls interface lo0.0\nset protocols mpls interface ae1.0\nset protocols mpls interface ae2.0\nset protocols ospf area ********* interface lo0.0 passive\nset protocols ospf area ********* interface ae1.0 apply-groups OSPF_LDP_INTERFACE_GROUP\nset protocols ospf area ********* interface ae1.0 metric 65000\nset protocols ospf area ********* interface ae2.0 apply-groups OSPF_LDP_INTERFACE_GROUP\nset protocols ospf area ********* interface ae2.0 metric 65000\nset protocols ospf area 0.0.0.0 interface xe-0/0/5.0 passive\nset protocols ospf traceoptions file ospf_log\nset protocols ospf traceoptions file size 1m\nset protocols ospf traceoptions file files 5\nset protocols ospf traceoptions flag state\nset protocols ospf traceoptions flag error\nset protocols ospf overload timeout 180\nset protocols ospf graceful-restart restart-duration 120\nset protocols ospf graceful-restart notify-duration 40\nset protocols pim family inet6 disable\nset protocols pim traceoptions file pim_log\nset protocols pim traceoptions file size 1m\nset protocols pim traceoptions file files 5\nset protocols pim traceoptions flag general\nset protocols pim interface xe-0/0/2.0 family inet\nset protocols pim interface xe-0/0/2.0 mode sparse\nset protocols pim interface xe-0/0/8.0 mode sparse\nset protocols pim interface ae1.0 mode sparse\nset protocols pim interface ae2.0 mode sparse\nset protocols pim interface xe-0/0/12.0 mode sparse\nset protocols lldp traceoptions file lldp_log\nset protocols lldp traceoptions file size 1m\nset protocols lldp traceoptions file files 5\nset protocols lldp traceoptions flag protocol\nset protocols lldp traceoptions flag interface\nset protocols lldp management-address ***********\nset protocols lldp port-id-subtype interface-name\nset protocols lldp neighbour-port-info-display port-id\nset protocols lldp interface all\nset protocols rstp disable\nset protocols mstp disable\nset protocols vstp disable\n',
};
