import React, { useCallback, useEffect, useState } from 'react';
import { debounce } from 'lodash';
import { IconButton, InputAdornment } from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';

import { useApiService } from 'api/ApiService/context';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';

interface SecretValueFieldProps {
  isPasswordPolicyRequired: boolean;
  policyId: string;
  policyDescription: string;
  label: string;
  value: string;
  name: string;
  helperText: string;
  error: boolean;
  handleChange: (event: any) => void;
  handleBlur: (event: any) => void;
  setPasswordValidStatus: (isValid: boolean) => void;
}

const SecretValueField: React.FC<SecretValueFieldProps> = ({
  isPasswordPolicyRequired,
  policyId,
  policyDescription,
  label,
  value,
  name,
  helperText,
  error,
  handleChange,
  handleBlur,
  setPasswordValidStatus,
}) => {
  const { apiSecretsManagement } = useApiService();
  const [fieldErrors, setFieldErrors] = useState<string[]>([]);
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const validateSecretValue = useCallback((secretValue: string, policyId: string) => {
    if (secretValue) {
      let payload = {
        policyId,
        secret: secretValue,
      };

      apiSecretsManagement.validateSecretPassword(payload).then((res) => {
        if (res.status) {
          if (!res.data.valid) {
            setPasswordValidStatus(false);
            setFieldErrors(res.data?.message ?? []);
          } else {
            setFieldErrors([]);
            setPasswordValidStatus(true);
          }
        } else {
          setPasswordValidStatus(false);
          setFieldErrors(['Password not meeting the Password policy criteria']);
        }
      });
    }
  }, []);

  let debounceValidateSecretValue = useCallback(debounce(validateSecretValue, 1000), [validateSecretValue]);

  useEffect(() => {
    if (isPasswordPolicyRequired) {
      setPasswordValidStatus(false);
      value && debounceValidateSecretValue(value, policyId);
    } else {
      setFieldErrors([]);
      setPasswordValidStatus(true);
    }
  }, [value, policyId, isPasswordPolicyRequired]);

  const getHelperText = () => {
    if (isPasswordPolicyRequired && !policyId) {
      return 'Please select Secret Policy';
    } else if (helperText) {
      return helperText;
    } else if (fieldErrors.length) {
      return fieldErrors.join('\n');
    } else if (policyDescription && !value) {
      return policyDescription;
    }
    return ' ';
  };

  //JSX
  return (
    <NblTextField
      mandatory
      type={showPassword ? 'text' : 'password'}
      label={label}
      placeholder="Enter"
      name={name}
      value={value}
      handleChange={handleChange}
      handleBlur={handleBlur}
      helperText={getHelperText()}
      error={error || Boolean(fieldErrors.length)}
      disabled={isPasswordPolicyRequired && !policyId}
      endAdornment={
        <InputAdornment position="end">
          <IconButton onClick={() => setShowPassword((show) => !show)} onMouseDown={(event) => event.preventDefault()} edge="end">
            {showPassword ? <VisibilityOff /> : <Visibility />}
          </IconButton>
        </InputAdornment>
      }
    />
  );
};

export default SecretValueField;
