import React from 'react';
import { StyledTooltip } from './styled';

interface NblTooltipProps {
  tooltipMessage: React.ReactNode;
  open?: boolean;
  children: React.ReactElement;
  onClose?: (event: React.SyntheticEvent | Event) => void;
}

const NblTooltip: React.FC<NblTooltipProps> = ({ tooltipMessage, open, children, onClose }) => (
  <StyledTooltip title={tooltipMessage} open={open} onClose={onClose} arrow>
    <span style={{ pointerEvents: 'auto' }}>{children}</span>
  </StyledTooltip>
);

export default NblTooltip;
