import { render, fireEvent, waitFor } from '@testing-library/react';
import Autocomplete from './';

describe('Autocomplete Component', () => {
  const OPTIONS = [{ label: 'Testing', value: 'Testing' }];

  test('Should render Autocomplete with options', async () => {
    const onChange = jest.fn();

    const { getByText, getByRole } = render(
      <Autocomplete data-testid="Test" label="test" options={OPTIONS} name="test" onChange={onChange} value={''} placeholder={''} />
    );

    const autoCompleteTextField = getByRole('combobox');

    fireEvent.mouseDown(autoCompleteTextField);

    await waitFor(() => {
      OPTIONS.forEach((option) => {
        expect(getByText(option.label)).toBeInTheDocument();
      });
    });

    fireEvent.click(getByText('Testing'));

    await waitFor(() => {
      expect(onChange).toHaveBeenCalledTimes(1);
    });
  });
});
