export const VMSizeRange = {
  noOfCores: {
    minValue: 4,
    maxValue: 32,
  },
  memory: {
    minValue: 4,
    maxValue: 64,
  },
  root: {
    minValue: 40,
    maxValue: 900,
  },
  home: {
    minValue: 2,
    maxValue: 900,
  },
  opt: {
    minValue: 8,
    maxValue: 900,
  },
  var: {
    minValue: 4,
    maxValue: 900,
  },
  varLog: {
    minValue: 4,
    maxValue: 900,
  },
  varLogAudit: {
    minValue: 4,
    maxValue: 900,
  },
  tmp: {
    minValue: 2,
    maxValue: 900,
  },
  varTmp: {
    minValue: 2,
    maxValue: 900,
  },
  customVolume: {
    minValue: 80,
    maxValue: 1000,
  },
  customMemory: {
    minValue: 4,
    maxValue: 64,
  },
  customMemoryWindows: {
    minValue: 8,
    maxValue: 64,
  },
  customCore: {
    minValue: 4,
    maxValue: 28,
  },
  vmSequence: {
    minValue: 1,
    maxValue: 999,
  },
};
