import { styled } from '@mui/material/styles';
import { Radio as MuiRadio, FormControlLabel } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledFormControlLabel = styled(FormControlLabel)<{ theme?: NebulaTheme; disabled?: boolean }>(({ theme, disabled }) => {
  const { typography, palette } = theme;
  const { radioButton } = palette;
  return {
    '&.MuiFormControlLabel-root': {
      color: radioButton.color,
      display: 'block',
      cursor: disabled ? 'not-allowed' : 'pointer',
    },
    '& .MuiTypography-root': {
      ...typography.subtitle1,
    },
  };
});

export const StyledRadio = styled(MuiRadio)<{ theme?: NebulaTheme }>(({ theme, color }) => {
  const { palette } = theme;
  const { radioButton } = palette;
  return {
    '&.MuiRadio-colorPrimary': {
      color: radioButton.border,
    },
    '&.MuiRadio-colorError': {
      color: radioButton.errorColor,
    },
    '&.Mui-disabled': {
      color: `${radioButton.disabled} !important`,
    },
    '&:hover': {
      backgroundColor: 'transparent',
      color: color === 'error' ? radioButton.hoverErrorColor : radioButton.hoverSelectColor,
    },
  };
});
