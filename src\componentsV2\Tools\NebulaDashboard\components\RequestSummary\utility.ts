// eslint-disable-next-line
import { requestStatusType } from '../../../../../api/ApiService/type';
// eslint-disable-next-line
import { CatalogWithSummary } from '../../utils/types';

export const getTableData = (catalogwithSummary: CatalogWithSummary[][], catalogLevel: number) => {
  const filterByDate = (a: requestStatusType, b: requestStatusType) => {
    //sort the requests in descending order , by date
    if (a.date < b.date) {
      return +1;
    } else if (a.date > b.date) {
      return -1;
    } else return 0;
  };

  let catalog1: [] | string[], catalog3: [] | string[], catalog4: [] | string[];

  const data = catalogwithSummary[catalogLevel].map((catalog) => {
    const definedRequests =
      catalog.requestListByRequestType && catalog.requestListByRequestType.filter((req): req is requestStatusType => req !== null);
    if (catalogLevel === 1) {
      catalog1 = [catalog.value];
    } else if (catalogLevel === 2) {
      catalog3 = catalogwithSummary[catalogLevel + 1].filter((item) => item.parentId === catalog.id).map((catalog) => catalog.value);
    } else if (catalogLevel === 3) {
      catalog3 = [catalog.value];
    } else if (catalogLevel === 4) {
      catalog4 = [catalog.value];
    }

    return {
      catalogName: catalog.name,
      allRequestMonthlyData:
        catalog.summary &&
        catalog.summary.reduce((acc: any, { month, count }) => {
          acc[month] = count;
          return acc;
        }, {}),
      allRequests: definedRequests && definedRequests.sort(filterByDate).slice(0, 10),
      id: catalog.id,
      catalog1: catalog1,
      catalog3: catalog3,
      catalog4: catalog4,
      parentId: catalog.parentId,
    };
  });

  const allZeroes = data?.every(
    (item) => item?.allRequestMonthlyData && Object.values(item.allRequestMonthlyData).every((value) => value === 0)
  );

  return allZeroes ? [] : data;
};
