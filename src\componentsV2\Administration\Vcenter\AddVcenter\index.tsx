import React, { useState } from 'react';
// eslint-disable-next-line no-unused-vars
import NblFormContainer, { NblFormHelpers } from 'sharedComponents/NblContainers/NblFormContainer';
// eslint-disable-next-line no-unused-vars
import { ComponentType, FormProps } from 'types';
import { isIconDefined } from 'utils/common';
import { AddvCenterIcon } from 'assets/images/icons/custom-icons';
import { AdminCatalogdetails } from 'types/Interfaces/AdminTileDetails';
import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import AddVCenterForm from './AddVCenterForm';
import NblConfirmPopUp from 'sharedComponents/NblConfirmPopUp';
import { AddVCenterSchema } from 'yupSchema/AddVCenterSchema';
import { useApiService } from 'api/ApiService/context';
import { useDispatch } from 'react-redux';
import { showSpinner } from 'store/reducers/spinner';
import ViewDetails from '../ViewDetails';
import withAdminPermissions from 'hoc/withAdminPermissions';

export type FormValues = {
  domain: string;
  cloudDatacenter: string;
  vCenterName: string;
  vCenterHost: string;
  vCenterPort: string;
  vCenterProtocol: string;
  vCenterUser: string;
  vCenterPassword: string;
};

interface AddVCenterProps extends FormProps {
  metaData?: AdminCatalogdetails;
}

const AddVCenter: React.FC<AddVCenterProps> & ComponentType = ({ metaData }) => {
  const Icon = metaData && isIconDefined(metaData.icon);
  const [showConfirmPopup, setShowConfirmPopup] = useState(false);
  const [LookupPayload, setLookupPayload] = useState<any>(null);
  const [LookupResponse, setLookupResponse] = useState<any>(null);
  const [showViewDetails, setShowViewDetails] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);

  const { apiVCenterService } = useApiService();
  const initialValues = {
    domain: '',
    cloudDatacenter: '',
    vCenterName: '',
    vCenterHost: '',
    vCenterPort: '',
    vCenterProtocol: '',
    vCenterUser: '',
    vCenterPassword: '',
  };

  const dispatch = useDispatch();

  const handleSubmitForm = (values: FormValues, helpers: NblFormHelpers<FormValues>) => {
    const encodedUsername = btoa(values.vCenterUser);
    const encodedPassword = btoa(values.vCenterPassword);

    const payload = {
      cloudName: values.cloudDatacenter,
      domain: values.domain,
      hostname: values.vCenterHost,
      password: encodedPassword,
      port: Number(values.vCenterPort),
      protocol: values.vCenterProtocol,
      username: encodedUsername,
      vcenterName: values.vCenterName,
    };
    dispatch(showSpinner({ message: 'Looking for vCenter...', status: true }));
    apiVCenterService
      .lookUpVCenter(payload)
      .then((res) => {
        setLookupPayload(values);
        setLookupResponse(res.data);
        setShowConfirmPopup(true);
      })
      .finally(() => {
        helpers.setSubmitting(false);
        dispatch(showSpinner({ message: '', status: false }));
      });
  };
  const handleConfigure = () => {
    setShowConfirmPopup(false);
    setFormSubmitted(true);
    setShowViewDetails(true);
  };

  return (
    <>
      {!formSubmitted && (
        <NblFormContainer<FormValues>
          title={metaData?.name || 'Add VCenter'}
          Icon={Icon || AddvCenterIcon}
          caption={'Fill the necessary details needed to Create VCenter'}
          formInitialValues={initialValues}
          formValidationSchema={AddVCenterSchema}
          steps={[
            {
              caption: '',
              errorFields: [''],
              icon: '',
              status: 'pending',
              title: '',
            },
          ]}
          formType="simple"
          onSubmit={handleSubmitForm}
          showPreview={false}
          submitText="Lookup"
        >
          <AddVCenterForm />
        </NblFormContainer>
      )}
      {showViewDetails && LookupPayload && LookupResponse && <ViewDetails payload={LookupPayload} response={LookupResponse} />}
      <NblConfirmPopUp
        title="Stamp VCenter Found"
        content={`Click "Configure" to Configure this VCenter in Nebula`}
        submitText="Configure"
        cancelText="Cancel"
        open={showConfirmPopup && LookupResponse}
        onClose={() => {
          setShowConfirmPopup(false);
          setFormSubmitted(false);
        }}
        onSubmit={handleConfigure}
        showCloseIcon
      />
    </>
  );
};

AddVCenter.type = ADMIN_TILE_PERMISSION_TYPE.form;

export default withAdminPermissions(AddVCenter);
