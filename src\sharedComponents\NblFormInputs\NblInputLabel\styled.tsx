import { styled } from '@mui/system';
import { InputLabel } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledInputLabel = styled(InputLabel)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography, palette } = theme;
  return {
    '&.MuiInputLabel-root': {
      ...typography.subtitle2,
      fontWeight:typography.fontWeightRegular,
      color: palette.primary.main,
    },
    '&.Mui-focused': {
      color: palette.primary.main,
    },
    '&.Mui-disabled': {
      opacity: 0.3,
    },
  };
});

export const StyledMandatoryLabel = styled('span')(({ theme }) => {
  const { tertiary } = theme.palette;
  return {
    color: tertiary.shade3.medium,
    marginLeft: '2px',
  };
});
