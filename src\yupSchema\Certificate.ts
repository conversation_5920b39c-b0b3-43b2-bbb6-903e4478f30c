import * as yup from 'yup';
import uniq from 'lodash/uniq';
import { yupMatchesParams } from 'utils/common';

export const subjectAlternateNamesSchema = () =>
  yup.string().test('subjectAlternateNames', yupMatchesParams.subjectAlternateName.errorMessage, function (value) {
    if (value && yupMatchesParams.subjectAlternateName.pattern.test(value)) {
      return true;
    }
    return false;
  });

const CertificatevalidationSchema = yup.object().shape({
  domain: yup.string(),
  projectName: yup.string().required('Project is required'),
  application: yup.string().required('Application is required'),
  environment: yup.string().required('Environment name is required'),
  appId: yup.string().required('AppId is required'),

  applicationName: yup
    .string()
    .trim()
    .required('Application Name is required')
    .matches(yupMatchesParams.alphaNumericWithHyphenUnderscrore.pattern, yupMatchesParams.alphaNumericWithHyphenUnderscrore.errorMessage),
  certificateName: yup
    .string()
    .matches(yupMatchesParams.alphaNumericWithHyphenUnderscrore.pattern, yupMatchesParams.alphaNumericWithHyphenUnderscrore.errorMessage)
    .required('Certificate Name is required'),

  subjectAlternateNames: yup
    .array()
    .of(subjectAlternateNamesSchema())
    .test('subjectAlternateNames', 'Please enter atleast one Subject Alternate Name', function (values) {
      if (!values?.length) {
        return false;
      } else {
        const subjectName = values || [];
        const hasUnique = subjectName.length === uniq(subjectName).length;
        if (!hasUnique) {
          return this.createError({ message: 'Duplicate Subject Alternate Name are not allowed' });
        }
      }
      return true;
    }),

  certPassword: yup
    .string()
    .trim()
    .required('Certificate Password is required')
    .matches(yupMatchesParams.certificatePassword.pattern, yupMatchesParams.certificatePassword.errorMessage),
  confirmPassword: yup
    .string()
    .trim()
    .required('Confirm Password is required')
    .oneOf([yup.ref('certPassword')], 'Passwords must match'),
  policyFolder: yup.object().required('Policy Folder is required'),
  subject: yup.string().required('Subject is required').matches(yupMatchesParams.fqdn.pattern, yupMatchesParams.fqdn.errorMessage),
  certificateFormat: yup.string().required('Certificate Format is required'),
  certificateType: yup.string().required('Certificate Type is required'),
});

export default CertificatevalidationSchema;
