import React, { useEffect, useState } from 'react';
import { AdministrationTabsData } from 'types';
import { getPermissionCatalogItems } from 'api/static-data';
import Catalog from 'components/Catalog';

interface PermissionProps {}

const Permissions: React.FunctionComponent<PermissionProps> = () => {
  const [content, setContent] = useState<AdministrationTabsData[]>([]);
  useEffect(() => {
    async function getCatalogItems() {
      try {
        const data = await getPermissionCatalogItems();
        console.log(data);
        setContent(data);
      } catch (error) {
        console.log(error);
      }
    }
    getCatalogItems();
  }, []);
  return content?.length ? <Catalog catalogItems={content} isAdminTiles /> : <div>No items to display</div>;
};

export default Permissions;
