import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';

import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import AdministrationService from 'api/ApiService/AdministrationService';
import AddApprovalForm from 'components/Administration/AddApprovalForm';
import { EditApprovalDetails } from 'types';
import { AdminEditRouteParams } from 'types/Enums';
import useNblNavigate from 'hooks/useNblNavigate';

const EditApprovals = () => {
  const dispatch = useDispatch();
  const apiAdministrationService = new AdministrationService();

  const [editApprovalDetails, setEditApprovalsDetails] = useState<EditApprovalDetails>();

  const { [AdminEditRouteParams.approvals]: approvalName } = useParams();
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const fetchProjectDetails = () => {
    if (approvalName) {
      dispatch(showSpinner({ id: SPINNER_IDS.approvalRequests, status: true, message: 'Loading group details...' }));
      apiAdministrationService
        .getApprovalDetails(approvalName)
        .then((res) => {
          if (res.status) {
            setEditApprovalsDetails(res.data);
          }
        })
        .finally(() => {
          dispatch(showSpinner({ id: SPINNER_IDS.approvalRequests, status: false, message: '' }));
        });
    }
  };

  useEffect(() => {
    fetchProjectDetails();
  }, []);

  const navigateToPermissionDetails = () => {
    navigate('/administration/approvals/view-approvals');
  };

  return (
    <AddApprovalForm
      title="Edit Approvals"
      permissions={{}}
      editApprovalDetails={editApprovalDetails}
      onSuccess={navigateToPermissionDetails}
      onClose={navigateToPermissionDetails}
    />
  );
};

export default EditApprovals;
