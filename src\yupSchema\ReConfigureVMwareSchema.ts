import * as yup from 'yup';
import { Address4, Address6 } from 'ip-address';
import { IPv4CidrRange, IPv6CidrRange } from 'ip-num/IPRange';
import { yupMatchesParams } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { FormValues } from 'componentsV2/IaaS/ReconfigureVMwareVM';

const networkSchema = yup.object().shape({
  id: yup.string().nullable(),
  network: yup.string().required('Network is required'),

  ipMode: yup.string().when('isExisting', {
    is: true,
    then: yup.string().nullable(), // Skip validation
    otherwise: yup
      .string()
      .required('IP mode is required')
      .test('require-ip-selection', 'At least one of IPv4 or IPv6 must be selected', function (ipMode) {
        const { ipv4, ipv6 } = this.parent;
        if (ipMode !== 'static_manual' && ipMode !== 'static_auto') return true;
        return !!ipv4 || !!ipv6;
      }),
  }),

  subnetIpv4: yup.string().nullable(),
  subnetIpv6: yup.string().nullable(),

  ipv4: yup.boolean(),
  ipv6: yup.boolean(),

  ipv4address: yup.string().when(['ipMode', 'ipv4'], {
    is: (ipMode: string, ipv4: boolean) => ['static_manual', 'static_auto'].includes(ipMode) && ipv4,
    then: yup
      .string()
      .required('IPv4 address is required')
      .when('ipMode', {
        is: 'static_manual',
        then: (schema) =>
          schema
            .matches(yupMatchesParams.validIPAddress.pattern, yupMatchesParams.validIPAddress.errorMessage)
            .test('valid-ipv4-address', 'Invalid IPv4 address', function (value) {
              const { subnetIpv4 } = this.parent;
              if (!value || !subnetIpv4) return true;

              if (Address4.isValid(value)) {
                const subnetIP = new Address4(subnetIpv4);
                const addressIPv4 = new Address4(value);
                const ipv4Range = IPv4CidrRange.fromCidr(subnetIpv4);

                if (!addressIPv4.isInSubnet(subnetIP)) {
                  return this.createError({
                    message: `IP address should be within the range of selected network: ${subnetIpv4}`,
                  });
                }

                if (ipv4Range.getFirst().toString() === value || ipv4Range.getLast().toString() === value) {
                  return this.createError({
                    message: "IP address shouldn't be network or broadcast address",
                  });
                }

                return true;
              }

              return this.createError({ message: 'Invalid IPv4 format' });
            })
            .test('duplicate-ipv4', 'Duplicate IPv4 address found', function (value) {
              const { networks = [] } = this.options.context || {};
              const currentIndex = networks.findIndex((n: FormValues['networks'][number]) => n === this.parent);
              if (!value || currentIndex === -1) return true;

              return !networks.some((n: FormValues['networks'][number], i: number) => i !== currentIndex && n.ipv4address === value);
            }),
        otherwise: (schema) => schema,
      }),
    otherwise: yup.string().nullable(),
  }),

  ipv6address: yup.string().when(['ipMode', 'ipv6'], {
    is: (ipMode: string, ipv6: boolean) => ['static_manual', 'static_auto'].includes(ipMode) && ipv6,
    then: yup
      .string()
      .required('IPv6 address is required')
      .when('ipMode', {
        is: 'static_manual',
        then: (schema) =>
          schema
            .matches(yupMatchesParams.ipv6Field.pattern, yupMatchesParams.ipv6Field.errorMessage)
            .matches(yupMatchesParams.validIPAddress.pattern, yupMatchesParams.validIPAddress.errorMessage)
            .test('valid-ipv6-address', 'Invalid IPv6 address', function (value) {
              const { subnetIpv6 } = this.parent;
              if (!value || !subnetIpv6) return true;

              if (Address6.isValid(value)) {
                const subnetIP = new Address6(subnetIpv6);
                const addressIPv6 = new Address6(value);
                const ipv6Range = IPv6CidrRange.fromCidr(subnetIpv6);

                if (!addressIPv6.isInSubnet(subnetIP)) {
                  return this.createError({
                    message: `IP address should be within the range of selected network: ${subnetIpv6}`,
                  });
                }

                if (ipv6Range.getFirst().toString() === value) {
                  return this.createError({
                    message: "IP address shouldn't be network address",
                  });
                }

                return true;
              }

              return this.createError({ message: 'Invalid IPv6 format' });
            })
            .test('duplicate-ipv6', 'Duplicate IPv6 address found', function (value) {
              const { networks = [] } = this.options.context || {};
              const currentIndex = networks.findIndex((n: FormValues['networks'][number]) => n === this.parent);
              if (!value || currentIndex === -1) return true;

              return !networks.some((n: FormValues['networks'][number], i: number) => i !== currentIndex && n.ipv6address === value);
            }),
        otherwise: (schema) => schema,
      }),
    otherwise: yup.string().nullable(),
  }),

  action: yup.string().oneOf(['Add', 'Delete']),
  isExisting: yup.boolean(),
});

const volumeSchema = yup.object().shape({
  diskName: yup.string().when('isExisting', {
    is: true,
    then: yup.string().nullable(),
    otherwise: yup
      .string()
      .required('Disk name is required')
      .matches(/^[a-zA-Z0-9_/\\]+$/, 'Disk name can only contain letters, numbers, underscores, slashes, and backslashes'),
  }),
  size: yup.number().when('isExisting', {
    is: true,
    then: yup.number().nullable(),
    otherwise: yup
      .number()
      .typeError('Size must be a number')
      .required('Size is required')
      .min(10, 'Size must be at least 10 GB')
      .max(2048, 'Size must not exceed 2048 GB'),
  }),
  diskFileSystem: yup.string().when('isExisting', {
    is: true,
    then: yup.string().nullable(),
    otherwise: yup.string().required('Filesystem is required'),
  }),
  action: yup.string().oneOf(['Add', 'Delete']),
  isExisting: yup.boolean(),
});

const ReConfigureVMwareSchema = () =>
  yup.object().shape({
    template: yup.string().when('isExisting', {
      is: false,
      then: yup.string().required('Template is required'),
      otherwise: yup.string(),
    }),
    memory: yup.number().when(['template', 'isExisting'], {
      is: (template: string, isExisting: boolean) => template === 'custom' && !isExisting,
      then: yup
        .number()
        .typeError('Memory must be a number')
        .required('Memory is required when template is custom')
        .min(2, 'Memory must be at least 2 GB')
        .max(64, 'Memory must not exceed 64 GB'),
    }),
    corecount: yup.number().when(['template', 'isExisting'], {
      is: (template: string, isExisting: boolean) => template === 'custom' && !isExisting,
      then: yup
        .number()
        .typeError('Corecount must be a number')
        .required('Corecount is required when template is custom')
        .min(2, 'Corecount must be at least 2')
        .max(24, 'Corecount must not exceed 24'),
    }),
    corepersocket: yup.number().when(['template', 'isExisting'], {
      is: (template: string, isExisting: boolean) => template === 'custom' && !isExisting,
      then: yup.number().typeError('Core per socket must be a number').required('Core per socket is required when template is custom'),
    }),
    networks: yup
      .array()
      .of(networkSchema)
      .test('no-duplicate-ips', 'Duplicate IP address found in networks', function (networks) {
        if (!networks) return true;

        const ipv4Set = new Set<string>();
        const ipv6Set = new Set<string>();

        for (const net of networks) {
          if (net.ipv4 && net.ipv4address) {
            if (ipv4Set.has(net.ipv4address)) {
              return this.createError({
                message: `Duplicate IPv4 address found: ${net.ipv4address}`,
              });
            }
            ipv4Set.add(net.ipv4address);
          }

          if (net.ipv6 && net.ipv6address) {
            if (ipv6Set.has(net.ipv6address)) {
              return this.createError({
                message: `Duplicate IPv6 address found: ${net.ipv6address}`,
              });
            }
            ipv6Set.add(net.ipv6address);
          }
        }

        return true;
      }),
    volumes: yup.array().of(volumeSchema),
  });

export default ReConfigureVMwareSchema;
