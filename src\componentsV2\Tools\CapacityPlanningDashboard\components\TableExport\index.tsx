import React from 'react';
import { CapacityPlanningDownloadIcon as CplanDownloadIcon } from 'assets/images/icons/custom-icons';
import { exportFormats, TimeInterval } from '../../utils/constant';
import { downloadFile } from '../../utils/utilization';
import * as htmlToImage from 'html-to-image';
import { useApiService } from 'api/ApiService/context';
import CplanDropdown from '../CplanDropdown';

import { useSelector } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';

interface TableExportProps {
  downloadOptions: string[];
  resourceId?: string;
  exportSectionId?: string;
  resourceType?: string;
}

const TableExport: React.FC<TableExportProps> = ({ downloadOptions, resourceId, exportSectionId, resourceType }) => {
  const { apiCapacityPlanningService } = useApiService();
  const hours = useSelector((state: State) => state.capacityPlanning.hours);
  const domainIds = useSelector((state: State) => state.capacityPlanning.selectedDomainIds);

  const handleFileDownload = async (filetype: string) => {
    switch (filetype) {
      case exportFormats.JPEG:
        handleImageDownload(exportFormats.JPEG);
        break;
      case exportFormats.Excel:
        handleExcelCSVPdfDownload(exportFormats.Excel);
        break;
      case exportFormats.CSV:
        handleExcelCSVPdfDownload(exportFormats.CSV);
        break;
      case exportFormats.PDF:
        handleExcelCSVPdfDownload(exportFormats.PDF);
        break;
    }
  };

  const handleExcelCSVPdfDownload = async (filetype: string) => {
    const interval = hours > 24 ? TimeInterval.Daily : TimeInterval.Hourly;

    if (hours) {
      if (resourceId) {
        const apiResponse = await apiCapacityPlanningService.getResourcesExportFile(
          filetype,
          hours,
          interval,
          null,
          resourceType,
          resourceId
        );
        downloadFile(apiResponse.data, 'VropsResourcesData' + `.${filetype}`);
      } else {
        const apiResponse = await apiCapacityPlanningService.getResourcesExportFile(filetype, hours, interval, domainIds);
        downloadFile(apiResponse.data, 'VropsResourcesData_' + Date.now() + `.${filetype}`);
      }
    }
  };

  const handleImageDownload = async (filetype: string) => {
    let exportSection = document.getElementById(exportSectionId!)!;
    if (exportSection) {
      const fontEmbedCss = await htmlToImage.getFontEmbedCSS(exportSection);
      htmlToImage
        .toBlob(exportSection, { quality: 0.95, backgroundColor: 'white', preferredFontFormat: 'woff2', fontEmbedCSS: fontEmbedCss })
        .then(function (dataUrl) {
          downloadFile(dataUrl!, 'VropsResourcesData_' + Date.now() + `.${filetype}`);
        });
    } else {
      console.log('Export section is not available!');
    }
  };

  return (
    <CplanDropdown
      label="Download"
      value={''}
      options={downloadOptions.map((filetype) => {
        return { label: filetype, value: filetype };
      })}
      onChange={(fileType) => handleFileDownload(fileType)}
      Icon={CplanDownloadIcon}
    />
  );
};

export default TableExport;
