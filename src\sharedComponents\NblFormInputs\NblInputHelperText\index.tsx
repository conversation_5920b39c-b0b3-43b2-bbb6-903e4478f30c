import React, { ReactNode } from 'react';
import { StyledFormHelperText } from './styled';

interface NblInputHelperTextProps {
  error?: boolean | string;
  helperText: string | ReactNode;
}

const NblInputHelperText: React.FC<NblInputHelperTextProps> = ({ error, helperText }) => {
  return <StyledFormHelperText error={Boolean(error)}>{helperText}</StyledFormHelperText>;
};

export default NblInputHelperText;
