import NblDataViewer from 'sharedComponents/NblDataViewer';
import { RequestPayloadMap } from 'types/Interfaces/RequestDetails';
import RequestType from 'types/Enums/RequestType';
import NblViewDetailsAccordion from 'componentsV2/NblViewDetailsAccordion';

interface CreateSpecFormEC2DetailsProps {
  formName: string;
  requestPayload: RequestPayloadMap[RequestType.SPEC_FLOW_EC2];
}

const CreateSpecFormEC2Details: React.FunctionComponent<CreateSpecFormEC2DetailsProps> = ({ formName, requestPayload }) => {
  const data = [
    { name: 'Project', value: requestPayload.projectName },
    { name: 'IAC Project Name', value: requestPayload.iacProjectName },
    { name: 'Namespace Id', value: requestPayload.namespaceId },
    { name: 'Instance Name', value: requestPayload.ec2_name },
    { name: 'Subnet Id', value: requestPayload.subnet_id },
    { name: 'AMI Id ', value: requestPayload.ami_id },
    { name: 'Instance Type', value: requestPayload.instance_type },
  ];

  return (
    <NblViewDetailsAccordion hasDivider summary={formName}>
      <NblDataViewer data={data} />
    </NblViewDetailsAccordion>
  );
};

export default CreateSpecFormEC2Details;
