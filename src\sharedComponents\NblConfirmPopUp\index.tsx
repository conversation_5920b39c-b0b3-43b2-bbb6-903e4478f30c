import { DialogActions, DialogContent, IconButton, useTheme } from '@mui/material';
import React, { ReactElement, useEffect } from 'react';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblTypography from 'sharedComponents/NblTypography';
import { StyledPopUpComponent, StyledPopUpIconButton, StyledPopUpTitle } from './styled';
import NblDivider from 'sharedComponents/NblDivider';
import { NebulaTheme } from 'NebulaTheme/type';
import CloseIcon from '@mui/icons-material/Close';
import { useDispatch } from 'react-redux';
import { setDialogMaximized } from 'store/reducers/common';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
//eslint-disable-next-line no-unused-vars

interface NblConfirmPopUpProps {
  title: string;
  content: string;
  open: boolean;
  onClose: () => void;
  onSubmit?: () => void;
  canMaximize?: boolean;
  fullWidth?: boolean;
  maxWidth?: any;
  fullHeight?: boolean;
  showCloseIcon?: boolean;
  showActionButton?: boolean;
  submitText?: string;
  cancelText?: string;
  isSubmitting?: boolean;
  renderElement?: ReactElement;
  disableSubmit?: boolean;
  showSubmit?: boolean;
}

function getConfirmPopupTextColor(status: string) {
  if (status === 'Reject') {
    return 'error';
  }
  if (status === 'Approve') {
    return 'success';
  } else {
    return 'primary';
  }
}

const NblConfirmPopUp: React.FC<NblConfirmPopUpProps> = ({
  title,
  content,
  open,
  onClose,
  onSubmit,
  fullWidth = true,
  maxWidth = '500px',
  showCloseIcon,
  submitText = 'Submit',
  cancelText = 'Cancel',
  isSubmitting = false,
  renderElement,
  disableSubmit = false,
  showActionButton = true,
  showSubmit = true,
  canMaximize = false,
  fullHeight,
}) => {
  const theme = useTheme<NebulaTheme>();
  const [maximized, setMaximized] = React.useState<boolean>(false);
  const dispatch = useDispatch();
  const resizeHandler = () => {
    setMaximized(!maximized);
  };

  useEffect(() => {
    dispatch(setDialogMaximized(maximized));
  }, [maximized]);

  const iconStyle = {
    fontSize: '3rem',
    [theme.breakpoints.down('xl')]: {
      fontSize: '1.625rem',
    },
  };
  const renderMinimizeMaximizeIcon = () => {
    return (
      <IconButton
        aria-label={maximized ? 'Minimize' : 'Maximize'}
        onClick={resizeHandler}
        sx={{
          position: 'absolute',
          right: 45,
          top: 16,
          [theme.breakpoints.down('xl')]: {
            right: 50,
            top: 13,
          },
        }}
      >
        {maximized ? <FullscreenExitIcon sx={iconStyle} /> : <FullscreenIcon sx={iconStyle} />}
      </IconButton>
    );
  };
  return (
    <StyledPopUpComponent
      open={open}
      onClose={onClose}
      fullWidth={fullWidth}
      maxWidth={maxWidth}
      PaperProps={{
        sx: {
          ...(maximized && {
            margin: 0,
            minWidth: '100%',
            minHeight: '100%',
          }),
          ...(fullHeight && {
            height: '100%',
          }),
        },
      }}
    >
      <StyledPopUpTitle color={theme.palette.typography.shade1}>
        <NblTypography variant="h3" color="shade1">
          {title}
        </NblTypography>
        {showCloseIcon && (
          <StyledPopUpIconButton aria-label="close" onClick={onClose} edge="end" color="inherit">
            <CloseIcon />
          </StyledPopUpIconButton>
        )}
        {canMaximize && renderMinimizeMaximizeIcon()}
      </StyledPopUpTitle>

      <NblDivider orientation="horizontal" length="100%" borderRadius={1} strokeWidth={0.3} color={theme.palette.nbldivider.variant3} />
      <DialogContent sx={{ 'MuiDialogContent-root': { padding: 0 } }}>
        {content && (
          <NblTypography variant="body1" color="shade1" padding="5px 0">
            {content}
          </NblTypography>
        )}
        {renderElement}
      </DialogContent>
      {showActionButton && (
        <DialogActions>
          <NblButton buttonID={`confirm-${cancelText}-btn`} onClick={onClose} variant="outlined" color="primary">
            {cancelText}
          </NblButton>
          {showSubmit && (
            <NblButton
              buttonID={`confirm-${submitText}-btn`}
              onClick={onSubmit}
              variant="contained"
              disabled={isSubmitting || disableSubmit}
              color={getConfirmPopupTextColor(submitText)}
              type="submit"
            >
              {submitText}
            </NblButton>
          )}
        </DialogActions>
      )}
    </StyledPopUpComponent>
  );
};

export default NblConfirmPopUp;
