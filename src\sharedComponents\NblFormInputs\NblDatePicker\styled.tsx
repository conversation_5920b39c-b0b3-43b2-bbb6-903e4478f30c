import styled from '@emotion/styled';
import { DatePicker, DateTimePicker, PickersDay } from '@mui/x-date-pickers';
//eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import { getOutlinedInputStyles } from '../common';

const getPickerStyles = (fieldStyles: any, theme: NebulaTheme) => ({
  '& .MuiOutlinedInput-root': {
    height: '2.2rem',
    '& .MuiOutlinedInput-input': {
      ...getOutlinedInputStyles(theme),
      color: fieldStyles.color,
      padding: '0.5rem 0.75rem',
      '&::placeholder': {
        color: fieldStyles.placeholderColor,
      },
    },
    '&.Mui-focused fieldset': {
      borderColor: fieldStyles.toggledBorderColor,
    },
    '&.Mui-error fieldset': {
      borderColor: fieldStyles.errorColor,
    },
    '& .MuiSvgIcon-root': {
      fontSize: '1rem',
    },
  },
});

export const StyledDatePicker = styled(DatePicker)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { datePickerField } = theme.palette;
  return getPickerStyles(datePickerField, theme);
});

export const StyledDateTimePicker = styled(DateTimePicker)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { datePickerField } = theme.palette;
  return getPickerStyles(datePickerField, theme);
});

export const StyledDay = styled(PickersDay)(() => {
  return {
    '&.Mui-disabled': {
      opacity: 0.2,
      'pointer-events': 'unset',
      cursor: 'not-allowed',
    },
  };
});
