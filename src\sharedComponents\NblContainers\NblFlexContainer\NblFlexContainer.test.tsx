import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblFlexContainer from '.';

describe('NblFlexContainer component', () => {
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblFlexContainer>Layout Wrapper</NblFlexContainer>
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
