// USAGE METRICS DASHBOARD STYLES

//Request Card Component
import { Box, Dialog, Button, TableContainer, Grid } from '@mui/material';
import { styled } from '@mui/material/styles';
import { NebulaTheme } from 'NebulaTheme/type';
export const StyledUMCard = styled(Box)<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    borderRadius: '10px',
    backgroundColor: theme.palette.secondary.main,
    padding: '15px',
    color: theme.palette.primary.main,
    minHeight: '100%',
    border: `1px solid ${theme.palette.secondary.shade3}`,
  };
});
interface CustomGridProps {
  bgColor: string;
}

export const StyledLegendIcon = styled(Grid, { shouldForwardProp: (prop) => prop != 'bgColor' })<CustomGridProps>(({ bgColor }) => {
  return {
    width: '10px',
    height: '10px',
    backgroundColor: bgColor,
    borderRadius: '50%',
    marginRight: '5px',
    display: 'inline',
    textAlign: 'right',
  };
});
export const StyledUMCardReqUpArrow = styled(Box)<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    color: theme.palette.tertiary.shade2.dark,
    transform: 'rotate(-90deg)',
  };
});

export const StyledUMCardReqDownArrow = styled(Box)<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    color: theme.palette.tertiary.shade3.medium,
    transform: 'rotate(90deg)',
  };
});

export const StyledUMProgressBarComplete = styled(Box)<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    borderRadius: '7px',
    height: '14px',
    backgroundColor: theme.palette.tertiary.shade2.dark,
  };
});

export const StyledUMProgressBarFailed = styled(Box)<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    borderRadius: '7px',
    height: '14px',
    backgroundColor: theme.palette.tertiary.shade3.medium,
  };
});
export const StyledUMTableConatiner = styled(TableContainer)({
  '.MuiDataGrid-root .MuiDataGrid-row:not(.MuiDataGrid-row--dynamicHeight)>.MuiDataGrid-cell:nth-child(2)': {
    overflow: 'visible !important',
  },
  '.MuiDataGrid-root .MuiDataGrid-row:not(.MuiDataGrid-row--dynamicHeight)>.MuiDataGrid-cell:nth-child(3)': {
    overflow: 'visible !important',
  },
  '.MuiDataGrid-root .MuiDataGrid-row:not(.MuiDataGrid-row--dynamicHeight)>.MuiDataGrid-cell:nth-child(1)': {
    whiteSpace: 'wrap !important',
  },
});
export const StyledAreaChartWrapper = styled(Box)({
  position: 'relative',
  right: '50px',
  left: '0',
  width: '140px',
  height: '30px',
});
export const StyledAreaChart = styled(Box)({
  position: 'absolute',
  right: '0px',
  left: '-20px',
  width: '100%',
  top: '-35px',
});
//Last10Requests component
export const StyledDot = styled(Box)({
  position: 'relative',
  display: 'inline-block',
  height: '8px',
  width: '8px',
  borderRadius: '50%',
  margin: '0 3px',
  '&:hover .tootip-text': {
    visibility: 'visible',
  },
});
export const StyledStatusTooltip = styled(Box)({
  visibility: 'hidden',
  backgroundColor: 'black',
  color: 'white',
  textAlign: 'center',
  padding: '5px',
  borderRadius: '6px',
  fontSize: '12px',
  top: '-30px',
  left: '-40px',
  position: 'absolute',
  zIndex: '1',
  minWidth: '100px',
});

//RequestByMonth Deropdown
export const StyledReqByMthDDWrap = styled(Box)({
  position: 'absolute',
  top: '-3px',
  right: '15px',
  zIndex: '2',
});

//Filter Dialog Box Styles
export const StyledFilterDialog = styled(Dialog)(() => ({
  '& .MuiDialog-paper': {
    borderRadius: '25px 0 0 25px',
    display: 'flex',
    background: 'linear-gradient(180deg, #003057 0%,rgb(29, 115, 176) 170%) 0% 0% no-repeat padding-box;',
    boxShadow: ' 0px 8px 24px #00000026',
    backdropFilter: 'blur(30px)',
    opacity: 1,
    width: '25vw',
    paddingRight: '5px',
    paddingLeft: '20px',
    border: 'none',
  },
  '& .MuiGrid-root': {
    width: '22vw',
  },
  '& .MuiChip-filled': {
    backgroundColor: '#A2DDF5',
    color: '#2382AA',
  },
}));
export const StyledClearAllBtn = styled(Button)(() => ({
  background: '#A2DDF5',
  borderRadius: '6px',
  opacity: '1',
  color: ' #2382AA',
}));
export const StyledApplyFilterBtn = styled(Button)(() => ({
  background: '#0E99D8',
  borderRadius: '6px',
  opacity: '1',
  color: ' #FFFFFF',
}));
