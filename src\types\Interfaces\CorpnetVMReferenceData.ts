import VmGroupData from './VmGroupData';

export default interface CorpnetVmReferenceData {
  size: { [key: string]: { [key: string]: number | string } };
  layouts: Array<{ id: number; code: string; shortName: string; name: string }>;
  vmMaxCount: string;
  patchingCycle: { name: string; value: string }[];
  backupOptions: { name: string; value: string }[];
  appTier: { name: string; value: string }[];
  environment: { name: string; value: string }[];
  groups: VmGroupData;
  datacenter: { id: number; name: string }[];
  database?: { name: string; value: string }[];
}
