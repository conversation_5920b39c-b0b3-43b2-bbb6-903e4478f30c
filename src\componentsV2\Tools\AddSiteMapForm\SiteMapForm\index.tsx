import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import SiteMapTable from '../SiteMapTable';
import { useApiService } from 'api/ApiService/context';
import { dispatch } from 'store';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import FileUpload from 'sharedComponents/NblFileUpload';
import { useState } from 'react';
import NblTypography from 'sharedComponents/NblTypography';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { toast } from 'react-toastify';

enum STEPS {
  uploadFile = 0,
  review = 1,
}
interface SiteMapFormProps {}

const SiteMapForm: React.FunctionComponent<SiteMapFormProps> = () => {
  const { currentStep, nblFormProps, nblFormValues, steps } = useNblForms();
  const { apiSiteMapService } = useApiService();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileUpload = async (file: File | null) => {
    if (file) {
      setSelectedFile(file);
      dispatch(showSpinner({ id: SPINNER_IDS.tools, status: true, message: 'Processing the uploaded file...' }));
      const formData = new FormData();
      formData.append('file', file);
      apiSiteMapService.uploadSiteMapFile(formData).then((res: any) => {
        if (res.status) {
          dispatch(showSpinner({ id: SPINNER_IDS.tools, status: false, message: '' }));
          console.log('Uploaded File :', file);
          nblFormProps.setValues({ siteMapData: res.data });
        } else {
          dispatch(showSpinner({ id: SPINNER_IDS.tools, status: false, message: '' }));
        }
      });
    } else {
      nblFormProps.setValues({ siteMapData: [] });
    }
  };

  const handleFileRemove = () => {
    handleFileUpload(null);
    steps[STEPS.review].status = 'pending';
  };

  const handleRowDelete = (rowID: any) => {
    let sitemapArray = [...nblFormValues.siteMapData];
    sitemapArray.splice(rowID, 1);
    nblFormProps.setValues({ siteMapData: sitemapArray });
    toast.success('Sitemap deleted successfully!', { position: toast.POSITION.BOTTOM_CENTER });
  };

  switch (currentStep) {
    case STEPS.uploadFile:
      return (
        <NblFlexContainer direction="column">
          <NblTypography variant="h4">Upload Completed SiteMap File *</NblTypography>
          <FileUpload file={selectedFile} onFileUpload={handleFileUpload} maxSizeKB={2000} onFileRemove={handleFileRemove} />
        </NblFlexContainer>
      );

    case STEPS.review:
      return (
        <NblFlexContainer margin="-3.5rem 0 0 0" height="58vh">
          <SiteMapTable siteMapData={nblFormValues.siteMapData} deleteRow={handleRowDelete} />;
        </NblFlexContainer>
      );

    default:
      return <></>;
  }
};

export default SiteMapForm;
