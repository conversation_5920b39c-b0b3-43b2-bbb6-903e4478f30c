import { useFormik } from 'formik';
import * as yup from 'yup';
import { Box, Divider, Grid, Typography, useTheme } from '@mui/material';

import TextField from 'components/TextField';
import ContentViewport from 'components/ContentViewport';
import FormWrapper from 'components/FormWrapper';
import DataGridTable from 'components/DataGridTable';
import { PendingRequestColumns, PendingRequestRows } from 'mock/Requests';

import { formWrapperError } from 'utils/common';
import icons from 'assets/images/icons';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';

const validationSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  country: yup.string().required('Country is required'),
});

const Playground = () => {
  const theme: NebulaTheme = useTheme();
  const { typography } = theme;

  const formik = useFormik({
    initialValues: {
      name: '',
      country: '',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      alert(values);
    },
  });

  const onCancel = () => {
    formik.resetForm();
  };

  const renderFormWrapper = () => {
    return (
      <FormWrapper
        title={'Form title'}
        isSubmitting={formik.isSubmitting}
        errors={formWrapperError(formik)}
        submitText={'Create'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        Icon={icons.AdminPanelSettingsRounded}
      >
        <Grid container item spacing={6}>
          <Grid container item spacing={6}>
            <Grid item xs={12} sm={6}>
              <TextField
                type="text"
                name="name"
                label="Name *"
                placeholder="Please Enter"
                value={formik.values.name}
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.name && formik.errors.name}
              />
            </Grid>
          </Grid>
        </Grid>
      </FormWrapper>
    );
  };

  const renderDataGrid = () => {
    return (
      <Box sx={{ width: '100%', margin: '0 auto' }}>
        <DataGridTable
          loading={false}
          rows={PendingRequestRows}
          columns={PendingRequestColumns}
          title={'Data table'}
          refreshHandler={() => { }}
        />
      </Box>
    );
  };

  const renderTypography = () => {
    return (
      <Grid item sm={12}>
        <Typography variant="h1" color={'text.primary'}>
          H1 Medium 40px
        </Typography>
        <Typography variant="h2" color={'text.primary'}>
          H2 Medium 32px
        </Typography>
        <Typography variant="h3" color={'text.primary'}>
          H3 Medium 20px
        </Typography>
        <Typography variant="h3" color={'text.primary'} sx={{ ...typography.bold }}>
          H3 Bold 20px
        </Typography>
        <Typography variant="h4" color={'text.primary'}>
          H4 Medium 18px
        </Typography>
        <Typography variant="h5" color={'text.primary'}>
          H5 Medium 16px
        </Typography>
        <Divider sx={{ my: 1 }} />
        <Typography variant="body2" color={'text.primary'} sx={{ ...typography.semiBold }}>
          Body Text Semi Bold 20px
        </Typography>
        <Divider sx={{ my: 1 }} />
        <Typography color={'text.primary'} sx={{ ...typography.bold }}>
          Body Text Bold 16px
        </Typography>
        <Typography color={'text.primary'} sx={{ ...typography.semiBold }}>
          Body Text Semi Bold 16px
        </Typography>
        <Typography color={'text.primary'} sx={{ ...typography.medium }}>
          Body Text Medium 16px
        </Typography>
        <Typography color={'text.primary'}>Body Text Regular 16px</Typography>
        <Divider sx={{ my: 1 }} />
        <Typography variant="subtitle1" color={'text.primary'}>
          Body Text Regular 12px
        </Typography>
        <Typography variant="caption" color={'text.primary'}>
          Caption Text Light Italic 12px
        </Typography>
      </Grid>
    );
  };

  return (
    <ContentViewport>
      <Grid container rowGap={5}>
        {renderTypography()}
        {renderFormWrapper()}
        {renderDataGrid()}
      </Grid>
    </ContentViewport>
  );
};

export default Playground;
