import React, { useState, useContext } from 'react';
import useMediaQuery from 'hooks/useMediaQuery';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblConfirmPopUp from 'sharedComponents/NblConfirmPopUp';
import { useApiService } from 'api/ApiService/context';
import { toast } from 'react-toastify';
import { DeleteIcon } from 'assets/images/icons/custom-icons';
import ResourceActionButton from 'componentsV2/Resources/ResourceActionsBar/ResourceActionButton';
import NblTypography from 'sharedComponents/NblTypography';
// eslint-disable-next-line no-unused-vars
import { ColumnData, NblTable } from '../../../sharedComponents/NblTable';
// eslint-disable-next-line no-unused-vars
import { GridRenderCellParams } from '@mui/x-data-grid';
import { ResourceContext } from '../../IaaS/CreateNamespace/ResourceDetails';

interface DeleteSecretProps {
  secretId: string | string[] | null;
  type: 'icon' | 'button';
  disabled: boolean;
  onSuccess?: () => void;
  secretInfo?: { key: string; path: string; namespaceName: string; secretId: string }[];
}

const DeleteSecret: React.FC<DeleteSecretProps> = ({ secretId, type, disabled = true, onSuccess, secretInfo }) => {
  const { apiSecretsManagement } = useApiService();
  const envContext = useContext(ResourceContext);

  const styles = useMediaQuery({ size: '52px' }, { size: '30px' });

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  interface RowProps {
    id: string;
    key: string;
    path: string;
    namespaceName: string;
    deviceName?: string;
    secretId: string;
  }
  const [rows, setRows] = useState<RowProps[]>([]);
  const [loading, setLoading] = useState(true);

  const addRowsWithDeviceInfo = async (rows: RowProps[]) => {
    setLoading(true);
    const deviceAssociations = (
      await Promise.all(
        [...new Set(rows.map((row) => row.path))].map((path) =>
          apiSecretsManagement.getDeviceSecrets(rows[0].namespaceName, path, envContext.envId)
        )
      )
    ).flatMap((assoc) => assoc.data);
    const devices = (await apiSecretsManagement.getDevices(envContext.envId)).data;

    setRows(
      rows.map((row) => {
        const deviceId = deviceAssociations.find((entry) => entry.secretId.includes(row.secretId))?.deviceId;
        return {
          ...row,
          deviceName: devices.find((device) => deviceId?.includes(device.configuration.charterId))?.configuration.name,
        };
      })
    );
    setLoading(false);
  };

  const handleDelete = () => {
    if (!secretId || (Array.isArray(secretId) && secretId.length === 0)) return;
    setIsDeleting(true);

    const ids = Array.isArray(secretId) ? secretId : [secretId];

    apiSecretsManagement
      .deleteSecretDetails(ids)
      .then((res) => {
        if (res.status) {
          const label = Array.isArray(secretId) ? secretId.join(', ') : secretId;
          toast.success(res.data.message || `Secret(s) with ID ${label} deleted successfully`, {
            position: toast.POSITION.BOTTOM_CENTER,
          });

          onSuccess?.();
        }
      })
      .finally(() => {
        setIsDeleting(false);
        setIsDialogOpen(false);
      });
  };

  const handleOpenDialog = () => {
    if (!disabled) {
      setIsDialogOpen(true);
      if (secretInfo) {
        addRowsWithDeviceInfo(
          secretInfo.map((secret) => ({
            id: secret.key,
            key: secret.key,
            path: secret.path,
            namespaceName: secret.namespaceName,
            secretId: secret.secretId,
          }))
        );
      }
    }
  };

  const columns: ColumnData[] = [
    {
      field: 'secretName',
      headerName: 'Secret Name',
      flex: 1,
      minWidth: 250,
      renderCell: ({ row: { key } }: GridRenderCellParams) => (
        <NblTypography variant="body1" cursor="pointer" weight="bold">
          {key}
        </NblTypography>
      ),
    },
    {
      field: 'deviceName',
      headerName: 'Associated Device',
      flex: 1,
      minWidth: 250,
      renderCell: ({ row: { deviceName } }: GridRenderCellParams) => (
        <NblTypography variant="body1" cursor="pointer" weight="bold">
          {deviceName ? deviceName : 'No Device Associated'}
        </NblTypography>
      ),
    },
  ];

  return (
    <>
      {type === 'icon' ? (
        <NblFlexContainer center height={styles.size} width={styles.size} borderRadius="50%">
          <ResourceActionButton
            disabled={disabled}
            onClick={handleOpenDialog}
            tooltip={'Delete Secret'}
            Icon={DeleteIcon}
            data-testId={`btnDeleteSecretIcon-${secretId}`}
            buttonID={`btnDeleteSecretIcon-${secretId}`}
          />
        </NblFlexContainer>
      ) : (
        <NblButton
          buttonID={`btnDeleteSecretButton-${secretId}`}
          disabled={disabled}
          variant="contained"
          onClick={handleOpenDialog}
          color="primary"
        >
          Delete
        </NblButton>
      )}

      <NblConfirmPopUp
        title="Confirm Delete"
        content={''}
        open={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onSubmit={handleDelete}
        showCloseIcon
        submitText="Delete"
        cancelText="Cancel"
        showActionButton
        showSubmit
        isSubmitting={isDeleting}
        disableSubmit={isDeleting}
        maxWidth={'1000px'}
        renderElement={
          <NblFlexContainer direction="column">
            <NblTypography variant={'h6'} color="shade1" weight="medium">
              {rows.find((row) => row?.deviceName)
                ? `Several secrets are associated to some device. Do you want to proceed? `
                : `Do you want to delete the secret`}
            </NblTypography>
            {<NblTable columns={columns} rows={rows} loading={loading} autoHeight />}
          </NblFlexContainer>
        }
      />
    </>
  );
};

export default DeleteSecret;
