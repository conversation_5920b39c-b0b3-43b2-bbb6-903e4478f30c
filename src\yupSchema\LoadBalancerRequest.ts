import * as yup from 'yup';
import uniq from 'lodash/uniq';
import { Address6 } from 'ip-address';

import IPAddressSchema from './IPAddressSchema';
import { yupMatchesParams } from 'utils/common';
import { subjectAlternateNamesSchema } from './Certificate';
import { CertificateOptions } from 'types/Enums';

export const portNumberSchema = () =>
  yup
    .string()
    .matches(yupMatchesParams.faxTollFreeChars.pattern, yupMatchesParams.faxTollFreeChars.errorMessage)
    .test('valid-port-address', 'Please enter valid port number', function (value) {
      if (value) {
        const portNumber = Number(value);
        return typeof portNumber === 'number' && portNumber >= 1 && portNumber <= 65535;
      }
      return false;
    });
export const addHostsSchema = () =>
  yup.object().shape({
    datacenter: yup.string().required('Datacenter is required'),
    ipv6: yup
      .array()
      .of(
        yup
          .string()
          .trim()
          .test('is-valid-ip', 'Invalid IP address', (value) => {
            if (!value) return true;
            const trimmedValue = value.trim();
            if (trimmedValue.includes('/')) {
              return new yup.ValidationError('Subnet mask is not allowed for IPv6 IP', trimmedValue, 'ipv6');
            }
            return Address6.isValid(trimmedValue);
          })
      )
      .max(1, 'Only one IPv6 IP is allowed')
      .required('IPv6 IPs is required'),
  });

const loadBalancerRequestSchema = () =>
  yup.object().shape({
    domain: yup.string().required('Domain is required'),
    projectName: yup.string().required('Project name is required'),
    application: yup.string().required('Application is required'),
    environment: yup.string().required('Environment name is required'),
    appId: yup.string().required('AppId is required'),
    dataCenters: yup.array().of(
      yup.object().shape({
        datacenter: yup.string().required('Datacenter is required'),
      })
    ),
    applicationName: yup
      .string()
      .trim()
      .required('Application Name is required')
      .matches(yupMatchesParams.alphaNumericWithHyphenUnderscrore.pattern, yupMatchesParams.alphaNumericWithHyphenUnderscrore.errorMessage),
    ipAddress: yup
      .array()
      .of(IPAddressSchema({}))
      .min(1, 'Please enter atleast one Host')
      .test('unique', 'Duplicate IP Addresses are not allowed', function (value) {
        const ipAddresses = value || [];
        const hasUnique = ipAddresses.length === uniq(ipAddresses).length;
        return hasUnique;
      }),
    listeningPort: portNumberSchema(),
    forwardingPort: portNumberSchema(),
    healthCheckType: yup.string().required('Health Check Type is required'),
    healthCheckUrl: yup
      .string()
      .trim()
      .when('healthCheckType', {
        is: 'URL',
        then: yup.string().required('Health Check URL is required'),
        otherwise: yup.string().optional(),
      })
      .matches(yupMatchesParams.healthCheckUrl.pattern, yupMatchesParams.healthCheckUrl.healthCheckUrlErrorMessage),
    protocol: yup.string().required('Protocol is required'),
    fqdnName: yup.string().matches(yupMatchesParams.fqdn.pattern, yupMatchesParams.fqdn.errorMessage).required('FQDN Name is required'),
    appHealthResponse: yup
      .string()
      .trim()
      .when('healthCheckType', {
        is: 'URL',
        then: yup.string().required('App Health Response is required, e.g., UP, OK'),
        otherwise: yup.string().optional(),
      })
      .matches(yupMatchesParams.appHealthResponse.pattern, yupMatchesParams.appHealthResponse.appHealthResponseErrorMessage),
    certificateOption: yup.string().required('Certificate Options is required'),
    existingCertPassword: yup
      .string()
      .trim()
      .test('cert-password', 'Certificate Password is required', function (value) {
        const { certificateOption } = this.parent;
        if (certificateOption === CertificateOptions.existingCertificateValue) {
          if (!value?.trim()) {
            return false;
          }
          if (!yupMatchesParams.certificatePassword.pattern.test(value)) {
            return this.createError({ message: yupMatchesParams.certificatePassword.errorMessage });
          }
        }
        return true;
      }),
    certFile: yup.mixed().test('certFile', 'Certificate File is required', function (value) {
      const { certificateOption } = this.parent;
      if (certificateOption === CertificateOptions.existingCertificateValue) {
        if (!value) {
          return false;
        } else if (typeof value.name === 'string' && !value.name.endsWith('.pem')) {
          return this.createError({ message: 'Please upload the .pem file' });
        }
      }
      return true;
    }),
    reason: yup
      .string()
      .trim()
      .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage)
      .required('Reason is required'),

    // Internal Certificate Fields
    certificateName: yup
      .string()
      .trim()
      .test('certificate-name', 'Certificate Name is required', function (value) {
        const { certificateOption } = this.parent;
        if (certificateOption === CertificateOptions.newCertificateValue) {
          return Boolean(value && value.trim());
        }
        return true;
      }),
    orgName: yup
      .string()
      .trim()
      .test('org-name', 'Organization Name is required', function (value) {
        const { certificateOption } = this.parent;
        if (certificateOption === CertificateOptions.newCertificateValue) {
          if (!value?.trim()) {
            return false;
          }
        }
        return true;
      }),
    state: yup.string().test('state', 'State is required', function (value) {
      const { certificateOption } = this.parent;
      if (certificateOption === CertificateOptions.newCertificateValue) {
        if (!value?.trim()) {
          return false;
        }
      }
      return true;
    }),
    subjectAlternateNames: yup
      .array()
      .of(subjectAlternateNamesSchema())
      .test('subjectAlternateNames', 'Subject Alternate Names should follow this pattern (DNS:UAMP-VOICE.COM)', function (values) {
        const { certificateOption } = this.parent;
        if (certificateOption === CertificateOptions.newCertificateValue) {
          if (!values?.length) {
            return false;
          } else {
            const subjectName = values || [];
            const hasUnique = subjectName.length === uniq(subjectName).length;
            if (!hasUnique) {
              return this.createError({ message: 'Duplicate Subject Alternate Name are not allowed' });
            }
          }
        }
        return true;
      }),
    keyType: yup.string().test('keyType', 'Key Type is required', function (value) {
      const { certificateOption } = this.parent;
      if (certificateOption === CertificateOptions.newCertificateValue) {
        if (!value?.trim()) {
          return false;
        }
      }
      return true;
    }),
    keySize: yup.string().test('keySize', 'keySize is required', function (value) {
      const { certificateOption } = this.parent;
      if (certificateOption === CertificateOptions.newCertificateValue) {
        if (!value?.trim()) {
          return false;
        }
      }
      return true;
    }),
    certPassword: yup
      .string()
      .trim()
      .test('certPassword', 'Certificate Password is required', function (value) {
        const { certificateOption } = this.parent;
        if (certificateOption === CertificateOptions.newCertificateValue) {
          if (!value?.trim()) {
            return false;
          }
          if (!yupMatchesParams.certificatePassword.pattern.test(value)) {
            return this.createError({ message: yupMatchesParams.certificatePassword.errorMessage });
          }
        }
        return true;
      }),
    confirmPassword: yup
      .string()
      .trim()
      .oneOf([yup.ref('certPassword'), null], 'Certificate Password & Confirm Certificate Password should be equal')
      .test('confirmPassword', 'Confirm Certificate Password is required', function (value) {
        const { certificateOption } = this.parent;
        if (certificateOption === CertificateOptions.newCertificateValue) {
          return Boolean(value?.trim());
        }
        return true;
      }),
    caConfig: yup.object().when('certificateOption', {
      is: CertificateOptions.newCertificateValue,
      then: yup
        .object()
        .shape({
          name: yup.string().required('Configuration is required'),
          value: yup.string(),
        })
        .required('Configuration is required'),
      otherwise: yup.object().nullable(),
    }),
    policyFolder: yup.object().when('certificateOption', {
      is: CertificateOptions.newCertificateValue,
      then: yup
        .object()
        .shape({
          name: yup.string().required('PolicyFolder is required'),
          value: yup.string(),
        })
        .required('PolicyFolder is required'),
      otherwise: yup.object().nullable(),
    }),
  });

export default loadBalancerRequestSchema;
