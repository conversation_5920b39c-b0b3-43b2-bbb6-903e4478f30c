import { styled } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledBorderContainer = styled('div')<{
  width: string;
  height: string;
  minHeight?: string;
  borderRadius: string;
  padding: string;
  backgroundColor: string;
  border?: string;
  borderType?: 'dashed' | 'solid';
  theme?: NebulaTheme;
  isHover?: boolean;
  margin?: string;
}>(({ theme, width, height, minHeight, borderRadius, padding, backgroundColor, border, isHover, borderType, margin }) => ({
  display: 'flex',
  flexDirection: 'column',
  width: width,
  height: height,
  gap: '16px',
  border: border ? border : `1px ${borderType || 'solid'} ${theme.palette.border.color}`,
  borderRadius: borderRadius,
  overflow: 'hidden',
  padding,
  backgroundColor,
  position: 'relative',
  ...(minHeight && { minHeight }),
  ...(margin && { margin }),
  ...(isHover && {
    '&:hover': {
      borderColor: theme.palette.border.borderHoverColor,
      boxShadowHover: theme.palette.border.boxShadowHover,
      boxShadow: ` 6px 6px 12px ${theme.palette.border.boxShadow}`,
      cursor: 'pointer',
    },
  }),
}));
