import { render } from '@testing-library/react';
import configureMockStore from 'redux-mock-store';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';

import CatalogTabs from '.';
// eslint-disable-next-line no-unused-vars
import { CatalogTab } from 'types';
import ThemeProvider from 'mock/ThemeProvider';

const TABS: CatalogTab[] = [
  { label: 'Network', id: 'network', url: '/IaaS/network' },
  { label: 'Compute', id: 'compute', url: '/IaaS/compute' },
];

const mockStore = configureMockStore();
const store = mockStore({ menu: { showNavigateConfirmDialog: false }, common: { disableDialogContentScroll: false } });

describe('CatalogTabs component', () => {
  test('renders CatalogTabs component', () => {
    const { getByText } = render(
      <ThemeProvider>
        <Router>
          <ReduxProvider store={store}>
            <CatalogTabs TABS={TABS}>
              <div>Content</div>
            </CatalogTabs>
          </ReduxProvider>
        </Router>
      </ThemeProvider>
    );
    expect(getByText('Network')).toBeInTheDocument();
    expect(getByText('Compute')).toBeInTheDocument();
  });
});
