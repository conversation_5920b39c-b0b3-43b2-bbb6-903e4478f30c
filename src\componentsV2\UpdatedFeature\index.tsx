import React from 'react';
import { useTheme } from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';

import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import { NebulaTheme } from 'NebulaTheme/type';

interface UpdatedFeatureProps {
  title: string;
  description: string;
  link: string;
}

const UpdatedFeature: React.FC<UpdatedFeatureProps> = ({ title, description, link }: UpdatedFeatureProps) => {
  const theme = useTheme<NebulaTheme>();

  const navigateHandler = () => {
    window.open(link, "_blank");
  }

  return (
    <NblFlexContainer
      direction="column"
      height={'auto'}
      padding="12px"
      borderRadius="10px"
      backgroundColor={theme.palette.secondary.shade1}
      onClick={navigateHandler}
      cursor={'pointer'}
    >
      <NblFlexContainer justifyContent={'space-between'}>
        <NblTypography variant='subtitle1' color={'shade1'}>{title}</NblTypography>
        <NavigateNextIcon sx={{ color: theme.palette.typography.shade1 }} />
      </NblFlexContainer>
      <NblTypography variant='body3' color={'shade1'} opacity={0.7}>{description}</NblTypography>
    </NblFlexContainer>
  );
};

export default UpdatedFeature;
