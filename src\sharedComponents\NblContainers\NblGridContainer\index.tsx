import React from 'react';
import { StyledGridItem, StyledGridContainer } from './styled';

interface NblGridProps {
  children: React.ReactNode;
  width?: string;
  height?: string;
  padding?: string;
  backgroundColor?: string;
  overflowX?: React.CSSProperties['overflowX'];
  overflowY?: React.CSSProperties['overflowY'];
  minHeight?: string;
  maxHeight?: string;
  minWidth?: string;
  maxWidth?: string;
  textAlign?: React.CSSProperties['textAlign'];
  margin?: string;
  border?: string;
  borderTop?: string;
  borderBottom?: string;
  borderLeft?: string;
  borderRight?: string;
  borderRadius?: string;
  id?: string;
}

interface NblGridContainerProps extends NblGridProps {
  spacing?: number;
  spacingX?: number;
  spacingY?: number;
  columnMinWidth?: string;
  columns?: string | number;
  rowHeight?: string;
  rows?: string | number;
  alignItems?: React.CSSProperties['alignItems'];
  justifyContent?: React.CSSProperties['justifyContent'];
  alignContent?: React.CSSProperties['alignContent'];
  position?: React.CSSProperties['position'];
}

interface NblGridItemProps extends NblGridProps {
  justifySelf?: React.CSSProperties['justifySelf'];
  alignSelf?: React.CSSProperties['alignSelf'];
  alignContent?: React.CSSProperties['alignContent'];
  colspan?: number;
  rowspan?: number;
  position?: 'relative' | 'static';
}

const NblGridContainer: React.FC<NblGridContainerProps> = ({
  children,
  columns = 'auto-fit',
  columnMinWidth = 'auto',
  width = '100%',
  height = '100%',
  rows = 'auto',
  rowHeight = '1fr',
  alignItems = 'initial',
  alignContent = 'initial',
  justifyContent = 'initial',
  spacing = 1,
  overflowX = 'hidden',
  overflowY = 'scroll',
  padding = '5px 0',
  backgroundColor = 'initial',
  position = 'static',
  minHeight,
  minWidth,
  maxHeight,
  maxWidth,
  margin,
  spacingX,
  spacingY,
  border,
  borderBottom,
  borderLeft,
  borderRight,
  borderTop,
  borderRadius,
  id,
}) => {
  //Utils
  let gridTemplateRows;
  if (!(String(rows).includes('%') || String(rows).includes('px')) || ['auto', 'auto-fit'].includes(String(rows))) {
    gridTemplateRows = `repeat(${rows}, ${rowHeight})`;
  } else {
    gridTemplateRows = rows;
  }

  //Jsx
  return (
    <StyledGridContainer
      id={id}
      role="grid"
      style={{
        position,
        gridTemplateColumns: `repeat(${columns}, minmax(${columnMinWidth}, 1fr))`,
        gridTemplateRows,
        width,
        height,
        alignItems,
        alignContent,
        justifyContent,
        gap: `${spacing * 8}px`,
        padding,
        overflowX,
        overflowY,
        backgroundColor,
        boxSizing: 'border-box',
        ...(margin && { margin }),
        ...(minHeight && { minHeight }),
        ...(maxHeight && { maxHeight }),
        ...(minWidth && { minWidth }),
        ...(maxWidth && { maxWidth }),
        ...(spacingX && { columnGap: `${spacingX * 8}px` }),
        ...(spacingY && { rowGap: `${spacingY * 8}px` }),
        ...(border && { border }),
        ...(borderTop && { borderTop }),
        ...(borderBottom && { borderBottom }),
        ...(borderLeft && { borderLeft }),
        ...(borderRight && { borderRight }),
        ...(borderRadius && { borderRadius }),
      }}
    >
      {children}
    </StyledGridContainer>
  );
};
const NblGridItem: React.FC<NblGridItemProps> = ({
  children,
  width = '100%',
  height = '100%',
  padding = '0',
  alignSelf = 'stretch',
  alignContent,
  justifySelf = 'flex-start',
  colspan,
  rowspan,
  backgroundColor = 'inherit',
  position = 'static',
  overflowX,
  overflowY,
  minHeight,
  minWidth,
  maxHeight,
  maxWidth,
  margin,
  textAlign,
  border,
  borderBottom,
  borderLeft,
  borderRight,
  borderTop,
  borderRadius,
}) => {
  //Jsx
  return (
    <StyledGridItem
      role="gridcell"
      style={{
        position,
        width,
        height,
        padding,
        alignSelf,
        justifySelf,
        textAlign,
        gridColumn: colspan ? `span ${colspan}` : 'auto',
        gridRow: rowspan ? `span ${rowspan}` : 'auto',
        backgroundColor,
        ...(overflowX && { overflowX }),
        ...(overflowY && { overflowY }),
        ...(margin && { margin }),
        ...(minHeight && { minHeight }),
        ...(maxHeight && { maxHeight }),
        ...(minWidth && { minWidth }),
        ...(maxWidth && { maxWidth }),
        ...(border && { border }),
        ...(borderTop && { borderTop }),
        ...(borderBottom && { borderBottom }),
        ...(borderLeft && { borderLeft }),
        ...(borderRight && { borderRight }),
        ...(borderRadius && { borderRadius }),
        ...(alignContent && { alignContent })
      }}
    >
      {children}
    </StyledGridItem>
  );
};

export { NblGridContainer, NblGridItem };
