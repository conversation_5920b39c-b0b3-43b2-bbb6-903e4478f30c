import { Box, Tabs as MuiTabs, Tab, useTheme } from '@mui/material';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';

interface TabsProps {
  currentTab: number;
  onTabChangeHandler: (param: number) => void;
  tabsList: Array<{ id: string; label: string; disabled?: boolean }>;
}

const Tabs: React.FunctionComponent<TabsProps> = ({ currentTab, onTabChangeHandler, tabsList }: TabsProps) => {
  const theme: NebulaTheme = useTheme();
  const {
    typography,
    palette: { tabs },
  } = theme;
  return (
    <Box
      sx={{
        borderColor: 'divider',
        marginBottom: 5.75,
        '& .MuiTabs-indicator': { height: '6px', bgcolor: tabs.textColor },
        '& .MuiTab-root.Mui-selected': { color: tabs.textColor },
        '& .MuiTab-root': {
          color: tabs.textColor,
          fontWeight: 400,
          '&.Mui-selected': {
            fontWeight: 'bold',
          },
        },
        [theme.breakpoints.down('xl')]: {
          '& .MuiTabs-indicator': { height: '3px' },
        },
      }}
    >
      <MuiTabs value={currentTab < 0 ? 0 : currentTab} aria-label="Tabs">
        {tabsList.map((tab, index) => (
          <Tab
            onClick={() => onTabChangeHandler(index)}
            disabled={tab.disabled}
            sx={{
              fontSize: '1rem',
              ...(index === 0 && { pl: 0 }),
              fontFamily: typography.primaryFontFamily,
              mr: 2,
              '&.Mui-disabled': {
                opacity: '0.38',
              },
              [theme.breakpoints.down('xl')]: {
                minWidth: '47px',
                fontSize: '0.625rem',
                lineHeight: 1.2,
                paddingBottom: '1px',
              },
            }}
            key={`${tab.id}-tab`}
            {...tab}
            id={`${tab.id}-tab`}
          />
        ))}
      </MuiTabs>
    </Box>
  );
};

export default Tabs;
