import { useEffect, useState } from 'react';
import { useApiService } from '../api/ApiService/context';
import { CatalogPermissions } from '../types';
import DomainList from '../types/Interfaces/DomainList';
export interface ApprovalDropDownItem {
  _id: string;
  groupName: string;
}

interface ApprovalLevelAndCount {
  maxApproverCount: number;
  maxApproverLevel: number;
}
const useApprovalGroupData = () => {
  const { apiAdministrationService, apiPermissionService } = useApiService();
  const [approvalGroups, setApprovalGroups] = useState<ApprovalDropDownItem[]>([]);
  const [approverCount, setapproverCount] = useState<ApprovalLevelAndCount>();
  const [level, setLevel] = useState<ApprovalLevelAndCount>();
  const [domainList, setDomainList] = useState<DomainList[]>([]);
  const [catalogPermissions, setCatalogPermissions] = useState<CatalogPermissions>({ catalogNames: [], catalogRoles: [] });

  useEffect(() => {
    const fetchApprovalData = async () => {
      apiPermissionService.getDomains().then((res) => {
        if (res.status) {
          setDomainList(
            res.data.map((domainData) => ({
              ...domainData,
              domainName: domainData.domainName,
            }))
          );
        }
      });
      apiPermissionService.getMultiEnvGroups().then((res) => {
        if (res.status) {
          setApprovalGroups(res.data);
        }
      });
      apiPermissionService.getCatalogPermissions().then((res) => {
        if (res.status) {
          setCatalogPermissions(res.data.catalogPermissions);
        }
      });
      apiAdministrationService.getApproverLevelAndCount().then((res) => {
        if (res.status) {
          setapproverCount(res.data);
          setLevel(res.data);
        }
      });
    };

    fetchApprovalData();
  }, []);
  return { approvalGroups, approverCount, level, catalogPermissions, domainList };
};

export default useApprovalGroupData;
