import React from 'react';
import { render } from '@testing-library/react';
import ViewPermissionTable from 'componentsV2/Administration/Permissions/ViewPermissionTable';

jest.mock('componentsV2/Administration/Permissions/ViewPermissionTable', () => ({
  __esModule: true,
  default: () => <div>Mocked ViewPermission</div>,
}));

describe('ViewPermission', () => {
  it('renders the ViewPermission component', () => {
    const { getByText } = render(<ViewPermissionTable />);
    expect(getByText('Mocked ViewPermission')).toBeInTheDocument();
  });
});
