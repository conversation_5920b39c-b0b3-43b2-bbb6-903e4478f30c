enum RequestStatus {
  PENDING_APPROVAL = 'PENDING APPROVAL',
  APPROVED = 'APPROVED',
  AUTO_APPROVED = 'AUTO APPROVED',
  REJECTED = 'REJECTED',
  PROCESSING = 'PROCESSING',
  SUBMITTED = 'SUBMITTED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  PARTIAL = 'COMPLETED WITH ERROR',
  RELEASED = 'RELEASED',
  PENDING_PIPELINE_CHECK = 'PENDING PIPELINE CHECK',
  PENDING_DAP_PROCESS = 'PENDING DAP PROCESS',
  PENDING_DEPLOYMENT = 'PENDING DEPLOYMENT',
  CREATED = 'CREATED',
  PARTIALLY_APPROVED = 'PARTIALLY APPROVED',
  PARTIALLY_REJECTED = 'PARTIALLY REJECTED',
  CANCELLED = 'CANCELLED',
  TIMED_OUT = 'TIMED OUT',
  SUCCESS = 'SUCCESS',
  CLOSED = 'CLOSED',
  CLOSED_MANUALLY = 'CLOSED MANUALLY',
  PARTIAL_SUCCESS = 'PARTIALLY COMPLETED',
  NO_CHANGE_REQUIRED = 'NO CHANGE REQUIRED',
  RETRYING = 'RETRYING',
  PENDING_RISK_ANALYSIS = 'PENDING RISK ANALYSIS',
  PENDING_DESIGNER_RESULTS = 'PENDING DESIGNER RESULTS',
  COMPLETED_EXTERNALLY = 'COMPLETED EXTERNALLY',
  CANCELLED_EXTERNALLY = 'CANCELLED EXTERNALLY',
}
export default RequestStatus;
