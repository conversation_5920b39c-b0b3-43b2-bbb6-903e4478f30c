//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import { NblTable } from 'sharedComponents/NblTable';
import useDataGridUtils from 'hooks/useDataGridUtils';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
//eslint-disable-next-line no-unused-vars
import { GridRenderEditCellParams } from '@mui/x-data-grid';

type StoryProps = ComponentProps<typeof NblTable>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Table/NblTable',
  component: NblTable,
  parameters: {
    layout: 'left',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

const TableComponent: React.FC<StoryProps> = (props) => {
  const canEdit = useDataGridUtils();
  const handleEditCellChange = (params: GridRenderEditCellParams, event: any) => {
    params.api.setEditCellValue({ id: params.id, field: params.field, value: event.target.value });
  };

  return (
    <NebulaTheme>
      <NblTable
        {...props}
        rows={[
          { id: 1, a: 'NEB-PAAS-DB-22258', c: 'running', d: 'Nebula Q1 Release', f: '********' },
          { id: 2, a: 'NEB-PAAS-DB-22257', c: 'running', d: 'Nebula Q1 Release', f: '********' },
          { id: 3, a: 'NEB-PAAS-DB-22256', c: 'running', d: 'Nebula Q1 Release', f: '********' },
          { id: 4, a: 'NEB-PAAS-DB-22255', c: 'running', d: 'Nebula Q1 Release', f: '********' },
          { id: 5, a: 'NEB-PAAS-DB-22256', c: 'running', d: 'Nebula Q1 Release', f: '********' },
          { id: 6, a: 'NEB-PAAS-DB-22254', c: 'running', d: 'Nebula Q1 Release', f: '********' },
          { id: 7, a: 'NEB-PAAS-DB-22250', c: 'running', d: 'Nebula Q1 Release', f: '********' },
          { id: 8, a: 'NEB-PAAS-DB-22254', c: 'running', d: 'Nebula Q1 Release', f: '********' },
        ]}
        columns={[
          { field: 'id', headerName: 'ID', width: 150 },
          {
            field: 'a',
            headerName: 'Request ID',
            flex: 1,
            editable: canEdit.isEditMode,
            renderCell: (params) =>
              canEdit?.canEditRow(canEdit.selectedRows, params.id, canEdit.isEditMode) ? (
                <NblTextField value={params.value} type={'text'} label={''} name={''} />
              ) : (
                params.value
              ),

            renderEditCell: (params) =>
              canEdit?.canEditRow(canEdit.selectedRows, params.id, canEdit.isEditMode) ? (
                <NblTextField
                  value={params.value}
                  handleChange={(event) => handleEditCellChange(params, event)}
                  type={'text'}
                  label={''}
                  name={''}
                />
              ) : (
                params.value
              ),
          },
          { field: 'd', headerName: 'Project Name', flex: 1 },
          { field: 'f', headerName: 'Created By', flex: 1 },
          { field: 'c', headerName: 'Status', flex: 1 },
        ]}
        rowSize="5"
        pageSizeOptions={['5', '20', '40', '60']}
        setEditMode={canEdit.setEditMode}
        selectedRows={canEdit.selectedRows}
        setSelectedRows={canEdit.setSelectedRows}
      />
    </NebulaTheme>
  );
};

export const Table: Story = {
  args: {
    rows: [
      { id: 1, a: 'NEB-PAAS-DB-22258', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: '********', g: '12/06/2024 11:10 AM' },
      { id: 2, a: 'NEB-PAAS-DB-22257', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: '********', g: '12/06/2024 11:10 AM' },
      { id: 3, a: 'NEB-PAAS-DB-22256', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: '********', g: '12/06/2024 11:10 AM' },
      { id: 4, a: 'NEB-PAAS-DB-22255', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: '********', g: '12/06/2024 11:10 AM' },
      { id: 5, a: 'NEB-PAAS-DB-22256', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: '********', g: '12/06/2024 11:10 AM' },
      { id: 6, a: 'NEB-PAAS-DB-22254', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: '********', g: '12/06/2024 11:10 AM' },
      { id: 7, a: 'NEB-PAAS-DB-22250', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: '********', g: '12/06/2024 11:10 AM' },
      { id: 8, a: 'NEB-PAAS-DB-22254', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: '********', g: '12/06/2024 11:10 AM' },
    ],
    columns: [
      { field: 'id', headerName: 'ID', flex: 1, width: 90 },
      { field: 'a', headerName: 'Request ID', flex: 1 },
      { field: 'c', headerName: 'Status', flex: 1 },
      { field: 'd', headerName: 'Project Name', flex: 1 },
      { field: 'e', headerName: 'Approval Date', flex: 1 },
      { field: 'f', headerName: 'Created By', flex: 1 },
      { field: 'g', headerName: 'Created Date', flex: 1 },
    ],
    rowSize: '5',
    pageSizeOptions: ['5', '20', '40', '60'],
    showResetFilter: false,
  },
  render: (args) => (
    <NblFlexContainer center>
      <NebulaTheme>
        <TableComponent {...args} />
      </NebulaTheme>
    </NblFlexContainer>
  ),
};
