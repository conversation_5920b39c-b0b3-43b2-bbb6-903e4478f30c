import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Link, useTheme } from '@mui/material';

import { useApiService } from 'api/ApiService/context';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblDivider from 'sharedComponents/NblDivider';
import { StyledRequestStatus, StyledDateTypography } from './styled';
import { dateFormatter, getHexColorWithOpacity } from 'utils/common';
import { NebulaTheme } from 'NebulaTheme/type';
import { ApprovalStatus } from 'types/Enums';
import NblTypography from 'sharedComponents/NblTypography';
import useNblNavigate from 'hooks/useNblNavigate';
import useCatalogRequests from 'hooks/useCatalogRequests';
import { Fragment } from 'react';

interface CatalogRequestsProps {}

interface CatalogRequestData {
  id: string;
  status: ApprovalStatus;
  createdAt: string;
}

const CatalogRequests: React.FC<CatalogRequestsProps> = () => {
  //Hooks
  const { palette } = useTheme<NebulaTheme>();
  const navigate = useNblNavigate();
  const { groupCatalogItem } = useCatalogRequests();
  const apiAssetService = useApiService();
  const { catalogLevel1 } = useParams();

  //States
  const [requestsData, setRequestsData] = useState<CatalogRequestData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const requestTypes = groupCatalogItem(1, 'value', 'requestType');

  //Local
  const approvalStatuses = [ApprovalStatus.PENDING, ApprovalStatus.APPROVED, ApprovalStatus.REJECTED];

  //Side Effects
  useEffect(() => {
    if (catalogLevel1 && requestTypes[catalogLevel1]) {
      const filter = {
        requestType: {
          in: requestTypes[catalogLevel1],
        },
      };
      fetchRequestsListPaginated(1, 8, '', JSON.stringify(filter));
    }

    return () => {
      setRequestsData([]);
    };
  }, [catalogLevel1, requestTypes]);

  //Utils
  const fetchRequestsListPaginated = (page: number, pageSize: number, sort?: string, filter?: string) => {
    setIsLoading(true);
    apiAssetService.apiAssetService.getMyRequestsv3(page, pageSize, sort, filter).then((res) => {
      if (res.status) {
        const formattedData = res.data.items.map((asset) => ({
          id: asset?.serviceRequestId || asset?._id,
          status: asset?.approvalStatus as ApprovalStatus,
          createdAt: asset?.createdAt ? dateFormatter(asset.createdAt) : '',
        }));
        setRequestsData(formattedData);
        setIsLoading(false);
      } else {
        setRequestsData([]);
      }
    });
  };

  //Handlers
  function handleRequestRowClick(id: string) {
    navigate(`/requests/${id}`);
  }

  const renderRequest = (request: CatalogRequestData, index: number) => {
    const totalIndex = requestsData.length - 1;
    return (
      <>
        <NblFlexContainer spacing={2} alignItems={'center'} margin={`0 0 ${index < totalIndex ? '0' : '40px'} 14px`} height="auto">
          <StyledRequestStatus status={request.status as ApprovalStatus} />
          <NblFlexContainer direction="column">
            <Link variant="button" onClick={() => handleRequestRowClick(request.id)} sx={{ textDecoration: 'none', cursor: 'pointer' }}>
              <NblTypography variant="subtitle1" color="shade1">
                {request.id}
              </NblTypography>
            </Link>
            <StyledDateTypography variant="body3">{request.createdAt}</StyledDateTypography>
          </NblFlexContainer>
        </NblFlexContainer>
        {index < totalIndex && (
          <NblDivider
            length="100%"
            strokeWidth={0.5}
            color={getHexColorWithOpacity(palette.nbldivider.variant4, 40)}
            borderRadius={1}
            mt={'10px'}
            mb={'10px'}
          />
        )}
      </>
    );
  };

  //Renders
  const renderRequestLegend = (status: ApprovalStatus) => {
    return (
      <NblFlexContainer spacing={1} height="auto" width="auto">
        <StyledRequestStatus status={status} />
        <NblTypography variant={'body4'} color="shade1">
          {status}
        </NblTypography>
      </NblFlexContainer>
    );
  };

  //JSX
  return (
    <NblFlexContainer direction="column" overflowY={'auto'}>
      {!isLoading && !requestsData.length ? (
        <NblFlexContainer center>
          <NblTypography textAlign={'center'} color="shade1" variant="body1">
            No records Found
          </NblTypography>
        </NblFlexContainer>
      ) : (
        requestsData.map((request, i) => <Fragment key={request.id ?? i}>{renderRequest(request, i)}</Fragment>)
      )}
      <NblFlexContainer
        center
        height={'auto'}
        position={'absolute'}
        bottom={'0'}
        left={'0'}
        spacing={2}
        padding={'20px'}
        backgroundColor={palette.secondary.shade1}
      >
        {approvalStatuses.map((status, i) => (
          <Fragment key={status ?? i}>{renderRequestLegend(status)}</Fragment>
        ))}
      </NblFlexContainer>
    </NblFlexContainer>
  );
};

export default CatalogRequests;
