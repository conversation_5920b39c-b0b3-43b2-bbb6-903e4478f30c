//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NebulaTheme from 'NebulaTheme';
import { ComponentProps } from 'react';
import NblPreviewContainer from 'sharedComponents/NblContainers/NblPreviewContainer';

type StoryProps = ComponentProps<typeof NblPreviewContainer>;

export default {
  title: 'Containers/Preview/NblPreview Container',
  tags: ['autodocs'],
  component: NblPreviewContainer,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    title: { type: 'string' },
    subTitle: { type: 'string' },
  },
} as Meta<StoryProps>;

type Story = StoryObj<StoryProps>;

const Template = () => {
  //Jsx
  return (
    <div style={{ display: 'flex', flexDirection: 'column', padding: '10px', fontSize: '0.9rem' }}>
      <>
        <h4>Your details are:</h4>
        <p>Name : Name</p>
        <p>Age :22</p>
      </>
    </div>
  );
};

const Preview = (args: StoryProps) => {
  //JSX
  return (
    <NblPreviewContainer {...args}>
      <Template />
    </NblPreviewContainer>
  );
};

export const Default: Story = {
  args: {
    title: 'Virtual Machine-Windows',
    subTitle: 'NEB-IAAS-VM-12345',
  },
  render: (args) => {
    return (
      <NebulaTheme>
        <Preview {...args} />
      </NebulaTheme>
    );
  },
};
