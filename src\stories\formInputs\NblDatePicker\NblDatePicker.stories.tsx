// eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblDatePicker from 'sharedComponents/NblFormInputs/NblDatePicker';
import { Dayjs } from 'dayjs';
import React, { useState } from 'react';

type StoryProps = ComponentProps<typeof NblDatePicker>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblDatePicker',
  component: NblDatePicker,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { type: 'string' },
    name: { type: 'string' },
    helperText: { control: 'text' },
  },
};

export default meta;

const Template: React.FC<StoryProps> = (args) => {
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(null);

  const handleChange = (newValue: Dayjs | null) => {
    setSelectedDate(newValue);
  };

  return (
    <NebulaTheme>
      <NblFlexContainer width="300px">
        <NblDatePicker {...args} value={selectedDate} handleChange={handleChange} />
      </NblFlexContainer>
    </NebulaTheme>
  );
};

export const DateField: Story = {
  args: {
    label: 'Date Picker',
    name: 'Name',
    disabled: false,
    helperText: 'helper text',
    error: false,
    mandatory: false,
    value: null,
  },
  render: (args) => <Template {...args} />,
};
