import { act, render, waitFor } from '@testing-library/react';
import { MemoryRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import ThemeProvider from 'mock/ThemeProvider';
import ViewRole from '.';
import { RolesRows } from 'mock/roles';
import RoleService from 'api/ApiService/RoleService';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'roles', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/roles/view-roles'];

describe('ViewRoles Component', () => {
  let getRolesSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getRolesSpy = jest.spyOn(RoleService.prototype, 'getRoles');

    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Should display the No Record Found message', async () => {
    getRolesSpy.mockResolvedValue({ status: false, data: [] });
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <Router initialEntries={BASE_ROUTE}>
            <ThemeProvider>
              <ViewRole permissions={{ canRead: true }} />
            </ThemeProvider>
          </Router>
        </ReduxProvider>
      )
    );
    await waitFor(() => {
      expect(getRolesSpy).toHaveBeenCalled();
    });
    expect(getByText('No Record Found')).toBeInTheDocument();
  });

  test('Should render the table with data ', async () => {
    getRolesSpy.mockResolvedValue({ status: false, data: RolesRows.data });
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <Router initialEntries={BASE_ROUTE}>
            <ThemeProvider>
              <ViewRole permissions={{ canRead: true }} />
            </ThemeProvider>
          </Router>
        </ReduxProvider>
      )
    );
    await waitFor(() => {
      expect(getRolesSpy).toHaveBeenCalled();
    });

    expect(getByText('Role Name')).toBeInTheDocument();
    expect(getByText('Permission Name')).toBeInTheDocument();
    expect(getByText('Description')).toBeInTheDocument();
    expect(getByText('Created Date')).toBeInTheDocument();
    expect(getByText('Created By')).toBeInTheDocument();
    expect(getByText('Updated Date')).toBeInTheDocument();
    expect(getByText('Updated By')).toBeInTheDocument();
  });
});
