import { useApiService } from 'api/ApiService/context';
import ResourceViewDetail from 'componentsV2/ResourceViewDetail';
import useCatalogRequests from 'hooks/useCatalogRequests';
import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { ResourcesDetails } from 'types';
import { dateFormatter } from 'utils/common';

const ResourceDetails = () => {
  //Hooks
  const { resourceId } = useParams();

  const { apiAssetService } = useApiService();
  const { groupCatalogItem } = useCatalogRequests();
  const [loading, setloading] = useState(true);

  //States
  const [resourceData, setResourceData] = useState<ResourcesDetails>({} as ResourcesDetails);
  const [componentName, setComponentName] = useState('');

  //Utils
  const fetchResource = useCallback(() => {
    const filter = JSON.stringify({
      resourceId: {
        contains: resourceId,
      },
    });
    setloading(true);
    setResourceData({} as ResourcesDetails);
    apiAssetService.getMyResourcesv2(1, 1, '', filter).then((res) => {
      if (res.status) {
        const asset = res.data.items[0];
        setResourceData({
          ...asset,
          ipv4Address: asset?.ipv4Address || asset?.ipaddress,
          resourceId: asset?.resourceId,
          createdAt: asset?.createdAt ? dateFormatter(asset.createdAt) : '',
          statusDate: asset?.statusDate ? dateFormatter(asset.statusDate) : '',
          updatedAt: asset?.updatedAt ? dateFormatter(asset.updatedAt) : '',
        });
      }
      setloading(false);
    });
  }, [resourceId]);

  //Side Effects
  useEffect(() => {
    if (resourceId) fetchResource();
  }, [resourceId]);

  useEffect(() => {
    if (resourceData.requestType) {
      const catalogItem = groupCatalogItem(4, 'requestType', 'component');
      setComponentName(catalogItem[resourceData.requestType]?.[0]);
    }
  }, [resourceData, groupCatalogItem]);

  //JSX
  return (
    <>
      <ResourceViewDetail
        componentName={componentName}
        title="Resource Details"
        resourceData={resourceData}
        fetchResource={fetchResource}
        setResourceData={setResourceData}
        loading={loading}
      />
    </>
  );
};

export default ResourceDetails;
