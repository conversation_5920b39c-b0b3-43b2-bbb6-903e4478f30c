import { act, render, waitFor } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import { MemoryRouter as Router } from 'react-router-dom';
import ViewRole from '.';
import AdministrationService from 'api/ApiService/AdministrationService';
import { TeamRows } from 'mock/teams';
import ViewTeam from 'pages/Administration/Teams/ViewTeams';

describe('ViewRoles Component', () => {
  let getTeamsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getTeamsSpy = jest.spyOn(AdministrationService.prototype, 'getTeams');
  });

  afterEach(() => {
    getTeamsSpy.mockRestore();
  });

  test('Should display the No Record Found message', async () => {
    getTeamsSpy.mockResolvedValue({ status: false, data: [] });
    const { getByText } = await act(async () =>
      render(
        <Router>
          <ThemeProvider>
            <ViewRole />
          </ThemeProvider>
        </Router>
      )
    );
    await waitFor(() => {
      expect(getTeamsSpy).toHaveBeenCalled();
    });
    expect(getByText('No Record Found')).toBeInTheDocument();
  });
  test('Should render the table with data ', async () => {
    getTeamsSpy.mockResolvedValue({ status: false, data: TeamRows.data });
    const { getByText } = await act(async () =>
      render(
        <Router>
          <ThemeProvider>
            <ViewTeam />
          </ThemeProvider>
        </Router>
      )
    );
    await waitFor(() => {
      expect(getTeamsSpy).toHaveBeenCalled();
    });

    expect(getByText('Team Name')).toBeInTheDocument();
    expect(getByText('Groups')).toBeInTheDocument();
    expect(getByText('Description')).toBeInTheDocument();
  });
});
