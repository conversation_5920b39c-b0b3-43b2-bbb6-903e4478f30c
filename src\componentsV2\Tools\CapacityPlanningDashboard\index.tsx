import { Box } from '@mui/material';
import { Route, Routes } from 'react-router';
import UtilizationStatus from './utilizationStatus';
import CplanMainDashboard from './CplanMainDashboard';
import CplanSunburstScreen from './CplanSunburstScreen';
// import CplanSunburstLevelTwoScreen from './CplanSunburstScreen/CplanSunburstLevelTwoScreen';

const CapacityPlanningDashboard: React.FunctionComponent = () => {
  return (
    <>
      <>
        <Routes>
          <Route path={'*'} element={<CplanMainDashboard />} />
          <Route path={'/:vcenterId'} element={<CplanSunburstScreen />} />
          <Route path={'/:vcenterId/:datacenterId'} element={<CplanSunburstScreen />} />
          <Route path={'/:vcenterId/:datacenterId/:clusterId'} element={<CplanSunburstScreen />} />
          <Route path={'/:vcenterId/:datacenterId/:clusterId/:vmId'} element={<CplanSunburstScreen />} />
        </Routes>
      </>
      <Box sx={{ display: 'inline-flex', position: 'absolute', bottom: 60, right: 20 }}>
        <UtilizationStatus />
      </Box>
    </>
  );
};

export default CapacityPlanningDashboard;
