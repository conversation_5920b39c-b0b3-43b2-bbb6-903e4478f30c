import { render, screen, fireEvent } from '@testing-library/react';
import NblFormButtonGroup from './index';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

const mockNavigate = jest.fn();
jest.mock('hooks/useNblNavigate', () => () => mockNavigate);
jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  useNblForms: () => ({
    nblFormProps: {
      handleChange: jest.fn(),
      handleBlur: jest.fn(),
      setFieldValue: jest.fn(),
      errors: {},
      touched: {},
    },
    nblFormValues: {
      permissionName: '',
      permissionKey: '',
      description: '',
    },
  }),
}));

const mockFormProps = {
  isSubmitting: false,
  isValid: true,
} as any;

const defaultProps = {
  nblFormProps: mockFormProps,
  onNextClickHandler: jest.fn(),
  onPreviousClickHandler: jest.fn(),
  onPreviewClickHandler: jest.fn(),
  isValid: true,
};

describe('NblFormButtonGroup', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders homepage button when onSuccess is true', () => {
    render(<NebulaThemeProvider><NblFormButtonGroup {...defaultProps} onSuccess /></NebulaThemeProvider>);
    expect(screen.getByText('Go to Homepage')).toBeInTheDocument();
    fireEvent.mouseDown(screen.getByText('Go to Homepage'));
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('renders Previous button and triggers handler', () => {
    render(<NebulaThemeProvider><NblFormButtonGroup {...defaultProps} showPrevious /></NebulaThemeProvider>);
    const btn = screen.getByText('Previous');
    expect(btn).toBeInTheDocument();
    fireEvent.mouseDown(btn);
    expect(defaultProps.onPreviousClickHandler).toHaveBeenCalledWith(mockFormProps);
  });

  it('renders Cancel button and triggers handler', () => {
    const onCancel = jest.fn();
    render(<NebulaThemeProvider><NblFormButtonGroup {...defaultProps} showCancel onCancel={onCancel} /></NebulaThemeProvider>);
    const btn = screen.getByText('Cancel');
    expect(btn).toBeInTheDocument();
    fireEvent.mouseDown(btn);
    expect(onCancel).toHaveBeenCalled();
  });

  it('renders Preview button and triggers handler', () => {
    render(<NebulaThemeProvider><NblFormButtonGroup {...defaultProps} showPreview /></NebulaThemeProvider>);
    const btn = screen.getByText('Preview');
    expect(btn).toBeInTheDocument();
    fireEvent.mouseDown(btn);
    expect(defaultProps.onPreviewClickHandler).toHaveBeenCalledWith(mockFormProps);
  });

  it('renders Next button and triggers handler', () => {
    render(<NebulaThemeProvider><NblFormButtonGroup {...defaultProps} showNext /></NebulaThemeProvider>);
    const btn = screen.getByText('Next');
    expect(btn).toBeInTheDocument();
    fireEvent.mouseDown(btn);
    expect(defaultProps.onNextClickHandler).toHaveBeenCalledWith(mockFormProps);
  });

  it('renders Submit button with custom text and disables based on props', () => {
    render(
      <NebulaThemeProvider>
        <NblFormButtonGroup
          {...defaultProps}
          showSubmit
          canCreate={true}
          canUpdate={true}
          submitText="Save"
        />
      </NebulaThemeProvider>
    );
    const btn = screen.getByText('Save');
    expect(btn).toBeInTheDocument();
    expect(btn).not.toBeDisabled();
  });

  it('disables Submit button when conditions are not met', () => {
    render(
      <NebulaThemeProvider>
        <NblFormButtonGroup
          {...defaultProps}
          showSubmit
          canCreate={false}
          canUpdate={false}
          submitText="Save"
        />
      </NebulaThemeProvider>
    );
    const btn = screen.getByText('Save');
    expect(btn).toBeDisabled();
  });
});
