import React, { useEffect, useState } from 'react';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import { generateEnum, getDatacenterOptions, getDomainOptions } from 'utils/common';
import { FormValues } from '..';
import NblAutoComplete from 'sharedComponents/NblFormInputs/NblAutoComplete';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import useGetDomainData from 'hooks/useGetDomainData';
import { FormProps } from 'react-router-dom';
import { useApiService } from 'api/ApiService/context';
import { dispatch } from 'store';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { SettingTypes } from 'types/Enums';
// eslint-disable-next-line no-unused-vars
import { MultiENVApplication, MultiENVEnvironment, MultiENVProject } from 'types';

export interface AddVCenterFormProps extends FormProps {
  isHover?: string;
}

const AddVCenterForm: React.FunctionComponent<AddVCenterFormProps> = () => {
  const { nblFormProps, nblFormValues } = useNblForms<FormValues>();
  const { domainData } = useGetDomainData();

  const FIELDNAMES = generateEnum<FormValues>(nblFormValues);
  const [isHovered, setIsHovered] = useState<string>('');
  const { apiComputeService } = useApiService();

  const hoverHandler = (value: string) => {
    setIsHovered(value);
  };

  const getProtocolOptions = [
    { label: 'HTTP', value: 'http' },
    { label: 'HTTPS', value: 'https' },
  ];

  const fetchCloudNameOptions = () => {
    if (!nblFormValues.domain) return;

    dispatch(showSpinner({ id: SPINNER_IDS.vmNetworkOptions, status: true, message: 'Loading Cloud Name options...' }));

    apiComputeService
      .getMultiENVProjectDetails()
      .then((res) => {
        if (res.status && Array.isArray(res.data)) {
          const cloudNames: string[] = [];
          const selectedDomainObj = domainData.find((domain) => domain.domainName === nblFormValues.domain);
          const selectedDomainId = selectedDomainObj?.id;
          res.data.forEach((project: MultiENVProject) => {
            project.applications?.forEach((app: MultiENVApplication) => {
              app.environments?.forEach((env: MultiENVEnvironment) => {
                env.settings
                  ?.filter((setting: any) => setting.type === SettingTypes.VLAN_VMWARE)
                  .forEach((setting: any) => {
                    setting.configurations?.forEach((config: any) => {
                      if (config.domain === selectedDomainId) {
                        cloudNames.push(config.value.cloudName);
                      }
                    });
                  });
              });
            });
          });
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: SPINNER_IDS.vmNetworkOptions, status: false, message: '' }));
      });
  };

  useEffect(() => {
    if (nblFormValues.domain) {
      fetchCloudNameOptions();
    }
  }, [nblFormValues.domain]);

  return (
    <NblGridContainer columns={4} spacingX={3}>
      <NblGridItem>
        <NblAutoComplete
          label={'Domain'}
          mandatory
          name={FIELDNAMES.domain}
          placeholder={'Select'}
          value={nblFormValues.domain}
          onChange={(selectedVal) => nblFormProps.setFieldValue(FIELDNAMES.domain, selectedVal)}
          handleBlur={nblFormProps.handleBlur}
          options={getDomainOptions(domainData)}
          helperText={nblFormProps.errors.domain}
          error={Boolean(nblFormProps.touched.domain && nblFormProps.errors.domain)}
        />
      </NblGridItem>
      <NblGridItem>
        <NblAutoComplete
          label={'Cloud Datacenter'}
          mandatory
          name={FIELDNAMES.cloudDatacenter}
          placeholder={'Select'}
          value={nblFormValues.cloudDatacenter}
          onChange={(selectedVal) => nblFormProps.setFieldValue(FIELDNAMES.cloudDatacenter, selectedVal)}
          handleBlur={nblFormProps.handleBlur}
          disabled={!nblFormValues.domain}
          options={getDatacenterOptions(domainData, nblFormValues.domain)}
          helperText={
            isHovered === FIELDNAMES.cloudDatacenter && !nblFormValues.domain
              ? 'Please select the Domain'
              : nblFormProps.errors.cloudDatacenter || ' '
          }
          onMouseEnter={() => hoverHandler(FIELDNAMES.cloudDatacenter)}
          onMouseLeave={() => hoverHandler('')}
          error={Boolean(nblFormProps.touched.cloudDatacenter && nblFormProps.errors.cloudDatacenter)}
        />
      </NblGridItem>
      <NblGridItem margin="-5px 0 0 0">
        <NblTextField
          type={'text'}
          label={'VCenter Name'}
          mandatory
          name={FIELDNAMES.vCenterName}
          placeholder="Please Enter"
          value={nblFormValues.vCenterName}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          tooltipText={'This is the unique name to indentify vCenter'}
          helperText={nblFormProps.errors.vCenterName}
          error={Boolean(nblFormProps.touched.vCenterName && nblFormProps.errors.vCenterName)}
        />
      </NblGridItem>
      <NblGridItem>
        <NblTextField
          type={'text'}
          label={'VCenter Host'}
          mandatory
          name={FIELDNAMES.vCenterHost}
          placeholder="Please Enter"
          value={nblFormValues.vCenterHost}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          helperText={nblFormProps.errors.vCenterHost}
          error={Boolean(nblFormProps.touched.vCenterHost && nblFormProps.errors.vCenterHost)}
        />
      </NblGridItem>
      <NblGridItem>
        <NblTextField
          type={'number'}
          label={'VCenter Port'}
          mandatory
          name={FIELDNAMES.vCenterPort}
          placeholder="Please Enter"
          value={nblFormValues.vCenterPort}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          helperText={nblFormProps.errors.vCenterPort}
          error={Boolean(nblFormProps.touched.vCenterPort && nblFormProps.errors.vCenterPort)}
        />
      </NblGridItem>
      <NblGridItem>
        <NblAutoComplete
          label={'VCenter Protocol'}
          mandatory
          name={FIELDNAMES.vCenterProtocol}
          placeholder={'Select'}
          value={nblFormValues.vCenterProtocol}
          onChange={(selectedVal) => nblFormProps.setFieldValue(FIELDNAMES.vCenterProtocol, selectedVal)}
          handleBlur={nblFormProps.handleBlur}
          options={getProtocolOptions}
          helperText={nblFormProps.errors.vCenterProtocol}
          error={Boolean(nblFormProps.touched.vCenterProtocol && nblFormProps.errors.vCenterProtocol)}
        />
      </NblGridItem>
      <NblGridItem>
        <NblTextField
          type={'text'}
          label={'VCenter User'}
          mandatory
          name={FIELDNAMES.vCenterUser}
          placeholder="Please Enter"
          value={nblFormValues.vCenterUser}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          helperText={nblFormProps.errors.vCenterUser}
          error={Boolean(nblFormProps.touched.vCenterUser && nblFormProps.errors.vCenterUser)}
        />
      </NblGridItem>
      <NblGridItem>
        <NblTextField
          type={'password'}
          name={FIELDNAMES.vCenterPassword}
          label="VCenter Password"
          placeholder="Enter Password"
          value={nblFormValues.vCenterPassword}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          helperText={nblFormProps.errors.vCenterPassword}
          error={Boolean(nblFormProps.touched.vCenterPassword && nblFormProps.errors.vCenterPassword)}
          mandatory
        />
      </NblGridItem>
    </NblGridContainer>
  );
};
export default AddVCenterForm;
