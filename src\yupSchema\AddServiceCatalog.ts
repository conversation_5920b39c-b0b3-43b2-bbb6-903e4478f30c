import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';

const TagsSchema = yup.object().shape({
  key: yup.string().required('Key is required'),
  value: yup.string().required('Value is required'),
  active: yup.string().required('Active is required'),
  description: yup
    .string()
    .required('Description is required')
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
    overridable: yup.string().required('Override is required'),
});

export const validationSchema = yup.object().shape({
  levelType: yup.string().required('Level is required'),
  level01: yup.string().test('is-required', 'Level 01 is required', function (value) {
    const { levelType } = this.parent;
    if (levelType === 'Level 02' || levelType === 'Level 03') {
      return !!value;
    }
    return true;
  }),
  level02: yup.string().test('is-required', 'Level 02 is required', function (value) {
    const { levelType } = this.parent;
    if (levelType === 'Level 03') {
      return !!value;
    }
    return true;
  }),
  name: yup
    .string()
    .trim()
    .required('Name is required')
    .matches(yupMatchesParams.catalogName.pattern, yupMatchesParams.catalogName.errorMessage),
  icon: yup.string().required('Icon is required'),
  description: yup
    .string()
    .trim()
    .required('Description is required')
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
  catalogTags: yup.array().of(TagsSchema),
});
