import React, { useEffect } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid } from '@mui/material';

import { formWrapperError, filterSelectedOptions } from 'utils/common';
import MultiSelect from 'components/MultiSelect';
import Select from 'components/Select';
import FormWrapper from 'components/FormWrapper';
import { AdminTilePermissions, SelectedGroupPermissions } from 'types';
import DialogBox from 'components/DialogBox/Dialog';

interface AdminPermissionProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  adminTilePermissions: AdminTilePermissions;
  selectedAdminPermissions: SelectedGroupPermissions['adminTilePermissions'];
  setAdminData: (values: any) => void;
  adminTileDetails?: { adminTileId: string; roles: string[] };
}

const validationSchema = yup.object().shape({
  adminTile: yup.string().trim().required('Admin Tile is required'),
  adminRoles: yup.array().of(yup.string()).min(1, 'Please select atleast one admin role').required('Admin Role is required'),
});

const AdminPermission: React.FunctionComponent<AdminPermissionProps> = ({
  open,
  onClose,
  setAdminData,
  adminTileDetails,
  adminTilePermissions,
  selectedAdminPermissions,
}: AdminPermissionProps) => {
  const formik = useFormik({
    initialValues: {
      adminTile: '',
      adminRoles: [],
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const { adminNames, adminRoles } = adminTilePermissions;
      const selectedAdmin = adminNames.find((admin) => admin.id === values.adminTile);
      setAdminData({
        adminTileId: selectedAdmin?.id,
        tileName: selectedAdmin?.tileName,
        roles: values.adminRoles.map((id) => adminRoles.find((roles) => roles._id === id)),
      });
    },
  });

  useEffect(() => {
    if (adminTileDetails) {
      formik.setValues({
        adminTile: adminTileDetails.adminTileId,
        /* @ts-ignore */
        adminRoles: adminTileDetails.roles,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [adminTileDetails]);

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const getAdminOptions = () => {
    const adminNames = adminTilePermissions.adminNames.map((admin) => ({ value: admin.id, label: admin.tileName }));
    const selectedAdminIds = selectedAdminPermissions.map((selectedPermissions) => selectedPermissions.adminTileId);
    return filterSelectedOptions(adminNames, selectedAdminIds, adminTileDetails?.adminTileId);
  };

  const renderFormFields = () => {
    return (
      <Grid container spacing={1}>
        <Grid container item justifyContent={'center'} spacing={3}>
          <Grid item xs={12}>
            <Select
              value={formik.values.adminTile}
              label="Admin Tile *"
              placeholder="Select"
              name="adminTile"
              handleChange={formik.handleChange}
              error={formik.touched.adminTile && formik.errors.adminTile}
              options={getAdminOptions()}
            />
          </Grid>
          <Grid item xs={12}>
            <MultiSelect
              value={formik.values.adminRoles}
              label="Admin Roles *"
              placeholder="Select"
              name="adminRoles"
              handleChange={formik.handleChange}
              error={formik.touched.adminRoles && formik.errors.adminRoles}
              options={adminTilePermissions.adminRoles.map((roles) => ({ label: roles.roleName, value: roles._id }))}
            />
          </Grid>
        </Grid>
      </Grid>
    );
  };

  return (
    <DialogBox fullWidth maxWidth={'sm'} open={open} onClose={onCancel}>
      <FormWrapper
        title={`${adminTileDetails?.adminTileId ? 'Edit' : 'Add'} Admin Tile Permissions`}
        errors={formWrapperError(formik)}
        submitText={'Save'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        isPopUpView
      >
        {renderFormFields()}
      </FormWrapper>
    </DialogBox>
  );
};

export default AdminPermission;
