import React, { ReactNode } from 'react';
import { StyledIconButton } from './styled';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import { EditIcon } from 'assets/images/icons/custom-icons';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';

export interface Data {
  name: string;
  value: string | number | ReactNode;
}

interface NblDataViewerProps {
  data: Data[];
  title?: string;
  isEdit?: boolean;
  onEditClick?: () => void;
  columnMinWidth?: string;
  columns?: string | number;
}

const NblDataViewer: React.FunctionComponent<NblDataViewerProps> = ({
  data,
  title,
  isEdit,
  onEditClick,
  columnMinWidth = '200px',
  columns = 'auto-fit',
}) => {
  return (
    <>
      {title && (
        <NblFlexContainer spacing={2}>
          <NblTypography variant="h3" weight={'bold'} color={'shade1'}>
            {title}
          </NblTypography>

          {isEdit && onEditClick && (
            <StyledIconButton type="button" onClick={onEditClick}>
              <EditIcon />
              <NblTypography variant="body1" color={'shade11'}>
                Edit
              </NblTypography>
            </StyledIconButton>
          )}
        </NblFlexContainer>
      )}
      <NblGridContainer spacing={2} height="auto" columnMinWidth={columnMinWidth} columns={columns} padding="10px 0px">
        {data.map((item, index) => (
          <NblGridItem key={index} height="auto">
            <NblTypography variant="subtitle1" weight="regular" color={'shade1'} textTransform={'capitalize'}>
              {item.name}
            </NblTypography>

            <NblTypography
              variant="subtitle1"
              padding="5px 0"
              weight={'medium'}
              color={'shade1'}
              whiteSpace={'pre-line'}
              wordBreak={'break-word'}
            >
              {item.value || '-'}
            </NblTypography>
          </NblGridItem>
        ))}
      </NblGridContainer>
    </>
  );
};

export default NblDataViewer;
