import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MemoryRouter } from 'react-router-dom';
import Clusters, { Cluster } from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { useApiService } from 'api/ApiService/context';

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

const mockClusters: Cluster[] = [
  {
    clusterMor: 'cluster1',
    clusterStatus: 'Healthy',
    type: 'Production',
    disabled: false,
    drsEnabled: true,
    haEnabled: true,
    hostCount: 2,
    hosts: ['host-1', 'host-2'],
    name: 'Cluster One',
    restricted: false,
    vmCount: 10,
  },
];

const mockHosts = [
  {
    hostMor: 'host-1',
    name: 'Host One',
    connectionState: 'https',
    powerState: 'power',
    disabled: true,
    location: 'london',
    vendor: 'horizon',
    model: 'new',
    cpu: 10,
    core: 100,
    memory: 25,
  },
  {
    hostMor: 'host-2',
    name: 'Host Two',
    connectionState: 'http',
    powerState: 'power',
    disabled: false,
    location: 'london',
    vendor: 'horizon',
    model: 'new',
    cpu: 10,
    core: 100,
    memory: 25,
  },
];

const mockGetProjects = jest.fn().mockResolvedValue({
  status: true,
  data: [
    { id: 'p1', name: 'Project One' },
    { id: 'p2', name: 'Project Two' },
  ],
});

beforeEach(() => {
  (useApiService as jest.Mock).mockReturnValue({
    apiComputeService: { getMultiENVProjectDetails: mockGetProjects },
  });
});

const store = configureStore({ reducer: (state = {}) => state });

const setup = (overrideProps = {}) => {
  const onChange = jest.fn();
  const props = {
    clusters: mockClusters,
    hosts: mockHosts,
    onChange,
    ...overrideProps,
  };

  const utils = render(
    <Provider store={store}>
      <MemoryRouter>
        <NebulaThemeProvider>
          <Clusters {...props} />
        </NebulaThemeProvider>
      </MemoryRouter>
    </Provider>
  );
  return { ...utils, onChange };
};

describe('Clusters Component', () => {
  it('switches to details view when cluster name is clicked', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <NebulaThemeProvider>
            <Clusters clusters={mockClusters} hosts={mockHosts} onChange={jest.fn()} />
          </NebulaThemeProvider>
        </MemoryRouter>
      </Provider>
    );
    fireEvent.click(screen.getByText('Cluster One'));
    expect(screen.getByText('Add Project')).toBeInTheDocument();
  });

  it('updates cluster disabled state on checkbox toggle', () => {
    const onChangeMock = jest.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <NebulaThemeProvider>
            <Clusters clusters={mockClusters} hosts={mockHosts} onChange={onChangeMock} />
          </NebulaThemeProvider>
        </MemoryRouter>
      </Provider>
    );
    const checkbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(checkbox);
    expect(onChangeMock).toHaveBeenCalled();
  });

  it('returns to list view when back icon is clicked', async () => {
    setup();
    fireEvent.click(screen.getByText('Cluster One'));
    await waitFor(() => {
      expect(screen.getByText('Add Project')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByLabelText('Back to list'));
    expect(screen.getByText('Cluster One')).toBeInTheDocument();
  });

  it('displays "No Clusters Found" when clusters are empty', () => {
    setup({ clusters: [] });
    expect(screen.getByText('No Clusters Found')).toBeInTheDocument();
  });

  it('calls onChange when "Restricted" checkbox is toggled', () => {
    const { onChange } = setup();
    const checkbox = screen.getAllByRole('checkbox')[1];
    fireEvent.click(checkbox);
    expect(onChange).toHaveBeenCalled();
  });

  it('renders Hosts tab on tab switch', async () => {
    setup();
    fireEvent.click(screen.getByText('Cluster One'));
    await waitFor(() => {
      expect(screen.getByText('Add Project')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Hosts'));
    expect(screen.getByText('Host One')).toBeInTheDocument();
    expect(screen.getByText('Host Two')).toBeInTheDocument();
  });
});
