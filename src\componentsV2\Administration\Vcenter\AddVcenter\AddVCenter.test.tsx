import React from 'react';
import { render, screen } from '@testing-library/react';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { MemoryRouter } from 'react-router-dom';
import MemoizedApiServiceProvider from 'api/ApiService/context';
import { Provider as ReduxProvider } from 'react-redux';
import { store } from 'store';
import AddVCenter from '.';

jest.mock('./AddVCenterForm', () => {
  const React = require('react');
  const MockComponent = React.forwardRef(() => React.createElement('div', null, 'Mocked AddVCenterForm'));
  return { __esModule: true, default: MockComponent };
});

jest.mock('hoc/withAdminPermissions', () => ({
  __esModule: true,
  default: (Component: React.FC) => (props: any) => <Component {...props} />,
}));

jest.mock('utils/common', () => ({
  isIconDefined: jest.fn(() => 'mock-icon'),
}));

jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe('AddVCenter', () => {
  it('renders in create mode with mocked AddVCenterForm', () => {
    render(
      <MemoryRouter>
        <ReduxProvider store={store}>
          <MemoizedApiServiceProvider>
            <NebulaThemeProvider>
              <AddVCenter onClose={() => {}} permissions={{}} />
            </NebulaThemeProvider>
          </MemoizedApiServiceProvider>
        </ReduxProvider>
      </MemoryRouter>
    );
    expect(screen.getByText('Mocked AddVCenterForm')).toBeInTheDocument();
  });
});
