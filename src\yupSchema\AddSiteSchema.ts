import * as yup from 'yup';

const AddSiteSchema = () =>
  yup.object().shape({
    siteName: yup.string().required('Site Name is required'),
    address: yup.string().required('Address is required'),
    state: yup.string().required('State is required'),
    city: yup.string().when('state', {
      is: (state: any) => !state,
      then: yup.string().required('State is required'),
      otherwise: yup.string().required('City is required'),
    }),
    latitude: yup.string().required('latitude is required'),
    longitude: yup.string().required('longitude is required'),
    siteType: yup.string().required('Site Type is required'),
    region: yup.string().required('Region is required'),
  });
export default AddSiteSchema;

export const AddResourceSchema = () =>
  yup.object().shape({
    vcenter: yup.string().required('Vcenter is required'),
    domainName: yup.string().required('Domain is required'),
    label: yup.string().required('Label is required'),
  });
