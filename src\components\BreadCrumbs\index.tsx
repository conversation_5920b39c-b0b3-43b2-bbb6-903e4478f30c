import { useEffect, useState } from 'react';
import { useLocation, useParams, Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Breadcrumbs, Typography, useTheme } from '@mui/material';
import get from 'lodash/get';

import ContentViewport from '../ContentViewport';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';

const BreadCrumbs = () => {
  const {
    palette: { breadCrumbs },
  }: NebulaTheme = useTheme();

  const theme: NebulaTheme = useTheme();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const vmSize = searchParams.get('vmSize');
  const vmType = searchParams.get('vmType');
  const { catalogLevel1, catalogLevel2, catalogLevel3, catalogItem } = useParams();

  const getVmSizePath = (vmSize: string, vmType: string) => {
    const path = location.pathname.split('/').filter((path) => path);
    path.pop();
    path.push(`${vmType} ( ${vmSize} )`);
    return path;
  };
  const [catalogNames, setCatalogNames] = useState<{ [key: string]: string }>();
  const { level1, level2, level3, level4 } = useSelector((state: State) => state.catalogs);
  const pathnames = vmSize && vmType ? getVmSizePath(vmSize, vmType) : location.pathname?.split('/').filter((path) => path);

  const getCatalogName = (catalogData: State['catalogs']['level1'], catalogShortName: string) => {
    const catalogDetails = catalogData.find((catalog) => catalog.shortName === catalogShortName);
    return catalogDetails?.name;
  };

  useEffect(() => {
    if (catalogLevel1) {
      let catalogLevelNames: { [key: string]: string } = {};
      catalogLevelNames[catalogLevel1] = getCatalogName(level1, catalogLevel1) || '';

      if (catalogLevel2 && level2[catalogLevel1]?.length) {
        catalogLevelNames[catalogLevel2] = getCatalogName(level2[catalogLevel1], catalogLevel2) || '';
      }

      if (catalogLevel3 && catalogLevel2 && level3[catalogLevel2]?.length) {
        catalogLevelNames[catalogLevel3] = getCatalogName(level3[catalogLevel2], catalogLevel3) || '';
      }

      if (catalogItem && catalogLevel3) {
        const catalogItemName = get(level4[catalogItem], 'name');
        if (catalogItemName) {
          catalogLevelNames[catalogItem] = catalogItemName;
        } else if (level4[catalogLevel3]?.length) {
          catalogLevelNames[catalogItem] = getCatalogName(level4[catalogLevel3], catalogItem) || '';
        }
      }

      setCatalogNames(prevNames => {
        if (JSON.stringify(prevNames) !== JSON.stringify(catalogLevelNames)) {
            return catalogLevelNames;
        }
        return prevNames;
    });
}
    // eslint-disable-next-line
  }, [catalogLevel1, catalogLevel2, catalogLevel3, catalogItem, level1, level2, level3, level4]);

  return (
    <ContentViewport sx={{ p: 1, mb: 3.75, [theme.breakpoints.down('xl')]: { mb: '4px' } }}>
      <Breadcrumbs
        sx={{
          '& a, & .MuiTypography-root': { color: breadCrumbs.textPrimaryColor, textDecoration: 'none', textTransform: 'capitalize' },
          '& svg': { fontSize: '1rem', color: breadCrumbs.textPrimaryColor },
        }}
        aria-label="breadcrumb"
        separator={<ArrowForwardIosIcon />}
      >
        <Typography variant="h5" id={'home-breadcrumb'}>
          Home
        </Typography>
        {pathnames.map((pathname, index) => {
          const routeTo = `/${pathnames.slice(0, index + 1).join('/')}`;
          const isLast = index === pathnames.length - 1;
          const renderedPathName = catalogNames?.[pathname] || pathname.replace(/-/g, ' ');

          return isLast ? (
            <Typography key={index} id={`${pathname}-breadcrumb`} variant="h5">
              {decodeURIComponent(renderedPathName)}
            </Typography>
          ) : (
            <Link key={index} to={routeTo}>
              <Typography variant="h5" id={`${pathname}-breadcrumb`}>
                {decodeURIComponent(renderedPathName)}
              </Typography>
            </Link>
          );
        })}
      </Breadcrumbs>
    </ContentViewport>
  );
};

export default BreadCrumbs;
