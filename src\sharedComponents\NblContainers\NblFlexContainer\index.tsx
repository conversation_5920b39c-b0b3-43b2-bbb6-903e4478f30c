import React, { useEffect } from 'react';
import { StyledFlexContainer } from './styled';
interface NblFlexContainerProps {
  children?: React.ReactNode;
  flexType?: 'flex' | 'inline-flex' | 'none';
  position?: React.CSSProperties['position'];
  direction?: React.CSSProperties['flexDirection'];
  alignItems?: React.CSSProperties['alignItems'];
  justifyContent?: React.CSSProperties['justifyContent'];
  alignContent?: React.CSSProperties['alignContent'];
  alignSelf?: React.CSSProperties['alignSelf'];
  spacing?: number;
  flex?: React.CSSProperties['flex'];
  width?: string;
  minWidth?: string;
  height?: string;
  minHeight?: string;
  padding?: string;
  backgroundColor?: string;
  overflowX?: React.CSSProperties['overflowX'];
  overflowY?: React.CSSProperties['overflowY'];
  center?: boolean;
  borderRadius?: string;
  border?: string;
  wrap?: React.CSSProperties['flexWrap'];
  cursor?: React.CSSProperties['cursor'];
  onClick?: (e: React.MouseEvent) => void;
  top?: string;
  bottom?: string;
  left?: string;
  right?: string;
  margin?: string;
  zIndex?: number;
  id?: string;
  opacity?: number;
}

const NblFlexContainer = React.forwardRef<HTMLDivElement, NblFlexContainerProps>(
  (
    {
      children,
      width = '100%',
      height = '100%',
      direction = 'row',
      alignItems,
      justifyContent,
      alignContent,
      alignSelf,
      spacing = 1,
      overflowX = 'unset',
      overflowY = 'unset',
      padding = '0px',
      backgroundColor = 'initial',
      position,
      flex,
      flexType = 'flex',
      center = false,
      borderRadius,
      border = 'none',
      wrap,
      cursor,
      onClick,
      top,
      bottom,
      left,
      right,
      margin = '0',
      minWidth,
      minHeight,
      zIndex,
      id,
      opacity,
    },
    ref
  ) => {
    //Side Effects
    useEffect(() => {
      if (center && (alignItems || justifyContent)) {
        console.warn('alignItem and justifyContent has no Effect on NblFlexContainer when center property is true.');
      }
    }, [center, alignItems, justifyContent]);

    //JSX
    return (
      <StyledFlexContainer
        id={id}
        flexType={flexType}
        ref={ref}
        style={{
          opacity,
          position,
          flexDirection: direction,
          flexWrap: wrap,
          width,
          height,
          ...(center ? { alignItems: 'center', justifyContent: 'center' } : { alignItems, justifyContent }),
          alignContent,
          alignSelf,
          flex,
          gap: `${spacing * 8}px`,
          padding,
          overflowX,
          overflowY,
          backgroundColor,
          border,
          borderRadius,
          boxSizing: 'border-box',
          cursor,
          ...(top && { top }),
          ...(bottom && { bottom }),
          ...(left && { left }),
          ...(right && { right }),
          ...(minWidth && { minWidth }),
          ...(minHeight && { minHeight }),
          ...(zIndex && { zIndex }),
          margin,
        }}
        onClick={onClick}
      >
        {children}
      </StyledFlexContainer>
    );
  }
);

export default NblFlexContainer;
