import { fireEvent, render, screen } from '@testing-library/react';
import NblUploadButton from './index';

// Mock dependencies
jest.mock('../NblButton', () => (props: any) => (
	<label data-testid="nbl-button" htmlFor="file-input">
		{props.children}
	</label>
));

jest.mock('../../NblTypography', () => (props: any) => (
	<span data-testid="nbl-typography">{props.children}</span>
));

jest.mock('sharedComponents/NblContainers/NblFlexContainer', () => (props: any) => (
	<div data-testid="nbl-flex-container">{props.children}</div>
));

jest.mock('./styled', () => (props: any) => (
	<input data-testid="file-input" type="file" onChange={props.onChange} />
));

describe('NblUploadButton', () => {
	const buttonID = 'upload-btn';

	it('renders upload button with label and icon', () => {
		render(<NblUploadButton buttonID={buttonID} />);

		expect(screen.getByTestId('nbl-button')).toBeInTheDocument();
		expect(screen.getByText('Choose File')).toBeInTheDocument();
	});

	it('calls handleFileUpload when a file is selected', async () => {
		const mockHandler = jest.fn();
		render(<NblUploadButton buttonID={buttonID} handleFileUpload={mockHandler} />);

		const fileInput = screen.getByTestId('file-input') as HTMLInputElement;
		const testFile = new File(['hello'], 'hello.txt', { type: 'text/plain' });

		fireEvent.change(fileInput, { target: { files: [testFile] } });

		expect(mockHandler).toHaveBeenCalled();
		expect(fileInput.files?.[0]).toBe(testFile);
	});

	it('renders description text when provided', () => {
		render(<NblUploadButton buttonID={buttonID} description="Upload a document" />);

		expect(screen.getByTestId('nbl-typography')).toHaveTextContent('Upload a document');
	});

	it('does not render description if not provided', () => {
		render(<NblUploadButton buttonID={buttonID} />);

		expect(screen.queryByTestId('nbl-typography')).toBeNull();
	});

	it('does not call handleFileUpload if input is disabled', () => {
		const mockHandler = jest.fn();
		render(<NblUploadButton buttonID={buttonID} disabled handleFileUpload={mockHandler} />);

		const fileInput = screen.getByTestId('file-input') as HTMLInputElement;
		expect(fileInput).toBeInTheDocument();

		fireEvent.change(fileInput, { target: { files: [new File(['data'], 'file.txt')] } });
	});
});
