import { render, screen, fireEvent } from '@testing-library/react';
import NblDownloadButton from './index';

// Mock NblButton and NblTypography
jest.mock('../NblButton', () => (props: any) => (
  <button data-testid="nbl-button" disabled={props.disabled} onClick={props.onClick}>
    {props.children}
  </button>
));

jest.mock('../../NblTypography', () => (props: any) => (
  <span data-testid="nbl-typography" {...props}>{props.children}</span>
));

// Mock NblFlexContainer
jest.mock('sharedComponents/NblContainers/NblFlexContainer', () => (props: any) => (
  <div data-testid="nbl-flex-container">{props.children}</div>
));

describe('NblDownloadButton', () => {
  const buttonID = 'test-download-button';

  it('renders the download button with icon and label', () => {
    render(<NblDownloadButton buttonID={buttonID} />);

    expect(screen.getByTestId('nbl-button')).toBeInTheDocument();
    expect(screen.getByText('Download')).toBeInTheDocument();
  });

  it('calls onClick handler when button is clicked', () => {
    const handleClick = jest.fn();
    render(<NblDownloadButton buttonID={buttonID} onClick={handleClick} />);

    fireEvent.click(screen.getByTestId('nbl-button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('disables the button when disabled prop is true', () => {
    render(<NblDownloadButton buttonID={buttonID} disabled />);

    expect(screen.getByTestId('nbl-button')).toBeDisabled();
  });

  it('renders description text if provided', () => {
    render(<NblDownloadButton buttonID={buttonID} description="This is a download button." />);

    expect(screen.getByTestId('nbl-typography')).toHaveTextContent('This is a download button.');
  });

  it('does not render description if not provided', () => {
    render(<NblDownloadButton buttonID={buttonID} />);

    expect(screen.queryByTestId('nbl-typography')).toBeNull();
  });
});
