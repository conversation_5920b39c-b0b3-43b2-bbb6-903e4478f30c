import React, { useState } from 'react';
// eslint-disable-next-line no-unused-vars
import NblFormContainer, { NblFormHelpers } from 'sharedComponents/NblContainers/NblFormContainer';
import { useApiService } from 'api/ApiService/context';
import { SuccessPageProps } from 'sharedComponents/NblSuccessfulPage';
import { CatalogLevel04Data, FormProps } from 'types';
import { validationSchema } from 'yupSchema/CreateSpecFormS3';
import { isIconDefined } from 'utils/common';
import CreateSpecFormS3 from './CreateSpecFormS3';
import { MultiENVProjectsResponse } from '../../../types/Interfaces/MultiENVProjectsResponse';
import withFetchMultiENVProjects from '../../../hoc/withFetchMultiENVProjects';

export type FormValues = {
  project: string;
  projectName: string;
  domain: string;
  application: string;
  environment: string;
  iacProjectName: string;
  namespaceId: number;
  bucket: string;
  versioning: string;
};

interface SpecFormS3 extends FormProps {
  formDetails?: CatalogLevel04Data;
  projectData?: MultiENVProjectsResponse;
}

const SpecFormS3: React.FunctionComponent<SpecFormS3> = ({ formDetails, projectData }) => {
  const [responseData, setResponseData] = useState<SuccessPageProps>({
    buttonTitle: 'Track Request',
    title: 'S3',
    requestId: '',
  });
  const Icon = formDetails && isIconDefined(formDetails.icon);
  const { apiSpecFlowService } = useApiService();

  const initialValues = {
    project: '',
    projectName: '',
    iacProjectName: '',
    domain: '',
    application: '',
    environment: '',
    namespaceId: 0,
    bucket: '',
    versioning: '',
  };

  const handleSubmitForm = (values: FormValues, nblFormHelpers: NblFormHelpers<FormValues>) => {
    const { environment } = values;
    const payload = {
      platformContext: {
        catalogId: formDetails?.id,
        envId: environment,
        domainId: null,
      },
      ...values,
    };
    apiSpecFlowService
      .createS3WithSpec(payload)
      .then((res) => {
        if (res.status) {
          setResponseData({
            ...responseData,
            requestId: res.data.serviceRequestId || res.data.id,
          });
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        nblFormHelpers.setSubmitting(false);
      });
  };

  return (
    <>
      <NblFormContainer<FormValues>
        title={formDetails ? formDetails.name : ''}
        Icon={Icon}
        caption={'Fill the necessary details needed to create an S3'}
        formInitialValues={initialValues}
        formValidationSchema={validationSchema}
        steps={[
          {
            caption: '',
            errorFields: [],
            icon: '',
            status: 'completed',
            title: '',
          },
        ]}
        formType="simple"
        onSubmit={handleSubmitForm}
        responseData={responseData}
        showPreview={false}
      >
        <CreateSpecFormS3 projectData={projectData} catalogShortName={formDetails?.shortName} />
      </NblFormContainer>
    </>
  );
};

export default withFetchMultiENVProjects(SpecFormS3);
