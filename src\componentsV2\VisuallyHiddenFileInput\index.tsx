import { styled } from '@mui/material';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

interface VisuallyHiddenFileInputProps {
  handleFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const VisuallyHiddenFileInput: React.FunctionComponent<VisuallyHiddenFileInputProps> = ({ handleFileUpload }) => {
  return <VisuallyHiddenInput type="file" onChange={handleFileUpload} />;
};

export default VisuallyHiddenFileInput;
