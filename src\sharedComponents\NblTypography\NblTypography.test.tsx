import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblTypography from '.';

describe('NblTypography component', () => {
  const props = {
    variant: 'h1',
    color: 'shade1'
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            {/* @ts-ignore */}
            <NblTypography {...props}>Info</NblTypography>
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
