import { useEffect, useMemo, useState } from 'react';
import Chart from 'react-apexcharts';
import { GENERATE_REQUEST_BY_MONTH_QUERY } from '../../queries';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line
import { State } from 'store/reducers/type';
// eslint-disable-next-line
import { RequestSummaryByMonthResponse, RequestSummaryMonthlyData } from 'api/ApiService/type';
// eslint-disable-next-line
import { ApexOptions } from 'apexcharts';
import { addSummaryToCatalog } from '../../utils/utility';
import { CalendarMonths, getStackedSeries } from './utility';
// eslint-disable-next-line
import { CatalogWithSummary } from '../../utils/types';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { updateRequestOverviewType } from 'store/reducers/requestOverview';
import useNblNavigate from 'hooks/useNblNavigate';
import { useApiService } from 'api/ApiService/context';
import NblBreadCrumbs, { NblBreadcrumbItem } from 'sharedComponents/NblNavigation/NblBreadCrumbs';

interface RequestByMonthChartProps {}

const RequestByMonthChart: React.FunctionComponent<RequestByMonthChartProps> = () => {
  const {
    allCatalog1,
    allCatalog3,
    allCatalog4,
    allCatalog2,
    year,
    selectedCatalog3,
    selectedCatalog1,
    selectedCatalog2,
    selectedCatalog4,
    isFilterApplied,
    quarters,
    levelSelected,
  } = useSelector((state: State) => state.RequestFilterConfig);
  const navigate = useNblNavigate();
  const dispatch = useDispatch();
  const { apiUsageMetricsService } = useApiService();
  const [drillLevel, setDrillLevel] = useState(levelSelected);
  const [breadcrumbItems, setBreadcrumbItems] = useState([{ label: 'Catalog-1', route: '' }]);
  const [requestSummary, setRequestSummary] = useState([] as RequestSummaryMonthlyData[]);
  const [barSeries, setBarSeries] = useState([] as { id: string; name: string; data: number[] }[]);

  // API call to get request summary count
  const fetchRequestSummaryByMonth = async () => {
    const allCatalog1Names = selectedCatalogs[1].map(({ value }) => value);
    const allCatalog3Names = selectedCatalogs[3].map(({ value }) => value);
    const allCatalog4Names = selectedCatalogs[4].map(({ value }) => value);
    const payload = {
      query: GENERATE_REQUEST_BY_MONTH_QUERY(year, quarters, allCatalog1Names, allCatalog3Names, allCatalog4Names),
    };
    const response = await apiUsageMetricsService.customGraphQL<RequestSummaryByMonthResponse>(payload);
    const requestSummaryData: RequestSummaryMonthlyData[] = response.data.data.getRequestSummaryByMonth;
    setRequestSummary(requestSummaryData);
  };

  // UseEffect for API call
  useEffect(() => {
    if (allCatalog1.length) {
      fetchRequestSummaryByMonth();
    }
  }, [year, quarters, selectedCatalog1, selectedCatalog2, selectedCatalog3, selectedCatalog4, allCatalog1]);

  // Applying Dialog Filters to data.
  const selectedCatalogs: CatalogWithSummary[][] = useMemo(() => {
    const filteredCatalog1 = allCatalog1.filter((item) => selectedCatalog1.includes(item.id));
    const filteredCatalog2 = allCatalog2.filter((item) => selectedCatalog2.includes(item.id));
    const filteredCatalog3 = allCatalog3.filter((item) => selectedCatalog3.includes(item.id));
    const filteredCatalog4 = allCatalog4.filter((item) => selectedCatalog4.includes(item.id));
    return [[], filteredCatalog1, filteredCatalog2, filteredCatalog3, filteredCatalog4];
  }, [selectedCatalog1, selectedCatalog2, selectedCatalog3, selectedCatalog4, requestSummary]);

  // Sum up the request count from level4 to level1 filtered data
  const catalogWithSummary: CatalogWithSummary[][] = useMemo(() => {
    const Catalog4WithSummary = selectedCatalogs[4].map((item) => {
      const summary = requestSummary.find((res) => res.requestType === item.requestType)?.summary;
      const newItem = { ...item, summary };
      return newItem;
    });
    const Catalog3WithSummary = addSummaryToCatalog(selectedCatalogs[3], Catalog4WithSummary);
    const Catalog2WithSummary = addSummaryToCatalog(selectedCatalogs[2], Catalog3WithSummary);
    const Catalog1WithSummary = addSummaryToCatalog(selectedCatalogs[1], Catalog2WithSummary);
    return [[], Catalog1WithSummary, Catalog2WithSummary, Catalog3WithSummary, Catalog4WithSummary];
  }, [selectedCatalogs]);

  useEffect(() => {
    setBarSeries(getStackedSeries(catalogWithSummary[levelSelected]));
    let newItems = [] as NblBreadcrumbItem[];
    for (let i = 1; i <= levelSelected; i++) {
      newItems.push({ label: 'Catalog-' + i, route: '' });
    }
    setBreadcrumbItems(newItems);
    setDrillLevel(levelSelected);
  }, [catalogWithSummary, levelSelected]);

  const handleStackedBarClick = (clickedCatalog: any, clickedMonth: number) => {
    if (drillLevel < 4) {
      setDrillLevel((prev) => prev + 1);
      setBarSeries(getStackedSeries(catalogWithSummary[drillLevel + 1].filter((item) => item.parentId === clickedCatalog.id)));
      setBreadcrumbItems((prevItems) => [...prevItems, { label: 'Catalog-' + (drillLevel + 1), route: clickedCatalog.id }]);
    } else {
      // navigate to request overview
      dispatch(
        updateRequestOverviewType({
          catalog1: [],
          catalog3: [],
          catalog4: [clickedCatalog.name],
          year,
          quarters,
          month: clickedMonth.toString(),
        })
      );
      navigate('requestoverview');
    }
  };

  const handleBreadcrumbItemClick = (item: NblBreadcrumbItem, index: number) => {
    setDrillLevel(index + 1);
    if (index === 0 || isFilterApplied) {
      setBarSeries(getStackedSeries(catalogWithSummary[index + 1]));
    }
    if (item.route) {
      setBarSeries(getStackedSeries(catalogWithSummary[index + 1].filter((i) => i.parentId === item.route)));
    }
    setBreadcrumbItems((prevItems) => prevItems.slice(0, index + 1));
  };

  const options: ApexOptions = {
    chart: {
      stacked: true,
      toolbar: {
        show: false,
      },
      events: {
        dataPointSelection: (event, context, config) => {
          const clickedCatalog = barSeries[config.seriesIndex];
          const clickedMonthNumber = config?.dataPointIndex;

          setTimeout(() => {
            handleStackedBarClick(clickedCatalog, clickedMonthNumber);
          }, 1);
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    xaxis: {
      tickPlacement: 'on',
      categories: CalendarMonths,
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val.toFixed(0);
        },
      },
    },
    plotOptions: {
      bar: {
        borderRadius: 5,
      },
    },
    noData: {
      align: 'center',
      verticalAlign: 'middle',
      text: 'No Data Found',
    },
    legend: {
      height: barSeries.length < 4 ? undefined : 100,
    },
  };

  return (
    <NblFlexContainer direction="column" spacing={2}>
      <NblBreadCrumbs breadCrumbs={breadcrumbItems} onClick={handleBreadcrumbItemClick} />
      <Chart key={drillLevel} options={options} series={barSeries} type="bar" />
    </NblFlexContainer>
  );
};

export default RequestByMonthChart;
