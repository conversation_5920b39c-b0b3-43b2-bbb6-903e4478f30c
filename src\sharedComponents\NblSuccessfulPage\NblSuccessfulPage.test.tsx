import { act, render } from '@testing-library/react';

import { MemoryRouter as Router } from 'react-router-dom';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblSuccessfulPage from '.';

describe('NblSuccessfulPage component', () => {
  const props = {
    title: 'Windows VM',
    requestId: 'NEB-IAAS-VM-20420',
    buttonTitle: 'Track Request',
  };
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <Router>
            <NebulaThemeProvider>
              <NblSuccessfulPage {...props} />
            </NebulaThemeProvider>
          </Router>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
