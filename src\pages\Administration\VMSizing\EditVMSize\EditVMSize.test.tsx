import React from 'react';
import { render } from '@testing-library/react';
import EditVMSizeForm from 'componentsV2/Administration/VMSize/EditVMSizeForm';

 jest.mock('componentsV2/Administration/VMSize/EditVMSizeForm', () => ({
  __esModule: true,
  default: () => <div>EditVMSizeForm Component</div>,
}));

describe('EditVMSize', () => {
  it('renders the EditVMSizeForm component', () => {
    const { getByText } = render(<EditVMSizeForm />);
    expect(getByText('EditVMSizeForm Component')).toBeInTheDocument();
  });
});
