import { styled } from '@mui/system';
import { ItemProps } from '../interface';
import { Typography } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

interface StyledItemProps {
  theme?: NebulaTheme;
  expanded: boolean | undefined;
  level: number;
  selectedLevel1?: ItemProps;
  selectedLevel2?: ItemProps;
  name: string;
  id: string;
}

// Define the styled components
const StyledItem = styled('button')<StyledItemProps>(({ theme, expanded }) => ({
  all: 'unset',
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '20px',
  width: '100%',
  height: '48px',
  borderRadius: '10px',
  padding: '0 8px',
  color: theme.palette.secondary.shade4,
  boxSizing: 'border-box',
  cursor: 'pointer',
  textTransform: 'capitalize',
  ...theme.typography.subtitle1,
  ...(expanded && {
    padding: '0 12px',
    justifyContent: 'flex-start',
  }),

  '&:hover': {
    background: theme.palette.primary.shade11,
  },
  '&:disabled': {
    cursor: 'not-allowed',
  },
  '&.selectedItem': {
    background: theme.palette.primary.shade11,
  },

  '&.level3Item': {
    padding: '0 !important',
    justifyContent: 'flex-start',
    '&:hover': {
      background: 'none !important',
      opacity: 0.5,
    },
  },

  [theme.breakpoints.down('2K')]: {
    height: '40px',
  },
}));

const StyledTypography = styled(Typography)<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    fontWeight: theme.typography.fontWeightBold,
  };
});

const Handle = styled('div')<{ expanded: boolean }>(({ theme }) => ({
  position: 'absolute',
  right: '4px',
  width: '2px',
  height: '32px',
  background: theme.palette.primary.shade6,
  borderRadius: '10px',
  [theme.breakpoints.down('2K')]: {
    height: '25px',
  },
}));

export { StyledItem, Handle, StyledTypography };
