import AddGroupForm from 'components/Administration/AddGroupForm';
import useNblNavigate from 'hooks/useNblNavigate';
import useShowNavigationWarning from 'hooks/useShowNavigationWarning';

const AddGroup = () => {
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const navigateToPermissionDetails = () => {
    navigate('/administration/groups');
  };

  return (
    <AddGroupForm
      title="Register AD group"
      permissions={{}}
      onSuccess={navigateToPermissionDetails}
      onClose={navigateToPermissionDetails}
    />
  );
};

export default AddGroup;
