import { styled } from '@mui/material';
import { TreeItem, treeItemClasses } from '@mui/x-tree-view/TreeItem';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledTreeItem = styled(TreeItem)<{ theme?: NebulaTheme }>(({ theme }) => ({
  color: theme.palette.primary.main,
  paddingBottom: '0px',
  marginBottom: '0px',
  '& *:not(button,hr)': {
    backgroundColor: 'transparent !important',
  },
  [`& .${treeItemClasses.content}`]: {
    borderRadius: theme.spacing(0.5),
    [`& .${treeItemClasses.label}`]: {
      padding: '0',
      fontSize: theme.typography.subtitle1.fontSize,
    },
  },

  [`& .MuiTreeItem-content.Mui-selected`]: {
    backgroundColor: 'transparent',
  },
  [`& .${treeItemClasses.iconContainer}`]: {
    position: 'absolute',
    right: '10px',
    top: '8px',
  },
  [`& .${treeItemClasses.groupTransition}`]: {
    marginTop: '2px',
    marginLeft: '10px',
    paddingLeft: '0px',
    borderLeft: `1px solid ${theme.palette.secondary.shade5}`,
  },
}));
