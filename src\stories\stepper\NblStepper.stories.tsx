//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblStepper from 'sharedComponents/NblStepper';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

type StoryProps = ComponentProps<typeof NblStepper>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Form/NblStepper',
  component: NblStepper,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    wrap: { type: 'boolean' },
    onStepClick: { action: 'onStepClick', type: 'function' },
  },
};

export default meta;

export const Stepper: Story = {
  args: {
    steps: [
      { icon: 'CloudOutlined', title: 'VM Details', caption: 'Enter the VM details', status: 'current', errorFields: [] },
      { icon: 'CloudOutlined', title: 'VM Details', caption: 'Enter the VM details', status: 'pending', errorFields: [] },
      { icon: 'CloudOutlined', title: 'VM Details', caption: 'Enter the VM details', status: 'pending', errorFields: [] },
      { icon: 'CloudOutlined', title: 'VM Details', caption: 'Enter the VM details', status: 'error', errorFields: [] },
    ],
    wrap: true,
  },
  render: (args) => (
    <NebulaTheme>
      <NblFlexContainer padding="10px">
        <NblStepper {...args} />
      </NblFlexContainer>
    </NebulaTheme>
  ),
};
