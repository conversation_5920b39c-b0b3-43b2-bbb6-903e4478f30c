import { render, fireEvent, screen } from '@testing-library/react';
import NblChipComponent from './index';

jest.mock('sharedComponents/NblFormInputs/NblFieldWrapper', () => (props: any) => (
  <div data-testid="nbl-field-wrapper">{props.children}</div>
));

jest.mock('./styled', () => ({
  StyledAutoComplete: (props: any) => (
    <input
      data-testid="autocomplete-input"
      value={props.inputValue}
      onChange={(e) => props.onInputChange({}, e.target.value)}
      onKeyDown={props.onKeyDown}
      onBlur={props.onBlur}
    />
  ),
  StyledChip: (props: any) => (
    <div data-testid="chip" {...props}>
      {props.label}
    </div>
  ),
}));

describe('NblChipComponent', () => {
  const mockHandleChange = jest.fn();
  const baseProps = {
    label: 'Test Label',
    name: 'test',
    value: [],
    handleChange: mockHandleChange,
    options: ['Option1', 'Option2'],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders component with label', () => {
    render(<NblChipComponent {...baseProps} />);
    expect(screen.getByTestId('nbl-field-wrapper')).toBeInTheDocument();
  });

  it('adds chip on blur with non-empty input', () => {
    render(<NblChipComponent {...baseProps} />);
    const input = screen.getByTestId('autocomplete-input');

    fireEvent.change(input, { target: { value: 'NewChip' } });
    fireEvent.blur(input);

    expect(mockHandleChange).toHaveBeenCalledWith(expect.anything(), ['NewChip']);
  });

  it('does not add chip on blur with empty input', () => {
    render(<NblChipComponent {...baseProps} />);
    const input = screen.getByTestId('autocomplete-input');

    fireEvent.change(input, { target: { value: '' } });
    fireEvent.blur(input);

    expect(mockHandleChange).not.toHaveBeenCalled();
  });

  it('adds chip on Tab key', () => {
    render(<NblChipComponent {...baseProps} />);
    const input = screen.getByTestId('autocomplete-input');

    fireEvent.change(input, { target: { value: 'TabChip' } });
    fireEvent.keyDown(input, { key: 'Tab' });

    expect(mockHandleChange).toHaveBeenCalledWith(expect.anything(), ['TabChip']);
  });

  it('triggers onKeyDown handler if provided', () => {
    const onKeyDownMock = jest.fn();
    render(<NblChipComponent {...baseProps} onKeyDown={onKeyDownMock} />);
    const input = screen.getByTestId('autocomplete-input');

    fireEvent.keyDown(input, { key: 'Enter' });
    expect(onKeyDownMock).toHaveBeenCalled();
  });

  it('does not add chips when disabled', () => {
    render(<NblChipComponent {...baseProps} disabled />);
    const input = screen.getByTestId('autocomplete-input');

    fireEvent.change(input, { target: { value: 'Chip' } });
    fireEvent.blur(input);

    expect(mockHandleChange).not.toHaveBeenCalled();
  });
});
