import React, { useState } from 'react';
// eslint-disable-next-line no-unused-vars
import NblFormContainer, { NblFormHelpers } from 'sharedComponents/NblContainers/NblFormContainer';
import { useApiService } from 'api/ApiService/context';
import { SuccessPageProps } from 'sharedComponents/NblSuccessfulPage';
import { CatalogLevel04Data, FormProps } from 'types';
import { validationSchema } from 'yupSchema/CreateSpecFormEC2';
import { isIconDefined } from 'utils/common';
import CreateSpecFormEC2 from './CreateSpecFormEC2';
import withFetchMultiENVProjects from '../../../hoc/withFetchMultiENVProjects';
import { MultiENVProjectsResponse } from '../../../types/Interfaces/MultiENVProjectsResponse';

export type FormValues = {
  project: string;
  projectName: string;
  domain: string;
  application: string;
  environment: string;
  iacProjectName: string;
  namespaceId: number;
  ec2_name: string;
  subnet_id: string;
  ami_id: string;
  instance_type: string;
};

interface SpecFormEC2 extends FormProps {
  formDetails?: CatalogLevel04Data;
  projectData?: MultiENVProjectsResponse;
}

const SpecFormEC2: React.FunctionComponent<SpecFormEC2> = ({ formDetails, projectData }) => {
  const [responseData, setResponseData] = useState<SuccessPageProps>({
    buttonTitle: 'Track Request',
    title: 'EC2',
    requestId: '',
  });
  const Icon = formDetails && isIconDefined(formDetails.icon);
  const { apiSpecFlowService } = useApiService();

  const initialValues = {
    project: '',
    projectName: '',
    iacProjectName: '',
    domain: '',
    application: '',
    environment: '',
    namespaceId: 0,
    ec2_name: '',
    subnet_id: '',
    ami_id: '',
    instance_type: '',
  };

  const handleSubmitForm = (values: FormValues, nblFormHelpers: NblFormHelpers<FormValues>) => {
    const { environment, projectName } = values;
    const payload = {
      platformContext: {
        catalogId: formDetails?.id,
        envId: environment,
        domainId: null,
      },
      ...values,
    };
    const selectedProject = projectData?.find((project) => project.id === projectName);
    payload.projectName = selectedProject?.name || projectName;
    apiSpecFlowService
      .createEC2WithSpec(payload)
      .then((res) => {
        if (res.status) {
          setResponseData({
            ...responseData,
            requestId: res.data.serviceRequestId || res.data.id,
          });
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        nblFormHelpers.setSubmitting(false);
      });
  };

  return (
    <>
      <NblFormContainer<FormValues>
        title={formDetails ? formDetails.name : ''}
        Icon={Icon}
        caption={'Fill the necessary details needed to create a VM'}
        formInitialValues={initialValues}
        formValidationSchema={validationSchema}
        steps={[
          {
            caption: '',
            errorFields: [],
            icon: '',
            status: 'completed',
            title: '',
          },
        ]}
        formType="simple"
        onSubmit={handleSubmitForm}
        responseData={responseData}
        showPreview={false}
      >
        <CreateSpecFormEC2 projectData={projectData} catalogShortName={formDetails?.shortName} />
      </NblFormContainer>
    </>
  );
};

export default withFetchMultiENVProjects(SpecFormEC2);
