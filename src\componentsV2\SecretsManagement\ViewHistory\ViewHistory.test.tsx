import { render, waitFor } from '@testing-library/react';
import ViewHistory from '.';
import { useApiService } from 'api/ApiService/context';
import { MemoryRouter } from 'react-router-dom';
import ReduxProvider from 'mock/ReduxProvider';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

// Mocks
jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

jest.mock('sharedComponents/NblContainers/NblBorderContainer', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

jest.mock('componentsV2/NblViewDetailsAccordion', () => ({
  __esModule: true,
  default: ({ children, summary }: any) => (
    <div>
      <div>{summary}</div>
      <div>{children}</div>
    </div>
  ),
}));

describe('ViewHistory Component', () => {
  it('calls the API to fetch secret data on mount', async () => {
    const mockGetSecretData = jest.fn().mockResolvedValue({ status: true, data: [] });

    (useApiService as jest.Mock).mockReturnValue({
      apiSecretsManagement: {
        getSecretData: mockGetSecretData,
      },
    });

    const secretId = '123';

    render(
      <NebulaThemeProvider>
        <MemoryRouter initialEntries={[`/view-history?secretId=${secretId}`]}>
          <ReduxProvider>
            <ViewHistory />
          </ReduxProvider>
        </MemoryRouter>
      </NebulaThemeProvider>
    );

    // Wait for the mock API call to complete
    await waitFor(() => {
      expect(mockGetSecretData).toHaveBeenCalledWith(secretId);
    });

    expect(mockGetSecretData).toHaveBeenCalledTimes(1);
  });
});
