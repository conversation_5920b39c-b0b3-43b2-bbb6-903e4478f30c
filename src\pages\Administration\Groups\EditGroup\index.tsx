import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';

import AddGroupForm from 'components/Administration/AddGroupForm';
import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import PermissionService from 'api/ApiService/PermissionService';
// eslint-disable-next-line
import { GroupDataProps } from 'types';
import { AdminEditRouteParams } from 'types/Enums';
import useNblNavigate from 'hooks/useNblNavigate';

const EditGroup = () => {
  const dispatch = useDispatch();
  const permissionService = new PermissionService();

  const [editGroupDetails, setEditGroupDetails] = useState<GroupDataProps>();

  const { [AdminEditRouteParams.groups]: groupName } = useParams();
  const decodedGroupName = decodeURIComponent(groupName as string);
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const fetchGroupDetails = () => {
    if (decodedGroupName) {
      dispatch(showSpinner({ id: SPINNER_IDS.groupDetails, status: true, message: 'Loading group details...' }));
      const specialCharsRegex = /[&\-_\s.]/;
      const cuurentGroupName = specialCharsRegex.test(decodedGroupName) ? encodeURIComponent(decodedGroupName) : decodedGroupName;
      permissionService
        .getGroupDetails(cuurentGroupName)
        .then((res) => {
          if (res.status) {
            setEditGroupDetails(res.data[0]);
          }
        })
        .finally(() => {
          dispatch(showSpinner({ id: SPINNER_IDS.groupDetails, status: false, message: '' }));
        });
    }
  };

  useEffect(() => {
    fetchGroupDetails();
  }, []);

  const navigateToPermissionDetails = () => {
    navigate('/administration/groups/manage-registered-ad-groups');
  };

  return (
    <AddGroupForm
      title="Edit Registered AD group"
      permissions={{}}
      editGroupDetails={editGroupDetails}
      onSuccess={navigateToPermissionDetails}
      onClose={navigateToPermissionDetails}
    />
  );
};

export default EditGroup;
