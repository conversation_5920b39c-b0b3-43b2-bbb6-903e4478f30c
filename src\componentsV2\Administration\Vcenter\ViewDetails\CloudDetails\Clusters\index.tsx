import React, { useMemo, useState } from 'react';
import ClusterOverview from './ClusterDetails/ClusterOverview';
import ClusterHostsDetails from './ClusterDetails/ClusterHostsDetails';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { HeaderBackIcon } from 'assets/images/icons/custom-icons';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import { Host } from './ClusterDetails/ClusterHostsDetails';
import { useTheme, IconButton, Link } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import NblTypography from 'sharedComponents/NblTypography';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
// eslint-disable-next-line no-unused-vars
import { NblTable, ColumnData } from 'sharedComponents/NblTable';
import { hexToRgb } from 'utils/common';
import { NblSwitchTabBar } from 'sharedComponents/NblNavigation/NblTabSwitch';

export interface Cluster {
  clusterMor: string;
  clusterStatus: string | null;
  type?: string;
  disabled: boolean;
  drsEnabled: boolean | null;
  haEnabled: boolean;
  hostCount: number;
  hosts: string[];
  name: string;
  restricted: boolean;
  vmCount: number;
  projects?: string[];
}

interface ClustersProps {
  clusters: Cluster[];
  hosts: Host[];
  onChange: (updatedClusters: Cluster[]) => void;
}

const Clusters: React.FC<ClustersProps> = ({ clusters, hosts, onChange }) => {
  const theme = useTheme<NebulaTheme>();
  const [viewMode, setViewMode] = useState<'list' | 'details'>('list');
  const [selectedCluster, setSelectedCluster] = useState<any>(null);
  const [activeTabIndex, setActiveTabIndex] = useState(0);

  const tabLabels = ['Cluster Details', 'Hosts'] as const;

  const handleChecked = (clusterMor: string, fieldName: keyof Cluster) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const isRestrictedChange = fieldName === 'restricted';
    const newValue = e.target.checked;

    const updatedClusters = clusters.map((cluster) => {
      if (cluster.clusterMor !== clusterMor) return cluster;

      if (isRestrictedChange) {
        if (newValue) {
          return { ...cluster, restricted: true, projects: cluster.projects ?? [] };
        } else {
          // eslint-disable-next-line no-unused-vars
          const { projects, ...rest } = cluster;
          return { ...rest, restricted: false };
        }
      }

      return { ...cluster, [fieldName]: newValue };
    });

    onChange(updatedClusters);

    if (isRestrictedChange && newValue) {
      const updatedCluster = updatedClusters.find((c) => c.clusterMor === clusterMor);
      if (updatedCluster) {
        const resolvedHosts = updatedCluster.hosts.map((hostId) => hosts.find((h) => h.hostMor === hostId)).filter((h): h is Host => !!h);

        setSelectedCluster({
          ...updatedCluster,
          hosts: resolvedHosts,
          id: updatedCluster.clusterMor,
          hostCount: resolvedHosts.length,
        });

        setViewMode('details');
        setActiveTabIndex(0);
      }
    }
  };

  const clusterData = useMemo(() => {
    if (!Array.isArray(clusters) || !Array.isArray(hosts)) return [];

    return clusters.map((cluster) => {
      const resolvedHosts = cluster.hosts.map((hostId) => hosts.find((h) => h.hostMor === hostId)).filter((h): h is Host => !!h);

      return {
        ...cluster,
        id: cluster.clusterMor,
        hosts: resolvedHosts,
        hostCount: resolvedHosts.length,
      };
    });
  }, [clusters, hosts]);

  const handleClusterChange = (updatedCluster: Cluster) => {
    const cleanedCluster = {
      ...updatedCluster,
      hosts: updatedCluster.hosts.map((h: any) => (typeof h === 'string' ? h : h.hostMor)),
      id: updatedCluster.clusterMor,
    };

    const updatedClusters = clusters.map((c) => (c.clusterMor === cleanedCluster.clusterMor ? cleanedCluster : c));

    onChange(updatedClusters);

    const resolvedHosts = cleanedCluster.hosts
      .map((hostMor: string) => hosts.find((h) => h.hostMor === hostMor))
      .filter((h): h is Host => !!h);

    setSelectedCluster({
      ...cleanedCluster,
      hosts: resolvedHosts,
      hostCount: resolvedHosts.length,
    });
  };

  const clusterColumns: ColumnData[] = [
    {
      field: 'name',
      headerName: 'Name',
      flex: 1,
      renderCell: (params: any) => {
        const cluster = clusterData.find((c) => c.id === params.id);
        return (
          <Link
            sx={{ cursor: 'pointer', color: '#' }}
            onClick={() => {
              if (cluster) {
                setSelectedCluster(cluster);
                setViewMode('details');
                setActiveTabIndex(0);
              }
            }}
          >
            <NblTypography variant="subtitle2" color="shade7">
              {params.value}
            </NblTypography>
          </Link>
        );
      },
    },
    { field: 'type', headerName: 'Type', flex: 1 },
    {
      field: 'disabled',
      headerName: 'Disable',
      flex: 1,
      renderCell: (params) => (
        <NblFlexContainer center>
          <NblCheckBox
            checked={!!params.row.disabled}
            label=""
            name=""
            onChange={handleChecked(params.row.clusterMor, 'disabled')}
            onBlur={() => {}}
          />
        </NblFlexContainer>
      ),
    },
    {
      field: 'restricted',
      headerName: 'Restricted',
      flex: 1,
      renderCell: (params) => (
        <NblFlexContainer center>
          <NblCheckBox
            checked={!!params.row.restricted}
            label=""
            name=""
            onChange={handleChecked(params.row.clusterMor, 'restricted')}
            onBlur={() => {}}
          />
        </NblFlexContainer>
      ),
    },
    { field: 'hostCount', headerName: 'Hosts', flex: 1 },
    { field: 'vmCount', headerName: 'VMs', flex: 1 },
    { field: 'clusterStatus', headerName: 'Status', flex: 1 },
  ];

  return (
    <NblFlexContainer direction="column">
      {viewMode === 'list' && (
        <NblTable
          columns={clusterColumns}
          rows={clusterData.map((c) => ({ ...c, id: c.id }))}
          rowsOverlayMessage="No Clusters Found"
          showResetFilter={false}
        />
      )}

      {viewMode === 'details' && selectedCluster && (
        <>
          <NblFlexContainer>
            <NblFlexContainer justifyContent="start" alignItems="center">
              <IconButton
                aria-label="Back to list"
                onClick={() => {
                  setViewMode('list');
                  setSelectedCluster(null);
                  setActiveTabIndex(0);
                }}
              >
                <HeaderBackIcon />
              </IconButton>
              <NblSwitchTabBar
                tabs={tabLabels.map((label) => ({ label }))}
                activeTab={activeTabIndex}
                onTabChange={(label) => {
                  const typedLabel = label as (typeof tabLabels)[number];
                  const index = tabLabels.indexOf(typedLabel);
                  if (index !== -1) {
                    setActiveTabIndex(index);
                  }
                }}
              />
            </NblFlexContainer>
          </NblFlexContainer>

          {activeTabIndex === 0 && (
            <NblFlexContainer width="70%">
              <NblBorderContainer padding="20px" backgroundColor={hexToRgb(theme.palette.secondary.shade4, 0.2)}>
                <NblTypography variant="h4" color="shade1" weight="bold" margin="0px 0px 16px 0px">
                  Add Project
                </NblTypography>
                <NblFlexContainer>
                  <ClusterOverview selectedCluster={selectedCluster} onClusterChange={handleClusterChange} />
                </NblFlexContainer>
              </NblBorderContainer>
            </NblFlexContainer>
          )}

          {activeTabIndex === 1 && <ClusterHostsDetails hosts={selectedCluster.hosts} />}
        </>
      )}
    </NblFlexContainer>
  );
};

export default Clusters;
