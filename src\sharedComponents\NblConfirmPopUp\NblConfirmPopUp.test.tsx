import { useDispatch } from 'react-redux';
import { render, screen, fireEvent } from '@testing-library/react';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblConfirmPopUp from './';

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));

describe('NblConfirmPopUp Component', () => {
  const mockDispatch = jest.fn();

  const defaultProps = {
    title: 'Confirm Action',
    content: 'Are you sure you want to proceed?',
    open: true,
    onClose: jest.fn(),
    onSubmit: jest.fn(),
  };

  beforeEach(() => {
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders with title and content', () => {
    render(<NebulaThemeProvider><NblConfirmPopUp {...defaultProps} /></NebulaThemeProvider>);
    expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to proceed?')).toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', () => {
    render(<NebulaThemeProvider><NblConfirmPopUp {...defaultProps} /></NebulaThemeProvider>);
    fireEvent.mouseDown(screen.getByText('Cancel'));
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('calls onSubmit when submit button is clicked', () => {
    render(<NebulaThemeProvider><NblConfirmPopUp {...defaultProps} /></NebulaThemeProvider>);
    fireEvent.mouseDown(screen.getByText('Submit'));
    expect(defaultProps.onSubmit).toHaveBeenCalled();
  });

  it('does not render submit button if showSubmit is false', () => {
    render(<NebulaThemeProvider><NblConfirmPopUp {...defaultProps} showSubmit={false} /></NebulaThemeProvider>);
    expect(screen.queryByText('Submit')).not.toBeInTheDocument();
  });

  it('renders close icon if showCloseIcon is true', () => {
    render(<NebulaThemeProvider><NblConfirmPopUp {...defaultProps} showCloseIcon /></NebulaThemeProvider>);
    expect(screen.getByLabelText('close')).toBeInTheDocument();
  });

  it('renders maximize icon if canMaximize is true', () => {
    render(<NebulaThemeProvider><NblConfirmPopUp {...defaultProps} canMaximize /></NebulaThemeProvider>);
    expect(screen.getByLabelText('Maximize')).toBeInTheDocument();
  });

  it('disables submit button when isSubmitting is true', () => {
    render(<NebulaThemeProvider><NblConfirmPopUp {...defaultProps} isSubmitting /></NebulaThemeProvider>);
    expect(screen.getByText('Submit')).toBeDisabled();
  });

  it('renders custom element if renderElement is provided', () => {
    render(
      <NebulaThemeProvider>
        <NblConfirmPopUp
          {...defaultProps}
          renderElement={<div data-testid="custom-element">Custom</div>}
        />
      </NebulaThemeProvider>
    );
    expect(screen.getByTestId('custom-element')).toBeInTheDocument();
  });
});
