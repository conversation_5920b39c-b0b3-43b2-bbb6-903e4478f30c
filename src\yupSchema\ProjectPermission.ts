import * as yup from 'yup';

const applicationsSchema = yup.object().shape({
  applicationId: yup.string().required('Application is required'),
  envId: yup.string().required('Env is required'),
  groupId: yup.string().required('AD Group is required'),
  roleId: yup.string().required('Role is required'),
});

export const projectPermissionSchema = yup.object().shape({
  applications: yup
    .array()
    .of(applicationsSchema)
    .test('unique-permission-combo', 'Duplicate permissions are not allowed', (permissions) => {
      if (!permissions) {
        return true;
      } else {
        const permissionSet = new Set(
          permissions.map(({ applicationId, envId, groupId, roleId }) => `${applicationId}-${envId}-${groupId}-${roleId}`)
        );
        return permissionSet.size === permissions.length;
      }
    }),
});
