import { VMTypeName } from 'componentsV2/Administration/VMSize/VMSizeForm/AddVmSizeForm';
import { VMSizeRange } from 'mock/VMSizeRange';
import * as yup from 'yup';
const isNotWindows = (value: string) => value !== VMTypeName.Windows;
 export let formValidationCheck: boolean = false;

const ifVmTypeIsNotWindows = (message: string, min: number, max: number) =>
  yup.number().when('vmType', {
    is: isNotWindows,
    then: yup
      .number()
      .nullable()
      .required(message)
      .notOneOf([0], 'Zero is not an acceptable value')
      .min(min, `Value must be greater than or equal to ${min}`)
      .max(max, `Value must be less than or equal to ${max}`),
    otherwise: yup.number().nullable().notRequired(),
  });

const isWindows = (value: string | undefined) => value === VMTypeName.Windows;
const ifVmTypeIsWindows = (message: string, min: number, max: number) =>
  yup.number().when('vmType', {
    is: isWindows,
    then: yup
      .number()
      .nullable()
      .required(message)
      .notOneOf([0], 'Zero is not an acceptable value')
      .min(min, `Value must be greater than or equal to ${min}`)
      .max(max, `Value must be less than or equal to ${max}`),
    otherwise: yup.number().nullable().notRequired(),
  });

const AddVMSizeSchema = (isCustomSize?: boolean) =>
  yup
    .object()
    .shape({
      vmSizeName: yup
        .string()
        .trim()
        .required('VM Type is required')
        .when({
          is: !isCustomSize,
          then: yup.string().required('VM Size Name is required'),
          otherwise: yup.string().nullable(),
        }),
      vmType: yup.string().required('VM Type is required'),
      vmSequence: yup
        .number()
        .when({
          is: !isCustomSize,
          then: yup.number().required('VM Sequence number is required'),
          otherwise: yup.number().nullable(),
        })
        .min(VMSizeRange.vmSequence.minValue, `Value must be greater than or equal to ${VMSizeRange.vmSequence.minValue}`)
        .max(VMSizeRange.vmSequence.maxValue, `Value must be less than or equal to ${VMSizeRange.vmSequence.maxValue}`),
      noOfCores: ifVmTypeIsNotWindows('No of Cores is required', VMSizeRange.noOfCores.minValue, VMSizeRange.noOfCores.maxValue),
      memory: ifVmTypeIsNotWindows('Memory is required', VMSizeRange.memory.minValue, VMSizeRange.memory.maxValue),
      root: ifVmTypeIsNotWindows('Root is required', VMSizeRange.root.minValue, VMSizeRange.root.maxValue),
      home: ifVmTypeIsNotWindows('Home is required', VMSizeRange.home.minValue, VMSizeRange.home.maxValue),
      opt: ifVmTypeIsNotWindows('Opt is required', VMSizeRange.opt.minValue, VMSizeRange.opt.maxValue),
      var: ifVmTypeIsNotWindows('Var is required', VMSizeRange.var.minValue, VMSizeRange.var.maxValue),
      varLog: ifVmTypeIsNotWindows('Var/Log is required', VMSizeRange.varLog.minValue, VMSizeRange.varLog.maxValue),
      varLogAudit: ifVmTypeIsNotWindows('Var/Log/Audit is required', VMSizeRange.varLogAudit.minValue, VMSizeRange.varLogAudit.maxValue),
      tmp: ifVmTypeIsNotWindows('Tmp is required', VMSizeRange.tmp.minValue, VMSizeRange.tmp.maxValue),
      varTmp: ifVmTypeIsNotWindows('Var/Tmp is required', VMSizeRange.varTmp.minValue, VMSizeRange.varTmp.maxValue),
      customVolume: ifVmTypeIsWindows('Custom Volume is required', VMSizeRange.customVolume.minValue, VMSizeRange.customVolume.maxValue),
      customMemory: ifVmTypeIsWindows('Custom Memory is required', VMSizeRange.customMemory.minValue, VMSizeRange.customMemory.maxValue),
      customCores: ifVmTypeIsWindows('Custom Cores is required', VMSizeRange.customCore.minValue, VMSizeRange.customCore.maxValue),
    })
    .test('non-windows-sum', 'The sum of all fields must not exceed 1000', function (values) {
      const { vmType, noOfCores, memory, root, home, opt, var: varField, varLog, varLogAudit, tmp, varTmp } = values;
      if (!isWindows(vmType)) {
        const sum =
          (noOfCores || 0) +
          (memory || 0) +
          (root || 0) +
          (home || 0) +
          (opt || 0) +
          (varField || 0) +
          (varLog || 0) +
          (varLogAudit || 0) +
          (tmp || 0) +
          (varTmp || 0);
        if (sum > 1000) {
          formValidationCheck = true;
          return this.createError({ path: 'sum', message: 'The Sum of all fields should not exceed 1000' });
        } else {
          formValidationCheck = false;
          return true;
        }
      } else return false;
    })
    .test('windows-sum', 'The sum of all fields must not exceed 1000', function (values) {
      const { vmType, customVolume, customCores, customMemory } = values;
      if (isWindows(vmType)) {
        const sum = (customVolume || 0) + (customCores || 0) + (customMemory || 0);
        if (sum > 1000) {
          formValidationCheck = true;
          return this.createError({ path: 'sum', message: 'The Sum of all fields should not exceed 1000' });
        } else {
          formValidationCheck = false;
          return true;
        }
      } else return false;
    });

export default AddVMSizeSchema;
