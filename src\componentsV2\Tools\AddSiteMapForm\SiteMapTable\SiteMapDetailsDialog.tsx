import NblConfirmPopUp from 'sharedComponents/NblConfirmPopUp';

interface SiteMapDetailsDialogProps {
  selectedSiteMap: any;
  open: boolean;
  onClose: () => void;
}

import React from 'react';
import { NblTable } from 'sharedComponents/NblTable';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

function SiteMapPopupTable({ siteMapData }: any) {
  const columns = [
    { field: 'id', headerName: 'Field Name', flex: 1 },
    { field: 'value', headerName: 'Value', flex: 1.5 },
  ];
  const rows = [
    { id: 'Site Name', value: siteMapData.siteName },
    { id: 'Facility Type', value: siteMapData.facilityType },
    { id: 'SAP Functional Name', value: siteMapData.SAPFunctionalName },
    { id: 'Vendor Address Header', value: siteMapData.vendorAddressHeader },
    { id: 'Sparing Depots', value: siteMapData.sparingDepots },
    { id: 'Entity', value: siteMapData.entity },
    { id: 'Sold To Address', value: siteMapData.soldToInfo?.soldToAddress },
    { id: 'Ship To Address', value: siteMapData.shipToInfo?.shipToAddress },
    { id: 'Latitude', value: siteMapData.latitude },
    { id: 'Longitude', value: siteMapData.longitude },
    { id: 'Sparing CLLI', value: siteMapData.spareCLLI },
    { id: 'Site Created Date', value: siteMapData.siteCreatedDate },
    { id: 'Site Modified Date', value: siteMapData.siteModifiedDate },
    { id: 'Region Origin', value: siteMapData.regionOrigin },
    { id: 'Market Origin', value: siteMapData.marketOrigin },
    { id: 'Region ISP', value: siteMapData.regionISP },
    { id: 'Market ISP', value: siteMapData.marketISP },
    { id: 'Region FOPS', value: siteMapData.regionFOPS },
    { id: 'Market FOPS', value: siteMapData.marketFOPS },
    { id: 'Colocation Cage Info', value: siteMapData.colocationCageInfo },
    { id: 'Sold to Id', value: siteMapData.soldToInfo?.soldToId },
    { id: 'Sold To CLLI', value: siteMapData.soldToInfo?.CLLI },
    { id: 'Sold To Site Address', value: siteMapData.soldToInfo?.address },
    { id: 'Sold To City', value: siteMapData.soldToInfo?.city },
    { id: 'Sold To State', value: siteMapData.soldToInfo?.state },
    { id: 'Sold To Zip', value: siteMapData.soldToInfo?.zip },
    { id: 'Sold To Site Contact PID', value: siteMapData.soldToInfo?.siteContactPID },
    { id: 'Sold To Manager', value: siteMapData.soldToInfo?.manager },
    { id: 'Sold To Contact', value: siteMapData.soldToInfo?.phone },
    { id: 'Ship To Id', value: siteMapData.shipToInfo?.shipToId },
    { id: 'Ship To Site Address', value: siteMapData.shipToInfo?.address },
    { id: 'Ship To City', value: siteMapData.shipToInfo?.city },
    { id: 'Ship To State', value: siteMapData.shipToInfo?.state },
    { id: 'Ship To Zip', value: siteMapData.shipToInfo?.zip },
    { id: 'Ship To Site Contact PID', value: siteMapData.shipToInfo?.siteContactPID },
    { id: 'Ship To Contact Person', value: siteMapData.shipToInfo?.siteContactPerson },
    { id: 'Ship To Contact', value: siteMapData.shipToInfo?.phone },
    { id: 'Ship To - Pallet Jack', value: siteMapData.shipToInfo?.palletJack },
    { id: 'Ship To - Lift Gate', value: siteMapData.shipToInfo?.liftGate },
  ];
  return (
    <NblFlexContainer minWidth="100%">
      <NblTable columns={columns} rows={rows} hideFooterAndPagination={true} showResetFilter={false} rowSize="100"></NblTable>
    </NblFlexContainer>
  );
}

const SiteMapDetailsDialog: React.FunctionComponent<SiteMapDetailsDialogProps> = ({ selectedSiteMap, open, onClose }) => {
  return (
    <NblConfirmPopUp
      title="Sitemap Details"
      content=""
      open={open}
      maxWidth={'50rem'}
      canMaximize={true}
      onClose={onClose}
      showActionButton={false}
      showCloseIcon={true}
      renderElement={<SiteMapPopupTable siteMapData={selectedSiteMap} />}
    ></NblConfirmPopUp>
  );
};

export default SiteMapDetailsDialog;
