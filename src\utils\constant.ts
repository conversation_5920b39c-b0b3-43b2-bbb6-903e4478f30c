export const CATALOG_LABELS = {
  iaas: 'iaas',
  ipam: 'ipam',
  reserveIPAddressBlock: 'reserveIPAddressBlock',
  updateIPAddressBlock: 'updateIPAddressBlock',
  releaseIPAddressBlock: 'releaseIPAddressBlock',
  monitoring: 'monitoring',
  createOrganization: 'createOrganization',
  onboardDevice: 'onboardDevice',
  offboardDevice: 'offboardDevice',
  netDevOps: 'netDevOps',
  doNotPage: 'doNotPage',
  firewallRuleRequest: 'firewallRuleRequest',
  addFirewallRules: 'addFirewallRules',
  virtualMachine: 'virtualMachine',
  linux7: 'linux7',
  linux8and9: 'linux8and9',
  ubuntu: 'ubuntu',
  windows: 'windows',
  paas: 'paas',
  mongodb: 'mongodb',
  postgresdb: 'postgresdb',
  redisdb: 'redisdb',
  oracle: 'oracledb',
};

export const FIREWALL_FORM_HOVER_TEXTS: any = {
  projectName: 'Enter a jira ticket summary name or request description',
  appId: 'Enter an App ID. Formatted as "APP" followed by numbers. e.g APP1234',
  supportOrganization: 'Select the sponsoring organization',
  region: 'Select the affected region(s)',
  source_location: 'Name of a location, or at least a state abbreviation',
  source_hostname: 'Source hostname as fully qualified domain name; leave blank for network',
  source_ipAddress: 'IPv4 or IPv6 address of a host or network or IP range',
  source_port: 'A port number (between 1 and 65535), comma-separated list of ports, or hyphenated port range',
  destination_location: 'Name of a location, or at least a state abbreviation',
  destination_hostname: 'Destination hostname as fully qualified domain name; leave blank for network',
  destination_ipAddress: 'IPv4 or IPv6 address of a host or network or IP range',
  destination_port: 'A port number (between 1 and 65535), comma-separated list of ports, or hyphenated port range',
  linkToJira: 'Check box to link this request to an existing Jira issue',
  jiraIssueLink: 'Enter an existing JIRA issue to link to. JIRA issues take the form of <PROJECT>-<NUMBER>, such as NETDCOPSFW-999.',
  netopsaskTicket: 'Enter a TOPS ticket. Formatted as "TOPS" followed by numbers. e.g TOPS-1001',
  nebulaProject: 'Select a Project',
  businessRequestDate: 'Business Request Date should be minimum 20 days from the time of requesting date',
  sourceIpAddress: 'IPv4 address of a host or network or IP range',
  destinationIpAddress: 'IPv4 address of a host or network or IP range',
  protocol: 'The SNMP and HTTP protocols will be converted to TCP after submitting the request.',
};

export const DEVICE_CONFIG_FORM_HOVER_TEXTS: any = {
  options: 'Select a option',
  ipAddress: 'Enter IPV4 or IPv6 address',
  deviceHost: 'Enter Host',
  interface: 'Enter Interface',
  asn: 'Enter ASN',
  kma: 'Select the KMA',
  market: 'Select the Market',
};

export const INTERNAL_CERT_FORM_HOVER_TEXTS: any = {
  certificateName: 'Enter Certificate Name',
  certificateEmail: 'Enter Certificate Email',
  orgName: 'Enter Organization Name',
  orgUnit: 'Enter Organization Unit',
  state: 'Select State',
  city: 'Enter City',
  country: 'Enter Country',
  divisionName: 'Enter Division Name',
  locality: 'Enter Locality',
  dnsName: 'DNS Name should be fully qualified domain name(FQDN)',
  subjectAlternateNames: 'Subject Alternate Names should follow this pattern (DNS:UAMP-VOICE.COM)',
  keyType: 'Enter Key Type',
  keySize: 'Enter Key Size',
  validity: 'Select Validity',
  certPassword: 'Enter Certificate Password',
  policyFolder: 'Select Policy Folder',
  format: 'Enter format',
  subject: 'Enter Subject',
  certificateFormat: 'Select Certificate Format',
  certificateType: 'Select Certificate Type',
};

export const COMMONFIREWALL_FORM_HOVER_TEXTS: any = {
  project: 'Enter a Project',
  appId: 'Enter an App ID. Formatted as "APP" followed by numbers. e.g APP1234',
  requestCreator: '',
  genomeId: 'Enter a Genome ID',
  requestName: 'Enter a Request Name',
  commonApplication: 'Enter a Common Application',
  ipAddress: 'IPv4 or IPv6 address of a host or network or IP range',
  inboundPort: 'A comma-separated list of port numbers (between 1 and 65535), followed by colon and protocol',
  outboundPort: 'A comma-separated list of port numbers (between 1 and 65535), followed by colon and protocol',
  atleastInboundOutboundPort: 'Either Inbound or Outbound Port & Protocol is required',
};

export const F5_TEXTS: any = {
  datacenter: 'Select the Datacenter',
  applicationName: 'Application Name should be entered without spaces',
  fqdnName:
    'Enter FQDN Name, e.g., uamp-voice.cpe.charter.com. Please ensure that no wildcard characters (such as *, ?, @, or %) are used.',
  lbMethod: 'Select the LB Method',
  listeningPort: 'Enter port number from 1 - 65535',
  forwardingPort: 'Enter port number from 1 - 65535',
  healthCheckType: 'Select the Health Check Type',
  appId: 'The App ID will be automatically populated by selection of environment.',
  healthCheckUrl: 'Please select Health Check Type as Url Check',
  appHealthResponse: 'Enter App Health Response',
  caConfig: 'Select the Configuration',
  policyFolder: 'Select the Configuration first',
  reason: 'Enter a reason',
  host: 'Enter host',
  ipv4: 'Enter IPV4 addresses',
  ipv6: 'Enter IPV6 addresses',
  protocol: 'Select a Protocol',
  port: 'Enter port number from 1 - 65535',
};

export enum ADMIN_TILE_PERMISSION_TYPE {
  form = 'form',
  grid = 'grid',
}

export const PERMISSION_MESSAGES = {
  adminCatalogItem: "You don't have permission to access this item",
  serviceCatalogItem: "You don't have any permission on this catalog. Please contact admin",
  updateRequest: "You don't have update permission",
  readRequest: "You don't have read permission",
  deleteRequest: "You don't have delete permission",
  formSubmitRequest: "You don't have create or update permission",
};

export const updatedStatus: { [key: string]: string } = {
  stoping: 'STOPPING',
  starting: 'STARTING',
  restarting: 'RESTARTING',
  // Not a spelling error: statuses internally append 'ing' to action names to map to correct updating states upon trigerring actions(e.g start -> starting , restore -> restoreing). This will not be displayed on UI
  restoreing: 'ACTIVE',
  stoped: 'STOPPED',
  started: 'RUNNING',
  restarted: 'RUNNING',
  suspending: 'SUSPENDING',
  reseting: 'RESETTING',
};

export const SUB_ACCOUNT_HOVER_TEXTS: any = {
  accountOwner: 'Enter first and lastname of account owner',
  costCenterID: 'Please enter 10 digit number',
  evName: 'Enter first and last name of the Account EVP',
  vpName: 'Enter first and last name of the Account VP',
  approve: 'Enter the CIDR base, then Save it before taking any Approval action',
};

export const REQUEST_FEATURE_HOVER_TEXTS: any = {
  requestingOrganization: 'Select the requesting organization',
  capabilityArea: 'Select the desired capability area',
  priority: 'Select the desired Jira priority',
  expectedDate: 'Expected Date should be minimum 20 days from the time of requesting date',
  summary: 'Enter the Jira summary',
  description: 'Enter the Jira description',
  justification: 'Enter the feature justification',
  watchers: 'List the desired watchers',
};

export const CAPABILITY_AREAS: string[] = ['Network', 'Compute', 'Security', 'Storage', 'DBaas', 'Caas', 'Other'];

export const JIRA_PRIORITY_LEVELS: any[] = [
  {
    label: 'P1 - Critical/High',
    value: '2',
  },
  {
    label: 'P2 - Major/Medium',
    value: '3',
  },
  {
    label: 'P3 - Minor/Low',
    value: '4',
  },
  {
    label: 'P4 - Trivial',
    value: '5',
  },
];

export const JIRA_STORY_TYPES: any = {
  request_feature: 'REQUEST_FEATURE',
};

export const TANIUM_LAYOUTS = process.env.REACT_APP_TANIUM_TARGET_LAYOUT?.split(',');

export const STORAGE_HELPER_TEXT: any = {
  bucketName: 'Please choose account name to get bucketname',
  bucketNameDesc: 'Enter a description to modify the bucket name. e.g.Format: <accountname>-<bucketnamedesc>-s3-NNN.',
  fileSystemNameDesc:
    'Enter a description to modify the file system name. Project shortname in file system name will get overwrite with entered description',
  datacenters: 'Please select the Project to get the datacenter options',
};

export const nebulaBasePath = process.env.REACT_APP_ROUTE_BASE_NAME + '';

export const HEADER_TITLES: { [key: string]: string } = {
  [nebulaBasePath]: 'Nebula Overview',
  [nebulaBasePath + '/resources']: 'My Resources',
  [nebulaBasePath + '/requests']: 'My Requests',
  [nebulaBasePath + '/approvals']: 'My Approvals',
  [nebulaBasePath + '/administration']: 'Administration',
  [nebulaBasePath + '/search']: 'Nebula Search',
  [nebulaBasePath + '/advanced-search']: 'Nebula Search',
};

export const BREADCRUMBS_PATH: { [key: string]: string } = {
  requestoverview: 'Request Overview',
};

export enum LANDING_PAGE_FILTERS {
  YEARLY = 'yearly',
  MONTHLY = 'monthly',
  QUARTELY = 'quarterly',
}

export const NEBULA_LAUNCH_YEAR = 2024;

export const QUARTERS = [
  { label: 'Q1', value: 1 },
  { label: 'Q2', value: 2 },
  { label: 'Q3', value: 3 },
  { label: 'Q4', value: 4 },
];

export const MONTHS = [
  { label: 'January', value: 1 },
  { label: 'February', value: 2 },
  { label: 'March', value: 3 },
  { label: 'April', value: 4 },
  { label: 'May', value: 5 },
  { label: 'June', value: 6 },
  { label: 'July', value: 7 },
  { label: 'August', value: 8 },
  { label: 'September', value: 9 },
  { label: 'October', value: 10 },
  { label: 'November', value: 11 },
  { label: 'December', value: 12 },
];

export const UNIT_REGEX = /^(\d+(\.\d+)?)\s*(\D+)$/;

export enum CORPNET_CATLAOGS_SHORTNAME {
  WINDOWS_CORPNET = 'windowscorpnet',
  LINUX_CORPNET = 'linuxcorpnet',
}

export const CorpnetVmConfig = {
  domain: 'Blue',
  optionTypeNetwork: 'network',
  optionTypeResource: 'resource-pool',
};

export const APPREFID_RELATED_TAGS = process.env.REACT_APP_APPREFID_RELATED_TAGS || '';
export const Catalog_Route = {
  CatalogApproval: 'catalog-approval',
  ViewCatalogList: '/administration/service-catalog/manage-service-catalog-lists',
};

export const NULL_DISPLAY_VALUE = '\u2014';

export const URLEncryptionSecret = process.env.REACT_APP_PASSWORD_ENCRYPTION_KEY || '';

export const BASE_SUBNET_COUNT_OPTIONS = [2, 3, 4];

export enum TOOLS {
  CENTRIFY = 'centrify',
  QUALYS = 'qualys',
  TANIUM = 'tanium',
  SPLUNK = 'splunk',
  GRANITE = 'granite',
  CROWDSTRIKE = 'crowdstrike',
  SCIENCE_LOGIC = 'scienceLogic',
}

export enum TOOL_NAMES {
  CENTRIFY = 'Centrify',
  QUALYS = 'Qualys',
  TANIUM = 'Tanium',
  SPLUNK = 'Splunk OS',
  GRANITE = 'Granite',
  CROWDSTRIKE = 'Crowd Strike',
  SCIENCE_LOGIC = 'Science Logic',
}

export enum TOOLS_OPTIONS {
  SECURITY_TOOLS = 'securityTools',
  COMPLIANCE_TOOLS = 'complianceTools',
  OBSERVABILITY_TOOLS = 'observabilityTools',
  INVENTORY_TOOLS = 'inventoryTools',
}

export enum DOMAIN_NAMES {
  PACE = 'pace',
}
