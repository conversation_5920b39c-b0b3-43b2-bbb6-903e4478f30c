import { styled } from '@mui/material/styles';
import { MenuItem, Select, Typography } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import {
  getContainedVariantStylesForMuiSelect,
  getDisabledInputStylesForMuiSelect,
  getMuiSelectPlaceholderStyles,
  getOutlinedBaseStyles,
  getOutlinedInputStyles,
  getScrollbarStyles,
} from '../common';

export const StyledSelect = styled(Select)<{ theme?: NebulaTheme; contained?: boolean }>(({ theme, contained }) => {
  return {
    '& .MuiSelect-select.MuiInputBase-input.MuiOutlinedInput-input': {
      ...getOutlinedBaseStyles(theme),
      ...getMuiSelectPlaceholderStyles(),
      minHeight: '32px',
      maxHeight: '200px',
      padding: '0.5px 8px',
      display: 'flex',
      alignItems: 'center',
      '&': {
        ...getScrollbarStyles(theme),
      },
    },
    ...getDisabledInputStylesForMuiSelect(),
    ...getOutlinedInputStyles(theme),
    ...(contained && getContainedVariantStylesForMuiSelect(theme)),
  };
});

export const StyledTypography = styled(Typography)<{ theme?: NebulaTheme; optionlabel?: string }>(({ theme, optionlabel }) => {
  const { typography } = theme;
  return {
    ...typography.subtitle1,
    opacity: 0.4,
    ...(optionlabel === 'true' && {
      ...typography.semiBold,
      marginLeft: '6px',
      opacity: 1,
    }),
  };
});

export const StyledMenuItem = styled(MenuItem)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography, palette } = theme;
  const { select } = palette;
  return {
    ...typography.subtitle1,
    borderBottom: `1px solid ${select.optionsBorderColor}`,
    color: select.color,
    lineHeight: '3',
    height: '40px',
    margin: '0px 6px',
    padding: '6px',
    '&.Mui-selected': {
      backgroundColor: 'transparent !important',
    },
    '& > div': {
      width: 'auto !important'
    },
    '& label': {
      marginRight: '0'
    }
  };
});

export const CustomScrollbarStyles = styled('div')<{ theme?: NebulaTheme }>(({ theme }) => ({
  '&': {
    ...getScrollbarStyles(theme),
  },
}));
