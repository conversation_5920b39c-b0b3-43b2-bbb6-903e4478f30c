import React, { useEffect } from 'react';
// eslint-disable-next-line
import { generateEnum } from 'utils/common';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import NblTypography from 'sharedComponents/NblTypography';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblButton from 'sharedComponents/Buttons/NblButton';
import { Credentials } from '..';

interface CredMappingFormProps {
  onClose: () => void;
  onSuccess?: () => void;
  setCredData: (values: Credentials) => void;
  credFormData?: Credentials;
}

const AddCredentialsForm: React.FunctionComponent<CredMappingFormProps> = ({
  onClose,
  setCredData,
  credFormData,
}: CredMappingFormProps) => {
  const { nblFormProps, nblFormValues } = useNblForms<Credentials>();
  const FIELDNAMES = generateEnum<Credentials>(nblFormValues);

  useEffect(() => {
    if (credFormData) {
      nblFormProps.setValues({
        name: credFormData.name,
        path: credFormData.path,
        provider: credFormData.provider,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [credFormData]);

  const onCancel = () => {
    nblFormProps.resetForm();
    onClose();
  };

  const submitHandler = () => {
    setCredData({
      name: nblFormValues?.name,
      path: nblFormValues?.path,
      provider: nblFormValues?.provider,
    });
    nblFormProps.resetForm();
    onClose();
  };

  return (
    <NblGridContainer>
      <NblTypography variant="h5" color="shade1" padding="5px">
        Add Cred
      </NblTypography>
      <NblGridItem>
        <NblGridContainer columns={4} spacingY={2} spacingX={9}>
          <NblGridItem>
            <NblTextField
              type="text"
              value={nblFormValues.name}
              label="Name *"
              name={FIELDNAMES.name}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.name}
              error={Boolean(nblFormProps.touched.name && nblFormProps.errors.name)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type="text"
              value={nblFormValues.path}
              label="Path *"
              name={FIELDNAMES.path}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.path}
              error={Boolean(nblFormProps.touched.path && nblFormProps.errors.path)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type="text"
              disabled
              value={nblFormValues.provider}
              label="Provider *"
              name={FIELDNAMES.provider}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.provider}
              error={Boolean(nblFormProps.touched.provider && nblFormProps.errors.provider)}
            />
          </NblGridItem>
          <NblGridItem margin="25px">
            <NblFlexContainer direction="row" height="20%">
              <NblButton buttonID={'add-credential-submit-btn'} variant="contained" type="button" onClick={submitHandler} disabled={Object.keys(nblFormProps.errors).length !== 0}>
                Submit
              </NblButton>
              <NblButton buttonID={'add-credential-cancel-btn'} variant="outlined" type="button" onClick={onCancel}>
                Cancel
              </NblButton>
            </NblFlexContainer>
          </NblGridItem>
        </NblGridContainer>
      </NblGridItem>
    </NblGridContainer>
  );
};

export default AddCredentialsForm;
