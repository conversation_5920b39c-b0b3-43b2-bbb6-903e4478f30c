import { Link } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';

import { Grid, Typography } from '@mui/material';
import MainCard from 'mantis/components/MainCard';
import uiVersionDetails from 'version.json';
import useNblNavigate from 'hooks/useNblNavigate';

const Footer = () => {
  const {
    palette: { footer },
  } = useTheme();

  const navigate = useNblNavigate();

  function onClickHandler(path) {
    navigate(path);
  }

  return (
    <MainCard
      sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        mt: 4.5,
        border: 'none',
        borderRadius: 0,
        pl: 1.5,
        pr: 1,
        backgroundColor: footer.backgroundColor,
        zIndex: 1201,
      }}
    >
      <Grid container wrap="nowrap">
        <Grid container item columnGap={7.75}>
          <Grid item>
            <Typography component={Link} to="#" sx={{ textDecoration: 'none', color: footer.textPrimaryColor }}>
              Help
            </Typography>
          </Grid>
          <Grid item>
            <Typography
              component={Link}
              to={`${process.env.REACT_APP_WEBEX_NEBULA_SUPPORT}`}
              sx={{ textDecoration: 'none', color: footer.textPrimaryColor }}
            >
              Report Issues
            </Typography>
          </Grid>
          <Grid item>
            <Typography onClick={() => onClickHandler('/feature-requests')} sx={{ textDecoration: 'none', color: footer.textPrimaryColor }}>
              Feature Requests
            </Typography>
          </Grid>
        </Grid>
        <Grid container item justifyContent="flex-end" columnGap={4}>
          <Typography sx={{ color: footer.textPrimaryColor }}>Nebula UI Version: {uiVersionDetails.version}</Typography>
          <Typography sx={{ color: footer.textPrimaryColor }}>&copy; 2024 All rights reserved.</Typography>
        </Grid>
      </Grid>
    </MainCard>
  );
};

export default Footer;
