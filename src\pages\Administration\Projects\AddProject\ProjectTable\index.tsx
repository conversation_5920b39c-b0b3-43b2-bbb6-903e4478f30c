import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Box, IconButton, Stack, useTheme } from '@mui/material';
import { InfoOutlined } from '@mui/icons-material';
// eslint-disable-next-line no-unused-vars
import { GridColDef } from '@mui/x-data-grid';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import DataGridTable from 'components/DataGridTable';
// eslint-disable-next-line
import { AddProjectData, EditProjectTags, ProjectTableDetail } from 'types';
import ProjectTableDetails from './ProjectTableDetails';
import { setProjectNetworkDetails } from 'store/reducers/projectNetworks';
import { deleteProjectDataCenterValue } from 'store/reducers/projectNetworks';
import { setProjectTagDetails } from 'store/reducers/projectTags';
import DialogBox from 'components/DialogBox/Dialog';
import ActionsColumn from 'components/Administration/ActionsColumn';

enum ProjectSettingName {
  Tags = 'Tags',
  Network = 'Network',
}

interface NetworkConfigTableProps {
  data: AddProjectData[];
  setProjectData: (rules: AddProjectData[]) => void;
  editProjectTags: (data: EditProjectTags) => void;
}

interface ProjectDataValue {
  settingName: string;
  dialogDataCenter: string;
  dialogVlan: string;
}

const ProjectTable: React.FunctionComponent<NetworkConfigTableProps> = ({ data, setProjectData, editProjectTags }) => {
  const theme: NebulaTheme = useTheme();
  const {
    palette: { table },
  } = theme;

  const [dialogOpen, setDialolgOpen] = useState<boolean>(false);
  const [projectDataValue, setProjectDataValue] = useState<ProjectDataValue>({ settingName: '', dialogDataCenter: '', dialogVlan: '' });
  const [projectTagValue, setProjectTagValue] = useState<AddProjectData>();
  const dispatch = useDispatch();

  const handleDelete = (rowId: string, rowValue: EditProjectTags) => {
    dispatch(deleteProjectDataCenterValue(rowValue.settingValue.split(':')[1]));
    let deletedRow = data.filter((item: AddProjectData) => item.id !== rowId);
    setProjectData(deletedRow);
  };

  const handleEdit = (row: EditProjectTags) => {
    if (row.settingName === ProjectSettingName.Tags) {
      const selectedTag = data.find((selectedTagObject: AddProjectData) => selectedTagObject.id === row.id.toString());
      dispatch(setProjectTagDetails(selectedTag));
    }
    row.settingName === ProjectSettingName.Network && dispatch(setProjectNetworkDetails(row));
    editProjectTags({ id: row.id, settingName: row.settingName, settingValue: row.settingValue });
  };
  const handleData = (data: AddProjectData[]) => {
    return data.map((item: AddProjectData, mapId: number) => ({
      id: item.id ? item.id : mapId,
      settingName: item.settingName,
      settingValue: item.dataCenter
        ? `${item?.networkValue?.map((val) => val.name)} : ${item.dataCenterLabel ? item.dataCenterLabel : item.dataCenter}`
        : `${item.tagKey}`,
      networkValue: item.networkValue,
      description: item.description,
    }));
  };

  const columns: GridColDef[] = [
    {
      field: 'settingName',
      headerName: 'Setting Name',
      flex: 1,
      width: 500,
    },
    {
      field: 'settingValue',
      headerName: 'Setting Value',
      flex: 1,
      width: 700,
    },
    {
      field: 'details',
      headerName: 'Details',
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: any) => {
        return (
          <Stack spacing={0} direction="row" alignItems={'center'}>
            <IconButton
              onClick={() => {
                dialogDetailsHandler(params.row);
              }}
            >
              <InfoOutlined style={{ color: table.editIconColor }} />
            </IconButton>
          </Stack>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: any) => {
        return (
          <ActionsColumn
            permissions={{ canUpdate: true, canDelete: true }}
            onEditHandler={() => handleEdit(params.row)}
            onDeleteHandler={() => handleDelete(params.row.id, params.row)}
            showConfirmWarning={false}
          />
        );
      },
    },
  ];

  const dialogDetailsHandler = (dialogData: ProjectTableDetail) => {
    setDialolgOpen(true);
    if (dialogData.settingName === ProjectSettingName.Tags) {
      const selectedTag = data.find((selectedTagObject: AddProjectData) => selectedTagObject.id === dialogData.id);
      setProjectTagValue(selectedTag);
    } else {
      setProjectTagValue({});
      let dialogDataValue = dialogData.settingValue.toString().split(':');
      setProjectDataValue({
        settingName: dialogData.settingName,
        dialogDataCenter: dialogDataValue[1],
        dialogVlan: dialogDataValue[0],
      });
    }
  };

  const dialogCloseHandler = () => {
    setDialolgOpen(false);
  };

  return (
    <Box sx={{ mt: 2 }}>
      {data.length !== 0 && <DataGridTable columns={columns} rows={handleData(data)} />}{' '}
      <DialogBox
        title="Project Setting Details"
        titleVariant="h2"
        sxTitle={{ m: 4 }}
        sxContent={{ width: '100%', textAlignLast: 'left', mb: 3 }}
        fullWidth
        open={dialogOpen}
        onClose={dialogCloseHandler}
      >
        <ProjectTableDetails
          name={projectDataValue.settingName}
          dataCenter={projectDataValue.dialogDataCenter}
          vlan={projectDataValue.dialogVlan}
          setTagDetails={projectTagValue}
        />
      </DialogBox>
    </Box>
  );
};

export default ProjectTable;
