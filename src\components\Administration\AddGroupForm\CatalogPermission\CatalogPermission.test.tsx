import { act, render, screen, fireEvent } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import CatalogPermission from '.';
import { CatalogPermissions } from 'mock/Groups';
import ThemeProvider from 'mock/ThemeProvider';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});

jest.mock('react-toastify');

describe('Render Catalog Permission form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  const handleData = jest.fn();

  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <CatalogPermission
              open
              catalogPermissions={CatalogPermissions}
              selectedCatalogPermissions={[]}
              onClose={handleClose}
              onSuccess={handleSuccess}
              setCatalogData={handleData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(screen.getByText('Add Catalog Permissions')).toBeInTheDocument();
    expect(screen.getByText('Catalogs *')).toBeInTheDocument();
    expect(screen.getByText('Catalog Roles *')).toBeInTheDocument();
    const saveButton = getByText('Save');
    const cancelButton = getByText('Cancel');
    expect(saveButton).toBeEnabled();
    expect(cancelButton).toBeEnabled();
  });

  test('Should save the selected Catalog Items and the Catalog Roles', async () => {
    const { getByText, container } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <CatalogPermission
              open
              catalogPermissions={CatalogPermissions}
              selectedCatalogPermissions={[]}
              onClose={handleClose}
              onSuccess={handleSuccess}
              setCatalogData={handleData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    expect(getByText('Catalogs *')).toBeInTheDocument();
    const catalogIdDropdown = container.querySelector('#catalogId') as HTMLDivElement | null;
    if (catalogIdDropdown) {
      fireEvent.mouseDown(catalogIdDropdown);
      fireEvent.click(getByText('onboardDevice'));
    }
  });
});
