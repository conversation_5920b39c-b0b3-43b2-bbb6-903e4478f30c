import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// component
import RequestViewDetails from 'componentsV2/RequestsDetail';
import { useApiService } from 'api/ApiService/context';
// eslint-disable-next-line
import { ApprovalData, GetPendingApprovalsDetailsResponse } from 'api/ApiService/type';
import NblRefreshProvider from 'sharedComponents/NblUtils/NblRefreshProvider';

const ApprovalDetails = () => {
  const [approvalDetailsData, setApprovalDetailsData] = useState<
    {
      id: string;
      level: string | number;
      approvalGroup: string;
      approvedOrRejectedBy: string;
      approvedOrRejectedAt: string;
      approvalStatus: string;
      comments: string;
    }[]
  >([]);

  const [evaluationResult, setEvaluationResult] = useState<{ canApprove: boolean; reason: string }>({ canApprove: false, reason: '' });

  const apiAssetService = useApiService();

  const fetchApprovalDetails = async () => {
    let parsedFilterObj = {
      serviceRequestId: {
        contains: serviceRequestId,
      },
    };
    let filter = JSON.stringify(parsedFilterObj);
    apiAssetService.apiAssetService.getPendingApprovalsDetails(filter).then((res: GetPendingApprovalsDetailsResponse | undefined) => {
      // Check if the response is undefined or if there's no data/items to process
      if (res?.data?.items?.[0]?.multiLevelApprovals) {
        setApprovalDetailsData(
          res.data.items[0].multiLevelApprovals?.map((val) => ({
            id: res.data.serviceRequestId ?? '',
            level: val?.level ?? '',
            approvalGroup: val?.approvalGroup ?? '',
            approvedOrRejectedBy: val?.approvedOrRejectedBy ?? '',
            approvedOrRejectedAt: val?.approvedOrRejectedAt ?? '',
            approvalStatus: val?.approvalStatus ?? '',
            comments: val?.rejectedReason ?? '',
          })) ?? []
        );
      }
      if (res?.data?.items[0]?.evaluationResult) {
        setEvaluationResult(res?.data?.items[0]?.evaluationResult);
      }
    });
  };

  useEffect(() => {
    fetchApprovalDetails();
  }, []);

  const { serviceRequestId } = useParams<{ serviceRequestId: string }>();
  return (
    <NblRefreshProvider refreshData={fetchApprovalDetails}>
      <RequestViewDetails
        evaluationResult={evaluationResult}
        approvalDetailsData={approvalDetailsData}
        requestType="APPROVALS"
        requestId={serviceRequestId as string}
        serviceRequestId={serviceRequestId as string}
      />
    </NblRefreshProvider>
  );
};

export default ApprovalDetails;
