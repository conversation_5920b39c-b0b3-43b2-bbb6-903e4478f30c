import { useEffect, useState } from 'react';
import { getVMSizingData } from 'api/static-data';
import Catalog from 'components/Catalog';
import { AdministrationTabsData } from 'types';

export default function VMSizing() {
  const [content, setContent] = useState<AdministrationTabsData[]>([]);
  useEffect(() => {
    async function getCatalogItems() {
      try {
        const data = await getVMSizingData();
        setContent(data);
      } catch (error) {
        console.log(error);
      }
    }
    getCatalogItems();
  }, []);

  return content?.length ? <Catalog catalogItems={content} isAdminTiles /> : <div>No items to display</div>;
}
