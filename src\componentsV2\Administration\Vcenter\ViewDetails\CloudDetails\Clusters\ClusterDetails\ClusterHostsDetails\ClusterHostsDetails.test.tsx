import React from 'react';
import { render, screen } from '@testing-library/react';
// eslint-disable-next-line no-unused-vars
import ClusterHostsDetails, { Host } from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

const mockHosts: Host[] = [
  {
    hostMor: 'host-1',
    name: 'Host-One',
    connectionState: 'connected',
    powerState: 'poweredOn',
    disabled: false,
    location: 'Datacenter A',
    vendor: 'Dell',
    model: 'PowerEdge R740',
    cpu: 32,
    core: 16,
    memory: 256,
  },
  {
    hostMor: 'host-2',
    name: 'Host-Two',
    connectionState: 'connected',
    powerState: 'poweredOn',
    disabled: false,
    location: 'Datacenter B',
    vendor: 'HP',
    model: 'ProLiant DL380',
    cpu: 64,
    core: 32,
    memory: 512,
  },
];

describe('ClusterHostsDetails', () => {
  it('renders without crashing with host data', () => {
    render(
      <NebulaThemeProvider>
        <ClusterHostsDetails hosts={mockHosts} />
      </NebulaThemeProvider>
    );

    expect(screen.getByText(/Host Name/i)).toBeInTheDocument();
    expect(screen.getByText(/Location/i)).toBeInTheDocument();
    expect(screen.getByText(/Vendor/i)).toBeInTheDocument();
    expect(screen.getByText(/Model/i)).toBeInTheDocument();
    expect(screen.getByText(/CPU/i)).toBeInTheDocument();
    expect(screen.getByText(/Cores/i)).toBeInTheDocument();
    expect(screen.getByText(/Memory/i)).toBeInTheDocument();

    expect(screen.getByText('Host-One')).toBeInTheDocument();
    expect(screen.getByText('Datacenter A')).toBeInTheDocument();
    expect(screen.getByText('Dell')).toBeInTheDocument();
    expect(screen.getByText('PowerEdge R740')).toBeInTheDocument();
    expect(screen.getAllByText('32').length).toBeGreaterThan(0);
    expect(screen.getAllByText('16').length).toBeGreaterThan(0);
    expect(screen.getAllByText('256').length).toBeGreaterThan(0);

    expect(screen.getByText('Host-Two')).toBeInTheDocument();
    expect(screen.getByText('Datacenter B')).toBeInTheDocument();
    expect(screen.getByText('HP')).toBeInTheDocument();
    expect(screen.getByText('ProLiant DL380')).toBeInTheDocument();
  });

  it('renders empty message when no hosts are provided', () => {
    render(
      <NebulaThemeProvider>
        <ClusterHostsDetails hosts={[]} />
      </NebulaThemeProvider>
    );

    expect(screen.getByText(/No Host Info Found/i)).toBeInTheDocument();
  });
});
