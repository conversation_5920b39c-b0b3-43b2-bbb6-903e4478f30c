import { validateQuota, yupMatchesParams } from 'utils/common';
import * as yup from 'yup';
import { Address4, Address6 } from 'ip-address';

const validateIPAddress = (value: any) => {
  if (value.includes('/')) return false;
  if (Address4.isValid(value) || Address6.isValid(value)) {
    return true;
  } else return false;
};
export const sourceIpSchema = yup.string().test('valid-ip-address', 'Please enter valid IP addresses', function (value) {
  return validateIPAddress(value);
});

const MAX_NFS_CHAR_LIMIT = Number(process.env.REACT_APP_MAX_NFS_CHAR_LIMIT) || 128;
export const NFSValidationSchema = yup.object().shape({
  domain: yup.string().required('Domain is required'),
  projectName: yup.string().required('Project name is required'),
  application: yup.string().required('Application is required'),
  environment: yup.string().required('Environment is required'),
  appId: yup
    .string()
    .required('App ID is missing for the selected application / environment. Please choose different project / application / environment.'),
  datacenters: yup.string().required('Datacenter is required'),
  quota: yup
    .number()
    .typeError('Quota should be a number')
    .integer('Quota should be an integer')
    .test('validate-quata', 'Quota min is 10 MB and Max 1024 GB', function (value) {
      const { quotaUnit } = this.parent;
      const maxQuotaLimit = Number(process.env.REACT_APP_MAX_QUOTA_NFS) || 1024;
      const minQuotaLimit = Number(process.env.REACT_APP_MIN_QUOTA_NFS) || 10;
      return validateQuota(Number(value), quotaUnit, maxQuotaLimit, minQuotaLimit);
    })
    .required('Quota is required'),
  quotaUnit: yup.string().required('Quota Unit is required'),
  nfsVersion: yup.array().of(yup.string()).min(1, 'Please select atleast one NFS version').required('NFS Version is required'),
  fileSystemName: yup.string().required('File system name is required'),
  fileSystemNameDesc: yup
    .string()
    .trim()
    .matches(yupMatchesParams.storageNameDescription.pattern, yupMatchesParams.storageNameDescription.errorMessage)
    .max(MAX_NFS_CHAR_LIMIT, 'File system name description cannot exceed 128 characters.'),
  sourceIp: yup
    .array()
    .of(
      yup.string().test('valid-ip-address', 'Please enter valid IP addresses', function (value) {
        return validateIPAddress(value);
      })
    )
    .min(1, 'Please enter atleast one Ip address')
    .test('unique', 'Duplicate Ip Addresses are not allowed', function (value) {
      const ips = value || [];
      const uniqueIps = new Set(ips);
      return ips.length === uniqueIps.size;
    }),
  reason: yup
    .string()
    .trim()
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage)
    .required('Reason is required'),
});
