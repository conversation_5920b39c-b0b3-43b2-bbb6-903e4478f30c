import React from 'react';
import { InputAdornment } from '@mui/material';
import NblTypography from 'sharedComponents/NblTypography';
import { StyledAdornmentBox } from './styled';

type Props = {
  position: 'start' | 'end';
  label: string;
};

const NblInputAdornment: React.FC<Props> = ({ position, label }) => {
  return (
    <InputAdornment position={position}>
      <StyledAdornmentBox>
        <NblTypography variant="h5">{label}</NblTypography>
      </StyledAdornmentBox>
    </InputAdornment>
  );
};

export default NblInputAdornment;
