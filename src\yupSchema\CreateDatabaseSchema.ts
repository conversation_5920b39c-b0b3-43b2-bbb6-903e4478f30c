// eslint-disable-next-line
import { MultiENVProjectsResponse } from 'types/Interfaces/MultiENVProjectsResponse';
import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

export const validationSchemaFunction = (isRedisDB: boolean) =>
  yup.object().shape({
    domain: yup.string().required('Domain is required'),
    projectName: yup.string().required('Project is required'),
    application: yup.string().required('Application is required'),
    environment: yup.string().required('Environment is required'),
    appId: yup.string().required('VM cannot be created without an APPID - Compliance error.'),
    datacenterDetails: yup
      .array()
      .of(
        yup.object().shape({
          datacenter: yup.string().required(),
          network: yup.string().required(),
          primaryDataCenter: yup.boolean().required(),
          serverCount: yup.number().required(),
        })
      )
      .min(1, 'Please add atleast one datacenter')
      .required('Datacenter is required')
      .test('validate-cluster-size', 'Server count does not match the cluster size', function (datacenterDetails) {
        const { clusterSize } = this.parent;
        const serverCount = datacenterDetails?.reduce((sum, datacenter) => sum + (Number(datacenter.serverCount) || 0), 0);
        return serverCount === Number(clusterSize);
      })
      .test('validate-primary-datacenter', 'Only one data center has to be selected as primary', function (datacenterDetails) {
        if (!datacenterDetails || this.parent.sharding) return true;
        const primaryDataCenters = datacenterDetails?.filter((datacenter) => datacenter.primaryDataCenter === true);
        return primaryDataCenters?.length <= 1;
      })
      .test('validate primarydatacenter', 'At least one primary data center has to be selected', function (datacenterDetails) {
        if (!datacenterDetails) return true;
        return datacenterDetails?.some((datacenter) => datacenter.primaryDataCenter === true);
      }),
    appCode: yup
      .string()
      .required('Host Prefix is required')
      .matches(yupMatchesParams.spaceNotAllowed.pattern, yupMatchesParams.spaceNotAllowed.errorMessage)
      .matches(yupMatchesParams.alphaLowerChars.pattern, yupMatchesParams.alphaLowerChars.errorMessage)
      .length(4, 'Host Prefix must be exactly 4 characters long'),
    vmOption: yup.string().required('VM Option is required'),
    clusterSize: yup.string().required('Cluster Size is required'),
    dbSize: yup.string().required('DB Size is required'),
    description: yup
      .string()
      .trim()
      .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage)
      .test('maxWords', 'Maximum 500 words allowed', (value) => {
        if (!value || value.trim().length === 0) return true;
        const count = value
          .trim()
          .split(/\s+/)
          .filter((word) => word.length > 0).length;
        return count <= 500;
      }),
    datacenter: yup.string().required('Datacenter is required'),
    network: yup.string().required('Network is required'),
    serverCount: yup
      .number()
      .required()
      .test('server-count-limit', function (value) {
        const { clusterSize } = this.parent;
        if (Number(value) > clusterSize) {
          return this.createError({
            message: `Server count cannot be more than ${clusterSize}`,
          });
        }
        return true;
      }),
    dbName: isRedisDB
      ? yup.string()
      : yup
          .string()
          .required('DB Name is required')
          .matches(yupMatchesParams.spaceNotAllowed.pattern, yupMatchesParams.spaceNotAllowed.errorMessage)
          .matches(yupMatchesParams.startWithLowerChar.pattern, yupMatchesParams.startWithLowerChar.errorMessage)
          .matches(yupMatchesParams.lowerCaseWithNumericChars.pattern, yupMatchesParams.lowerCaseWithNumericChars.errorMessage)
          .max(8, 'DB Name must be at most 8 characters long'),
    dbConfig: yup.array().of(
      yup.object().shape({
        username: yup
          .string()
          .required('Username is required')
          .matches(yupMatchesParams.spaceNotAllowed.pattern, yupMatchesParams.spaceNotAllowed.errorMessage)
          .matches(yupMatchesParams.startWithLowerChar.pattern, yupMatchesParams.startWithLowerChar.errorMessage)
          .matches(yupMatchesParams.lowerCaseWithNumericChars.pattern, yupMatchesParams.lowerCaseWithNumericChars.errorMessage)
          .max(10, 'Username must be at most 10 characters long')
          .test('unique', 'Username should be unqiue', function (value) {
            const { dbConfig } = this.options.context as { dbConfig: Array<{ username: string }> };
            return dbConfig.filter(({ username }) => username === value).length === 1;
          }),
      })
    ),
    isDatabaseSelected: yup.boolean().test('is-database-selected', 'Database should be selected', function (value) {
      if (!value) {
        return this.createError({
          message: 'Atleast one Database should be added',
        });
      }
      return true;
    }),
  });
