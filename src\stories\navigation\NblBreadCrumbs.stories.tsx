//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblBreadCrumbs from 'sharedComponents/NblNavigation/NblBreadCrumbs';

type StoryProps = ComponentProps<typeof NblBreadCrumbs>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Navigation/NblBreadCrumbs',
  component: NblBreadCrumbs,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

export const Default: Story = {
  args: {
    seperator: 'arrow',
    breadCrumbs: [
      { label: 'route1', route: '#' },
      { label: 'route2', route: '#' },
      { label: 'route3', route: '#' },
    ],
  },
  render: (args) => (
    <NebulaTheme>
      <NblBreadCrumbs {...args} />
    </NebulaTheme>
  ),
};
