// import { useEffect } from 'react';
//import { Box, Grid, Typography } from '@mui/material';
//import AuthenticationFailedImg from 'assets/images/icons/authenticationFailed.png';
import { OidcProvider, OidcSecure } from '@axa-fr/react-oidc';
import { configuration } from 'oidc';

const SessionLost: React.FC = () => {
  
  return (
    <div>
      <h1>Popup - Token refresh</h1>
      {
        <OidcProvider configuration={configuration}>
          <OidcSecure>{/* <header>Popup</header> */}</OidcSecure>
        </OidcProvider>
      }
    </div>
  );
};

export default SessionLost;

