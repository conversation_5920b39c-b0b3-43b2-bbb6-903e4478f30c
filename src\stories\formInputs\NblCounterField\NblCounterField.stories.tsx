//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblCounterField from 'sharedComponents/NblFormInputs/NblCounterField';

type StoryProps = ComponentProps<typeof NblCounterField>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblCounterField',
  component: NblCounterField,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { type: 'string' },
    name: { type: 'string' },
    type: { type: 'string', options: ['text', ' number'] },
    helperText: { control: 'text' },
  },
};

export default meta;

export const CounterField: Story = {
  args: {
    label: 'Label',
    name: 'Name',
    type: 'number',
    disabled: false,
    helperText: 'helper text',
  },
  render: (args) => (
    <NebulaTheme>
      <NblFlexContainer>
        <NblCounterField {...args} />
      </NblFlexContainer>
    </NebulaTheme>
  ),
};
