import { ComponentProps } from 'react';
// eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';

import NebulaTheme from 'NebulaTheme';
import Nbl<PERSON><PERSON><PERSON>hart from 'sharedComponents/NblPieChart';

type StoryProps = ComponentProps<typeof NblPieChart>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Charts/NblPieChart',
  component: Nbl<PERSON><PERSON><PERSON><PERSON>,

  tags: ['autodocs'],
  argTypes: {
    data: {
      control: {
        type: 'object', // The control type will be array
      },
      description: 'Dataset for the chart, should be an array of objects',
    },
    title: { type: 'string' },
    subtitle: { type: 'string' },
    loading: { type: 'boolean' },
  },
};

export default meta;

export const PieChart: Story = {
  args: {
    data: [
      { value: 50, label: 'IaaS' },
      { value: 50, label: 'PaaS' },
    ],
    title: 'Overall Tool Usage',
    subtitle: 'Nebula Metrics',
    loading: false,
  },
  render: (args: any) => (
    <NebulaTheme>
      <div style={{ width: '50%', height: '50%' }}>
        <NblPieChart {...args} />
      </div>
    </NebulaTheme>
  ),
};
