import React from 'react';
import { Divider } from '@mui/material';
import { SxProps, Theme, useTheme } from '@mui/material';
// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';

interface CustomDividerProps {
  sx?: SxProps<Theme>;
}

const CustomDivider: React.FC<CustomDividerProps> = ({ sx }: CustomDividerProps) => {
  const theme: NebulaTheme = useTheme();
  const {
    palette: { button },
  } = theme;
  return (
    <Divider
      orientation="vertical"
      sx={{
        mx: 1,
        borderWidth: '1px',
        height: '18px',
        [theme.breakpoints.down('xl')]: { height: '10px' },
        borderColor: button.outlined.textColor,
        ...sx,
      }}
    />
  );
};

export default CustomDivider;
