import { useCallback, useEffect, useMemo, useState } from 'react';
import CplanMapContainer from './CplanMapContainer';
import { useTheme } from '@mui/material';
import TableExport from '../../components/TableExport';
import { exportFormats } from '../../utils/constant';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { useApiService } from 'api/ApiService/context';
import { CalculatedVropsResource, Facility } from '../../utils/types';
import NblDivider from 'sharedComponents/NblDivider';
import CplanDropdown from '../../components/CplanDropdown';
// eslint-disable-next-line
import { NebulaTheme } from 'NebulaTheme/type';
import { CalculateVropsResource } from '../../utils/utilization';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { useDispatch, useSelector } from 'react-redux';
import { setOverviewResource } from 'store/reducers/capacityplanning';
import NoData from '../../components/NoData';
import { showSpinner } from 'store/reducers/spinner';
import CplanDomainDropdown from '../../components/CplanDomainDropdown';

const CplanMapView2: React.FunctionComponent = () => {
  const theme: NebulaTheme = useTheme();
  const hours = useSelector((state: State) => state.capacityPlanning.hours);
  const dispatch = useDispatch();
  const overviewResource = useSelector((state: State) => state.capacityPlanning.overviewResource);
  const { apiCapacityPlanningService } = useApiService();
  const [facilities, setFacilities] = useState<Facility[] | []>([]);
  const [calcResources, setCalcResources] = useState<CalculatedVropsResource[] | []>([]);
  const [selectedRegion, setSelectedRegion] = useState('ALL');
  const domainIds = useSelector((state: State) => state.capacityPlanning.selectedDomainIds);

  const getFacilities = useCallback(async () => {
    const Apiresponse = await apiCapacityPlanningService.getFacilities();
    const response = Apiresponse.data;
    setFacilities(response);
  }, [apiCapacityPlanningService]);

  useEffect(() => {
    getFacilities();
  }, [getFacilities]);

  const getCalculatedResources = useCallback(
    async (hours: number) => {
      dispatch(showSpinner({ status: true, message: 'Fetching Vrops resources' }));
      const resources = (await apiCapacityPlanningService.getResources(hours)).data;
      const calculatedResources: CalculatedVropsResource[] = resources.map((res) => CalculateVropsResource(res));
      setCalcResources(calculatedResources);
      if (overviewResource.resourceid) {
        const hoveredResource = calculatedResources.find((res) => res.resourceid === overviewResource.resourceid);
        dispatch(setOverviewResource(hoveredResource));
      } else {
        const hoveredResource = calculatedResources[calculatedResources.length - 1];
        dispatch(setOverviewResource(hoveredResource));
      }
    },
    [apiCapacityPlanningService, hours]
  );

  useEffect(() => {
    getCalculatedResources(hours);
  }, [hours, getCalculatedResources]);

  const getUniqueRegionOptions = useMemo(() => {
    const regions = [...new Set(facilities.map((facility) => facility.region))];
    const regionsOptions = regions.map((region) => {
      return { label: region, value: region };
    });
    return [{ label: 'ALL', value: 'ALL' }, ...regionsOptions];
  }, [facilities]);

  let filteredFacilities = useMemo(() => {
    if (selectedRegion === 'ALL') return facilities.filter((fac) => fac.resources?.length);
    return facilities.filter((fac) => fac.region === selectedRegion && fac.resources?.length);
  }, [selectedRegion, facilities]);

  filteredFacilities = useMemo(() => {
    return filteredFacilities.map((fac) => {
      let resources = fac.resources.filter((res) => domainIds.includes(res.domainid));
      return { ...fac, resources };
    });
  }, [filteredFacilities, domainIds]);

  const calcFacilities = useMemo(() => {
    const updatedFacilities = filteredFacilities.map((fac) => {
      const facilityCalcResources = fac?.resources?.map((res) => {
        return calcResources.find((calcRes) => calcRes.resourceid === res.resourceid);
      });
      return { ...fac, resources: facilityCalcResources as CalculatedVropsResource[] };
    });
    return updatedFacilities;
  }, [filteredFacilities, calcResources, domainIds]);

  
  return (
    <>
      <NblFlexContainer direction="column">
        <NblFlexContainer direction="row" height="auto">
          <CplanDomainDropdown />
          <NblDivider orientation="vertical" color={theme.palette.primary.shade6} length="70%" />
          <CplanDropdown
            label={'Sites'}
            options={getUniqueRegionOptions}
            value={selectedRegion}
            onChange={(value: string) => setSelectedRegion(value)}
          />
          <NblDivider orientation="vertical" color={theme.palette.primary.shade6} length="70%" />
          {facilities.length !== 0 && (
            <TableExport
              downloadOptions={[exportFormats.Excel, exportFormats.CSV, exportFormats.JPEG, exportFormats.PDF]}
              exportSectionId="exportSection"
            ></TableExport>
          )}
        </NblFlexContainer>
        <NblFlexContainer justifyContent="center">
          {calcFacilities?.length ? (
            dispatch(showSpinner({ status: false, message: '' })) && <CplanMapContainer facilities={calcFacilities} />
          ) : (
            <NoData />
          )}
        </NblFlexContainer>
      </NblFlexContainer>
    </>
  );
};

export default CplanMapView2;
