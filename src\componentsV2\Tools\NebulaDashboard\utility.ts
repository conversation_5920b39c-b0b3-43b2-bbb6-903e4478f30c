import { RequestStatus } from 'types/Enums';

type MonthFormatType = 'numeric' | '2-digit' | 'long' | 'short' | 'narrow' | undefined;

export function getMonthsInQuarter(quarters: number[]) {
  let filteredQuarters = [...quarters];
  filteredQuarters = filteredQuarters.sort((a, b) => a - b);

  // Create an array of month names dynamically
  const months = Array.from({ length: 12 }, (_, i) => {
    const date = new Date(0, i);
    // Create a Date object for each month
    return { index: i + 1, month: date.toLocaleString('en-US', { month: 'short' }) };
  });
  // Get the month name in English

  let allmonths: { index: number; month: string }[] = [];

  filteredQuarters.forEach((quarter) => {
    if (quarter < 1 || quarter > 4) {
      throw new Error('Invalid quarter. Quarter must be between 1 and 4.');
    }
    const startMonth = (quarter - 1) * 3;
    allmonths = allmonths.concat(months.slice(startMonth, startMonth + 3));
  });
  return allmonths;
}

export function getQuarter(date: Date) {
  const month = date.getMonth() + 1; // Months are 0-indexed
  return Math.ceil(month / 3);
}
export function getQuartersInRange(startDate: Date) {
  const allquartersAndYears = [];
  const endDate = new Date(startDate);
  // Get the quarter for the start and end dates
  const startQuarter = getQuarter(startDate);
  allquartersAndYears.push({ quarter: startQuarter, year: startDate.getFullYear() });
  for (let i = 1; i <= 6; i++) {
    endDate.setMonth(endDate.getMonth() - 1);
    const endYear = endDate.getFullYear();
    const endQuarter = getQuarter(endDate);
    allquartersAndYears.push({ quarter: endQuarter, year: endYear });
  }

  let requiredQuarterWithYear: { [key: number]: number[] } = {};

  allquartersAndYears.forEach(({ year, quarter }) => {
    if (requiredQuarterWithYear[year]) {
      !requiredQuarterWithYear[year].includes(quarter)
        ? (requiredQuarterWithYear[year] = requiredQuarterWithYear[year].concat(quarter))
        : null;
    } else {
      requiredQuarterWithYear[year] = [quarter];
    }
  });

  return requiredQuarterWithYear;
}

export const statusStyles = (status: string) => {
  switch (status) {
    case RequestStatus.FAILED:
    case RequestStatus.REJECTED:
    case RequestStatus.CANCELLED:
      return { backgroundColor: '#FCCFCD', color: '#F5443C', label: status };

    case RequestStatus.COMPLETED:
    case RequestStatus.APPROVED:
    case RequestStatus.AUTO_APPROVED:
    case RequestStatus.RELEASED:
    case 'CLOSED':
    case RequestStatus.SUBMITTED:
    case RequestStatus.PARTIAL:
      return { backgroundColor: '#DDFAD2', color: '#5EB242', label: status };

    case RequestStatus.PENDING_APPROVAL:
    case RequestStatus.PENDING_PIPELINE_CHECK:
    case RequestStatus.PENDING_DAP_PROCESS:
    case RequestStatus.PENDING_DEPLOYMENT:
    case 'PENDING RISK ANALYSIS':
    case 'PENDING DESIGNER RESULTS':
      return { backgroundColor: '#CAFAEF', color: '#1D9378', label: status };

    case RequestStatus.PROCESSING:
    case RequestStatus.PARTIALLY_APPROVED:
    case RequestStatus.PARTIALLY_REJECTED:
    case 'PARTIALLY COMPLETED':
      return { backgroundColor: '#D1ECF7', color: '#0099D8', label: status };

    case RequestStatus.CREATED:
      return { backgroundColor: '#FCCFCD', color: '#F5443C', label: 'CREATED' };

    case RequestStatus.TIMED_OUT:
    case 'NO CHANGE REQUIRED':
      return { backgroundColor: '#FAF6C7', color: '#A2960B', label: status };

    default:
      return { backgroundColor: '#FAF6C7', color: '#A2960B', label: status };
  }
};
