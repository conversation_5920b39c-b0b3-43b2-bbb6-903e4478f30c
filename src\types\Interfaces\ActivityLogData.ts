import { ACTIVITYLOGSTATUS } from 'types/Enums/ActivityLogStatus';

export default interface ActivityLogData {
  serviceRequestId: string;
  activityLogs: ActivityLog[];
  requestType?: string;
}

export interface ActivityLog {
  eventName: string;
  eventCode: string;
  startTime: string;
  status: ACTIVITYLOGSTATUS;
  endTime: string;
  timeTaken: string;
  retryCount?: number;
  downStreamErrorData?: string;
  subTaskLogs?: {
    startTime: string;
    endTime: string;
    status: ACTIVITYLOGSTATUS;
    timeTaken: string;
    resourceName: string;
    retryCount: number;
    downStreamErrorData: string;
  }[];
}
