// eslint-disable-next-line no-unused-vars
import { GridColDef, GridColumnVisibilityModel, GridFilterModel, GridRowParams, GridRowSelectionModel } from '@mui/x-data-grid';

import DataGridTable from 'components/DataGridTable';
import ConfirmationDialog from 'components/ConfirmationDialog';
import { ApprovalDialogData, ViewDetailsDialog } from 'types';
import { PaginationConfiguration } from '../../types/Interfaces/PaginationResponse';
import { Box } from '@mui/material';

interface RequestsGridProps extends PaginationConfiguration {
  isLoading: boolean;
  rows: any;
  columns: GridColDef[];
  title?: string;
  fetchData?: () => void;
  showReserveIPBlockStatus?: boolean;
  viewDetailsDialog?: ViewDetailsDialog;
  setViewDetailsDialog?: (params: ViewDetailsDialog) => void;
  confirmDialog?: ApprovalDialogData['initialState'];
  dialogConfirmHandler?: () => void;
  dialogCloseHandler?: () => void;
  columnVisibilityModel?: GridColumnVisibilityModel;
  onColumnVisibilityModelChange?: (newModel: GridColumnVisibilityModel) => void;
  isToolBarVisible?: boolean;
  setRejectedReason?: (value: string) => void;
  rejectedReason?: string;
  renderCheckbox?: () => React.ReactElement;
  checkboxSelection?: boolean;
  onRowSelectionModelChange?: (selected: GridRowSelectionModel) => void;
  actionComponent?: React.ReactNode;
  isRowsSelectable?: (params: GridRowParams) => boolean;
  filterModel?: GridFilterModel;
  selectionModel?: GridRowSelectionModel;
  clientRefreshData?: (params: GridFilterModel) => void;
  filterDataForRefresh?: GridFilterModel;
  isCopyMode?: boolean;
  isEditMode?: boolean;
  isRegenerateMode?: boolean;
  isButtonDisabled?: boolean;
  showResetFilter?: boolean;
  isSearch?: boolean;
}

const RequestsGrid: React.FunctionComponent<RequestsGridProps> = ({
  isLoading,
  rows,
  columns,
  title,
  fetchData,
  confirmDialog,
  dialogConfirmHandler,
  dialogCloseHandler,
  columnVisibilityModel,
  onColumnVisibilityModelChange,
  isToolBarVisible,
  setRejectedReason,
  rejectedReason,
  renderCheckbox,
  checkboxSelection,
  onRowSelectionModelChange,
  actionComponent,
  isRowsSelectable,
  filterModel,
  selectionModel,
  serverPagination,
  pageInfo,
  serverPaginationFn,
  componentApiMappings,
  filterDataForRefresh,
  clientRefreshData,
  isButtonDisabled,
  showResetFilter,
  isSearch,
}: RequestsGridProps) => {
  return (
    <>
      <Box sx={{ margin: '0 auto' }}>
        <DataGridTable
          loading={isLoading}
          rows={rows}
          columns={columns}
          title={title}
          refreshHandler={fetchData}
          columnVisibilityModel={columnVisibilityModel}
          onColumnVisibilityModelChange={onColumnVisibilityModelChange}
          isToolBarVisible={isToolBarVisible}
          renderCheckbox={renderCheckbox}
          checkboxSelection={checkboxSelection}
          onRowSelectionModelChange={onRowSelectionModelChange}
          rowSelectionModel={selectionModel}
          actions={actionComponent}
          isRowsSelectable={isRowsSelectable}
          filterModel={filterDataForRefresh && Object.keys(filterDataForRefresh).length ? filterDataForRefresh : filterModel}
          serverPagination={serverPagination}
          pageInfo={pageInfo}
          serverPaginationFn={serverPaginationFn}
          componentApiMappings={componentApiMappings}
          clientRefreshData={clientRefreshData}
          showResetFilter={showResetFilter}
          isSearch={isSearch}
        />
      </Box>
      {confirmDialog?.open && dialogConfirmHandler && dialogCloseHandler && (
        <ConfirmationDialog
          open={confirmDialog.open}
          title={confirmDialog.title}
          content={confirmDialog.content}
          confirmationText={confirmDialog.confirmationText}
          confirmationTextId={'approve-submit-btn'}
          onConfirm={dialogConfirmHandler}
          onClose={dialogCloseHandler}
          setRejectedReason={setRejectedReason}
          rejectedReason={rejectedReason}
          canMaximize={false}
          disabled={isButtonDisabled}
        />
      )}
    </>
  );
};

export default RequestsGrid;
