import { styled } from '@mui/system';
import { hexToRgb } from 'utils/common';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledDivider = styled('div')<{ theme?: NebulaTheme; height: number; margin?: string; }>(({ theme, height, margin }) => ({
  position: 'relative',
  width: '1px',
  height,
  backgroundColor: hexToRgb(theme.palette.primary.main, 0.2),
  ...(margin && {
    margin
  })
}));

export const StyledDiv = styled('div')(() => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
}));
