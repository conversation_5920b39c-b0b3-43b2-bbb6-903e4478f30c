import FireallSplittedRules from './FirewallSplittedRules';
import FirewallTicketDetails from './FirewallTicketDetails';

type FirewallDetails = {
  projectName: string;
  projectCreator: string;
  date: string;
  supportOrganization: string;
  region: string[];
  appId: string;
  netopsaskTicket: string;
  businessRequestDate: string;
  nebulaProject: string;
  jiraIssueLink: string[];
  firewallRules: FireallSplittedRules;
  ticketDetails: FirewallTicketDetails[];
  serviceRequestId: string;
  status: string;
  userName?: string;
};

export default FirewallDetails;
