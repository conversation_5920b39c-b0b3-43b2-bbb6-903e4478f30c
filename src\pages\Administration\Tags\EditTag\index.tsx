import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';

import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { TagDetailProps } from 'types';
import AdministrationService from 'api/ApiService/AdministrationService';
import AddTagForm from 'components/Administration/AddTagForm';
import { AdminEditRouteParams } from 'types/Enums';
import useNblNavigate from 'hooks/useNblNavigate';

const EditTag = () => {
  const dispatch = useDispatch();
  const apiAdministrationService = new AdministrationService();
  const [editTagDetails, setEditTagDetails] = useState<TagDetailProps>();
  const { [AdminEditRouteParams.tags]: catalogTagShortName } = useParams();
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const fetchGroupDetails = () => {
    if (catalogTagShortName) {
      dispatch(showSpinner({ id: SPINNER_IDS.tagDetails, status: true, message: 'Loading tag details...' }));
      apiAdministrationService
        .getTagDetails(catalogTagShortName)
        .then((res) => {
          if (res.status) {
            setEditTagDetails(res.data[0]);
          }
        })
        .finally(() => {
          dispatch(showSpinner({ id: SPINNER_IDS.tagDetails, status: false, message: '' }));
        });
    }
  };

  useEffect(() => {
    fetchGroupDetails();
  }, []);

  const navigateToPermissionDetails = () => {
    navigate('/administration/tags/view-tags');
  };

  return (
    <AddTagForm
      title="Edit Tag"
      permissions={{}}
      editTagDetails={editTagDetails}
      onSuccess={navigateToPermissionDetails}
      onClose={navigateToPermissionDetails}
    />
  );
};

export default EditTag;
