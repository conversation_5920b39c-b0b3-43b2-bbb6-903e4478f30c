import * as yup from 'yup';
import { convertToMB, validateQuota, yupMatchesParams } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { BucketData } from 'api/ApiService/type';

const MAX_S3_BUCKET_CHAR_LIMIT = Number(process.env.REACT_APP_MAX_S3_BUCKET_CHAR_LIMIT) || 16;
// eslint-disable-next-line no-unused-vars
const CreateStorageS3Schema = (projectData: any, bucketData: BucketData[], isEditMode?: boolean, isRegenerateMode?: boolean) =>
  yup.object().shape({
    userDetails: yup
      .array()
      .of(
        yup.object().shape({
          username: yup.string().max(63, 'Username must be at most 63 characters').required('Username is required'),
          permissions: yup.array().of(yup.string()),
        })
      )
      .required('User details are required'),
    projectName: yup.string().required('Project is required'),
    domain: yup.string().required('Domain is required'),
    appId: yup
      .string()
      .test(
        'appId-notrequired-if-regenerate',
        'App ID is missing for the selected application / environment. Please choose different project / application / environment.',
        function (value) {
          if (isRegenerateMode) {
            return true;
          }
          return !!value;
        }
      ),

    datacenters: yup.string().required('Datacenter is required'),
    accountName: yup.string().when('createNewAccount', {
      is: true,
      then: yup.string().required('account name is required').max(63, 'Account Name cannot exceed 63 characters.'),
      otherwise: yup.string(),
    }),
    account: yup
      .string()
      .when('createNewAccount', { is: false, then: yup.string().required('account is required'), otherwise: yup.string() }),
    accountQuota: yup
      .number()
      .typeError('Account Quota should be a number')
      .integer('Account Quota should be an integer')
      .test('validate-quata', 'Account quota should be of min 10 MB and Max 1024 GB', function (value) {
        const { accountQuotaUnit } = this.parent;
        const maxQuotaLimit = Number(process.env.REACT_APP_MAX_ACCOUNT_QUOTA_S3) || 1024;
        const minQuotaLimit = Number(process.env.REACT_APP_MIN_ACCOUNT_QUOTA_S3) || 10;
        return validateQuota(Number(value), accountQuotaUnit, maxQuotaLimit, minQuotaLimit);
      })
      .required('Account Quota is required'),
    accountQuotaUnit: yup.string().required('Account Quota Unit is required'),
    accountDescription: yup
      .string()
      .trim()
      .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage)
      .required('Description is required'),
    bucketName: yup.string().trim().required('Bucket Name is required').max(63, 'Bucket Name cannot exceed 63 characters.'),
    bucketNameDesc: yup
      .string()
      .trim()
      .matches(yupMatchesParams.storageNameDescription.pattern, yupMatchesParams.storageNameDescription.errorMessage)
      .max(MAX_S3_BUCKET_CHAR_LIMIT, 'Bucket name description cannot exceed 16 characters.'),
    bucketQuota: yup
      .number()
      .typeError('Bucket Quota should be a number')
      .integer('Bucket Quota should be an integer')
      .test('validate-quata', 'Bucket quota should be of min 10 MB and Max 1024 GB', function (value) {
        const { bucketQuotaUnit, accountQuota, accountQuotaUnit } = this.parent;
        const maxQuotaLimit = Number(process.env.REACT_APP_MAX_BUCKET_QUOTA_S3) || 1024;
        const minQuotaLimit = Number(process.env.REACT_APP_MIN_BUCKET_QUOTA_S3) || 10;
        if (value === 0) return false;
        else if (!value || !bucketQuotaUnit) return true;
        const totalQuota = convertToMB(bucketQuotaUnit, Number(value));
        if (totalQuota < convertToMB('MB', minQuotaLimit) || totalQuota > convertToMB('GB', maxQuotaLimit)) {
          return this.createError({
            message: `Bucket quota must be between ${minQuotaLimit} MB and ${maxQuotaLimit} GB`,
          });
        }
        const totalExistingQuota = bucketData.reduce((total: number, bucket: any) => {
          const bucketQuota = bucket.bucketQuota.split(' ');
          return total + Number(convertToMB(bucketQuota[1], parseInt(bucketQuota[0])));
        }, 0);

        const accountQuotaInMB = convertToMB(accountQuotaUnit, accountQuota);
        const totalQuotaWithNewValue = totalExistingQuota + totalQuota;
        if (totalQuotaWithNewValue > accountQuotaInMB) {
          return this.createError({
            message: `Total bucket quota for the account should not exceed the account quota of ${accountQuota} ${accountQuotaUnit}.`,
          });
        }
        return true;
      })
      .required('Bucket Quota is required'),
    bucketQuotaUnit: yup.string().required('Bucket Quota Unit is required'),
    bucketDescription: yup
      .string()
      .trim()
      .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage)
      .required('Description is required'),
  });

export default CreateStorageS3Schema;
