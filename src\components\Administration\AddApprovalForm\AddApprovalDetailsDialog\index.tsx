import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid } from '@mui/material';
import { formWrapperError, filterSelectedOptions } from 'utils/common';
import Select from 'components/Select';
import FormWrapper from 'components/FormWrapper';
import { ApprovalDetails } from 'types';
import AdministrationService from 'api/ApiService/AdministrationService';
import PermissionService from 'api/ApiService/PermissionService';
import DialogBox from 'components/DialogBox/Dialog';

interface AddApprovalDetailsProps {
  onClose: () => void;
  onSuccess?: () => void;
  setApprovalData: (values: any) => void;
  approvalDetails?: ApprovalDetails;
  selectedGroupData: ApprovalDetails[];
  groupLength?: (length: number) => void;
  open: boolean;
}
interface ApprovalDropDownItem {
  _id: string;
  groupName: string;
}

interface ApprovalLevelAndCount {
  maxApproverCount: number;
  maxApproverLevel: number;
}

const validationSchema = yup.object().shape({
  groupId: yup.string().required('Approval Group is required'),
  approverCount: yup.number().required('Approver Count is required'),
  level: yup.number().required('Approver Level is required'),
});

const AddApprovalDetailsDialog: React.FunctionComponent<AddApprovalDetailsProps> = ({
  onClose,
  setApprovalData,
  approvalDetails,
  selectedGroupData,
  groupLength,
  open,
}: AddApprovalDetailsProps) => {
  const apiAdministrationService = new AdministrationService();
  const apiPermissionService = new PermissionService();
  const [approvalGroups, setApprovalGroups] = useState<ApprovalDropDownItem[]>([]);
  const [approverCount, setapproverCount] = useState<ApprovalLevelAndCount>();
  const [level, setLevel] = useState<ApprovalLevelAndCount>();

  const getApprovalDetailsData = async () => {
    apiPermissionService.getGroups().then((res) => {
      if (res.status) {
        setApprovalGroups(res.data);
        if (groupLength) {
          groupLength(res.data.length);
        }
      }
    });
    apiAdministrationService.getApproverLevelAndCount().then((res) => {
      if (res.status) {
        setapproverCount(res.data);
        setLevel(res.data);
      }
    });
  };
  useEffect(() => {
    getApprovalDetailsData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const formik = useFormik({
    initialValues: {
      groupId: '',
      approverCount: 1,
      level: 1,
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const selectedGroup = approvalGroups.find((group) => group._id === values.groupId);
      selectedGroup &&
        setApprovalData({
          ...values,
          approvalGroupName: selectedGroup?.groupName,
          groupId: selectedGroup?._id,
        });
      formik.resetForm();
      onClose();
    },
  });
  useEffect(() => {
    if (approvalDetails) {
      approvalDetails?.groupId &&
        formik.setValues({
          groupId: approvalDetails?.groupId,
          approverCount: approvalDetails.approverCount,
          level: approvalDetails.level,
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [approvalDetails]);

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const getapproverCount = () => {
    const totalApproverCount = approverCount?.maxApproverCount ? Number(approverCount?.maxApproverCount) : 1;
    return Array.from({ length: totalApproverCount }, (_, i) => ({ label: i + 1, value: i + 1 }));
  };

  const getapproverLevel = () => {
    const totalApproverLevel = level?.maxApproverLevel ? Number(level?.maxApproverLevel) : 1;
    return Array.from({ length: totalApproverLevel }, (_, i) => ({ label: i + 1, value: i + 1 }));
  };

  const getApprovalGroupOptions = () => {
    const groupNames = approvalGroups?.map((data) => ({ label: data.groupName, value: data._id }));
    const selectedGroupIds = selectedGroupData.map((value) => value.groupId);
    return filterSelectedOptions(groupNames, selectedGroupIds, approvalDetails?.groupId);
  };
  return (
    <DialogBox fullWidth open={open} maxWidth="sm" onClose={onCancel}>
      <FormWrapper
        title={'Add Approval Details'}
        isSubmitting={formik.isSubmitting}
        errors={formWrapperError(formik)}
        submitText={'Save'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        isPopUpView
      >
        <Grid container spacing={1}>
          <Grid container item justifyContent={'center'} spacing={3}>
            <Grid item xs={12}>
              <Select
                value={formik.values.groupId}
                label="Approval Group *"
                placeholder="Select"
                name="groupId"
                handleChange={formik.handleChange}
                error={formik.touched.groupId && formik.errors.groupId}
                options={getApprovalGroupOptions()}
              />
            </Grid>
            <Grid item xs={12}>
              <Select
                value={formik.values.approverCount}
                label="Number of Approvers *"
                placeholder="Select"
                name="approverCount"
                handleChange={formik.handleChange}
                error={formik.touched.approverCount && formik.errors.approverCount}
                options={getapproverCount()}
              />
            </Grid>
            <Grid item xs={12}>
              <Select
                value={formik.values.level}
                label="Approval Level *"
                placeholder="Select"
                name="level"
                handleChange={formik.handleChange}
                error={formik.touched.level && formik.errors.level}
                options={getapproverLevel()}
              />
            </Grid>
          </Grid>
        </Grid>
      </FormWrapper>
    </DialogBox>
  );
};

export default AddApprovalDetailsDialog;
