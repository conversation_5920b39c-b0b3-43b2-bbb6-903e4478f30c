import React from 'react';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblInputLabel from 'sharedComponents/NblFormInputs/NblInputLabel';
import NblInputHelperText from 'sharedComponents/NblFormInputs/NblInputHelperText';

interface NblFieldWrapper {
  label?: string;
  name: string;
  disabled: boolean;
  mandatory: boolean;
  helperText?: string | JSX.Element;
  error: boolean;
  children: React.ReactElement;
  center?: boolean;
  tooltipText?:string;
}

const NblFieldWrapper: React.FC<NblFieldWrapper> = ({ label, name, disabled, mandatory, error, helperText, children, center,tooltipText }) => {
  return (
    <NblFlexContainer direction="column" position="relative" height="auto" {...(center && { justifyContent: 'center' })}>
      {label && <NblInputLabel label={label} name={name} mandatory={mandatory} disabled={disabled}  tooltipText={tooltipText}/>}
      {children}
      {helperText && <NblInputHelperText error={Boolean(error)} helperText={helperText} />}
    </NblFlexContainer>
  );
};

export default NblFieldWrapper;
