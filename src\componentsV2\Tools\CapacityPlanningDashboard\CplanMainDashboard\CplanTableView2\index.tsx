import { Facility } from '../../utils/types';
import { Grid, useTheme } from '@mui/material';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import CplanTable from './CplanTable';
import useNblNavigate from 'hooks/useNblNavigate';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { useSelector, useDispatch } from 'react-redux';
import TableExport from '../../components/TableExport';
import NoData from '../../components/NoData';
import { exportFormats } from '../../utils/constant';
import { CalculateVropsResource } from '../../utils/utilization';
import { setOverviewResource, setSelectedDomainIds } from 'store/reducers/capacityplanning';
// eslint-disable-next-line
import { NebulaTheme } from 'NebulaTheme/type';
import { useApiService } from 'api/ApiService/context';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblDivider from 'sharedComponents/NblDivider';
import CplanDropdown from '../../components/CplanDropdown';
import { CalculatedVropsResource } from '../../utils/types';
import { showSpinner } from 'store/reducers/spinner';
import { NblGridContainer } from 'sharedComponents/NblContainers/NblGridContainer';
import CplanDomainDropdown from '../../components/CplanDomainDropdown';

const CplanTableView2: React.FunctionComponent = () => {
  const theme: NebulaTheme = useTheme();
  const navigate = useNblNavigate();
  const dispatch = useDispatch();
  const { apiCapacityPlanningService } = useApiService();
  const hours = useSelector((state: State) => state.capacityPlanning.hours);
  const overviewResource = useSelector((state: State) => state.capacityPlanning.overviewResource);
  const [calcResources, setCalcResources] = useState<CalculatedVropsResource[] | []>([]);
  const [facilities, setFacilities] = useState([] as Facility[]);
  const [selectedRegion, setSelectedRegion] = useState('ALL');
  const domainIds = useSelector((state: State) => state.capacityPlanning.selectedDomainIds);

  // Don't do any new filter sort logic in below functions directly. Use filteredCalcResources section for that.

  const getCalculatedResources = useCallback(
    async (hours: number) => {
      dispatch(showSpinner({ status: true, message: 'Fetching Vrops resources' }));
      const resources = (await apiCapacityPlanningService.getResources(hours)).data;
      const calculatedResources: CalculatedVropsResource[] = resources.map((res) => CalculateVropsResource(res));
      setCalcResources(calculatedResources);
      if (overviewResource?.resourceid) {
        const hoveredResource = calculatedResources.find((res) => res.resourceid === overviewResource.resourceid);
        dispatch(setOverviewResource(hoveredResource));
      } else {
        const hoveredResource = calculatedResources[calculatedResources.length - 1];
        dispatch(setOverviewResource(hoveredResource));
      }
      dispatch(showSpinner({ status: false, message: '' }));
    },
    [apiCapacityPlanningService, hours]
  );

  useEffect(() => {
    getCalculatedResources(hours);
  }, [hours, getCalculatedResources]);

  const getFacilities = useCallback(async () => {
    const Apiresponse = await apiCapacityPlanningService.getFacilities();
    const response = Apiresponse.data;
    setFacilities(response);
  }, [apiCapacityPlanningService]);

  useEffect(() => {
    getFacilities();
  }, [getFacilities]);

  const getUniqueRegionOptions = useMemo(() => {
    const regions = [...new Set(facilities.map((facility) => facility.region))];
    const regionsOptions = regions.map((region) => {
      return { label: region, value: region };
    });
    return [{ label: 'ALL', value: 'ALL' }, ...regionsOptions];
  }, [facilities]);

  const facilityMap = useMemo(() => {
    if (facilities.length === 0) return new Map();
    const map = new Map();
    facilities?.forEach((fac) => {
      fac.resources &&
        fac.resources.forEach((r) => {
          map.set(r.resourceid, { facilityId: fac.facilityid, facilityName: fac.facilityname });
        });
    });
    return map;
  }, [facilities]);

  const filteredFacilityIdsByRegion = useMemo(() => {
    if (selectedRegion === 'ALL') return null;
    return new Set(facilities.filter((fac) => fac.region === selectedRegion).map((fac) => fac.facilityid));
  }, [selectedRegion, facilities]);

  const calcResourcesWithFac: CalculatedVropsResource[] = useMemo(() => {
    return calcResources.map((res) => {
      const facilityData = facilityMap.get(res.resourceid);
      return facilityData ? { id: res.resourceid, ...res, ...facilityData } : { id: res.resourceid, ...res };
    });
  }, [calcResources, facilityMap]);

  // Don't modify above logics unless its needed

  const regionFilteredResources = useMemo(() => {
    return calcResourcesWithFac.filter((res) => res?.facilityId && filteredFacilityIdsByRegion?.has(res?.facilityId));
  }, [filteredFacilityIdsByRegion]);

  // Add you filter/sort functions above (e.g. regionFilteredResources) and assign return to the result in filteredCalcResources

  const filteredCalcResources = (() => {
    let result: CalculatedVropsResource[] = calcResourcesWithFac;
    result = filteredFacilityIdsByRegion ? regionFilteredResources : calcResourcesWithFac;
    result = result.filter((res) => domainIds.includes(res.domainid));

    // Apply your future filtering sorting logic here to result variable only
    return result;
  })();

  const handleRowClick = (row: any) => {
    navigate(`${row.resourceid}`);
    dispatch(setSelectedDomainIds([row.domainid]));
  };
  const handleRowMousehover = (event: React.MouseEvent<HTMLDivElement>) => {
    const resourceId = event.currentTarget.getAttribute('data-id');
    const hoveredResource = filteredCalcResources.find((res: any) => res.resourceid === resourceId);
    dispatch(setOverviewResource(hoveredResource));
  };

  return (
    <>
      <NblFlexContainer direction="column">
        <NblFlexContainer direction="row" height="auto">
          <CplanDomainDropdown />
          <NblDivider orientation="vertical" color={theme.palette.primary.shade6} length="70%" />
          <CplanDropdown
            label={'Sites'}
            options={getUniqueRegionOptions}
            value={selectedRegion}
            onChange={(region: string) => {
              setSelectedRegion(region);
            }}
          />
          <NblDivider orientation="vertical" color={theme.palette.primary.shade6} length="70%" />
          {calcResources.length !== 0 && (
            <TableExport
              downloadOptions={[exportFormats.Excel, exportFormats.CSV, exportFormats.JPEG, exportFormats.PDF]}
              exportSectionId="exportSection"
            ></TableExport>
          )}
        </NblFlexContainer>
        <NblFlexContainer margin="-3.125rem 0 0 0" height="80vh">
          {filteredCalcResources.length !== 0 ? (
            <Grid id="exportSection" minWidth="100%">
              <CplanTable data={filteredCalcResources} onRowClick={handleRowClick} onRowMouseHover={handleRowMousehover} />
            </Grid>
          ) : (
            <NblGridContainer alignItems="center">
              <NoData />
            </NblGridContainer>
          )}
        </NblFlexContainer>
      </NblFlexContainer>
    </>
  );
};

export default CplanTableView2;
