import { useSelector } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import JiraService from 'api/ApiService/JiraService';
import { renderFullName } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { toast } from 'react-toastify';
// eslint-disable-next-line no-unused-vars
import NblFormContainer, { NblFormHelpers } from 'sharedComponents/NblContainers/NblFormContainer';
import { validationSchema } from 'yupSchema/IntakeRequestFormSchema';
import RequestFeatureForm from './RequestFeatureForm';

export type FormValues = {
  requestingOrganization: string,
  capabilityArea: string,
  priority: string,
  summary: string,
  description: string,
  justification: string,
}

const initialValues: FormValues = {
  requestingOrganization: '',
  capabilityArea: '',
  priority: '',
  summary: '',
  description: '',
  justification: '',
};

const RequestFeature: React.FunctionComponent = () => {
  const jiraService = new JiraService();
  const { userDetails } = useSelector((state: State) => state.user);
  const fullName = renderFullName(userDetails);

  const handleSubmitForm = (values: FormValues, nblFormHelpers: NblFormHelpers<FormValues>) => {
    const payload = {
      userName: fullName,
      userEmail: userDetails.email,
      summary: values.summary,
      description: values.description,
      additionalRequestData: {
        requestingOrganization: values.requestingOrganization,
        capabilityArea: values.capabilityArea,
        priority: values.priority,
        justification: values.justification,
      },
    };
    jiraService
      .requestFeatureSubmitForm(payload)
      .then((res) => {
        if (res.status) {
          toast.success(res.data?.message || 'Request submitted successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        nblFormHelpers.setSubmitting(false);
        nblFormHelpers.resetForm();
      });
  };

  return (
    <NblFormContainer
      formType={'simple'}
      title={'Request a Feature'}
      caption="Request new features or functionality for Nebula"
      formInitialValues={initialValues}
      formValidationSchema={validationSchema}
      steps={[
        {
          caption:'',
          errorFields: [
            'requestingOrganization',
            'capabilityArea',
            'priority',
            'summary',
            'description',
            'justification',
          ],
          icon: '',
          status: 'completed',
          title: '',
        }
      ]}
      onSubmit={handleSubmitForm}
      showCancel={false}
    >
      <RequestFeatureForm />
    </NblFormContainer>
  );
};

export default RequestFeature;
