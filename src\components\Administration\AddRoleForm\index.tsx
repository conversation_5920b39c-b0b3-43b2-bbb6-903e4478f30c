import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid } from '@mui/material';
import { formWrapperError, yupMatchesParams } from 'utils/common';

import withAdminPermissions from 'hoc/withAdminPermissions';
import TextField from 'components/TextField';
import Select from 'components/Select';
import FormWrapper from 'components/FormWrapper';
import icons from 'assets/images/icons';
// eslint-disable-next-line
import { FormProps, AddRolePayload, RoleDataProps, AdminComponent } from 'types';
import { useDispatch } from 'react-redux';
import { SPINNER_IDS, showSpinner } from 'store/reducers/spinner';
import RoleService from 'api/ApiService/RoleService';
import MultiSelect from 'components/MultiSelect';
import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import useNblNavigate from 'hooks/useNblNavigate';

interface AddRoleFormProps extends FormProps {
  editRoleDetails?: RoleDataProps;
}

const validationSchema = yup.object().shape({
  roleName: yup.string().trim().required('Role Name is required'),
  roleType: yup.string().required('Role Type is required'),
  permissionName: yup.array().of(yup.string()).min(1, 'Please select atleast one permission').required('Permision Name is required'),
  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});

const AddRoleForm: AdminComponent<AddRoleFormProps> = ({
  title,
  onClose,
  editRoleDetails,
  permissions: { canUpdate, canCreate },
}: AddRoleFormProps) => {
  const dispatch = useDispatch();
  const navigate = useNblNavigate();
  const [permissionNames, setPermissionNames] = useState<{ permissionKey: string }[]>([{ permissionKey: '' }]);
  const [roleTypes, setRoleTypes] = useState<string[]>([]);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);

  const roleService = new RoleService();
  const fetchPermissions = () => {
    dispatch(showSpinner({ id: SPINNER_IDS.rolePermission, status: true, message: 'Loading role permissions...' }));
    roleService
      .getRolePermissions()
      .then((res) => {
        if (res.status) {
          setPermissionNames(res.data.permissions);
          setRoleTypes(res.data.type);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: SPINNER_IDS.rolePermission, status: false, message: '' }));
      });
  };

  useEffect(() => {
    fetchPermissions();
  }, []);

  useEffect(() => {
    if (editRoleDetails) {
      formik.setValues({
        roleName: editRoleDetails.roleName,
        roleType: editRoleDetails.roleType,
        permissionName: editRoleDetails.permissions.map((permission: any) => permission.permissionKey),
        description: editRoleDetails.description,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editRoleDetails]);

  const addRoleHandler = (payload: AddRolePayload) => {
    setIsFormSubmitting(true);
    roleService
      .addRole(payload)
      .then((res) => {
        if (res.status) {
          toast.success(res.data?.message || 'Request submitted successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
          navigate('/administration/roles/view-roles');
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        setIsFormSubmitting(false);
      });
  };

  const editRoleHandler = (payload: AddRolePayload) => {
    setIsFormSubmitting(true);
    roleService
      .editRoleDetails(payload, editRoleDetails?.shortName || '')
      .then((res) => {
        if (res.status) {
          toast.success(res.data?.message || 'Request submitted successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
          navigate('/administration/roles/view-roles');
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        setIsFormSubmitting(false);
      });
  };
  interface InitialValues {
    roleName: string;
    roleType: string;
    permissionName: string[];
    description: string;
  }
  const formik = useFormik<InitialValues>({
    initialValues: {
      roleName: '',
      roleType: '',
      permissionName: [],
      description: '',
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const payload = {
        roleName: values.roleName,
        description: values.description,
        roleType: values.roleType,
        permissions: values.permissionName.map((permission) => {
          return { permissionKey: permission };
        }),
      };

      editRoleDetails ? editRoleHandler(payload) : addRoleHandler(payload);
    },
  });

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  return (
    <>
      <FormWrapper
        title={title}
        isSubmitting={isFormSubmitting}
        errors={formWrapperError(formik)}
        submitText={'Submit'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        Icon={icons.AddOutlined}
        canCreate={canCreate}
        canUpdate={canUpdate}
      >
        <Grid container spacing={1}>
          <Grid container item justifyContent={'center'} spacing={3}>
            <Grid item xs={6}>
              <TextField
                type="text"
                name="roleName"
                label="Role Name *"
                placeholder="Please Enter"
                value={formik.values.roleName}
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.roleName && formik.errors.roleName}
              />
            </Grid>
            <Grid item xs={6}>
              <Select
                value={formik.values.roleType}
                label="Role Type *"
                placeholder="Select"
                name="roleType"
                handleChange={formik.handleChange}
                error={formik.touched.roleType && formik.errors.roleType}
                options={roleTypes?.map((data) => ({ value: data, label: data }))}
              />
            </Grid>
            <Grid item xs={6}>
              <MultiSelect
                value={formik.values.permissionName}
                label="Permission Name *"
                placeholder="Select"
                name="permissionName"
                handleChange={formik.handleChange}
                error={formik.touched.permissionName && formik.errors.permissionName}
                options={permissionNames?.map((data) => ({ value: data.permissionKey, label: data.permissionKey }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                type="text"
                value={formik.values.description}
                label="Description"
                name="description"
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.description && formik.errors.description}
              />
            </Grid>
          </Grid>
        </Grid>
      </FormWrapper>
    </>
  );
};

AddRoleForm.type = ADMIN_TILE_PERMISSION_TYPE.form;

export default withAdminPermissions(AddRoleForm);
