import AttachFileIcon from '@mui/icons-material/AttachFile';

import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblButton from '../NblButton';
import NblTypography from '../../NblTypography';
import VisuallyHiddenInput from './styled';

interface NblUploadButtonProps {
  buttonID: string;
  handleFileUpload?: (event: React.ChangeEvent<HTMLInputElement>) => void | Promise<void>;
  disabled?: boolean;
  description?: string;
}

const NblUploadButton: React.FunctionComponent<NblUploadButtonProps> = ({ buttonID, handleFileUpload, disabled, description }) => {
  //Jsx
  return (
    <NblFlexContainer direction={'column'} width={'auto'}>
      <NblButton
        component={'label'}
        variant={'contained'}
        buttonID={buttonID}
        disabled={disabled}
        startIcon={<AttachFileIcon sx={{ fontSize: '1.5rem' }} />}
      >
        Choose File
        <VisuallyHiddenInput type="file" onChange={handleFileUpload} />
      </NblButton>
      {description && (
        <NblTypography variant="caption" color={'shade1'} weight={'regular'}>
          {description}
        </NblTypography>
      )}
    </NblFlexContainer>
  );
};

export default NblUploadButton;
