import { useEffect, useState } from 'react';
import React from 'react';
import MaskedValueCell from 'componentsV2/MaskedValueCell';
// eslint-disable-next-line no-unused-vars
import { LookupPayload } from 'types/Interfaces/LookUpVCenterResponse';

export type VCenterField = {
  title: string;
  value: string | React.ReactElement;
  span?: number;
};

const useVCenterFields = (dataFromLookup: LookupPayload, domainData: any[]): VCenterField[] => {
  const [fields, setFields] = useState<VCenterField[]>([]);

  useEffect(() => {
    if (!dataFromLookup) return;

    const domain = dataFromLookup.domain || '-';
    const result: VCenterField[] = [
      { title: 'Domain', value: domain || '-' },
      { title: 'Cloud Datacenter', value: dataFromLookup?.cloudDatacenter || '-' },
      { title: 'VCenter Name', value: dataFromLookup?.vCenterName || '-' },
      { title: 'VCenter Host', value: dataFromLookup?.vCenterHost || '-' },
      { title: 'VCenter Port', value: dataFromLookup?.vCenterPort || '-' },
      { title: 'VCenter Protocol', value: dataFromLookup?.vCenterProtocol || '-' },
      { title: 'VCenter User', value: dataFromLookup?.vCenterUser || '-' },
      {
        title: 'VCenter Password',
        value: <MaskedValueCell value={dataFromLookup?.vCenterPassword || '-'} />,
      },
    ];

    setFields(result);
  }, [dataFromLookup, domainData]);

  return fields;
};

export default useVCenterFields;
