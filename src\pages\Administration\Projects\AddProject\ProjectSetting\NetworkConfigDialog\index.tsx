import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid } from '@mui/material';
import { formWrapperError, yupMatchesParams } from 'utils/common';
import Select from 'components/Select';
import TextField from 'components/TextField';
import FormWrapper from 'components/FormWrapper';
import MultiSelect from 'components/MultiSelect';

// eslint-disable-next-line no-unused-vars
import { ProjectNetworkSettings, ProjectNetworkOptions, AddProjectData } from 'types';
import ProjectCreationService from 'api/ApiService/ProjectCreationService';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { setProjectNetworkDetails } from 'store/reducers/projectNetworks';
import DialogBox from 'components/DialogBox/Dialog';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';

interface NetworkConfigProps {
  onClose: () => void;
  onSuccess?: () => void;
  setNetworkData: (values: ProjectNetworkSettings) => void;
  openNetworkDialog: boolean;
  dataCenter: { id: string; name: string }[];
  projectData?: AddProjectData[];
}

const validationSchema = yup.object().shape({
  dataCenter: yup.string().required('Data Center is required'),
  value: yup.array().of(yup.string()).min(1, 'Please select atleast one Network').required('Network is required'),
  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});

const NetworkConfigDialog: React.FunctionComponent<NetworkConfigProps> = ({
  onClose,
  setNetworkData,
  openNetworkDialog,
  dataCenter,
  projectData,
}: NetworkConfigProps) => {
  const [network, setNetwork] = useState<ProjectNetworkOptions[]>([]);
  const [selectedDataCenterLabel, setSelectedDataCenterLabel] = useState<string[]>([]);
  const [dataCenterObject, setDataCenterObject] = useState<{ id: string; name: string }[]>([]);
  const [editProjectDataCenter, setEditProjectDataCenter] = useState<boolean>(false);
  const [projectDataList, setProjectDataList] = useState<AddProjectData[]>();

  const apiProjectCreationService = new ProjectCreationService();

  const { projectValues } = useSelector((state: State) => state.projectNetworks);
  const { projectDataCenterValues } = useSelector((state: State) => state.projectNetworks);

  useEffect(() => {
    if (projectValues?.settingValue) {
      let editDataCenterValue = projectValues.settingValue.toString().split(':')[1];
      if (editDataCenterValue && dataCenter.map((data) => data.name === editDataCenterValue.trim())) {
        setEditProjectDataCenter(true);
      }
    }
  }, [projectValues]);

  useEffect(() => {
    if (projectDataCenterValues?.dataCenterName && projectDataCenterValues?.dataCenterName?.trim() === selectedDataCenterLabel[0]?.trim()) {
      setSelectedDataCenterLabel([]);
    }
  }, [projectDataCenterValues]);

  const dispatch = useDispatch();

  useEffect(() => {
    setDataCenterObject([...dataCenter]);
  }, [dataCenter]);

  useEffect(() => {
    setProjectDataList(projectData);
  }, [projectData]);

  const fetchProjectNetwork = (dataCenterValue: string) => {
    dispatch(showSpinner({ id: SPINNER_IDS.groupPermissions, status: true, message: 'Loading Network(VLAN)...' }));
    apiProjectCreationService
      .getProjectNetworkData(dataCenterValue)
      .then((res) => {
        if (res.status) {
          setNetwork(res.data);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: SPINNER_IDS.groupPermissions, status: false, message: '' }));
      });
  };

  const initialValues: ProjectNetworkSettings = {
    name: 'Network',
    value: [],
    dataCenter: '',
    settingName: '',
    description: '',
    id: '',
  };

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const selectedDatacenterId = formik.values.dataCenter;
      const selectedDataCenterLabel = dataCenter.find((item) => {
        if (item.id === selectedDatacenterId) return item;
      });

      const newValue = network.filter((networkVal) => values.value.includes(networkVal.id));

      const projectSetting: ProjectNetworkSettings = {
        name: '',
        id: '',
        dataCenter: '',
        dataCenterLabel: selectedDataCenterLabel?.name,
        value: [],
        networkValue: newValue,
        settingName: '',
        description: '',
      };

      {
        projectValues?.id ? (projectSetting.id = projectValues?.id) : (projectSetting.id = (Math.random() * 100).toString());
      }
      projectSetting.dataCenter = values.dataCenter;
      projectSetting.dataCenterLabel = selectedDataCenterLabel?.name;
      projectSetting.settingName = values.name.toString();
      projectSetting.value = values.value;
      projectSetting.description = values.description;
      setNetworkData(projectSetting);
      dispatch(
        setProjectNetworkDetails({
          id: '',
          settingName: '',
          settingValue: [],
        })
      );
      setEditProjectDataCenter(false);
      formik.resetForm();
      selectedDataCenterLabel?.name && setSelectedDataCenterLabel((prevValue) => [...prevValue, selectedDataCenterLabel.name]);
    },
  });

  useEffect(() => {
    if (projectValues.settingName === 'Network') {
      const editSettingValue = projectValues.settingValue.toString().split(':');
      const editDataCenterLabel = editSettingValue[1];
      const selectedDataCenterId = dataCenter.find((item) => {
        if (item.name === editDataCenterLabel.trim()) return item;
      });
      {
        selectedDataCenterId &&
          formik.setValues({
            name: 'Network',
            settingName: projectValues.settingName,
            //@ts-ignore
            value: projectValues.networkValue.map((val) => val.id),
            dataCenter: selectedDataCenterId.id,
            id: projectValues.id.toString(),
            description: projectValues.description,
          });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectValues, network]);
  useEffect(() => {
    const selectedDataCenter = dataCenter.find((dc) => dc.id === formik.values.dataCenter);
    formik.values.dataCenter.length !== 0 && selectedDataCenter && fetchProjectNetwork(selectedDataCenter.name);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.values.dataCenter]);

  const onCancel = () => {
    dispatch(
      setProjectNetworkDetails({
        id: '',
        settingName: '',
        settingValue: [],
      })
    );
    setEditProjectDataCenter(false);
    formik.resetForm();
    onClose();
  };
  const findSelectedDataCenterObject = (name: string) => {
    if (name && name.length > 0) {
      return dataCenterObject.filter((data) => data.name !== name);
    } else return dataCenter;
  };

  useEffect(() => {
    let newDataCenterObject = findSelectedDataCenterObject(selectedDataCenterLabel[0]);
    setDataCenterObject(newDataCenterObject);
  }, [selectedDataCenterLabel]);

  const filterOptionsData = () => {
    const selectedDataCenterIds = new Set(projectDataList?.map((item) => item.dataCenter));
    const availableDataCenters = dataCenterObject.filter((item) => !selectedDataCenterIds.has(item.id));
    return availableDataCenters.map((item) => ({
      value: item.id,
      label: item.name,
    }));
  };

  return (
    <>
      <DialogBox fullWidth open={openNetworkDialog} maxWidth="sm" onClose={onCancel}>
        <FormWrapper
          title={'Network Setting Config'}
          isSubmitting={formik.isSubmitting}
          errors={formWrapperError(formik)}
          submitText={'Save'}
          onCancel={onCancel}
          onSubmit={formik.handleSubmit}
          isPopUpView
        >
          <Grid container spacing={1}>
            <Grid container item justifyContent={'center'} spacing={3}>
              <Grid item xs={12}>
                <Select
                  value={formik.values.dataCenter}
                  label="Data Center *"
                  placeholder="Select"
                  name="dataCenter"
                  handleChange={formik.handleChange}
                  error={formik.touched.dataCenter && formik.errors.dataCenter}
                  options={editProjectDataCenter ? dataCenter?.map((data) => ({ value: data.id, label: data.name })) : filterOptionsData()}
                />
              </Grid>
              <Grid item xs={12}>
                <MultiSelect
                  value={formik.values.value}
                  label="Network(VLAN) *"
                  placeholder="Select"
                  name="value"
                  handleChange={formik.handleChange}
                  disabled={!formik.values.dataCenter}
                  error={formik.touched.value && formik.errors.value}
                  options={network?.map((data) => ({ value: data.id, label: data.name }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  type="text"
                  value={formik.values.description}
                  label="Description"
                  name="description"
                  handleChange={formik.handleChange}
                  handleBlur={formik.handleBlur}
                  error={formik.touched.description && formik.errors.description}
                />
              </Grid>
            </Grid>
          </Grid>
        </FormWrapper>
      </DialogBox>
    </>
  );
};

export default NetworkConfigDialog;
