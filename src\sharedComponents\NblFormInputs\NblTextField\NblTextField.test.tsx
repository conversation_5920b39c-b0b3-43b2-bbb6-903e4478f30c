import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblTextField from '.';

describe('NblTextField component', () => {
  const props = {
    label: 'Label',
    name: 'Name',
    disabled: false,
    helperText: 'helper text',
    value: '',
    placeholder: '',
    error: false,
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblTextField {...props} type={'text'} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
