//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblChipComponent from 'sharedComponents/NblFormInputs/NblChipComponent';

type StoryProps = ComponentProps<typeof NblChipComponent>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblChipComponent',
  component: NblChipComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { control: 'text' },
    name: { control: 'text' },
    placeholder: { control: 'text' },
    readOnly: { control: 'boolean' },
    disabled: { control: 'boolean' },
    mandatory: { control: 'boolean' },
    errorMessage: { control: 'object' },
  },
};

export default meta;

export const Default: Story = {
  args: {
    label: 'Label',
    name: 'name',
    placeholder: 'Type and press Enter to add...',
    value: [],
    options: [],
    readOnly: false,
    disabled: false,
    mandatory: false,
    errorMessage: [],
  },
  render: (args) => {
    const WrapperComponent = () => {
      const [value, setValue] = useState<string[]>([]);

      return (
        <NebulaTheme>
          <NblFlexContainer width="300px">
            <NblChipComponent
              {...args}
              handleChange={(_, newValue) => setValue(newValue)}
              value={value}
              errorMessage={[
                { error: true, message: 'Some error occurred' },
                { error: false, message: 'Success variant message' },
              ]}
            />
          </NblFlexContainer>
        </NebulaTheme>
      );
    };

    return <WrapperComponent />;
  },
};
