import { act, render } from '@testing-library/react';

import { MemoryRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblRequestDetailsContainer from '.';
import { RequestStatus } from 'types/Enums';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    expandedAccordions: []
  },
});

describe('NblRequestDetailsContainer component', () => {
  const props = {
    title: 'RequestDetails',
    componentName: 'ReserveIPBlockForm',
    formName: 'Reserve IP Address Block Details',
    data: {
      requestId: 'IaaS-NEB-REQ-23456',
      catalogItem: 'IaaS-Naas-Create-IP-Pool',
      createdBy: 'P3236788',
      startDate: '2025-01-14T09:23:19.498Z',
      completedAt: '2025-01-14T09:30:52.314Z',
      status: 'failed',
      approvalStatus: 'approved',
      resourceId: 'NEB-RES-VM-2343'
    },
    requestMetaData: {
      id: '67a9e9fc8167e5ee7ac3fae1',
      serviceRequestId: 'IaaS-NEB-REQ-23456',
      status: RequestStatus.PENDING_APPROVAL,
      requesterEmail: '<EMAIL>',
      canApprove: true,
    },
    requestPayload: {
      network: '**********/20',
      cidr: 27,
      ipAddrType: 'IPV4',
      comment: 'Testing',
      datacenter: 'Irvine - IRVOCAFL',
      description: 'Testing create IP pool address',
      company: 'LCHARTER',
      environment: 'RDC',
      addrReachability: 'Private',
      ipAllocType: 'Management',
      cidrBlock: '27',
    },
    approvalDetails: [
      {
        id: 1,
        level: 1,
        approvalGroup: 'AP-Nebula-APS-ReliabilityEng-Catalog-Admin',
        approvedOrRejectedBy: 'Sec-nebula-test-admn',
        approvedOrRejectedAt: '06/01/2024 08:24 PM',
        approvalStatus: 'approved',
        comments: 'Placeholder',
      },
    ],
    activityLogs: [],
    serviceRequestType: undefined,
  };
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <Router>
            <ReduxProvider store={store}>
              <NebulaThemeProvider>
                <NblRequestDetailsContainer ApprovalConfirmButton={jest.fn()} {...props} />
              </NebulaThemeProvider>
            </ReduxProvider>
          </Router>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
