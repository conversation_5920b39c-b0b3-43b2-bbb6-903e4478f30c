import DownStreamError from './DownStreamError';
import IPV4DownstreamResponseData from './IPV4DownstreamResponseData';
import IPV6DownstreamResponseData from './IPV6DownstreamResponseData';

type ReserveIPAddress = {
  reserveIps: {
    id: number;
    comment: string;
    hostName: string;
    ipAddress: string;
    networkBlock: string;
  }[];
  requestId: string;
  status: string;
  downstreamError?: DownStreamError[];
  downstreamResponseData: (IPV4DownstreamResponseData & IPV6DownstreamResponseData)[];
};

export default ReserveIPAddress;
