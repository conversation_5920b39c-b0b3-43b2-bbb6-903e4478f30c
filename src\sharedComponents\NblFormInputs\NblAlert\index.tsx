import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import { StyledAlert, StyledAlertTitle } from './styled';

type Props = {
  variant: 'warning' | 'success' | 'info' | 'error';
  title: string;
  message: string;
  center?: boolean;
  fullWidth?: boolean;
};

export default function NblAlert({ variant = 'warning', title, message, center = false, fullWidth }: Props) {
  return (
    <NblFlexContainer width={fullWidth ? '100%' : 'auto'} center={center}>
      <StyledAlert severity={variant} fullWidth={fullWidth}>
        {title && <StyledAlertTitle titleVariant={variant}>{title}:</StyledAlertTitle>}
        <NblTypography variant={'body3'}>{message}</NblTypography>
      </StyledAlert>
    </NblFlexContainer>
  );
}
