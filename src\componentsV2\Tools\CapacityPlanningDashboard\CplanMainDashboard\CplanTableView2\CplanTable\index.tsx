import React, { useMemo, useState } from 'react';
import { NblTable } from 'sharedComponents/NblTable';
import { Box } from '@mui/material';
import ColorIndicatorIcon from '../../../components/ColorIndicationIcon';
import { getUsageColor } from '../../../utils/colors';
import RowUtilization from './RowUtilization';
import NblTypography from 'sharedComponents/NblTypography';
import { CalculatedVropsResource } from 'componentsV2/Tools/CapacityPlanningDashboard/utils/types';
import { GridFilterModel } from '@mui/x-data-grid';

interface TableBodyProps {
  data: CalculatedVropsResource[];
  onRowClick?: (row: any) => void;
  onRowMouseHover?: (event: React.MouseEvent<HTMLDivElement>) => void;
  rowSize?: string;
  pageSizeOptions?: string[];
  label?: string;
}
const CplanTable: React.FunctionComponent<TableBodyProps> = ({
  data,
  onRowClick,
  onRowMouseHover,
  rowSize = '10',
  pageSizeOptions = ['5', '10', '20', '40', '60'],
  label,
}) => {
  const [filteringModel, setFilteringModel] = useState<GridFilterModel>({ items: [] });

  const filteredRows = useMemo(() => {
    if (filteringModel.items.length === 0) return data;
    return data.filter((row: any) =>
      filteringModel.items.every((filter) => {
        let cellValue = row[filter.field];

        if (filter.field === 'resourcelabel') {
          cellValue = row.resourcelabel || row.resourcename;
        }
        return cellValue?.toString().toLowerCase().includes(filter.value.toLowerCase());
      })
    );
  }, [data, filteringModel]);

  const columns = [
    {
      field: 'resourcelabel',
      headerName: label ?? 'Vcenter',
      flex: 2,
      valueGetter: (params: any) => params.row.resourcelabel || params.row.resourcename,
      renderCell: (params: any) => (
        <Box display="flex" flexDirection="column" height="100%" justifyContent="center" overflow="hidden">
          <NblTypography variant="body1" color="shade6" textAlign="left">
            {params.row.facilityName || '\u00A0'}
          </NblTypography>
          <NblTypography variant="subtitle1" color="shade1" textAlign="left" showEllipsis={true}>
            {params.row.resourcelabel || params.row.resourcename}
          </NblTypography>
        </Box>
      ),
    },

    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      renderCell: (params: any) => {
        const maxUtilization = Math.max(params.row.cpuUtilized, params.row.memoryUtilized, params.row.storageUtilized);
        const color = getUsageColor(maxUtilization);
        return (
          <Box display="flex" flexDirection="row" alignItems="center" height="100%" justifyContent="center">
            <ColorIndicatorIcon color={color} />
            <NblTypography variant="body1" color="shade1">
              {params.row.status}
            </NblTypography>
          </Box>
        );
      },
    },
    {
      field: 'cpuUtilized',
      headerName: 'CPU',
      flex: 1,
      renderHeader: () => (
        <Box display="flex" flexDirection="column" height="100%" justifyContent="center">
          <NblTypography variant="h5" color="shade1" textAlign="center">
            CPU
          </NblTypography>
          <NblTypography variant="body1" color="shade6" textAlign="left">
            Utilization%
          </NblTypography>
        </Box>
      ),
      renderCell: (params: any) => {
        return <RowUtilization params={params} utilization="cpu" />;
      },
    },
    {
      field: 'memoryUtilized',
      headerName: 'Memory',
      flex: 1,
      renderHeader: () => (
        <Box display="flex" flexDirection="column" height="100%" justifyContent="center">
          <NblTypography variant="h5" color="shade1" textAlign="center">
            Memory
          </NblTypography>
          <NblTypography variant="body1" color="shade6" textAlign="left">
            Utilization%
          </NblTypography>
        </Box>
      ),
      renderCell: (params: any) => {
        return <RowUtilization params={params} utilization="memory" />;
      },
    },
    {
      field: 'storageUtilized',
      headerName: 'Storage',
      flex: 1,
      renderHeader: () => (
        <Box display="flex" flexDirection="column" height="100%" justifyContent="center">
          <NblTypography variant="h5" color="shade1" textAlign="center">
            Storage
          </NblTypography>
          <NblTypography variant="body1" color="shade6" textAlign="left">
            Utilization%
          </NblTypography>
        </Box>
      ),
      renderCell: (params: any) => {
        return <RowUtilization params={params} utilization="storage" />;
      },
    },
  ];

  return (
    <>
      <NblTable
        columns={columns}
        rows={filteredRows}
        handleRequestRowClick={onRowClick}
        rowSize={rowSize}
        pageSizeOptions={pageSizeOptions}
        onMouseEnter={onRowMouseHover}
        onFilterModelChange={(model) => setFilteringModel(model)}
      />
    </>
  );
};

export default CplanTable;
