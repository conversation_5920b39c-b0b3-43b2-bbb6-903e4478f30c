import { render, act } from '@testing-library/react';
import DatePickerField from '.';
import ThemeProvider from 'mock/ThemeProvider';
import { BrowserRouter } from 'react-router-dom';

describe('DatePickerField component', () => {
  const datePicketProps = {
    label: 'Business Request Date',
    name: 'businessRequestDate',
    value: null,
    error: '',
    disabled: false,
    handleChange: jest.fn(),
  };

  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <ThemeProvider>
            <DatePickerField {...datePicketProps} />
          </ThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });

  test('Should render the date picker field', async () => {
    const { getByText } = render(
      <BrowserRouter>
        <ThemeProvider>
          <DatePickerField {...datePicketProps} />
        </ThemeProvider>
      </BrowserRouter>
    );
    expect(getByText('Business Request Date')).toBeInTheDocument();
  });
});
