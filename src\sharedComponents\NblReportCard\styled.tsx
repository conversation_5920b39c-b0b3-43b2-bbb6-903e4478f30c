import { styled } from '@mui/system';
import PlayArrowRoundedIcon from '@mui/icons-material/PlayArrowRounded';

import { NblReportCardProps } from '.';

type StyledArrowIcon = {
  arrowColor: string;
  direction: NblReportCardProps['chip']['arrow'];
};

export const StyledArrowIcon = styled(PlayArrowRoundedIcon)<StyledArrowIcon>(({ arrowColor, direction }) => {
  return {
    color: arrowColor,
    transform: direction === 'up' ? 'rotate(-90deg)' : 'rotate(90deg)',
  };
});
