import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblSelect from '.';

describe('NblSelect component', () => {
  const props = {
    label: 'Label',
    name: 'name',
    placeholder: 'Select options',
    options: [
      { label: '<PERSON>', value: 'opt1' },
      { label: '<PERSON>', value: 'opt2' },
      { label: '<PERSON>', value: 'opt3' },
      { label: 'Jack', value: 'opt4' },
      { label: 'Tom', value: 'opt5' },
      { label: '<PERSON>', value: 'opt6' },
    ],
    helperText: 'helper text',
    error: false,
    disabled: false,
    maxLength: 10,
    mandatory: false,
    contained: false,
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblSelect {...props} handleChange={() => jest.fn()} value={'opt1'} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
