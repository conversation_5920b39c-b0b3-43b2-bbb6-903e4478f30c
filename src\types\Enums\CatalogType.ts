enum CatalogType {
  NA = 'NA',
  VM = 'IaaS-Compute-Virtual-Server',
  MONGODB = 'PaaS-DbaaS-Create-MongoDB',
  POSTGRESDB = 'PaaS-DbaaS-Create-PostgresDB',
  REDISDB = 'PaaS-DbaaS-Create-RedisDB',
  DAP = 'IaaS-NaaS-Dynamic-Access-Policies',
  FIREWALL_V2 = 'IaaS-NaaS-Firewall-v2',
  ONBOARD = 'Onboard-SysAcc-AWS-Acc-IAM',
  CREATE_LB_F5 = 'IaaS-NaaS-LB-F5',
  INTERNAL_CERTIFICATE = 'Iaas-Naas-Internal-Certificate',
  ZERO_TOUCH_PROVISIONING = 'IaaS-NaaS-ZTP-Create',
}

export default CatalogType;
