import { useParams } from 'react-router-dom';
import RequestViewDetails from 'componentsV2/RequestsDetail';
import { useApiService } from 'api/ApiService/context';
import { useEffect, useState } from 'react';
import { ApprovalData } from 'api/ApiService/type';

const RequestDetails = () => {
  const [approvalDetailsData, setApprovalDetailsData] = useState<ApprovalData[]>([]);
  const apiAssetService = useApiService();
  const [approvalDialogData, setApprovalDialogData] = useState('NA');

  const { serviceRequestId } = useParams<{ serviceRequestId: string }>();
  let parsedFilterObj = {
    serviceRequestId: {
      contains: serviceRequestId,
    },
  };

  let filter = JSON.stringify(parsedFilterObj);

  useEffect(() => {
    apiAssetService.apiAssetService.getMyRequestsv3(1, 1, '', filter).then((res: any) => {
      if (res.status && res.data.items?.length) {
        if (res.data.items[0].multiLevelApprovals) {
          setApprovalDetailsData(
            res.data.items[0].multiLevelApprovals?.map((asset: ApprovalData) => ({
              id: serviceRequestId ?? '',
              level: asset?.level ?? '',
              approvalGroup: asset?.approvalGroup ?? '',
              approvedOrRejectedBy: asset?.approvedOrRejectedBy ?? '',
              approvedOrRejectedAt: asset?.approvedOrRejectedAt ?? '',
              approvalStatus: asset?.approvalStatus ?? '',
              comments: asset?.rejectedReason ?? '',
            }))
          );
        }
        setApprovalDialogData(res.data.items[0].metadata?.serviceCatalog?.catalogName ?? 'NA');
      }
    });
  }, []);

  return (
    <RequestViewDetails
      approvalDialogData={approvalDialogData}
      approvalDetailsData={approvalDetailsData}
      requestType="REQUESTOR"
      requestId={serviceRequestId as string}
      serviceRequestId={serviceRequestId as string}
    />
  );
};

export default RequestDetails;
