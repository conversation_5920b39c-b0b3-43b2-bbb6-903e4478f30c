import { Typography } from '@mui/material';
import React from 'react';

import ItemsList from '../ItemsList';
//eslint-disable-next-line no-unused-vars
import { Bar2Props } from '../interface';
import { StyledBar, StyledBarTitle, StyledItemListContainer } from './styled';

const Bar2: React.FC<Bar2Props> = ({
  loading,
  level,
  itemsList,
  onLevel2Click,
  selectedLevel1,
  selectedLevel2,
  sidebarExpanded,
  bar2ItemsPosition,
}) => {
  //Renders
  function render() {
    if (loading || itemsList?.[0]?.name) {
      return (
        <ItemsList
          loading={loading}
          itemsList={itemsList}
          level={level}
          onLevel2Click={onLevel2Click}
          selectedLevel2={selectedLevel2}
          sidebarExpanded={sidebarExpanded}
        />
      );
    } else {
      return <Typography variant="h5">No items found</Typography>;
    }
  }

  //JSX
  if (level === 2 && !selectedLevel1?.name) {
    return null;
  }
  return (
    <StyledBar expanded={sidebarExpanded} className={'bar2'}>
      <StyledBarTitle variant="h3">{selectedLevel1.label}</StyledBarTitle>
      <StyledItemListContainer bar={2} listStartPosition={bar2ItemsPosition}>
        {render()}
      </StyledItemListContainer>
    </StyledBar>
  );
};
export default Bar2;
