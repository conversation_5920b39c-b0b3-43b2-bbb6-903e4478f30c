import { useState, useEffect, Suspense, lazy, ComponentType } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import useNblNavigate from 'hooks/useNblNavigate';

import CatalogService from 'api/ApiService/CatalogService';
import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import PermissionWrapper from 'components/PermissionWrapper';
import FallbackComponent from 'components/Error/FallbackComponent';
import withAuthorization from 'hoc/withAuthorization';
import FormDetails from '../FormDetails';
import { setCatalogItemDetails } from 'store/reducers/catalogs';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { CatalogLevel04Data } from 'types';

interface FormsProps {
  catalogItem: string;
  catalogLevel1: string;
  catalogLevel2: string;
  catalogLevel3: string;
}

const CatalogFormFolders = ['IaaS', 'PaaS', 'Tools', 'OnBoarding', 'PaaSv2', 'SpecFlow'];

const Forms: React.FunctionComponent<FormsProps> = ({ catalogItem, catalogLevel1, catalogLevel2, catalogLevel3 }: FormsProps) => {
  const [searchParams] = useSearchParams();
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const dispatch = useDispatch();
  const { catalogItemDetails } = useSelector((state: State) => state.catalogs);

  const apiCatalogService = new CatalogService();
  const [formDetails, setFormDetails] = useState<CatalogLevel04Data | null>(null);
  const [componentDetails, setComponentDetails] = useState<{
    isLoaded: boolean;
    Component: ComponentType<any> | null;
    error: string;
  } | null>(null);

  const requestId = searchParams.get('requestID');

  const loadComponent = async (componentName: string) => {
    for (const folder of CatalogFormFolders) {
      try {
        const module = await import(`componentsV2/${folder}/${componentName}`);
        if (module?.default) {
          return { default: module.default };
        } else {
          console.log(`Failed to load component "${componentName}" inside ${folder} but no default export`);
        }
      } catch {
        console.log(`Failed to load component "${componentName}" inside ${folder}`);
      }
    }
    const componentNotFound = `Failed to load component "${componentName}" in any of the specified folder`;
    console.log(componentNotFound);
    throw new Error(componentNotFound);
  };

  const fetchCatalogItemDetails = () => {
    if (catalogItemDetails?.catalogItem) {
      setFormDetails(catalogItemDetails.catalogItem);
    } else {
      catalogItem &&
        apiCatalogService.getCatalogLevel04Details(catalogItem).then((res) => {
          if (res.status) {
            setFormDetails(res.data);
            dispatch(
              setCatalogItemDetails({
                [catalogItem]: res.data,
              })
            );
          }
        });
    }
  };

  useEffect(() => {
    if (catalogItem) {
      fetchCatalogItemDetails();
    }
  }, [catalogItem]);

  useEffect(() => {
    if (formDetails) {
      loadComponent(formDetails.component)
        .then((module) => {
          setComponentDetails({
            isLoaded: true,
            Component: lazy(() => Promise.resolve(module)),
            error: '',
          });
        })
        .catch(() => {
          setComponentDetails({
            isLoaded: true,
            Component: null,
            error: 'Failed to load component, Please contact nebula admin',
          });
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formDetails]);

  const navigateHandler = () => {
    navigate(`/${catalogLevel1}/${catalogLevel2}/${catalogLevel3}`);
  };

  const renderForm = () => {
    const commonProps = {
      formDetails,
      onSuccess: navigateHandler,
      onClose: navigateHandler,
    };

    if (componentDetails?.isLoaded && componentDetails.error) {
      return <FallbackComponent message={componentDetails.error} maxWidth={300} />;
    }

    if (componentDetails?.isLoaded && !componentDetails.Component) {
      return <FallbackComponent message={'Component not found'} maxWidth={300} />;
    }

    const Component = componentDetails?.Component;

    return (
      <Suspense fallback={'Loading...'}>
        {Component && (
          <PermissionWrapper
            canCreate={Boolean(!requestId)}
            canRead={Boolean(requestId)}
            permissionsPath={[`'${catalogLevel1}'`, `'${catalogLevel3}'`, `'${catalogItem}'`]}
          >
            <FormDetails requestId={requestId || ''}>
              <Component {...commonProps} />
            </FormDetails>
          </PermissionWrapper>
        )}
      </Suspense>
    );
  };

  return renderForm();
};

export default withAuthorization(Forms);
