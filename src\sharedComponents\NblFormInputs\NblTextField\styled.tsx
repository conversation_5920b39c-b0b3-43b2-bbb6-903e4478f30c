import { styled } from '@mui/system';
import { OutlinedInput } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { getOutlinedBaseStyles, getOutlinedInputStyles, getScrollbarStyles } from '../common';

export const StyledOutlinedInput = styled(OutlinedInput)<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    '&.MuiOutlinedInput-root': {
      ...getOutlinedBaseStyles(theme),
      ...getOutlinedInputStyles(theme),
      '& textarea': {
        ...getScrollbarStyles(theme),
      },
    },

    '&.MuiInputBase-root :hover input::placeholder, &.MuiInputBase-root :hover textarea::placeholder': {
      opacity: 0.3,
    },
  };
});
