import { styled } from '@mui/material/styles';
import { Checkbox as MuiCheck<PERSON>, FormControlLabel } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledFormControlLabel = styled(FormControlLabel)<{ theme?: NebulaTheme }>(({ theme, disabled }) => {
  const { typography, palette } = theme;
  const { checkBox } = palette;
  return {
    '&.MuiFormControlLabel-root': {
      color: checkBox.color,
      display: 'block',
      cursor: disabled ? 'not-allowed' : 'pointer',
    },
    '& .MuiTypography-root': {
      ...typography.subtitle1,
    },
  };
});

export const StyledCheckbox = styled(MuiCheckbox)<{ theme?: NebulaTheme; checked: boolean }>(({ theme, checked, color }) => {
  const { palette } = theme;
  const { checkBox } = palette;
  return {
    '&.MuiCheckbox-colorPrimary': {
      color: checked ? checkBox.selectColor : checkBox.border,
    },

    '&.MuiCheckbox-colorError': {
      color: checkBox.errorColor,
    },
    '&.Mui-disabled': {
      color: `${checkBox.disabled} !important`,
    },
    '&:hover': {
      color: color === 'error' ? checkBox.hoverErrorColor : checkBox.hoverSelectColor,
    },
  };
});
