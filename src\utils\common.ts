import format from 'date-fns/format';
import parseISO from 'date-fns/parseISO';
import { toast } from 'react-toastify';
import get from 'lodash/get';

import icons from 'assets/images/icons';
import { ApprovalStatus, CatalogPermissions, RequestStatus } from 'types/Enums';
// eslint-disable-next-line
import { CatalogItem, MultiENVEnvironment } from 'types';
import { store } from 'store/index';
import CryptoJS from 'crypto-js';
import { ResourceStatus } from 'types/Enums/ResourceStatus';
// eslint-disable-next-line
import { NblCardChipProps } from 'sharedComponents/NblChip';
// eslint-disable-next-line
import { F5Datacenter, MultiENVProject } from 'types';
import { UNIT_REGEX, URLEncryptionSecret } from './constant';
// eslint-disable-next-line no-unused-vars
import { Catalog } from 'types/Interfaces/CatalogTable';
import { SettingTypes } from 'types/Enums';
import { ToolStatus } from 'componentsV2/IaaS/EditVMWare/EditVMWareTools';

export const isDevMode = () => {
  return !process.env.NODE_ENV || process.env.NODE_ENV === 'development';
};

export const dateFormatter = (isoDate: string) => {
  try {
    return format(parseISO(isoDate), 'MM/dd/yyyy hh:mm a'); // 11/21/2023 08:38 PM
  } catch {
    console.log('Invalid date format');
    return '';
  }
};

// @ts-ignore
export const formWrapperError = (formik) => {
  return Object.keys(formik.errors).some((errorKey) => formik.touched[errorKey] && formik.errors[errorKey]);
};

// @ts-ignore
export const hasFormikErrors = (formik) => {
  // @ts-ignore
  const recursiveCheck = (obj, touched, errors) => {
    if (!obj || !touched || !errors) {
      return false;
    }

    for (const key in obj) {
      if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        if (recursiveCheck(obj[key], touched[key], errors[key])) {
          return true;
        }
      } else if (Array.isArray(obj[key])) {
        for (let i = 0; i < obj[key].length; i++) {
          if (recursiveCheck(obj[key], touched[key], errors[key])) {
            return true;
          }
        }
      } else if (touched[key] && errors[key]) {
        return true;
      }
    }
    return false;
  };

  return recursiveCheck(formik.values, formik.touched, formik.errors);
};

export const removeEmptyValues = (payload: any) => {
  let payloadObj = {};
  Object.keys(payload).map((payloadKey) => {
    if (typeof payload[payloadKey] !== 'string' || payload[payloadKey]) {
      payloadObj = {
        ...payloadObj,
        [payloadKey]: payload[payloadKey],
      };
    }
  });
  return payloadObj;
};

// @ts-ignore
export const renderApiError = (errorObj) => {
  let errorMessage = 'Something went wrong !';
  if (errorObj?.response?.data) {
    const { error, message } = errorObj.response.data;
    if (typeof message === 'string') {
      errorMessage = message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
  }
  toast.error(errorMessage, {
    position: toast.POSITION.BOTTOM_CENTER,
  });
};

export const downloadFile = (data: Blob, fileName: string) => {
  const url = window.URL.createObjectURL(new Blob([data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(url);
  link.remove();
};

export const checkPermissions = (permission: CatalogPermissions, permissionPath?: string[] | string): boolean => {
  const state = store.getState();
  if (permissionPath) {
    const allPermissions: any = get(state.authorization.permissions, permissionPath)?.Permissions || [];
    return allPermissions.includes(CatalogPermissions.ALL) || allPermissions.includes(permission);
  }
  return false;
};

export const checkCanCreatePermission = (permissionPath?: string[] | string): boolean => {
  return checkPermissions(CatalogPermissions.CREATE, permissionPath);
};

export const checkCanUpdatePermission = (permissionPath?: string[] | string): boolean => {
  return checkPermissions(CatalogPermissions.UPDATE, permissionPath);
};

export const checkCanReadPermission = (permissionPath?: string[] | string): boolean => {
  return checkPermissions(CatalogPermissions.READ, permissionPath);
};

export const filterSelectedOptions = (options: Array<{ value: any; label: string }>, selectedOptionIds: string[], editedOptionId?: any) => {
  return options?.filter((data) => !selectedOptionIds.some((id) => editedOptionId !== id && id === data.value));
};

export const isExposureParamActive = (exposureParam: string, exposureParams: string[]): boolean => {
  const lowerCaseExposureFlag = `ec_${exposureParam.toLowerCase().replace(/\s/g, '')}`;
  return exposureParams.some((exp) => exp.toLowerCase() === lowerCaseExposureFlag);
};

export const yupMatchesParams = {
  alphaNumericChars: {
    pattern: /^[a-zA-Z0-9\s]+$/,
    errorMessage: 'Special characters are not allowed',
  },
  alphaChars: {
    pattern: /^[a-zA-Z\s]+$/,
    errorMessage: 'Only alphabets are allowed',
  },
  alphaLowerChars: {
    pattern: /^[a-z]+$/,
    errorMessage: 'Only lowercase alphabets are allowed',
  },
  alphaNumericWithHyphenAndSlash: {
    pattern: /^[a-zA-Z0-9\s][a-zA-Z0-9\s-_/]*$/,
    errorMessage: 'Allowed special characters are hyphen ( - ) and slash ( / ) not in beginning.',
  },
  alphaNumericWithHyphenUnderscrore: {
    pattern: /^[a-zA-Z0-9_-]+$/,
    errorMessage: 'Allowed special characters are hyphen ( - ) and underscore ( _ )',
  },
  spaceNotAllowed: {
    pattern: /^\S+$/,
    errorMessage: 'Spaces are not allowed',
  },
  startWithLowerChar: {
    pattern: /^[a-z]/,
    errorMessage: 'First character should be lowercase alphabet',
  },
  lowerCaseWithNumericChars: {
    pattern: /^[a-z0-9]+$/,
    errorMessage: 'Only lowercase characters and numbers are allowed',
  },
  tagValue: {
    pattern: /^[a-zA-Z0-9@._\- ]+$/,
    errorMessage: 'Allowed special characters are - _ . @ and space( ) ',
  },
  catalogItem: {
    pattern: /^[^\x5C]*$/,
    errorMessage: 'Backslash ( \\ ) is not allowed',
  },
  catalogName: {
    pattern: /^[A-Za-z0-9&\s]+$/,
    errorMessage: 'Name can only contain letters,numbers and spaces',
  },
  groupName: {
    pattern: /^[a-zA-Z0-9&_\- .]+$/,
    errorMessage: 'Allowed special characters are space, hyphen ( - ), ampersand (&), underscore ( _ ), and dot ( . )',
  },
  description: {
    pattern: /^[a-zA-Z0-9._,`'"+-\s]+$/,
    errorMessage: 'Allowed special characters are  _  +  -  "  .  , `',
  },
  catalogDescription: {
    pattern: /^[a-zA-Z0-9.&_,`'"+-/\s]+$/,
    errorMessage: 'Allowed special characters are & / _  +  -  "  .  , `',
  },
  hostname: {
    pattern: /^[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*$/,
    errorMessage: 'Hostname can only contain letters, numbers and hyphen ( - )',
  },
  faxTollFreeChars: {
    pattern: /^\+?[0-9]+$/,
    errorMessage: 'Only numeric values are allowed',
  },
  latitudeRange: {
    pattern: /^(\+|-)?(?:90(?:(?:\.0{1,6})?)|(?:[0-9]|[1-8][0-9])(?:(?:\.[0-9]{1,6})?))$/,
    errorMessage: 'Latitude measurements range from -90° to +90°',
  },
  longitudeRange: {
    pattern: /^(\+|-)?(?:180(?:(?:\.0{1,6})?)|(?:[0-9]|[1-9][0-9]|1[0-7][0-9])(?:(?:\.[0-9]{1,6})?))$/,
    errorMessage: 'Longitude measurements range from -180° to +180°',
  },
  fqdn: {
    pattern: /(?=^.{4,253}\.?$)(^((?!-)[a-zA-Z0-9-]{1,63}(?<!-)\.)+[a-zA-Z]{2,63}$)/,
    errorMessage: 'Please enter valid FQDN',
    hostErrorMessage: 'Please enter valid host name',
    dnsErrorMessage: 'Please enter valid DNS',
  },
  healthCheckUrl: {
    pattern: /^[a-zA-Z0-9]+(-[a-zA-Z0-9]+)*(_[a-zA-Z0-9]+)*(\/[a-zA-Z0-9]+(-[a-zA-Z0-9]+)*(_[a-zA-Z0-9]+)*)*\/?$/,
    healthCheckUrlErrorMessage: 'Please enter valid Health Check URL, e.g., nebula-api/health',
  },
  appHealthResponse: {
    pattern: /^[a-zA-Z0-9]+(\s[a-zA-Z0-9]+)*$/,
    appHealthResponseErrorMessage: 'Please enter valid App Health Response, e.g., UP, OK',
  },
  subjectAlternateName: {
    pattern: /^DNS:[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
    errorMessage: 'Please enter valid Subject Alternate Name',
  },
  validIPAddress: {
    pattern:
      /^(?:(?:\d{1,3}\.){3}\d{1,3})$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^(?:[0-9a-fA-F]{1,4}:){1,7}:$|^:(?::[0-9a-fA-F]{1,4}){1,7}$|^(?:[0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}$|^(?:[0-9a-fA-F]{1,4}:){1,5}(?::[0-9a-fA-F]{1,4}){1,2}$|^(?:[0-9a-fA-F]{1,4}:){1,4}(?::[0-9a-fA-F]{1,4}){1,3}$|^(?:[0-9a-fA-F]{1,4}:){1,3}(?::[0-9a-fA-F]{1,4}){1,4}$|^(?:[0-9a-fA-F]{1,4}:){1,2}(?::[0-9a-fA-F]{1,4}){1,5}$|^(?:[0-9a-fA-F]{1,4}:){1,1}(?::[0-9a-fA-F]{1,4}){1,6}$|^:(?::[0-9a-fA-F]{1,4}){1,7}$/,
    errorMessage: 'Please enter valid IP address',
  },
  firewallLocation: {
    pattern: /^[a-zA-Z0-9\s-_.?/\\]+$/,
    errorMessage: 'Allowed special characters are  _  -  ?  /  \\  .',
  },
  port: {
    pattern: /^[0-9,-]+$/,
    errorMessage: 'Please enter valid port number',
  },
  numbers: {
    pattern: /^[0-9,-]+$/,
    errorMessage: 'Please enter valid key size',
  },
  appId: {
    pattern: /^\s*APP\d+\s*$/i,
    errorMessage: 'Please enter valid App Id',
  },
  numericChars: {
    pattern: /^\+?[0-9]+$/,
    errorMessage: 'Please enter numeric characters only',
  },
  projectAppId: {
    pattern: /^APP\d{4}$/,
    errorMessage: 'App ID must start with "APP" followed by 4 digits',
  },
  vmPassword: {
    pattern: /^(?!.*[$\\])(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{12,}$/,
    errorMessage:
      "Password must be at least 12 characters with uppercase, lowercase, numbers and shouldn't be existing default password. $ and \\ not allowed in passwords",
  },
  certificatePassword: {
    pattern: /^(?!.*[$\\])(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{12,}$/,
    errorMessage:
      'Password must be at least 12 characters with uppercase, lowercase, numbers and special characters. $ and \\ not allowed in passwords',
  },
  firewallProjectName: {
    pattern: /^[a-zA-Z0-9 +\-_#@.:/=!^(),]+$/,
    errorMessage: 'Allowed special characters are + - _ # @ . : / = ! ^ ( ) ,',
  },
  jiraIssueLink: {
    pattern: /^[A-Z0-9-]+$/,
    errorMessage: 'Please enter valid  JIRA ID',
  },
  projectName: {
    pattern: /^[a-zA-Z0-9_\- ]+$/,
    errorMessage: 'Allowed special characters are hyphen ( - ), underscore ( _ ) and space( )',
  },
  approvalName: {
    pattern: /^[a-zA-Z0-9_\- ]+$/,
    errorMessage: 'Allowed special characters are hyphen ( - ), underscore ( _ ) and space( )',
  },
  databaseName: {
    pattern: /^[a-zA-Z0-9_]+$/,
    errorMessage: 'Allowed special characters are underscore ( _ )',
  },
  databasePassword: {
    pattern: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{12,}$/,
    errorMessage: 'Password must be at least 12 characters with uppercase, lowercase, numbers',
  },
  company: {
    pattern: /^[a-zA-Z0-9-_.\s]+$/,
    errorMessage: 'Allowed special characters are - _ .',
  },
  sshKey: {
    pattern: /^[a-zA-Z0-9+/@=\\\- ]+$/,
    errorMessage: 'Allowed special characters are + / @ = ',
  },
  firstandLastName: {
    pattern: /^[A-Za-z]+ [A-Za-z]+$/,
    errorMessage: 'Must have only first and last name',
  },
  costCenter: {
    pattern: /^[0-9]{10}$/,
    errorMessage: 'Cost center ID must be exactly 10 digits',
  },
  storageNameDescription: {
    pattern: /^[a-z0-9-]*$/,
    errorMessage: 'Allowed only lowercase letters, numbers, and hyphens.',
  },
  ipv6Field: {
    pattern: /^[a-f0-9:]+$/,
    errorMessage: 'IPv6 address must contain only lowercase alphabets and numbers',
  },
  resourceTagValue: {
    pattern: /^[a-zA-Z0-9 _-]+$/,
    errorMessage: 'Tag value must contain only alphanumeric characters, spaces, underscores, and dashes',
  },
  createNamespaceName: {
    pattern: /^[a-zA-Z0-9._-]+$/,
    errorMessage: 'Namespace name can only contain letters, numbers, dots, underscores, and hyphens',
  },
  dnsDomainName: {
    pattern: /\.com$/,
    errorMessage: 'Please enter valid domain name',
  },
  ztpHostName: {
    pattern: /^[a-zA-Z0-9]+\.netops\.charter\.com$/,
    errorMessage: 'Please enter a valid host name. It must follow the pattern [subdomain].netops.charter.com.',
  },
  passwordPolicyName: {
    pattern: /^[a-z0-9-_ ]+$/,
    errorMessage: 'Only lowercase letters, numbers, spaces and special characters such as -, _ are allowed',
  },
};

export const errorMessages = {
  projectAppId: {
    Message: 'Please select the project having App Id or update project with App Id',
  },
};

export const isPortEntryValid = (
  value?: string
): {
  ports?: string;
  errors?: string;
  security?: string;
} => {
  value = value?.replace(/\s+/g, '');
  const parts = value?.split(',');
  const partsValid: string[] = [];
  const errors: string[] = [];
  const security: string[] = [];
  const retObj: { ports?: string; errors?: string; security?: string } = {};
  if (value) {
    if (/^,|,$/.test(value)) {
      errors.push('cannot start or end with a comma');
    }
    if (/,,/.test(value)) {
      errors.push('cannot have two commas in a row');
    }
  }
  for (const part of parts ?? '') {
    if (/^-/.test(part)) {
      errors.push('cannot start with a hyphen');
    } else if (part !== '') {
      const myerrors: string[] = [];
      const portRange = part.split('-');
      for (const port of portRange) {
        const badchars = port.match(/[^\d]+/g);
        if (badchars && badchars.length > 0) {
          myerrors.push(`non-numeric characters (${badchars.join(',')}) are not allowed`);
        } else if (parseInt(port) < 1 || parseInt(port) > 65535) {
          myerrors.push('valid port should be between 1 and 65535');
        }
      }
      if (portRange.length > 2) {
        myerrors.push('valid port range should be two numbers separated by a hyphen');
      }

      if (portRange.length === 2 && parseInt(portRange[0]) >= parseInt(portRange[1])) {
        myerrors.push('first number of port range should be less than second number');
      }

      if (myerrors.length > 0) {
        errors.push(...myerrors);
      } else {
        partsValid.push(part);
      }
    }
  }
  if (partsValid.length > 0) {
    retObj.ports = partsValid.join(', ');
  }
  if (errors.length > 0) {
    retObj.errors = `${errors.join('; ')}`;
    console.log('errorObj', retObj.errors);
  }
  if (security.length > 0) {
    retObj.security = `${security.join('; ')}`;
  }
  return retObj;
};

export const isIconDefined = (iconName: string) => {
  try {
    // @ts-ignore
    return icons[iconName];
  } catch (error) {
    console.log('isIconDefined::', error);
    return '';
  }
};

export const padSingleDigit = (number: number) => {
  return number.toString().padStart(2, '0');
};

export const pluralize = (array: any[], singular: string, plural: string) => {
  const count = array.length;
  return `${count} ${count === 1 ? singular : plural}`;
};

export const getDialogMaxHeight = (isDialogMaximized: boolean) => {
  return `calc(100vh - ${isDialogMaximized ? '170px' : '230px'})`;
};

export const isVmTypeWindows = (vmType: string) => {
  return vmType?.toLocaleLowerCase() === 'windows';
};

export const isVmTypeWindowsV3 = (vmType: string) => {
  return vmType?.toLocaleLowerCase() === 'windowsv3';
};

export const isVmTypeWindowsV3Admin = (vmType: string) => {
  return vmType?.toLocaleLowerCase() === 'windowsv3admin';
};

export const isVmTypeUbuntu = (vmType: string) => {
  return vmType?.toLocaleLowerCase() === 'ubuntu';
};

export const isVmTypeLinux = (vmType: string) => {
  return (
    vmType?.toLocaleLowerCase() === 'linux8&9' ||
    vmType?.toLocaleLowerCase() === 'linux8and9v3' ||
    vmType?.toLocaleLowerCase() === 'linux8&9v3admin'
  );
};

export const getAdminColumnWidth = (isSmaller: boolean) => {
  return isSmaller ? 110 : 130;
};

export const roundTo2DecimalPlaces = (num: number): number => {
  const rounded = Math.round(num * 100) / 100;
  return rounded;
};

export const convertTimeToMostRelevantUnit = (timeInSeconds: number): string => {
  if (timeInSeconds < 1) return `${roundTo2DecimalPlaces(timeInSeconds * 1000)} ms`;
  if (timeInSeconds < 60) return `${roundTo2DecimalPlaces(timeInSeconds)} s`;
  return `${roundTo2DecimalPlaces(timeInSeconds / 60)} min`;
};

export const getFormattedTotalTimeTaken = (startTime: Date, endTime: Date): string => {
  const timeTakenInSeconds = (endTime.getTime() - startTime.getTime()) / 1000;
  return convertTimeToMostRelevantUnit(timeTakenInSeconds);
};

export const getQuotaUnits = () => {
  return [
    { value: 'MB', label: 'MB' },
    { value: 'GB', label: 'GB' },
  ];
};

export const getValueandUnit = (input: string) => {
  const match = input.trim().match(UNIT_REGEX);

  if (match) {
    const value = parseFloat(match[1]);
    const unit = match[3].trim();
    return { value, unit };
  }
  return { value: '', unit: '' };
};

export const getProjects = (selectedProject: string, projects?: Array<{ name: string }>, isReadOnlyMode?: boolean) => {
  if (isReadOnlyMode && selectedProject) {
    return [{ label: selectedProject, value: selectedProject }];
  } else {
    return projects?.map((project) => ({ value: project.name, label: project.name }));
  }
};

export const encrypt = (value: string) => {
  const key = process.env.REACT_APP_PASSWORD_ENCRYPTION_KEY || '';
  const iv = CryptoJS.enc.Hex.parse('00000000000000000000000000000000');
  const cipher = CryptoJS.AES.encrypt(value, CryptoJS.enc.Utf8.parse(key), {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  }).ciphertext.toString(CryptoJS.enc.Hex);
  return cipher;
};

export const validateQuota = (value: number, quotaUnit: string, maxLimit: number, minLimit: number) => {
  if (value === 0) return false;
  else if (!value || !quotaUnit) return true;
  const quotainMB = quotaUnit === 'GB' ? value * 1024 : value;
  return quotainMB >= minLimit && quotainMB <= maxLimit * 1024 && value !== 0;
};

export const renderFullName = (userDetails: { firstName: string; lastName: string }) => {
  return `${userDetails.firstName || ''} ${userDetails.lastName || ''}`;
};

export const validateDiskValue = (value: number, unit: string, maxLimit: number, minLimit: number) => {
  if (value === 0) return false;
  else if (!value || !unit) return true;
  const quotainGB = unit === 'TB' ? value * 1024 : value;
  return quotainGB >= minLimit && quotainGB <= maxLimit && value !== 0;
};

export const convertToMB = (unit: string, value: number) => {
  return unit === 'GB' ? Number(value) * 1024 : value;
};

type SetChipErrorMessage = React.Dispatch<React.SetStateAction<{ value?: string | undefined; error: boolean; message: string }[]>>;
export const chipComponentUtils = {
  updateChipMessage: (value: string, isError: boolean, message: string, setErrorMessage: SetChipErrorMessage) => {
    setErrorMessage((prevErrors) => {
      const newErrors = [...prevErrors];
      const index = newErrors.findIndex((err) => err.value === value);

      if (index !== -1) {
        newErrors[index] = { error: isError, message, value };
      } else {
        newErrors.push({ error: isError, message, value });
      }

      return newErrors;
    });
  },
};

export const getFormattedSuffix = (data: number, paddingLength: number) => {
  return (data + 1).toString().padStart(paddingLength, '0');
};

export function getLargestNumberSuffix<T>(
  items: T[],
  prefix: string,
  nameExtractor: (item: T) => string,
  regex: RegExp,
  paddingLength: number
): string {
  const filteredItems = items.filter((item) => nameExtractor(item).startsWith(prefix));

  const numbers = filteredItems
    .map((item) => {
      const match = nameExtractor(item).match(regex);
      return match ? parseInt(match[1], 10) : null;
    })
    .filter((number): number is number => number !== null);

  const largestNumber = numbers.length ? Math.max(...numbers) : 0;

  return largestNumber > 0 ? getFormattedSuffix(largestNumber + 1, paddingLength) : '01';
}

export function sortCatalogItems(catalogItems: CatalogItem[]) {
  return [...catalogItems].sort((item1, item2) => item1.sequenceNumber - item2.sequenceNumber);
}

export const extractCatalogItemsByLevel = (catalogs: CatalogItem[]) => {
  let level1: CatalogItem[] = [];
  let level2: CatalogItem[] = [];
  let level3: CatalogItem[] = [];
  let level4: CatalogItem[] = [];

  const processCatalog = (items: CatalogItem[], parentId: string = '', level: number = 1) => {
    sortCatalogItems(items).forEach((item) => {
      // Create the item object for the current level
      const currentItem: CatalogItem = {
        id: item.id,
        name: item.name,
        icon: item.icon,
        value: item.value,
        parentId: parentId,
        subCatalogs: item.subCatalogs,
        description: item.description,
        enabled: item.enabled,
        sequenceNumber: item.sequenceNumber,
      };

      if (level === 1) {
        level1.push(currentItem);
      } else if (level === 2) {
        level2.push(currentItem);
      } else if (level === 3) {
        level3.push(currentItem);
      } else if (level === 4) {
        level4.push(currentItem);
      }

      // If there are subCatalogs, process the next level
      if (item.subCatalogs && item.subCatalogs.length > 0) {
        processCatalog(item.subCatalogs, item.id, level + 1);
      }
    });
  };

  // top level
  processCatalog(catalogs);

  // Return the arrays for all levels
  return { level1, level2, level3, level4 };
};

export const formatMetricQueryArray = (catalogs: CatalogItem[]) => {
  let result = '';
  catalogs?.forEach(({ value }) => {
    result = result ? result + `,` + `"${value}"` : `"${value}"`;
  });
  return result;
};

export const getCatalogItemsQuery = () => {
  const commonParams = 'id value name description icon sequenceNumber';
  return {
    query: `query MetricsData { catalogs(all:true) { ${commonParams} subCatalogs { ${commonParams} subCatalogs { ${commonParams} enabled subCatalogs { ${commonParams} enabled requestType component } } } } }`,
  };
};

export function getStatusTextColor(status: string) {
  if (status === RequestStatus.APPROVED) {
    return 'shade12';
  } else if (status === RequestStatus.PENDING_APPROVAL || status === ApprovalStatus.PENDING) {
    return 'shade11';
  } else if (status === RequestStatus.FAILED) {
    return 'shade13';
  } else if (status === RequestStatus.TIMED_OUT) {
    return 'shade14';
  } else if (status === RequestStatus.COMPLETED || status === RequestStatus.SUCCESS) {
    return 'shade8';
  } else {
    return undefined;
  }
}

export function getStatusChipColor(status: string) {
  if (status === RequestStatus.APPROVED) {
    return 'secondary';
  } else if (status === RequestStatus.PENDING_APPROVAL || status === ApprovalStatus.PENDING) {
    return 'primary';
  } else if (status === RequestStatus.FAILED) {
    return 'error';
  } else if (status === RequestStatus.TIMED_OUT) {
    return 'warning';
  } else if (status === RequestStatus.COMPLETED || status === RequestStatus.SUCCESS) {
    return 'success';
  } else {
    return undefined;
  }
}

export function getResourceStatusChipColor(status: string): NblCardChipProps['color'] {
  switch (status) {
    case ResourceStatus.SUCCESS:
    case ResourceStatus.COMPLETED:
      return 'success';
    case ResourceStatus.ACTIVE:
      return 'primary';
    case ResourceStatus.FAILED:
    case ResourceStatus.DELETEFAILED:
      return 'error';
    case ResourceStatus.RUNNING:
      return 'secondary';
    case ResourceStatus.DELETING:
      return 'info';
    case ResourceStatus.PARTIALLYDELETED:
    case ResourceStatus.STOPPED:
      return 'warning';
    case ResourceStatus.DELETED:
    case ResourceStatus.SOFTDELETE:
      return 'default';
    default:
      return undefined;
  }
}

export function getVMToolStatusChipColor(status: string): NblCardChipProps['color'] {
  switch (status) {
    case ToolStatus.success:
      return 'success';
    case ToolStatus.failed:
      return 'error';

    default:
      return undefined;
  }
}

export const generateEnum = <T extends object>(obj: T): { [key in keyof T]: key } => {
  return Object.keys(obj).reduce((acc, key) => {
    acc[key as keyof T] = key as keyof T;
    return acc;
  }, {} as { [key in keyof T]: key });
};

export const textOverflowStyles = (maxWidth: string, lines: number) => ({
  display: '-webkit-box',
  maxWidth,
  WebkitLineClamp: lines,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  whiteSpace: 'normal',
  wordWrap: 'break-word',
  textOverflow: 'ellipsis',
});

export const hexToRgb = (hex: string, opacity: number = 1) => {
  // Remove the hash if present
  hex = hex.replace('#', '');

  // If the hex code is shorthand (e.g., #abc), expand it to full form (#aabbcc)
  if (hex.length === 3) {
    hex = hex
      .split('')
      .map(function (c) {
        return c + c;
      })
      .join('');
  }

  // Parse the red, green, and blue values from the hex string
  var r = parseInt(hex.substring(0, 2), 16);
  var g = parseInt(hex.substring(2, 4), 16);
  var b = parseInt(hex.substring(4, 6), 16);

  // Return the RGB value as a string
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

export const getIPAddressRows = (ipv4Addresses: string[], ipv6Addresses: string[], hostname: string, sequenceNumber: any) => {
  const rows = [];
  const maxLength = Math.max(ipv4Addresses.length, ipv6Addresses?.length);
  const generateHostnames = () => {
    const start = Number(sequenceNumber);
    let length = ipv4Addresses.length || ipv6Addresses.length;
    return Array.from({ length }, (_, i) => `${hostname}-${start + i}`);
  };

  let mapToHosts = maxLength === 1 ? [hostname] : generateHostnames();
  for (let i = 0; i < maxLength; i++) {
    rows.push({
      sno: i + 1,
      ipv4: ipv4Addresses[i] || '-',
      ipv6: ipv6Addresses[i] || '-',
      mapToHost: mapToHosts[i],
      id: i, // Unique ID for each row
    });
  }

  return rows;
};

export const getHexColorWithOpacity = (color: string, percentage: number) => {
  if (percentage < 1 || percentage > 100) {
    return color + '';
  } else {
    const alpha = Math.round((percentage / 100) * 255);
    return color + alpha.toString(16).padStart(2, '0').toUpperCase();
  }
};

export const formatedMemory = (data: number) => {
  if (data === 0) {
    return '0';
  }
  const units = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
  let index = 0;
  while (data >= 1000 && index < units.length - 1) {
    if (data < 1000) {
      break;
    }
    data /= 1000;
    index++;
  }
  return `${data?.toFixed(2)} ${units[index]}`;
};

export const decodeJWTToken = (token: string) => {
  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

  const jsonPayload = decodeURIComponent(
    atob(base64)
      .split('')
      .map((c) => `%${('00' + c.charCodeAt(0).toString(16)).slice(-2)}`)
      .join('')
  );

  return JSON.parse(jsonPayload);
};

export const isDatabaseAppTier = (apptier: string) => {
  return apptier === 'd';
};

export const isDuplicateIPv6 = (hostsDetails: F5Datacenter[], selectedHostIndex: number | undefined, updatedValues: F5Datacenter) => {
  return hostsDetails.some(
    (host, index) =>
      index !== selectedHostIndex &&
      updatedValues.ipv6.some((newIp: any) =>
        host.ipv6.some((existingIp: any) => existingIp.trim().toLowerCase() === newIp.trim().toLowerCase())
      )
  );
};

//

export const groupPermissions = (permissions: { catalogId: string; domainId?: string; roleId: string }[]): Catalog[] => {
  const result: Catalog[] = [];

  permissions.forEach(({ catalogId, domainId, roleId }) => {
    const domain_Id = domainId || null;
    // Find if the catalogId already exists in the result array
    let catalog = result.find((item) => item.catalogId === catalogId);

    if (!catalog) {
      // If not, create a new catalog entry and push to the result array
      catalog = {
        catalogId,
        domain: [],
      };
      result.push(catalog);
    }

    // Find if the domainId already exists in the domain array
    let domain = catalog.domain.find((item) => item.domainId === domain_Id);

    if (!domain) {
      // If not, create a new domain entry and add to the domain array
      domain = {
        domainId: domain_Id,
        roles: [],
      };
      catalog.domain.push(domain);
    }

    // Add the roleId to the roles array of the matching domain
    domain.roles.push({ roleId });
  });

  return result;
};

export const reverseGroupPermissions = (
  groupedPermissions: Catalog[]
): { catalogId: string; domainId: string | null; roleId: string; count: number }[] => {
  // Flatten the grouped permissions into the original structure
  const flattenedPermissions: { catalogId: string; domainId: string | null; roleId: string }[] = [];

  groupedPermissions.forEach(({ catalogId, domain }) => {
    domain.forEach(({ domainId, roles }) => {
      roles.forEach(({ roleId }) => {
        // Push each permission object to the flattened array
        flattenedPermissions.push({
          catalogId,
          domainId: domainId || null, // Normalize undefined/empty domainId to null
          roleId,
        });
      });
    });
  });

  // Now, count the occurrences of each permission object
  const counts: { catalogId: string; domainId: string | null; roleId: string; count: number }[] = [];

  flattenedPermissions.forEach((permission) => {
    const existingPermission = counts.find(
      (count) => count.catalogId === permission.catalogId && count.domainId === permission.domainId && count.roleId === permission.roleId
    );

    if (existingPermission) {
      // If the permission already exists, increment the count
      existingPermission.count += 1;
    } else {
      // If the permission doesn't exist, add it to the counts array with count 1
      counts.push({ ...permission, count: 1 });
    }
  });

  return counts;
};

export const getVmDatacenterOptions = (selectedEnvironment?: MultiENVEnvironment, selectedDomain?: string, type?: string) => {
  if (selectedEnvironment) {
    const networkType = type || SettingTypes.VLAN;
    const vlanSettings = selectedEnvironment.settings?.filter((setting) => setting.type === networkType);
    if (vlanSettings?.length) {
      const dataCenterOptions = vlanSettings.flatMap(
        (setting) =>
          setting.configurations
            ?.filter((item) => (selectedDomain ? selectedDomain === item.domain : true))
            ?.map((item) => ({
              label: item.dataCenterName,
              value: item.dataCenterName,
            })) || []
      );

      // Remove duplicate datacenters based on the `value` (dataCenter)
      const uniqueDataCenters = Array.from(new Map(dataCenterOptions.map((item) => [item.value, item])).values());
      return uniqueDataCenters.length ? uniqueDataCenters : [{ label: '', value: '' }];
    } else return [{ label: '', value: '' }];
  } else return [{ label: '', value: '' }];
};

export const formatId = (id: string) => {
  return id.replace(/\s+/g, '-');
};

export const getDataCenters = (selectedProject: MultiENVProject, selectedDomain?: string) => {
  const allDataCenters =
    selectedProject?.applications?.flatMap(
      (app) =>
        app.environments?.flatMap(
          (env) =>
            env.settings?.flatMap((setting) =>
              setting.type === SettingTypes.VLAN
                ? setting.configurations
                    ?.filter((item) => (selectedDomain ? selectedDomain === item.domain : true))
                    ?.map((dc: { dataCenterName: string; dataCenter: string }) => ({
                      label: dc.dataCenterName,
                      value: dc.dataCenterName,
                    }))
                : []
            ) || []
        ) || []
    ) || [];

  // Deduplicate by dataCenterName
  const uniqueDataCentersMap = new Map<string, { label: string; value: string }>();
  allDataCenters.forEach((dc) => {
    if (!uniqueDataCentersMap.has(dc.value)) {
      uniqueDataCentersMap.set(dc.value, dc);
    }
  });

  return Array.from(uniqueDataCentersMap.values());
};

export const getDataCenterDetails = (selectedProject: MultiENVProject, dataCenter: string) => {
  return selectedProject?.applications?.flatMap(
    (app) =>
      app.environments?.flatMap((env) =>
        env.settings
          ?.filter((setting) => setting.type === SettingTypes.VLAN)
          .flatMap((setting) => setting.configurations?.filter((config) => config.dataCenterName === dataCenter) || [])
      ) || []
  )?.[0];
};

export const getNetworkOptions = (selectedEnvironment: MultiENVEnvironment, datacenter: string, domain: string, type?: string) => {
  const networkType = type || SettingTypes.VLAN;
  // Filter settings with type 'VLAN'
  const vlanSettings = selectedEnvironment.settings?.filter((setting) => setting.type === networkType);
  // If vlanSettings is found, iterate over each setting's configuration
  if (vlanSettings?.length) {
    return vlanSettings.flatMap(
      (setting) =>
        setting.configurations?.filter((item) => item.dataCenterName === datacenter && item.domain === domain).map((item) => item.value) // Extract 'value' from each filtered item
    );
  }
};

export const exportCSV = <T extends Object[]>(data: T, filename: `${string}.csv`) => {
  if (!data || data.length === 0) {
    console.warn('No data to export.');
    return;
  }

  //Creating Table headers using the field name
  const headers =
    Object.keys(data[0])
      .join(',')
      //since field name will be camelcase using regex to seperate it into two Capitalized words
      .replaceAll(/([A-Z])/g, ' $1')
      .replaceAll(/(?<=,).|^./g, (str) => str.toUpperCase()) + '\n';

  const rows = data
    .map((row) =>
      Object.values(row)
        .map((value) => {
          const stringValue = String(value); //CSV will support only string
          return stringValue;
        })
        .join(',')
    )
    .join('\n');

  const csvData = '\uFEFF' + headers + rows; // Add UTF-8 BOM
  downloadFile(new Blob([csvData], { type: 'text/csv;charset=utf-8;' }), filename);
};

export function encryptUrlState(data: Object) {
  return CryptoJS.AES.encrypt(JSON.stringify(data), URLEncryptionSecret).toString();
}

export function decryptUrlState(cipherText: string) {
  try {
    const bytes = CryptoJS.AES.decrypt(cipherText, URLEncryptionSecret);
    return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
  } catch (e) {
    console.log(e);
    return null;
  }
}
