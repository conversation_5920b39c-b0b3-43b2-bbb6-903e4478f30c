import React, { useEffect, useState } from 'react';
import { AdministrationTabsData } from 'types';
import { getTeamItems } from 'api/static-data';
import Catalog from 'components/Catalog';

interface TeamProps {}

const Teams: React.FunctionComponent<TeamProps> = () => {
  const [content, setContent] = useState<AdministrationTabsData[]>([]);
  useEffect(() => {
    async function getCatalogItems() {
      try {
        const data = await getTeamItems();
        console.log(data);
        setContent(data);
      } catch (error) {
        console.log(error);
      }
    }
    getCatalogItems();
  }, []);
  return content?.length ? <Catalog catalogItems={content} isAdminTiles /> : <div>No items to display</div>;
};

export default Teams;
