import { ReactElement, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import NblBorderContainer from '../NblBorderContainer';
import NblFlexContainer from '../NblFlexContainer';
import { StyledTypography } from './styled';
import NblButton from 'sharedComponents/Buttons/NblButton';
import { NblGridContainer, NblGridItem } from '../NblGridContainer';
import { UnfoldLess, UnfoldMore } from '@mui/icons-material';
import NblChip from 'sharedComponents/NblChip';
import { dateFormatter, getStatusChipColor } from 'utils/common';
import { NblTable } from 'sharedComponents/NblTable';
import { NebulaTheme } from 'NebulaTheme/type';
import { useTheme } from '@mui/material';
import useLoadComponentDetails from 'hooks/useLoadComponentDetails';
import { RequestPayloadMap, CatalogRequestType, RequestMetaData } from 'types/Interfaces/RequestDetails';
import ActivityLogger from 'componentsV2/RequestsDetail/ActivityLogger';
import { DefaultAccordions, setAccordions } from 'store/reducers/common';
import NblViewDetailsAccordion, { isViewDetailsAccordionsExpanded } from 'componentsV2/NblViewDetailsAccordion';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import NblTooltip from 'sharedComponents/NblTooltip';
// eslint-disable-next-line no-unused-vars
import { DownStreamError } from 'types';
import { ActivityLog } from 'types/Interfaces/ActivityLogData';
import { RequestType } from 'types/Enums';
import NavigateToResourceDetails from 'componentsV2/NavigateToResourceDetails';

interface RequestData {
  requestId: string;
  catalogItem: string;
  createdBy: string;
  startDate: string;
  completedAt: string;
  status: string;
  approvalStatus: string;
  resourceId: string;
}

interface ApprovalData {
  id: number;
  level: number;
  approvalGroup: string;
  approvedOrRejectedBy: string;
  approvedOrRejectedAt: string;
  approvalStatus: string;
  comments: string;
}

interface NblRequestDetailsContainerProps<T extends CatalogRequestType> {
  title: string;
  componentName: string;
  formName: string;
  data: RequestData;
  requestMetaData: RequestMetaData;
  requestPayload: RequestPayloadMap[T] | undefined;
  approvalDetails: ApprovalData[];
  ApprovalConfirmButton: () => ReactElement;
  requestType?: 'REQUESTOR' | 'APPROVALS';
  activityLogs: ActivityLog[];
  serviceRequestType: RequestType | undefined;
}

const NblRequestDetailsContainer = <T extends CatalogRequestType>({
  title,
  formName,
  componentName,
  data,
  requestMetaData,
  requestPayload,
  approvalDetails,
  ApprovalConfirmButton,
  requestType,
  activityLogs,
  serviceRequestType,
}: NblRequestDetailsContainerProps<T>) => {
  const { Component: RequestDetailsComponent } = useLoadComponentDetails(componentName);
  const dispatch = useDispatch();
  const { expandedAccordions } = useSelector((state: State) => state.common);

  const theme = useTheme<NebulaTheme>();
  const { palette } = theme;
  const expanded = useMemo(() => isViewDetailsAccordionsExpanded(), [expandedAccordions]);

  const getError = (downstreamError: DownStreamError[]) => {
    return downstreamError.map((error: DownStreamError) => error?.data?.message || '').join(', ');
  };
  const errorMessage =
    requestMetaData.downstreamError && requestMetaData.downstreamError.length > 0 ? getError(requestMetaData.downstreamError) : '';

  const requestData = [
    { name: 'Request ID', value: data.requestId },
    ...(data.resourceId ? [{ name: 'Resource ID', value: <NavigateToResourceDetails resourceId={data?.resourceId} /> }] : []),
    { name: 'Catalog Item', value: data.catalogItem },
    { name: 'Created By', value: data.createdBy },
    { name: 'Start Date', value: data.startDate ? dateFormatter(data.startDate) : '' },
    { name: 'Completed Date', value: data.completedAt ? dateFormatter(data.completedAt) : '' },
    {
      name: 'Request Status',
      value: (
        <>
          <NblTooltip
            tooltipMessage={
              data.status === 'FAILED' || data.status === 'COMPLETED WITH ERROR' || data.status === 'TIMED OUT' ? errorMessage : data.status
            }
          >
            <span>
              <NblChip id={data.requestId} label={data.status} borderRadius="lg" color={getStatusChipColor(data.status.toUpperCase())} />
            </span>
          </NblTooltip>
        </>
      ),
    },
    {
      name: 'Approval Status',
      value: (
        <NblChip
          id={data.requestId}
          label={data.approvalStatus}
          borderRadius="lg"
          color={getStatusChipColor(data.approvalStatus.toUpperCase())}
        />
      ),
    },
  ];

  const handleRow = (approvalDetails: any) => {
    return approvalDetails?.map((item: any, index: number) => ({
      id: index,
      level: item.level,
      approvalGroup: item.approvalGroup,
      approvalStatus: item.approvalStatus,
      approvedOrRejectedAt: item.approvedOrRejectedAt,
      approvedOrRejectedBy: item.approvedOrRejectedBy,
      comments: item.comments,
    }));
  };

  const approvalTableColumns = [
    { field: 'level', headerName: 'Level', flex: 1 },
    { field: 'approvalGroup', headerName: 'Group', flex: 1 },
    { field: 'approvedOrRejectedBy', headerName: 'Approved/Rejected By', flex: 1 },
    { field: 'approvedOrRejectedAt', headerName: 'Approved/Rejected Date', flex: 1 },
    {
      field: 'approvalStatus',
      headerName: 'Approval Status',
      flex: 1,
      renderCell: (params: any) => {
        const {
          row: { approvalStatus },
        } = params;
        return (
          <div>
            <NblChip
              id={approvalStatus}
              label={approvalStatus}
              borderRadius="lg"
              color={getStatusChipColor(approvalStatus?.toUpperCase())}
            />
          </div>
        );
      },
    },
    { field: 'comments', headerName: 'Comments', flex: 1 },
  ];

  function getActivitylogAllowedCatalogs() {
    const activitylogAllowedCatalogs = process.env.REACT_APP_ACTIVITY_LOG_ENABLED_CATALOGS;
    return activitylogAllowedCatalogs ? activitylogAllowedCatalogs.split(',').map((catalog) => catalog.trim()) : [];
  }

  //Utils
  const toggleExpandCollapse = () => {
    dispatch(setAccordions(expanded ? DefaultAccordions.NONE : DefaultAccordions.ALL));
  };

  //Side effcets
  useEffect(() => {
    dispatch(setAccordions(DefaultAccordions.NONE));
  }, []);

  //JSX
  return (
    <NblBorderContainer padding="20px" height="auto" backgroundColor={palette.background.paper}>
      <NblFlexContainer direction="row" alignItems="center" justifyContent="space-between" margin="0px 0px 15px">
        <NblFlexContainer alignItems="center">
          <StyledTypography variant="h2">{title}</StyledTypography>
          <NblButton buttonID={'toggle-expand-collapse-btn'} color="primary" onClick={toggleExpandCollapse}>
            {expanded ? <UnfoldLess /> : <UnfoldMore />}
            <StyledTypography variant="subtitle2">{expanded ? 'Collapse All' : 'Expand All'}</StyledTypography>
          </NblButton>
        </NblFlexContainer>
        {ApprovalConfirmButton()}
      </NblFlexContainer>
      <NblBorderContainer>
        <NblGridContainer spacing={2} columnMinWidth="200px" height="auto" padding="20px">
          {requestData.map((item, index) => (
            <NblGridItem key={index} height="auto">
              <StyledTypography variant="subtitle1">{item.name}</StyledTypography>
              <StyledTypography variant="subtitle2">{item.value}</StyledTypography>
            </NblGridItem>
          ))}
        </NblGridContainer>
      </NblBorderContainer>
      <NblViewDetailsAccordion summary="Approval Details">
        <NblTable rows={handleRow(approvalDetails)} columns={approvalTableColumns} showResetFilter={false} autoHeight isToolBarVisible />
      </NblViewDetailsAccordion>
      {RequestDetailsComponent && (
        <RequestDetailsComponent
          formName={`${formName} Details`}
          requestMetaData={requestMetaData}
          requestPayload={requestPayload}
          requestType={requestType}
        />
      )}
      {requestMetaData.catalogStepsId && getActivitylogAllowedCatalogs().includes(data.catalogItem) && (
        <ActivityLogger status={data.status} activityLogs={activityLogs} serviceRequestType={serviceRequestType} />
      )}
    </NblBorderContainer>
  );
};

export default NblRequestDetailsContainer;
