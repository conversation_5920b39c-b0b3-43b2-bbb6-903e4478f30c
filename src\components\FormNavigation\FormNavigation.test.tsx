import { act, render, fireEvent } from '@testing-library/react';

import FormNavigation from '.';
import ThemeProvider from 'mock/ThemeProvider';

describe('Render the FormNavigation', () => {
  const setSelectedTabIndex = jest.fn();

  test('Should render the previous and next buttons', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <FormNavigation
            isPreviousBtnDisabled={true}
            isNextBtnDisabled={false}
            selectedTabIndex={0}
            setSelectedTabIndex={setSelectedTabIndex}
          />
        </ThemeProvider>
      )
    );

    expect(getByText('Previous')).toBeInTheDocument();
    expect(getByText('Next')).toBeInTheDocument();
  });

  test('Should be able to click on previous and next buttons', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <FormNavigation
            isPreviousBtnDisabled={false}
            isNextBtnDisabled={false}
            selectedTabIndex={0}
            setSelectedTabIndex={setSelectedTabIndex}
          />
        </ThemeProvider>
      )
    );

    fireEvent.click(getByText('Previous'));
    expect(setSelectedTabIndex).toHaveBeenCalled();

    fireEvent.click(getByText('Next'));
    expect(setSelectedTabIndex).toHaveBeenCalled();
  });

  test('Should not be able to click on previous and next disabled buttons', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <FormNavigation
            isPreviousBtnDisabled={true}
            isNextBtnDisabled={true}
            selectedTabIndex={0}
            setSelectedTabIndex={setSelectedTabIndex}
          />
        </ThemeProvider>
      )
    );

    fireEvent.click(getByText('Previous'));
    fireEvent.click(getByText('Next'));
    expect(setSelectedTabIndex).toHaveBeenCalledTimes(0);
  });
});
