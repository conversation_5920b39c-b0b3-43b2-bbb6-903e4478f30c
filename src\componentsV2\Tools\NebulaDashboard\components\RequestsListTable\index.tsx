import { Link } from '@mui/material';
import { GetRequestList } from 'store/reducers/usagemetricsrequest';
import GetStatus from './GetStatus';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
// eslint-disable-next-line
import { ColumnData, NblTable, TableRowData } from 'sharedComponents/NblTable';
import { useEffect, useState } from 'react';
import { PageInfoData } from 'types/Interfaces/PaginationResponse';
// eslint-disable-next-line
import { GridRenderCellParams } from '@mui/x-data-grid';
import useNblNavigate from 'hooks/useNblNavigate';
import NblTypography from 'sharedComponents/NblTypography';
import NblBox from 'sharedComponents/NblContainers/NblBox';

interface Column {
  label: string;
  field: string;
  align?: 'left' | 'center' | 'right';
}

interface RequestListTableProps {
  payload: GetRequestList | null;
  catalogData: any;
  count: number;
  isLoading: boolean;
  handlePageChange: (page: any, pageSize: number) => void;
  page: number;
  rowperPage: number;
}
const RequestListTable: React.FunctionComponent<RequestListTableProps> = ({
  payload,
  catalogData,
  isLoading,
  count,
  handlePageChange,
  page,
  rowperPage,
}) => {
  const navigate = useNblNavigate();

  const findNameByrequestType = (catalogs: any[], requestType: string): string | null => {
    for (const catalog of Array.isArray(catalogs) ? catalogs : [catalogs]) {
      if (catalog.requestType === requestType) {
        return catalog.name;
      }
      if (catalog.subCatalogs && Array.isArray(catalog.subCatalogs)) {
        const result = findNameByrequestType(catalog.subCatalogs, requestType);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };
  const data: TableRowData[] =
    payload && payload.data.length > 0
      ? payload.data.map((data: any) => {
          const { id, projectName, catalogName, createdBy, status, date, requestType, serviceRequestId } = data;
          const options: Intl.DateTimeFormatOptions = {
            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            hour12: true, // 12-hour format
            hour: 'numeric',
            minute: 'numeric',
            second: 'numeric',
          };
          const formattedDate = new Date(date).toLocaleDateString('en-US', options);
          const nameByRequestType = catalogData && findNameByrequestType(catalogData, requestType);
          const requestId = serviceRequestId ? serviceRequestId : id;
          return {
            id,
            requestId,
            projectName,
            catalogName,
            createdBy,
            status,
            date: formattedDate,
            catalogItem: nameByRequestType,
          };
        })
      : [];

  const [pageInfo, setPageInfo] = useState<PageInfoData>();

  function handleRequestRowClick(row: TableRowData) {
    navigate(`/requests/${row.row.requestId}`);
  }

  const columns: ColumnData[] = [
    {
      field: 'requestId',
      headerName: 'Request ID',
      flex: 1,
      renderCell(params: GridRenderCellParams) {
        const {
          row: { requestId, date },
        } = params;
        return (
          <NblTypography variant="h6">
            <Link variant="button" onClick={() => handleRequestRowClick(params)} sx={{ textDecoration: 'none', cursor: 'pointer' }}>
              {requestId}
            </Link>
            <NblTypography variant="subtitle2" display="block" color="shade6">
              {date}
            </NblTypography>
          </NblTypography>
        );
      },
    },
    {
      field: 'projectName',
      headerName: 'Project Name',
      flex: 1,
      renderCell(params: GridRenderCellParams) {
        const {
          row: { projectName },
        } = params;
        return <NblBox>{projectName ? projectName : '--'}</NblBox>;
      },
    },
    {
      field: 'catalogName',
      headerName: 'Catalog Level',
      flex: 1,
    },
    {
      field: 'catalogItem',
      headerName: 'Catalog Item',
      flex: 1,
    },
    {
      field: 'createdBy',
      headerName: 'Created By',
      flex: 1,
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      renderCell(params: GridRenderCellParams) {
        const {
          row: { status },
        } = params;
        return <GetStatus status={status} />;
      },
    },
  ];

  useEffect(() => {
    if (count) {
      const totalPages = Math.ceil(count / rowperPage);

      setPageInfo({
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
        limit: rowperPage,
        nextPage: page + 1,
        prevPage: page - 1,
        page: page,
        totalDocs: count,
        totalPages: totalPages,
        pagingCounter: 0,
      });
    }
  }, [count, rowperPage, page]);

  // eslint-disable-next-line
  const serverPaginationFn = (page: number, pageSize: number, sort?: string, filter?: string) => {
    console.log('pagination event triggered', page, pageSize);
    handlePageChange(page, pageSize);
  };
  return (
    <NblFlexContainer>
      <NblTable
        pageInfo={pageInfo}
        loading={isLoading}
        rows={data}
        columns={columns}
        serverPagination={true}
        serverPaginationFn={serverPaginationFn}
        rowSize={rowperPage.toString()}
        pageSizeOptions={['5', '10', '20']}
        autoHeight={true}
      />
    </NblFlexContainer>
  );
};
export default RequestListTable;
