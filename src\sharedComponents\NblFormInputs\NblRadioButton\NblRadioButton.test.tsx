import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblRadioButton from '.';

describe('NblRadioButton component', () => {
  const props = {
    label: 'IPv4',
    name: 'ipv4',
    disabled: false,
    checked: false,
    error: false,
  };
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblRadioButton value={''} {...props} onChange={() => {}} onBlur={() => {}} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
