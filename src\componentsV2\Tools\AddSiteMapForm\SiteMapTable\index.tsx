import { IconButton, useTheme } from '@mui/material';
import { useEffect, useState } from 'react';
import { DeleteOutlineOutlined, InfoOutlined } from '@mui/icons-material';
import SiteMapDetailsDialog from './SiteMapDetailsDialog';
// eslint-disable-next-line
import { NebulaTheme } from 'NebulaTheme/type';
// eslint-disable-next-line
import { ColumnData, NblTable } from 'sharedComponents/NblTable';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblConfirmPopUp from 'sharedComponents/NblConfirmPopUp';

interface SiteMapTableProps {
  siteMapData: any[];
  deleteRow: (id: any) => void;
  rowSize?: string;
}

const SiteMapTable: React.FunctionComponent<SiteMapTableProps> = ({ siteMapData, deleteRow, rowSize }) => {
  const [data, setData] = useState<any[]>([]);
  const [selectedSiteMap, setSelectedSiteMap] = useState<any>({});
  const [siteMapDialog, setSiteMapDialog] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [rowToDelete, setRowToDelete] = useState<any>(null);
  const theme: NebulaTheme = useTheme();

  useEffect(() => {
    let rows: any[] = [];
    siteMapData.forEach((item, index) => {
      var rowWithId = item;
      rowWithId.id = index;
      rows.push(rowWithId);
    });
    setData(rows);
  }, [siteMapData]);

  const onViewDetailsClickHandler = (siteMap: any) => {
    setSelectedSiteMap(siteMap);
    setSiteMapDialog(true);
  };

  const onDeleteClickHandler = (rowId: any) => {
    setDeleteDialogOpen(true);
    setRowToDelete(rowId);
  };

  const handleRowDelete = () => {
    if (rowToDelete !== null) {
      deleteRow(rowToDelete);
      setRowToDelete(null);
      setDeleteDialogOpen(false);
    }
  };

  const renderViewDetails = (params: any) => {
    const row = params.row;
    return (
      <NblFlexContainer spacing={0}>
        <IconButton
          data-testid={'view'}
          onClick={() => {
            onViewDetailsClickHandler(row);
          }}
          color="info"
        >
          <InfoOutlined />
        </IconButton>
        <IconButton
          data-testid={`delete-icon-${params.row.id}`}
          onClick={() => {
            onDeleteClickHandler(params.row.id);
          }}
        >
          <DeleteOutlineOutlined style={{ color: theme.palette.tertiary.shade3.dark }} />
        </IconButton>
      </NblFlexContainer>
    );
  };

  const columns: ColumnData[] = [
    {
      field: 'siteName',
      headerName: 'Site Name',
      flex: 1,
      width: 700,
    },
    {
      field: 'facilityType',
      headerName: 'Facility Type',
      flex: 1,
      width: 300,
      renderCell: (params) => params.row.facilityType,
    },
    {
      field: 'vendorAddressHeader',
      headerName: 'Vendor Address Header',
      flex: 1,
      width: 500,
      renderCell: (params) => params.row.vendorAddressHeader,
    },
    {
      field: 'regionOrigin',
      headerName: 'Region Origin',
      flex: 1,
      width: 200,
      renderCell: (params) => params.row.regionOrigin,
    },
    {
      field: 'marketOrigin',
      headerName: 'Market Origin',
      flex: 1,
      width: 700,
      renderCell: (params) => params.row.marketOrigin,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      flex: 0.4,
      renderCell: (params: any) => renderViewDetails(params),
    },
  ];

  const renderTable = () => {
    return <NblTable columns={columns} rows={data} rowSize={rowSize} pageSizeOptions={['5', '10', '20', '40', '80']} />;
  };

  return (
    <NblFlexContainer minWidth="100%" height="auto">
      {data.length ? renderTable() : 'No data available'}
      <SiteMapDetailsDialog open={siteMapDialog} selectedSiteMap={selectedSiteMap} onClose={() => setSiteMapDialog(false)} />
      <NblConfirmPopUp
        title="Confirm Deletion"
        content="Are you sure you want to delete this row?"
        open={deleteDialogOpen}
        onClose={() => {
          setDeleteDialogOpen(false);
          setRowToDelete(null);
        }}
        submitText="Delete"
        onSubmit={handleRowDelete}
      />
    </NblFlexContainer>
  );
};

export default SiteMapTable;
