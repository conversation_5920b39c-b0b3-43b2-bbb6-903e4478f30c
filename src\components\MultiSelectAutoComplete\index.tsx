import React from 'react';
import { Autocomplete, TextField, Stack, SxProps, FormHelperText, InputLabel, MenuItem, Checkbox, ListItemText } from '@mui/material';

interface MultiSelectAutoCompleteProps {
  label: string;
  name: string;
  options: any;
  value: { value: string; label: string }[];
  error?: string | string[] | boolean;
  placeholder?: string;
  handleChange: (event: any, value: { value: string; label: string }[]) => void;
  disabled?: boolean;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  sx?: SxProps;
}

const MultiSelectAutoComplete: React.FunctionComponent<MultiSelectAutoCompleteProps> = ({
  label,
  name,
  options,
  value,
  error,
  disabled,
  placeholder,
  handleChange,
  onMouseEnter,
  onMouseLeave,
  sx,
}: MultiSelectAutoCompleteProps) => {
  const handleOptionChange = (event: React.ChangeEvent<{}>, newValue: { value: string; label: string }[]) => {
    handleChange(event, newValue);
  };

  return (
    <Stack spacing={1} sx={sx}>
      <InputLabel htmlFor={name}>{label}</InputLabel>
      <Autocomplete
        multiple
        id={name}
        options={options}
        value={value}
        onChange={handleOptionChange}
        disableCloseOnSelect
        disabled={disabled}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        getOptionLabel={(option) => option.label}
        isOptionEqualToValue={(option, value) => option.value === value.value}
        renderOption={(props, option, { selected }) => (
          <MenuItem {...props}>
            <Checkbox checked={selected} />
            <ListItemText primary={option.label} />
          </MenuItem>
        )}
        renderInput={(params) => (
          <TextField
            {...params}
            sx={{ '& .MuiChip-label': { color: 'black' }, '& .MuiSvgIcon-root': { color: 'grey !important' } }}
            placeholder={placeholder}
            error={Boolean(error)}
          />
        )}
      />
      {error && <FormHelperText error>{error}</FormHelperText>}
    </Stack>
  );
};

export default MultiSelectAutoComplete;
