import { useSelector } from 'react-redux';

// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';

/** Get Catalog Level 4 Details based on Request Type */
const useGetCatalogLevel4Details = (requestType: string) => {
  const { level4 } = useSelector((state: State) => state.catalogs);

  const getObjectByRequestType = () => {
    if (requestType) {
      for (const category in level4) {
        const item = level4[category].find((item) => item.requestType === requestType);
        if (item) {
          return item;
        }
      }
    }
    return {
      name: '',
      component: '',
      shortName: '',
    };
  };

  const catalogDetails = getObjectByRequestType();

  return {
    catalogDetails,
  };
};

export default useGetCatalogLevel4Details;
