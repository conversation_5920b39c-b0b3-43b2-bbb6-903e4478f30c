//eslint-disable-next-line no-unused-vars
import { ButtonHTMLAttributes, ReactElement, ReactNode } from 'react';
import StyledNblButton from './styled';
import NblTooltip from 'sharedComponents/NblTooltip';

interface NblButtonProps {
  buttonID: string;
  children?: ReactNode;
  onClick?: () => void;
  variant?: 'contained' | 'outlined' | 'text';
  color?: 'primary' | 'info' | 'error' | 'success';
  disabled?: boolean;
  startIcon?: ReactElement;
  endIcon?: ReactElement;
  type?: ButtonHTMLAttributes<HTMLButtonElement>['type'];
  component?: string;
  tooltip?: string;
  margin?: string;
}

const NblButton: React.FunctionComponent<NblButtonProps> = ({
  buttonID,
  children,
  onClick = () => { },
  variant = 'outlined',
  disabled,
  color = 'info',
  startIcon,
  endIcon,
  type = 'button',
  component,
  tooltip,
  margin,
}) => {
  //Renders
  const Button = () => {
    return (
      <StyledNblButton
        startIcon={startIcon}
        endIcon={endIcon}
        disableRipple
        data-testid={buttonID}
        id={buttonID}
        onMouseDown={
          disabled
            ? undefined
            : (e) => {
              e.preventDefault();
              e.stopPropagation();
              onClick();
            }
        }
        variant={variant}
        disabled={disabled}
        color={color}
        type={type}
        margin={margin}
        // @ts-ignore
        component={component}
      >
        {children}
      </StyledNblButton>
    );
  };

  //Jsx
  return tooltip ? (
    <NblTooltip tooltipMessage={tooltip}>
      <Button />
    </NblTooltip>
  ) : (
    <Button />
  );
};

export default NblButton;
