import React, { useState, useEffect } from 'react';
import { useMediaQuery, useTheme } from '@mui/material';

// eslint-disable-next-line no-unused-vars
import { GridRenderCellParams } from '@mui/x-data-grid';
import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import withAdminPermissions from 'hoc/withAdminPermissions';
// eslint-disable-next-line no-unused-vars
import { NblTable, ColumnData } from 'sharedComponents/NblTable';
import { ServiceCatalogGroupsData } from 'types';
import ActionsColumn from 'componentsV2/Administration/ActionsColumn';
// eslint-disable-next-line no-unused-vars
import { ComponentType, AdminGridProps } from 'types';
import { dateFormatter, getAdminColumnWidth } from 'utils/common';
import { useApiService } from 'api/ApiService/context';
import { NebulaTheme } from 'NebulaTheme/type';

interface MultiViewServiceCatalogProps extends AdminGridProps {}

const MultiViewServiceCatalog: React.FunctionComponent<MultiViewServiceCatalogProps> & ComponentType = ({ permissions }) => {
  const { apiCatalogAuthService } = useApiService();
  const theme = useTheme<NebulaTheme>();
  const isSmaller = useMediaQuery(theme.breakpoints.down('xl'));
  const [serviceCatalogData, setServiceCatalogData] = useState<ServiceCatalogGroupsData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchServiceCatalogGroups = () => {
    setIsLoading(true);
    apiCatalogAuthService
      .getMultiServiceCatalogGroups()
      .then((res) => {
        if (res.status) {
          const result = res.data.flat();
          setServiceCatalogData(
            result.map((catalogItems) => ({
              ...catalogItems,
              id: catalogItems?.id,
              catalogGroupName: catalogItems.level01Name || catalogItems.level02Name || catalogItems.level03Name || '',
              createdBy: catalogItems?.createdBy,
              createdAt: catalogItems?.createdAt ? dateFormatter(catalogItems.createdAt) : '',
              updatedAt: catalogItems?.updatedAt ? dateFormatter(catalogItems.updatedAt) : '',
              updatedBy: catalogItems?.updatedBy,
            }))
          );
        } else {
          setServiceCatalogData([]);
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    fetchServiceCatalogGroups();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ColumnData[] = [
    {
      field: 'catalogGroupName',
      headerName: 'Catalog Group Name',
      flex: 1,
    },
    { field: 'description', headerName: 'Description', flex: 1 },
    { field: 'createdAt', headerName: 'Created Date', flex: 1 },
    { field: 'createdBy', headerName: 'Created By', flex: 1 },
    { field: 'updatedAt', headerName: 'Updated Date', flex: 1 },
    { field: 'updatedBy', headerName: 'Updated By', flex: 1 },
    {
      field: 'actions',
      headerName: 'Actions',
      filterable: false,
      sortable: false,
      width: getAdminColumnWidth(isSmaller),
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        let selectedLevel;
        if (params.row.level01Name) {
          selectedLevel = 'level=1';
        } else if (params.row.level02Name) {
          selectedLevel = 'level=2';
        } else if (params.row.level03Name) {
          selectedLevel = 'level=3';
        }
        return (
          <ActionsColumn
            editUrl={`${params.row.shortName}?${selectedLevel}`}
            onDeleteHandler={() => {}}
            permissions={permissions}
            disableDelete
          />
        );
      },
    },
  ];

  return (
    <NblTable
      loading={isLoading}
      rows={serviceCatalogData}
      columns={columns}
      rowSize="10"
      pageSizeOptions={['5', '10', '20', '40', '60']}
      onRefreshHandler={fetchServiceCatalogGroups}
      showRefreshButton={true}
      isToolBarVisible
    />
  );
};

MultiViewServiceCatalog.type = ADMIN_TILE_PERMISSION_TYPE.grid;

export default withAdminPermissions(MultiViewServiceCatalog);
