import React from 'react';
import { useSelector } from 'react-redux';

import { PERMISSION_MESSAGES } from 'utils/constant';
import useExposureParams from 'hooks/useExposureParams';
import CatalogItem from '../index';
import { AdminTileDetails, CatalogTilesData } from 'types';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';

interface AdminCatalogItemProps extends CatalogTilesData {}

const AdminCatalogItem: React.FC<AdminCatalogItemProps> = (props: AdminCatalogItemProps) => {
  const { exposureParamsEnabled } = useExposureParams();

  const { adminPermissions } = useSelector((state: State) => state.authorization);

  const catalogItemPermissions = adminPermissions.find((permissions) => permissions.shortName === props.shortName);

  const canAccess =
    props.permissions?.length && catalogItemPermissions
      ? props.permissions.some((permission) => catalogItemPermissions[permission as keyof AdminTileDetails])
      : false;

  const isDisabled = (props.disabled && !exposureParamsEnabled(props.name)) || !canAccess;

  return (
    <CatalogItem
      description={props.description}
      disabled={isDisabled}
      canAccess={canAccess}
      accessDeniedMessage={PERMISSION_MESSAGES.adminCatalogItem}
      path={props.path || ''}
      id={props.id || ''}
      icon={props.icon}
      title={props.name}
    />
  );
};

export default AdminCatalogItem;
