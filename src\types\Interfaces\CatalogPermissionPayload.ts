import { NumberLocale } from 'yup/lib/locale';

export interface Role {
  roleId: string;
}

export interface Domain {
  domainId: string;
}

export interface DomainPermission {
  domainId: string | null;
  roles: Role[];
}

export interface CatalogPermission {
  groupId: string;
  domain: DomainPermission[];
}

export interface CatalogTag {
  key: string;
  value: string;
  active: boolean;
  description: string;
}

export default interface CatalogPermissionpayload {
  name: string;
  level03Id: string;
  icon: string;
  description: string;
  enabled: boolean;
  approvalRequired: boolean;
  component: string;
  domain: Domain[] | null;
  tags: CatalogTag[];
  catalogPermissions: CatalogPermission[];
}
