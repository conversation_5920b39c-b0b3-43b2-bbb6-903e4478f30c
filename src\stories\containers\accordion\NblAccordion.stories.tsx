//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblAccordion from 'sharedComponents/Accordion/NblAccordion';

type StoryProps = ComponentProps<typeof NblAccordion>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Accordion/NblAccordion',
  component: NblAccordion,
  tags: ['autodocs'],
  argTypes: {
    summary: { type: 'string' },
    bgColor: { type: 'string' },
    hasDivider: { control: 'boolean' },
    defaultExpanded: { control: 'boolean' },
    expanded: { control: 'boolean' },
  },
};

export default meta;

const Template = () => {
  //Jsx
  return (
    <div style={{ display: 'flex', flexDirection: 'column', padding: '10px', fontSize: '0.9rem' }}>
      <>
        <h4>VM details are:</h4>
        <p>VM Name : neb-test-vm</p>
        <p>VM Type :Linux 8 and 9</p>
      </>
    </div>
  );
};
export const Divider: Story = {
  args: {
    summary: 'VM Details',
    hasDivider: true,
    bgColor: '#ccd5dc40',
    defaultExpanded: false,
  },
  render: (args) => (
    <NebulaTheme>
      <div style={{ width: '100%', height: '100%' }}>
        <NblAccordion {...args}>
          <Template />
        </NblAccordion>
      </div>
    </NebulaTheme>
  ),
};
