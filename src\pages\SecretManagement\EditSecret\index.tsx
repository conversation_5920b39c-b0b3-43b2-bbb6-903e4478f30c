import CreateNewSecret from 'componentsV2/SecretsManagement/CreateNewSecret';
import ViewHistory from 'componentsV2/SecretsManagement/ViewHistory';
import { useState } from 'react';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblRefreshProvider from 'sharedComponents/NblUtils/NblRefreshProvider';

export default function EditSecretPage() {
  const [state, setState] = useState(1);
  return (
    <NblFlexContainer height="auto" direction="column" overflowY="auto">
      <NblRefreshProvider refreshData={() => setState((prev) => prev + 1)}>
        <CreateNewSecret editMode />
        <ViewHistory key={state} />
      </NblRefreshProvider>
    </NblFlexContainer>
  );
}
