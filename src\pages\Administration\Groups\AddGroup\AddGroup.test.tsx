import { render, act, fireEvent } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import { MemoryRouter as Router, Routes, Route } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';

import AddGroup from './index';
import ThemeProvider from 'mock/ThemeProvider';
import { GetGroupsPermissions } from 'mock/Groups';
import * as api from 'api/static-data';
import PermissionService from 'api/ApiService/PermissionService';
import { GetAdminstrationCatalogItems, GetAdminTiles, GetAdminTileRoles } from 'mock/AdminTiles';

const mockStore = configureMockStore();

const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'groups', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/groups/register-ad-group'];

describe('AddGroup component', () => {
  let getGroupsPermissionsSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;
  let getAdminTilesSpy: jest.SpyInstance;
  let getAdminTileRolesSpy: jest.SpyInstance;

  beforeEach(async () => {
    getGroupsPermissionsSpy = jest.spyOn(PermissionService.prototype, 'getGroupsPermissions');
    getGroupsPermissionsSpy.mockResolvedValue(GetGroupsPermissions);

    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);

    getAdminTilesSpy = jest.spyOn(PermissionService.prototype, 'getAdminTiles');
    getAdminTilesSpy.mockResolvedValue(GetAdminTiles);

    getAdminTileRolesSpy = jest.spyOn(PermissionService.prototype, 'getAdminTileRoles');
    getAdminTileRolesSpy.mockResolvedValue(GetAdminTileRoles);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <Router initialEntries={BASE_ROUTE}>
            <ReduxProvider store={store}>
              <ThemeProvider>
                <AddGroup />
              </ThemeProvider>
            </ReduxProvider>
          </Router>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });

  test('Should render the Register ad form', async () => {
    const { getByText, getByLabelText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AddGroup />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );

    expect(getByText('Register AD group')).toBeInTheDocument();
    expect(getByLabelText('Group Name *')).toHaveValue('');
    expect(getByLabelText('Description')).toHaveValue('');
  });

  test('Should navigate to adminstration groups page when user clicks on  Cancel button', async () => {
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <Routes>
                <Route path="/administration/groups/register-ad-group" element={<AddGroup />}></Route>
                <Route path="/administration/groups" element={<div>Adminstration Groups</div>} />
              </Routes>
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    fireEvent.click(getByText('Cancel'));
    expect(getByText('Adminstration Groups')).toBeInTheDocument();
  });
});
