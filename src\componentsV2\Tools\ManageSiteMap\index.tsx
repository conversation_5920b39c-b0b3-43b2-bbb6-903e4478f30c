import { useEffect, useState } from 'react';
import SiteMapTable from '../AddSiteMapForm/SiteMapTable';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { useDispatch } from 'react-redux';
import { useApiService } from 'api/ApiService/context';
import { toast } from 'react-toastify';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

const ManageSiteMap: React.FunctionComponent = () => {
  const dispatch = useDispatch();
  const { apiSiteMapService } = useApiService();
  const [data, setData] = useState<any[]>([]);
  const [placeHolderMessage, setPlaceHolderMessage] = useState<String>('Loading...');

  const getSiteMapTableData = async () => {
    dispatch(showSpinner({ id: SPINNER_IDS.tools, status: true, message: 'Sitemaps are being fetched...' }));
    const siteMapData = await apiSiteMapService.getAllSiteMapData();
    dispatch(showSpinner({ id: SPINNER_IDS.tools, status: false, message: '' }));
    let sitemapArr: any[] = [];
    if (siteMapData?.data?.length) {
      siteMapData.data.forEach((item: any) => {
        let obj: any = item;
        obj.id = item._id;
        sitemapArr.push(obj);
      });
    } else setPlaceHolderMessage('No data available!');
    setData(sitemapArr);
  };

  const handleDelete = async (rowID: any) => {
    const deletedSiteMap = await apiSiteMapService.deleteSiteMapData(data.find((x) => x.id == rowID)._id);
    if (deletedSiteMap) {
      toast.success('Sitemap deleted successfully!', { position: toast.POSITION.BOTTOM_CENTER });
      getSiteMapTableData();
    }
  };

  useEffect(() => {
    getSiteMapTableData();
  }, []);

  return (
    <>
      {data.length ? (
        <NblFlexContainer height="100%">
          <SiteMapTable siteMapData={data} rowSize="10" deleteRow={handleDelete} />
        </NblFlexContainer>
      ) : (
        placeHolderMessage
      )}
    </>
  );
};

export default ManageSiteMap;
