import { Box, Typography, styled } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

interface styledProps {
  color?: string;
  size?: number;
  circle?: string;
  value?: string;
}

export const Container = styled(Box)({
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  position: 'relative',
  padding: '10px',
});

export const Title = styled(Typography)<styledProps>(({ color, theme }) => ({
  textAlign: 'left',
  ...theme.typography.subtitle1,
  fontWeight: theme.typography.fontWeightBold,
  letterSpacing: '0px',
  color: color,
  opacity: 1,
}));

export const LegendContainer = styled(Box)({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  gap: '6px',
});

export const LegendItem = styled(Box)({
  display: 'flex',
  alignItems: 'center',
});

export const LegendCircle = styled(Box)(({ color }) => ({
  width: 8,
  height: 8,
  borderRadius: '50%',
  background: `${color} 0% 0% no-repeat padding-box`,
  opacity: 1,
  marginRight: 6,
}));

export const LegendLabel = styled(Typography)<styledProps>(({ color, theme }) => ({
  textAlign: 'left',
  ...theme.typography.caption,
  letterSpacing: '0.48px',
  color: color,
  opacity: 0.5,
}));

export const CircleContainer = styled(Box)<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    width: '100%',
    height: '100%',
    borderRadius: '10px',
    padding: '5px',
    boxSizing: 'border-box',
    backgroundColor: theme.palette.secondary.shade1,
  };
});
export const Circles = styled(Box)<{ theme?: NebulaTheme }>(() => {
  return {
    transform: 'rotate(30deg)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    gap: '2px',
  };
});

export const Circle = styled(Box)<styledProps>(({ theme, color, size = 100 }) => {
  return {
    width: size,
    height: size,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: color,
    borderRadius: '50%',
    position: 'relative',
    [theme.breakpoints.down('2K')]: {
      width: size / 1.5,
      height: size / 1.5,
    },
  };
});

export const CircleContent = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: '100%',
  transform: 'rotate(-30deg)',
});

export const CircleValue = styled(Typography)<styledProps>(({ value, theme }) => ({
  ...theme.typography.subtitle1,
  letterSpacing: '0px',
  color: value,
  position: 'relative',
}));

export const CircleLabel = styled(Typography)<styledProps>(({ value, theme }) => {
  return {
    ...theme.typography.caption,
    letterSpacing: '0px',
    color: value,
    position: 'relative',
  };
});
