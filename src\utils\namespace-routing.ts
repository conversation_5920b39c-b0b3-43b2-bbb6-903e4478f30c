export function getBaseRoute() {
    const baseRoute = process.env.REACT_APP_ROUTE_BASE_NAME;
    if (baseRoute === "/" || baseRoute === "") {
        return "";
    } else {
        return '/' + baseRoute + '/';
    }
}

export function constructRoute(part: string) {
    return (getBaseRoute() + part).replaceAll('//', '/');
}

export function isProductionBuild () {
    return process.env.NODE_ENV === 'production';
}