//Each Item in list
export interface ItemProps {
  id: string;
  icon?: string;
  name: string;
  label: string;
  enabled: boolean;
}

export type SideBarMouseHandlerType = 'expand' | 'navigate';

//For ItemsList prop for each bar
export interface BarItemsProps {
  loading: boolean;
  items: ItemProps[];
}

//For ItemsList prop for each bar3
export interface Bar3ItemsProps {
  loading: boolean;
  items: { level3Item: ItemProps; level4Items: ItemProps[] }[];
}

//Each List in Bar
export interface ItemsListProps {
  loading?: boolean;
  itemsList: ItemProps[] | undefined;
  level: number;
  onLevel1Click?: (level1: ItemProps, position: number, handlerType: SideBarMouseHandlerType) => void;
  onLevel2Click?: (level2: ItemProps, position: number, handlerType: SideBarMouseHandlerType) => void;
  onLevel4Click?: (level4: ItemProps) => void;
  onMenuItemClick?: (menuItem: ItemProps) => void;
  selectedLevel1?: ItemProps;
  selectedLevel2?: ItemProps;
  selectedMenu?: ItemProps;
  sidebarExpanded?: boolean;
}

//For first bar
export interface Bar1Props {
  itemsList: BarItemsProps[];
  onLevel1Click: ItemsListProps['onLevel1Click'];
  onMenuItemClick: (tool: ItemProps) => void;
  onLogoClick: () => void;
  selectedLevel1: ItemProps;
  selectedMenu: ItemProps;
  sidebarExpanded: boolean;
}

//For second bar
export interface Bar2Props extends Omit<ItemsListProps, 'onLevel1Click' | 'onMenuItemClick'> {
  loading: boolean;
  selectedLevel1: ItemProps;
  selectedLevel2: ItemProps;
  sidebarExpanded: boolean;
  bar2ItemsPosition: number;
}

// For third bar
export interface Bar3Props extends Omit<ItemsListProps, 'loading' | 'itemsList' | 'onLevel1Click' | 'onMenuItemClick' | 'onLevel4Click'> {
  selectedLevel2: ItemProps;
  sidebarExpanded: boolean;
  bar3Items: Bar3ItemsProps;
  bar3ItemsPosition: number;
  onLevel4Click: (level3: ItemProps, level4: ItemProps) => void;
}

export interface NblSideBarProps {
  bar1Items: BarItemsProps[];
  bar2Items: BarItemsProps;
  bar3Items: Bar3ItemsProps;
  onLogoClick?: () => void;
  onLevel1Click: (level1: ItemProps, handlerType: SideBarMouseHandlerType) => void;
  onLevel2Click: (level1: ItemProps, level2: ItemProps, handlerType: SideBarMouseHandlerType) => void;
  onLevel4Click: (level1: ItemProps, level2: ItemProps, level3: ItemProps, level4: ItemProps) => void;
  onMenuItemClick: (menuItem: ItemProps) => void;
  defaultLevel1?: ItemProps;
  defaultLevel2?: ItemProps;
}
