import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import * as api from 'api/static-data';
import Catalog from 'components/Catalog';
import VMSizing from '.';

jest.mock('api/static-data', () => ({
  getVMSizingData: jest.fn(),
}));

jest.mock('components/Catalog', () => ({
  __esModule: true,
  default: jest.fn(() => <div>Mocked Catalog</div>),
}));

describe('VMSizing', () => {
  it('renders a "No items to display" message when no data is fetched', async () => {
    (api.getVMSizingData as jest.Mock).mockResolvedValue([]);

    render(<VMSizing />);

    await waitFor(() => expect(screen.getByText('No items to display')).toBeInTheDocument());
  });

  it('handles error case gracefully', async () => {
    (api.getVMSizingData as jest.Mock).mockRejectedValue(new Error('API error'));

    render(<VMSizing />);

    await waitFor(() => expect(screen.getByText('No items to display')).toBeInTheDocument());
  });
});
