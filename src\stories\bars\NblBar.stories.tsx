//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblBar from 'sharedComponents/NblBar';

type StoryProps = ComponentProps<typeof NblBar>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Bar/NblBar',
  component: NblBar,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    color: { control: 'radio', options: ['success', 'warning', 'error'], type: 'string' },
    orientation: { control: 'radio', options: ['vertical', 'horizontal'], type: 'string' },
    length: { type: 'string' },
  },
};

export default meta;

export const Bar: Story = {
  args: {
    orientation: 'vertical',
    color: 'warning',
    length: '100px',
  },
  render: (args) => (
    <NebulaTheme>
      <NblBar {...args} />
    </NebulaTheme>
  ),
};
