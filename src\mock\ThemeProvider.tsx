import { useMemo } from 'react';

// material-ui
// eslint-disable-next-line no-unused-vars
import { CssBaseline, StyledEngineProvider, ThemeOptions, PaletteMode } from '@mui/material';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { presetPalettes } from '@ant-design/colors';

// project import
import ThemeOption from 'mantis/themes/theme';
import Typography from 'mantis/themes/typography';
import CustomShadows from 'mantis/themes/shadows';
import componentsOverride from 'mantis/themes/overrides';
import { GetUseThemeConfig } from './Theme';

// ==============================|| Mocked THEME ||============================== //

type ThemeCustomizationProps = {
  children: React.ReactElement;
};

const Palette = (mode: PaletteMode | undefined) => {
  const colors = presetPalettes;
  const paletteColor = ThemeOption(colors);

  return createTheme({
    mode,
    ...GetUseThemeConfig,
    palette: {
      ...paletteColor,
      ...GetUseThemeConfig.palette,
    },
  } as ThemeOptions);
};

export default function ThemeCustomization({ children }: ThemeCustomizationProps) {
  const theme = Palette('light');

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const themeTypography = Typography(`'Public Sans', sans-serif`);
  const themeCustomShadows = useMemo(() => CustomShadows(), [theme]);

  const themeOptions = useMemo(
    () => ({
      breakpoints: {
        values: {
          xs: 0,
          sm: 768,
          md: 1024,
          lg: 1266,
          xxl: 1500,
          xl: 1536,
          '2K': 1536,
          fullHD: 1920,
        },
      },
      direction: 'ltr',
      mixins: {
        toolbar: {
          minHeight: 60,
          paddingTop: 8,
          paddingBottom: 8,
        },
      },
      palette: theme.palette,
      customShadows: themeCustomShadows,
      typography: themeTypography,
    }),
    [theme, themeTypography, themeCustomShadows]
  );

  const themes = createTheme(themeOptions as ThemeOptions);
  themes.components = componentsOverride(themes);

  return (
    <StyledEngineProvider injectFirst>
      <ThemeProvider theme={themes}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </StyledEngineProvider>
  );
}
