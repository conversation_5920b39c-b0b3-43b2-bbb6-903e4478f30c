import * as Yup from 'yup';

const validationSchema = Yup.object({
  rows: Yup.array().of(
    Yup.object({
      userEmail: Yup.string()
        .matches(/@charter\.com$/, 'Email must be from @charter.com')
        .required('Email is required'),
      pid: Yup.string().required('PID is required'),
      name: Yup.string().required('Name is required'),
    }).when('editable', {
      is: true, // Apply validation only if the row is editable
      then: Yup.object({
        userEmail: Yup.string().email('Please enter a valid email').required('Email is required'),
        pid: Yup.string().required('PID is required'),
        name: Yup.string().required('Name is required'),
      }),
      otherwise: Yup.object({
        userEmail: Yup.string().notRequired(),
        pid: Yup.string().notRequired(),
        name: Yup.string().notRequired(),
      }),
    })
  ),
});

export default validationSchema
