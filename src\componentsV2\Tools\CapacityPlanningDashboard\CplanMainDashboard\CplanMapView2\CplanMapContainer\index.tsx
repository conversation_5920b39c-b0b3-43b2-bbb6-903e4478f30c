import { ComposableMap, Geographies, Geography, ZoomableGroup } from 'react-simple-maps';
import cdnJson from './cdnData.json';
// eslint-disable-next-line
import { NebulaTheme } from 'NebulaTheme/type';
import { geoAlbersUsa, geoCentroid } from 'd3-geo';
import { useTheme } from '@mui/material';
import { CalculatedFacility } from '../../../utils/types';
import FacilityMarker from './FacilityMarker';
import { useMemo, useState } from 'react';
import statesInfo from '../../../utils/statesinfo';
import StateMarker from './StateMarker';
import { moveItemToLastByIndex } from '../../../utils/utilization';

const geoUrl = cdnJson;
const projection = geoAlbersUsa();
const isValidCoordinate = ([lat, long]: number[]) => !!projection([long, lat]);
const US_CENTER: [number, number] = [-96, 39];

interface CplanMapContainerProps {
  facilities: CalculatedFacility[];
}

const CplanMapContainer: React.FunctionComponent<CplanMapContainerProps> = ({ facilities }) => {
  const theme: NebulaTheme = useTheme();
  const [position, setPosition] = useState({ zoom: 1, coordinates: US_CENTER, statename: '' });
  const [selectedFacilityId, setSelectedFacilityId] = useState<number | null>(null);

  const handleMarkerClick = (facilityId: number) => {
    setSelectedFacilityId((preId) => (preId === facilityId ? null : facilityId));
  };

  const handleGeoClick = (geo: any) => {
    const isZoomed = position.zoom > 1;
    const statename = geo.properties.name;
    const centroid = geoCentroid(geo);
    setPosition((prev) => {
      if (isZoomed && prev.statename === statename) {
        return { coordinates: US_CENTER, zoom: 1, statename: '' };
      }
      return { coordinates: [centroid[0], centroid[1]], zoom: 2, statename: statename };
    });
  };

  const handleStateClick = (statename: string, coordinates: [number, number]) => {
    setPosition({ coordinates: [coordinates[0], coordinates[1]], zoom: 2, statename: statename });
  };

  const handleMoveEnd = (position: any) => {
    setPosition((prev) => ({ statename: prev.statename, ...position }));
  };

  // State Markers Logic Starts below

  const StatesMarker = useMemo(() => {
    const statecodes = [
      ...new Set(
        facilities.map((fac) => {
          if (fac.resources?.length) return fac.statecode;
        })
      ),
    ];
    let stateUtilizationMap: any = {};
    facilities.forEach(({ statecode, resources }) => {
      const maxUtilized = resources.reduce((max, res) => {
        const resourceMax = Math.max(res?.cpuUtilized, res?.memoryUtilized, res?.storageUtilized);
        return Math.max(max, resourceMax);
      }, 0);
      stateUtilizationMap[statecode] = Math.max(stateUtilizationMap[statecode] || 0, maxUtilized);
    });
    const markers = statesInfo
      .filter((st) => statecodes.includes(st.state_short))
      .map((st: any) => {
        if (isValidCoordinate([st.latitude, st.longitude])) {
          return (
            <StateMarker
              key={st.state_short}
              statename={st.state}
              statecode={st.state_short}
              latitude={st.latitude}
              longitude={st.longitude}
              maxUtilized={(st && stateUtilizationMap[st.state_short]) || null}
              onMarkerClick={handleStateClick}
            />
          );
        }
      });

    return markers;
  }, [facilities]);

  // State Marker Logic Ends Above

  // Facility Marker Logic starts below

  const FacilityMarkers = useMemo(() => {
    const clickedMarkerIndex = facilities.findIndex((fac) => fac.facilityid === selectedFacilityId);
    const markers = moveItemToLastByIndex(facilities, clickedMarkerIndex).map((fac: CalculatedFacility) => {
      if (isValidCoordinate([fac.latitude, fac.longitude])) {
        return (
          <FacilityMarker
            key={fac.facilityid}
            facility={fac}
            isSelected={fac.facilityid === selectedFacilityId}
            onMarkerClick={handleMarkerClick}
          />
        );
      }
    });
    return markers;
  }, [selectedFacilityId, facilities]);

  return (
    <ComposableMap
      id="exportSection"
      projection="geoAlbersUsa"
      viewBox={'-50 75 1000 525'}
      preserveAspectRatio="xMidYMidmeet"
      projectionConfig={{ scale: 925 }}
    >
      <ZoomableGroup center={position.coordinates} zoom={position.zoom} onMoveEnd={handleMoveEnd}>
        <Geographies geography={geoUrl}>
          {({ geographies }) => (
            <>
              {geographies.map((geo: any) => (
                <Geography
                  key={geo.rsmKey}
                  stroke={theme.palette.secondary.main}
                  onClick={() => handleGeoClick(geo)}
                  geography={geo}
                  fill={'#6A7B88'}
                  style={{
                    default: { fill: '#6A7B88', outline: 'none' },
                    hover: { fill: '#5DAEF0', outline: 'none' },
                  }}
                />
              ))}
            </>
          )}
        </Geographies>
        {position.zoom <= 1.5 && facilities?.length && StatesMarker}
        {position.zoom > 1.5 && facilities?.length && FacilityMarkers}
      </ZoomableGroup>
    </ComposableMap>
  );
};

export default CplanMapContainer;
