import { StyledChip } from 'componentsV2/Tools/NebulaDashboard/components/RequestsListTable/styledRequestTable';
import { statusStyles } from 'componentsV2/Tools/NebulaDashboard/utility';

interface StatusChipProps {
  status: string;
}
const GetStatus: React.FunctionComponent<StatusChipProps> = ({ status }) => {
  const statusStyle = statusStyles(status);
  return (
    <StyledChip
      label={statusStyle?.label}
      sx={{
        color: statusStyle?.color,
        backgroundColor: statusStyle?.backgroundColor,
      }}
    />
  );
};

export default GetStatus;
