import React from 'react';

import NblFlexContainer from '../NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import { StyledDiv, StyledDivider } from './styled';

interface NblORDividerProps {
  height: number;
  margin?: string;
}

const NblORDivider: React.FC<NblORDividerProps> = ({
  height,
  margin
}) => {
  //JSX
  return (
    <NblFlexContainer direction={'column'} alignItems="center" position="relative" width='auto' margin={margin}>
      <StyledDivider height={height / 2} margin={'0 0 10px'} />
      <StyledDiv>
        <NblTypography variant={'body3'} color={'shade1'}>
          OR
        </NblTypography>
      </StyledDiv>
      <StyledDivider height={height / 2} margin={'10px 0 0'} />
    </NblFlexContainer>
  );
};

export default NblORDivider;
