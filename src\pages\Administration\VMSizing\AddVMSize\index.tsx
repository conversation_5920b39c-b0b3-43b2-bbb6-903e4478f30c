import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import useNblNavigate from 'hooks/useNblNavigate';
import WrappedVMSizeForm from 'componentsV2/Administration/VMSize/VMSizeForm';

const AddVMSize = () => {
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const navigateToRoleDetails = () => {
    navigate('/administration/VM-sizing');
  };

  return <WrappedVMSizeForm permissions={{}} onSuccess={navigateToRoleDetails} onClose={navigateToRoleDetails} />;
};

export default AddVMSize;
