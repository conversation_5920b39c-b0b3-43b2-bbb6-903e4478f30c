import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import VCenterDetails from '.';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import '@testing-library/jest-dom';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

const mockStore = configureMockStore([thunk]);

export const LookupPayload = {
  cloudDatacenter: 'STAMP-VMWARE',
  domain: 'APVS-RED',
  vCenterHost: 'cdptpabb04-s-vcsa01.stage.charter.com',
  vCenterPassword: 'Q2hhbmdlTTMh',
  vCenterPort: '443',
  vCenterProtocol: 'https',
  vCenterUser: '****************************',
  vCenterName: 'CouderSport',
};

export const mockResponseData = {
  cloudDetails: [
    {
      cloudId: 'cloud-001',
      cloudName: 'AWS Mumbai',
    },
  ],
};

jest.mock('hooks/useGetDomainData', () => () => ({
  domainData: [],
}));

jest.mock('./useVCenterCommonFields', () => () => [
  { title: 'VCenter Name', value: 'VC1', span: 1 },
  { title: 'VCenter Host', value: 'vc-host.example.com', span: 1 },
  { title: 'VCenter Port', value: 443, span: 1 },
]);

jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  useNblForms: () => ({
    nblFormProps: {
      setFieldValue: jest.fn(),
    },
    nblFormValues: {
      cloudDetails: [],
    },
  }),
}));

describe('VCenterDetails Component', () => {
  const mockSetCloudDatacenter = jest.fn();

  const defaultStore = mockStore({
    vcenterdetails: {
      data: {
        submitted: {},
        apiResponse: {
          cloudDetails: [
            { cloudName: 'Datacenter 1', cloudId: 'dc1' },
            { cloudName: 'Datacenter 2', cloudId: 'dc2' },
          ],
        },
      },
    },
  });

  const renderComponent = (store = defaultStore, cloudDatacenter = '', responseData = mockResponseData) => {
    return render(
      <ReduxProvider store={store}>
        <NebulaThemeProvider>
          <VCenterDetails
            cloudDatacenter={cloudDatacenter}
            setCloudDatacenter={mockSetCloudDatacenter}
            payload={LookupPayload}
            responseData={responseData}
          />
        </NebulaThemeProvider>
      </ReduxProvider>
    );
  };

  test('renders vCenter fields', () => {
    renderComponent();

    expect(screen.getByText('VCenter Name')).toBeInTheDocument();
    expect(screen.getByText('VC1')).toBeInTheDocument();
    expect(screen.getByText('VCenter Host')).toBeInTheDocument();
    expect(screen.getByText('vc-host.example.com')).toBeInTheDocument();
    expect(screen.getByText('VCenter Port')).toBeInTheDocument();
    expect(screen.getByText('443')).toBeInTheDocument();
  });

  test('handles missing cloudDetails safely', () => {
    const store = mockStore({
      vcenterdetails: {
        data: {
          submitted: {},
          apiResponse: {},
        },
      },
    });

    renderComponent(store);
  });

  test('does not crash with undefined apiResponse', () => {
    const store = mockStore({
      vcenterdetails: {
        data: {
          submitted: {},
        },
      },
    });

    renderComponent(store);
  });
});
