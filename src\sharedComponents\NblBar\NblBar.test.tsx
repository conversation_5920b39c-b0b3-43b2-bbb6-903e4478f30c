import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblBar, { NblBarProps } from '.';

describe('NblDropdown component', () => {
  const props: NblBarProps = {
    length: '40px',
    orientation: 'vertical',
    color: 'success',
  };

  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblBar {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
