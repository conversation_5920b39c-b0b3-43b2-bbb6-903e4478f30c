import { SizeTypes, DataSize } from './datasizes';

export enum MetricType {
  CPU = 'CPU',
  Memory = 'Memory',
  Storage = 'Storage',
}

export enum MetricTypeOnly {
  CPUOnly = 'CPU Only',
  MemoryOnly = 'Memory Only',
  StorageOnly = 'Storage Only',
}
export enum StatusAndSite {
  Status = 'Status',
  Site = 'Vcenter',
}
export enum MetricUtilization {
  CPU = 'CPU Utilization',
  Memory = 'Memory Utilization',
  Storage = 'Storage Utilization',
}

export const AllMetricTypes = [MetricType.CPU, MetricType.Memory, MetricType.Storage];

export enum ValueType {
  Bytes = 'B',
  Cores = ' cores',
  Hertz = 'Hz',
  Number = '',
  Percent = '%',
}
export const SizeScale = {
  [ValueType.Hertz]: 1000,
  [ValueType.Bytes]: 1024,
  [ValueType.Percent]: 1,
  [ValueType.Number]: 1,
  [ValueType.Cores]: 1,
};
export interface StatisticInterface {
  raw: string; // Raw string name from Vrops
  name: string; // Statistic name used for titles
  size: DataSize;
  valueType: ValueType;
  description?: string | undefined;
}
function determineMetric(statname: string) {
  if (statname.startsWith('cpu')) return MetricType.CPU; //'CPU'
  if (statname.startsWith('mem')) return MetricType.Memory; //'Memory'
  if (statname.startsWith('diskspace')) return MetricType.Storage; //'Storage'
}
export class StatisticInfo implements StatisticInterface {
  // Constructor and properties
  raw: string;
  name: string;
  size: DataSize;
  valueType: ValueType;
  metricType?: MetricType;
  description?: string;
  constructor(raw: string, name: string, description: string | undefined, size: DataSize, type: ValueType) {
    this.raw = raw;
    this.name = name;
    this.size = size;
    this.valueType = type;
    this.metricType = determineMetric(raw); // from function it will return 'CPU' or 'Memory' or'Storage'
    this.description = description;
  }
  // Static constructor
  static createFromInterface(si: StatisticInterface): StatisticInfo {
    return new StatisticInfo(si.raw, si.name, si.description, si.size, si.valueType);
  }
  get statScale() {
    return SizeScale[this.valueType];
  }
  formatScale(v: number) {
    return this.size.formatScale(v, this.valueType, SizeScale[this.valueType]);
  }
}
export const StatisticTypes = {
  cpu_capacity_usagepct_average: StatisticInfo.createFromInterface({
    raw: 'cpu|capacity_usagepct_average',
    name: 'CPU Capacity Usage',
    description: 'Percent capacity used.',
    size: SizeTypes.Default,
    valueType: ValueType.Percent,
  }),

  cpu_corecount_provisioned: StatisticInfo.createFromInterface({
    raw: 'cpu|corecount_provisioned',
    name: 'CPU Total Core Count',
    description: 'CPU Total Core Count.',
    size: SizeTypes.Default,
    valueType: ValueType.Number,
  }),
  cpu_vcpus_allocated_on_all_vms: StatisticInfo.createFromInterface({
    raw: 'cpu|vcpus_allocated_on_all_vms',
    name: 'CPU Provisioned',
    description: 'Total CPUs provisioned in cores',
    size: SizeTypes.Default,
    valueType: ValueType.Cores,
  }),
  cpu_usage_average: StatisticInfo.createFromInterface({
    raw: 'cpu|usage_average',
    name: 'CPU Capacity Usage',
    description: 'Percent capacity used.',
    size: SizeTypes.Default,
    valueType: ValueType.Percent,
  }),

  mem_host_usable: StatisticInfo.createFromInterface({
    raw: 'mem|host_usable',
    name: 'Memory Total Capacity',
    description: 'Host memory capacity in kilobytes.',
    size: SizeTypes.Kilo,
    valueType: ValueType.Bytes,
  }),
  mem_host_usage: StatisticInfo.createFromInterface({
    raw: 'mem|host_usage',
    name: 'Memory Usage',
    description: 'Host memory use in kilobytes.',
    size: SizeTypes.Kilo,
    valueType: ValueType.Bytes,
  }),
  mem_guest_provisioned: StatisticInfo.createFromInterface({
    raw: 'mem|guest_provisioned',
    name: 'Memory Usage',
    description: 'Host memory use in kilobytes.',
    size: SizeTypes.Kilo,
    valueType: ValueType.Bytes,
  }),
  mem_host_provisioned: StatisticInfo.createFromInterface({
    raw: 'mem|host_provisioned',
    name: 'Memory Provisioned',
    description: 'Total memory allocated to hosts',
    size: SizeTypes.Kilo,
    valueType: ValueType.Bytes,
  }),

  mem_haTotalCapacity_average: StatisticInfo.createFromInterface({
    raw: 'mem|haTotalCapacity_average',
    name: 'Memory Usable Capacity',
    description:
      'The usable memory resources available for the virtual machines after considering reservations for vSphere HA and other vSphere services.',
    size: SizeTypes.Kilo,
    valueType: ValueType.Bytes,
  }),
  mem_usage_average: StatisticInfo.createFromInterface({
    raw: 'mem|usage_average',
    name: 'Memory Usage Capacity',
    description: 'Percent capacity used.',
    size: SizeTypes.Default,
    valueType: ValueType.Percent,
  }),

  diskspace_total_capacity: StatisticInfo.createFromInterface({
    raw: 'diskspace|total_capacity',
    name: 'Disk Total Capacity',
    description: 'Total storage space available on the connected vSphere datastores.',
    size: SizeTypes.Giga,
    valueType: ValueType.Bytes,
  }),
  diskspace_total_usage: StatisticInfo.createFromInterface({
    raw: 'diskspace|total_usage',
    name: 'Disk Usage Capacity',
    description: 'Total storage space available on the connected vSphere datastores.',
    size: SizeTypes.Giga,
    valueType: ValueType.Bytes,
  }),
  diskspace_total_provisioned: StatisticInfo.createFromInterface({
    raw: 'diskspace|total_provisioned',
    name: 'Disk Provisioned Capacity',
    description: 'Total storage space allocated to resource.',
    size: SizeTypes.Giga,
    valueType: ValueType.Bytes,
  }),
  diskspace_provisionedSpace: StatisticInfo.createFromInterface({
    raw: 'diskspace|provisionedSpace',
    name: 'Disk Total Capacity',
    description: 'Total storage space available on the connected vSphere datastores.',
    size: SizeTypes.Giga,
    valueType: ValueType.Bytes,
  }),
  disk_usage_average: StatisticInfo.createFromInterface({
    raw: 'disk|usage_average',
    name: 'Disk Usage Capacity',
    description: 'Percent capacity used.',
    size: SizeTypes.Default,
    valueType: ValueType.Percent,
  }),
  disk_usage_average_vm: StatisticInfo.createFromInterface({
    raw: 'guestfilesystem|percentage_total',
    name: 'Disk Usage Capacity VM',
    description: 'Percent capacity used.',
    size: SizeTypes.Default,
    valueType: ValueType.Percent,
  }),
};
export const NormalizedStats = {
  [MetricType.CPU]: {
    total_capacity: StatisticTypes.cpu_corecount_provisioned,
    usage: StatisticTypes.cpu_capacity_usagepct_average,
    capacity_provisioned: StatisticTypes.cpu_vcpus_allocated_on_all_vms,
  },
  [MetricType.Storage]: {
    total_capacity: StatisticTypes.diskspace_total_capacity,
    usage: StatisticTypes.diskspace_total_usage,
    capacity_provisioned: StatisticTypes.diskspace_total_provisioned,
  },
  [MetricType.Memory]: {
    total_capacity: StatisticTypes.mem_host_usable,
    usage: StatisticTypes.mem_host_usage,
    capacity_provisioned: StatisticTypes.mem_host_provisioned,
  },
};

export const NormalizedStatsVM = {
  [MetricType.CPU]: {
    total_capacity: StatisticTypes.cpu_corecount_provisioned,
    usage: StatisticTypes.cpu_usage_average,
  },
  [MetricType.Storage]: {
    total_capacity: StatisticTypes.diskspace_provisionedSpace,
    usage: StatisticTypes.disk_usage_average_vm,
  },
  [MetricType.Memory]: {
    total_capacity: StatisticTypes.mem_guest_provisioned,
    usage: StatisticTypes.mem_usage_average,
  },
};
