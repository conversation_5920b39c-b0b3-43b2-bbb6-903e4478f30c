import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Stack } from '@mui/material';

import Tabs from 'components/Tabs';
import ConfirmationDialog from 'components/ConfirmationDialog';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { CatalogTab } from 'types';

type CatalogTabProps = {
  children?: React.ReactElement;
  TABS: CatalogTab[];
};

const CatalogTabs: React.FunctionComponent<CatalogTabProps> = ({ children, TABS }: CatalogTabProps) => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const [showNavigateConfirmDialog, setShowNavigationConfirmDialog] = useState<{ open: boolean; url: string }>({ open: false, url: '' });
  const { showNavigateWarning } = useSelector((state: State) => state.menu);

  const onTabChangeHandler = (selectedTabIndex: number) => {
    if (showNavigateWarning) {
      setShowNavigationConfirmDialog({ open: true, url: TABS[selectedTabIndex]['url'] });
    } else {
      navigate(TABS[selectedTabIndex]['url']);
    }
  };

  const onConfirmHandler = () => {
    onCloseHandler();
    navigate(showNavigateConfirmDialog.url);
  };

  const onCloseHandler = () => {
    setShowNavigationConfirmDialog({ open: false, url: '' });
  };

  const routeSegments = pathname.split('/');
  const selectedTabIndex = TABS.findIndex((tab: any) => routeSegments.includes(tab.id));
  return (
    <Stack>
      <Tabs currentTab={selectedTabIndex} onTabChangeHandler={onTabChangeHandler} tabsList={TABS} />
      {children}
      <ConfirmationDialog
        open={showNavigateConfirmDialog.open}
        title={'Confirmation'}
        content={'Do you really want to proceed without submitting the request?'}
        confirmationText={'Proceed'}
        onConfirm={onConfirmHandler}
        onClose={onCloseHandler}
        canMaximize={false}
      />
    </Stack>
  );
};

export default CatalogTabs;
