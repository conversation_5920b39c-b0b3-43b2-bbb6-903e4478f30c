import { act, render, screen } from '@testing-library/react';
import { MemoryRouter as Router } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import AddApprovalDetailsTable from '.';
import ThemeProvider from 'mock/ThemeProvider';

jest.mock('react-toastify');

describe('Render Approval Data Table form', () => {
  const handleData = jest.fn();
  const data = [{ id: 1, groupId: '1001', approvalGroupName: 'Group-1', level: 1, approverCount: 2 }];

  const mockStore = configureMockStore();
  const store = mockStore({
    common: {
      isDialogMaximized: false,
    },
  });

  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <Router>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AddApprovalDetailsTable data={data} setApprovalDetails={handleData} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );

    expect(screen.getByText('Approval Group')).toBeInTheDocument();
    expect(screen.getByText('Approver Count')).toBeInTheDocument();
    expect(screen.getByText('Approver Level')).toBeInTheDocument();
    expect(getByText('Actions')).toBeInTheDocument();
  });
});
