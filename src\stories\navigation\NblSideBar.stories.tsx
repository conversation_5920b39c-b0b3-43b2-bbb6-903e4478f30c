// eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
// eslint-disable-next-line no-unused-vars
import { Bar3ItemsProps, BarItemsProps, ItemProps } from 'sharedComponents/NblNavigation/NblSidebar/interface';
import { bar1ItemsStatic, bar2ItemsStatic, bar3ItemsStatic } from './sideBarStaticData';
import NblSideBar from 'sharedComponents/NblNavigation/NblSidebar';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import { Bar3Item, BarItem } from 'sharedComponents/NblNavigation/NblSidebar/BarItem';

type StoryProps = ComponentProps<typeof NblSideBar>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Navigation/NblSideBar',
  component: NblSideBar,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    onLevel1Click: { action: 'onLevel1Click', type: 'function' },
    onMenuItemClick: { action: 'onMenuItemClick', type: 'function' },
  },
};

export default meta;

const NblSideBarWrapper: React.FC<StoryProps> = (props) => {
  const bar1Items: BarItemsProps[] = bar1ItemsStatic;
  const [bar2Items, setBar2Items] = useState<BarItemsProps>({
    loading: false,
    items: [new BarItem()],
  });
  const [bar3Items, setBar3Items] = useState<Bar3ItemsProps>({
    loading: false,
    items: [new Bar3Item()],
  });

  const onLevel1Click = (item: ItemProps) => {
    setBar2Items({ loading: true, items: [new BarItem()] });
    const time = setTimeout(() => {
      setBar2Items({ loading: false, items: bar2ItemsStatic[item.name] });
      clearTimeout(time);
    }, 1000);
  };
  const onLevel2Click = (level1: ItemProps, level2: ItemProps) => {
    setBar3Items({ loading: true, items: [new Bar3Item()] });
    const time2 = setTimeout(() => {
      setBar3Items({ loading: false, items: bar3ItemsStatic[level2.name] });
      clearTimeout(time2);
    }, 1000);
  };

  return (
    <div style={{ height: '100vh', overflow: 'hidden' }}>
      <NblSideBar
        {...props}
        bar1Items={bar1Items}
        bar2Items={bar2Items}
        bar3Items={bar3Items}
        onLevel1Click={onLevel1Click}
        onLevel2Click={onLevel2Click}
      />
    </div>
  );
};

export const Default: Story = {
  args: {
    onLevel4Click: () => {},
    onMenuItemClick: () => {},
  },
  render: (args) => (
    <NebulaTheme>
      <NblSideBarWrapper {...args} />
    </NebulaTheme>
  ),
};
