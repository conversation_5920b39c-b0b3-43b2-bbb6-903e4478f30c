import * as yup from 'yup';

// eslint-disable-next-line no-unused-vars
import { yupMatchesParams } from 'utils/common';

const CreateCorpnetVMSchema = (isWindowsCorpnet: boolean) =>
  yup.object().shape({
    projectName: yup.string().required('Project is required'),
    domain: yup.string().required('Domain is required'),
    size: yup.string().required('VM Size is required'),
    vmCount: yup.number().required('VM Count is required'),
    description: yup
      .string()
      .trim()
      .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage)
      .required('Description is required'),
    targetLayout: yup.string().required('OS Version is required'),
    patchCycle: yup.string().required('Patch Cycle is required'),
    backupOption: yup.string().required('Backup Option is required'),

    // Only required for Linux Corpnet
    driveOwner: yup.string().when([], {
      is: () => !isWindowsCorpnet,
      then: yup.string().required('Service Account is required'),
      otherwise: yup.string().notRequired(),
    }),

    driveGroup: yup.string().when([], {
      is: () => !isWindowsCorpnet,
      then: yup.string().required('ADM Group is required'),
      otherwise: yup.string().notRequired(),
    }),

    appSearch: yup.string().required('App Search is required'),
    datacenter: yup.string().required('Datacenter is required'),
    resourceId: yup.string().required('Cluster is required'),
    network: yup.string().required('Network is required'),

    // Only required for Windows Corpnet
    adGroup: yup.string().when([], {
      is: () => isWindowsCorpnet,
      then: yup.string().required('AD Group is required'),
      otherwise: yup.string().notRequired(),
    }),

    dbName: yup.string().when('appTier', {
      is: 'd',
      then: yup.string().required('Database name is required'),
      otherwise: yup.string(),
    }),

    isValidDisk: yup.boolean().oneOf([true]),
  });

export default CreateCorpnetVMSchema;
