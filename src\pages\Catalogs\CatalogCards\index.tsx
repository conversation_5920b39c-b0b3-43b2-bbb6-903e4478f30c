import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Nbl<PERSON>pinner from 'sharedComponents/NblSpinner';
import useExposureParams from 'hooks/useExposureParams';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblCard from 'sharedComponents/NblCard';
import { setPermissions } from 'store/reducers/authorization';
import PermissionService from 'api/ApiService/PermissionService';
import { checkCanCreatePermission } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { GetLevel3PermissionsResponse } from 'api/ApiService/type';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { CatalogsItem } from 'store/reducers/catalogs';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import CatalogFavorites from './CatalogFavorites';
import { useApiService } from 'api/ApiService/context';
import { toast } from 'react-toastify';
import { PERMISSION_MESSAGES } from 'utils/constant';
import { initUserDataAsyncThunk } from 'store/reducers/user';
import { Dispatch } from 'store';
import NblTypography from 'sharedComponents/NblTypography';
import useNblNavigate from 'hooks/useNblNavigate';

interface CatalogCardsProps {
  catalogLevel1: string;
  catalogLevel2: string;
  level3CatalogItems: CatalogsItem[];
  getLevel2CatalogName: (level3ShortName?: string) => { catalogLevel2ShortName: string; catalogLevel2Name: string };
  getLevel4CatalogItems: (level3ShortName: string) => CatalogsItem[];
}

const CatalogCards: React.FunctionComponent<CatalogCardsProps> = ({
  catalogLevel1,
  catalogLevel2,
  level3CatalogItems,
  getLevel2CatalogName,
  getLevel4CatalogItems,
}) => {
  const navigate = useNblNavigate();
  const dispatch = useDispatch<Dispatch>();
  const { exposureParamsEnabled } = useExposureParams();
  const apiServices = useApiService();
  const favoriteItems = useSelector((state: State) => state.user.userDetails.favoriteTools);
  const [isPermissionsLoading, setIsPermissionLoading] = useState(true);
  const apiPermissionService = new PermissionService();
  const { permissions } = useSelector((state: State) => state.authorization);
  const [favoriteStates, setFavoriteStates] = useState<{ [key: string]: boolean }>({});

  const filterFavoriteItems = () => {
    if (catalogLevel2) {
      return favoriteItems.filter((items) => {
        const level2ShortName = items.formLink?.split('/')?.[1];
        return catalogLevel2 === level2ShortName;
      });
    } else if (catalogLevel1) {
      return favoriteItems.filter((items) => {
        const level1ShortName = items.formLink?.split('/')?.[0];
        return catalogLevel1 === level1ShortName;
      });
    }
    return [];
  };

  const fetchPermissions = async (level3CatalogShortnames: string[]) => {
    let permissionsState = {
      ...permissions,
    };
    setIsPermissionLoading(true);
    const promises = level3CatalogShortnames.map((shortname: string) => {
      const isPermissionsFetched = permissions?.[`'${catalogLevel1}'`]?.[`'${shortname}'`];
      if (!isPermissionsFetched) {
        return apiPermissionService.getLevel3MultiEnvPermissions(shortname);
      }
      return Promise.resolve().then(() => ({ status: false, data: {}, message: `Already fetched data for ${shortname}` }));
    });

    const results = await Promise.all(promises);
    results.map((result: GetLevel3PermissionsResponse, index: number) => {
      if (result && result.status && result.data) {
        permissionsState = {
          ...permissionsState,
          [`'${catalogLevel1}'`]: {
            ...permissionsState?.[`'${catalogLevel1}'`],
            [`'${level3CatalogShortnames[index]}'`]: Object.keys(result.data).reduce((acc, key) => {
              /**
               * we are storing the object keys in string format so we shouldn't get
               * any issue while accessing them when dot (.) is added in the catalog name
               */
              const stringKey = `'${key}'`;
              acc[stringKey] = result.data[key];
              return acc;
            }, {} as { [key: string]: { Permissions: string[] } }),
          },
        };
      }
    });

    dispatch(setPermissions(permissionsState));
    setIsPermissionLoading(false);
  };

  useEffect(() => {
    const level3CatalogShortnames = level3CatalogItems?.length
      ? level3CatalogItems.filter((catalog) => catalog.enabled !== false).map((catalog) => catalog.shortName)
      : [];
    fetchPermissions(level3CatalogShortnames);
  }, [level3CatalogItems]);

  useEffect(() => {
    const initialFavoriteStates = favoriteItems.reduce((acc, item) => {
      acc[item.shortName] = true;
      return acc;
    }, {} as { [key: string]: boolean });
    setFavoriteStates(initialFavoriteStates);
  }, [favoriteItems]);

  const catalogItemDisabled = (name: string, permissionsPath: string[], disabled: boolean) => {
    const canCreate = checkCanCreatePermission(permissionsPath);
    const isDisabled = (disabled && !exposureParamsEnabled(name)) || !canCreate;
    return {
      canCreate,
      isDisabled,
    };
  };

  const navigateToCatalogItem = (level2ShortName: string, level3ShortName: string, level4ShortName: string) => {
    navigate(`/${catalogLevel1}/${level2ShortName}/${level3ShortName}/${level4ShortName}`);
  };

  if (isPermissionsLoading) {
    return (
      <NblSpinner
        display="inline"
        spinnerData={[
          {
            id: 'catalogForms',
            message: 'Loading...',
            status: true,
          },
        ]}
      />
    );
  }

  const level2 = catalogLevel2 || 'All';

  const addFavourite = (level04ShortName: string) => {
    apiServices.apiUserService.addFavourites(level04ShortName).then((res) => {
      if (res.status) {
        setFavoriteStates((prevState) => ({ ...prevState, [level04ShortName]: true }));
        // add favoriteItems in the state
        dispatch(initUserDataAsyncThunk(apiServices.apiUserService));
        toast.success(res.data?.message || 'Favorite tool added successfully', {
          position: toast.POSITION.BOTTOM_CENTER,
        });
      } else {
        // Handle API failure
        toast.error(res.data?.message, {
          position: toast.POSITION.BOTTOM_CENTER,
        });
      }
    });
  };

  const removeFavourite = (level04ShortName: string) => {
    setFavoriteStates((prevState) => ({ ...prevState, [level04ShortName]: false }));
    apiServices.apiUserService.removeFavourites(level04ShortName).then((res) => {
      if (res.status) {
        // remove favoriteItems in the state
        dispatch(initUserDataAsyncThunk(apiServices.apiUserService));
        toast.success(res.data?.message || 'Favorite tool removed successfully', {
          position: toast.POSITION.BOTTOM_CENTER,
        });
      }
    });
  };

  const favoriteItem = filterFavoriteItems().map((item) => {
    return (
      <CatalogFavorites
        key={item._id}
        icon={item.icon}
        id={`${item.shortName}-favorites`}
        shortName={item.shortName}
        name={item.name}
        navigateToCatalogItem={() => {
          navigate(`/${item.formLink}`);
        }}
      />
    );
  });

  return (
    <NblFlexContainer direction="column">
      <NblFlexContainer direction="column" height="auto" margin="22px 0 0 0">
        {favoriteItem?.length > 0 && (
          <NblTypography variant="subtitle2" color="shade1">
            Favorites
          </NblTypography>
        )}
        <NblFlexContainer justifyContent="space-between">
          <NblFlexContainer overflowX="auto">{favoriteItem}</NblFlexContainer>
        </NblFlexContainer>
      </NblFlexContainer>
      <NblGridContainer columns={3} spacing={3} padding={'24px 0 32px'} alignContent={'start'}>
        {level3CatalogItems?.map((level3) => {
          const level3ShortName = level3.shortName;
          const level4CatalogItems = getLevel4CatalogItems(level3ShortName);
          return level4CatalogItems.map((level4) => {
            const { isDisabled, canCreate } = catalogItemDisabled(
              level4.name,
              [`'${catalogLevel1}'`, `'${level3ShortName}'`, `'${level4.shortName}'`],
              level4.enabled === false
            );
            return (
              <NblGridItem key={`${level2}-${level4.id}`}>
                <NblCard
                  isFavourite={favoriteStates[level4.shortName] || false}
                  chip={getLevel2CatalogName(level3ShortName).catalogLevel2Name}
                  color="primary"
                  description={level4.description}
                  expanded
                  icon={level4.icon}
                  id={`catalog-${level4.shortName}`}
                  label={level3.name}
                  onClickCard={() =>
                    navigateToCatalogItem(getLevel2CatalogName(level3ShortName).catalogLevel2ShortName, level3ShortName, level4.shortName)
                  }
                  onFavourite={() => {
                    favoriteStates[level4.shortName] ? removeFavourite(level4.shortName) : addFavourite(level4.shortName);
                  }}
                  shortName={level4.shortName}
                  title={level4.name}
                  width="auto"
                  disabled={isDisabled}
                  tooltipMessage={!canCreate ? PERMISSION_MESSAGES.serviceCatalogItem : isDisabled ? 'Coming Soon...' : ''}
                />
              </NblGridItem>
            );
          });
        })}
      </NblGridContainer>
    </NblFlexContainer>
  );
};

export default CatalogCards;
