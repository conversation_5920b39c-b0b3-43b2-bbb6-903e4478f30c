import React, { useEffect } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
//eslint-disable-next-line no-unused-vars
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblInputLabel from '../NblInputLabel';
import NblInputHelperText from '../NblInputHelperText';
import { CustomListbox, StyledAutocomplete, StyledMenuItem, StyledPaper, StyledPopper, StyledTextField, StyledTypography } from './styled';
import { truncateLabel } from '../common';

interface NblAutoCompleteProps {
  error?: boolean;
  label: string;
  name: string;
  options: any;
  value: string | number | any[] | undefined;
  placeholder: string;
  readOnly?: boolean;
  disabled?: boolean;
  maxLength?: number;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onChange: (value: any) => void;
  handleBlur?: React.FocusEventHandler<HTMLDivElement>;
  mandatory?: boolean;
  helperText?: string;
  onInputChange?: (value: string) => void;
  multiple?: boolean;
  onClick?: () => void;
  title?: string;
}

function NblAutoComplete({
  error,
  label,
  name,
  options = [],
  value,
  readOnly,
  disabled,
  maxLength,
  placeholder,
  onMouseEnter,
  onMouseLeave,
  onChange,
  handleBlur = () => {},
  mandatory = false,
  helperText = '',
  onInputChange,
  multiple,
  onClick,
  title,
}: NblAutoCompleteProps) {
  //hooks
  //to show default value if only one option in dropdown
  useEffect(() => {
    if (mandatory && !multiple && options?.length === 1 && (!value || value === '')) {
      onChange(options[0].value);
    }
  }, [options, value, mandatory, multiple]);

  // JSX
  return (
    <NblFlexContainer direction="column" position="relative" onClick={onClick}>
      {label && <NblInputLabel label={label} name={name} mandatory={mandatory} disabled={disabled} />}
      <StyledAutocomplete
        options={options}
        title={title}
        value={value === undefined || Array.isArray(value) ? value : options?.find((option: any) => option.value === value)?.label || ''}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        readOnly={readOnly}
        onChange={(_: any, newValue: any) => {
          onChange(Array.isArray(newValue) ? newValue.map((v) => v?.value || v) : newValue?.value);
        }}
        onInputChange={(_, newInputValue) => {
          onInputChange && onInputChange(newInputValue); // Call the prop to handle the input change
        }}
        onKeyDown={(e) => e.stopPropagation()}
        onDoubleClick={(e) => e.stopPropagation()}
        onBlur={handleBlur}
        isOptionEqualToValue={(option: any, value: any) => option.label === value}
        renderInput={(params: any) => {
          const updatedParams = {
            ...params,
            inputProps: {
              ...params.inputProps,
              id: name,
            },
          };
          return <StyledTextField {...updatedParams} placeholder={placeholder} name={name} disabled={disabled} error={Boolean(error)} />;
        }}
        renderOption={(props: any, option: any) => {
          return (
            <StyledMenuItem {...props} key={`${name}-option-${option.label}-${option.value}`} value={option.label}>
              <StyledTypography>{maxLength ? truncateLabel(option.label, maxLength) : option.label}</StyledTypography>
            </StyledMenuItem>
          );
        }}
        disableClearable
        popupIcon={<KeyboardArrowDownIcon />}
        PaperComponent={StyledPaper}
        PopperComponent={StyledPopper}
        ListboxComponent={CustomListbox}
        disabled={disabled}
        multiple={multiple}
      />
      <NblInputHelperText error={Boolean(error)} helperText={helperText} />
    </NblFlexContainer>
  );
}

export default NblAutoComplete;
