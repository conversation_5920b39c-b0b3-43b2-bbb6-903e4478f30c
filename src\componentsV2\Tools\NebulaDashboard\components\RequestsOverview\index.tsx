import UsageMetricsService from 'api/ApiService/UsageMetricsService';
import { useEffect, useState } from 'react';
import { GENERATE_QUERY_FOR_CATALOG, GENERATE_REQUEST_QUERY } from '../../queries';

import RequestListTable from '../RequestsListTable';
// eslint-disable-next-line
import { GetRequestList } from 'store/reducers/usagemetricsrequest';
import { useSelector } from 'react-redux';
// eslint-disable-next-line
import { State } from 'store/reducers/type';
import { MetricApiServiceResponse } from 'api/ApiService/type';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import useNblNavigate from 'hooks/useNblNavigate';

const RequestOverview: React.FunctionComponent = () => {
  const usageMetricsService = new UsageMetricsService();
  const navigate = useNblNavigate();

  const [requestListData, setRequestListData] = useState<GetRequestList | null>(null);
  const { catalog1, catalog2, catalog3, catalog4, status, year, quarters, month, totalCreatedRequests } = useSelector(
    (state: State) => state.RequestOverviewSlice.fetchCatalogData
  );

  const [catalogData, setCatalogData] = useState<any>(null);
  const [isLoading, setisLoading] = useState<any>(false);
  const [totalCount, setTotalCount] = useState<number>(0);
  //pagination variables
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  // request params variables
  const clickedMonth: number | undefined = typeof Number(month) === 'number' ? Number(month) + 1 : undefined;
  const resClickedQuarter = clickedMonth ? [Math.ceil(clickedMonth / 3)] : undefined; // return the respective quarter for clicked month
  const currentYear = year ? year : new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1; // + 1 because Date constructor consider january as 0
  const quarter = resClickedQuarter ? resClickedQuarter : quarters ? quarters : [Math.ceil(currentMonth / 3)];

  useEffect(() => {
    fetchData(page, pageSize);
  }, [catalog1, catalog3, catalog4, month, quarters, year, status, page]);

  //This is a temporary fix. Will be handled in a proper way
  useEffect(() => {
    if (!catalog1?.length && !catalog2?.length && !catalog3?.length && !catalog4?.length) {
      navigate(-1);
    }
  }, [catalog1, catalog3, catalog4, catalog2]);

  const fetchData = async (page: number, pageSize: number) => {
    setPageSize(pageSize);
    setPage(page);
    try {
      setRequestListData(null);
      setisLoading(true);
      const payloadForCatalog = { query: GENERATE_QUERY_FOR_CATALOG() };
      const responseForCatalog = await usageMetricsService.getCatalogsAndResources(payloadForCatalog);
      setCatalogData(responseForCatalog && responseForCatalog.data.data.catalogs);

      const payloadforPresentQuarter = {
        query: GENERATE_REQUEST_QUERY(
          currentYear,
          quarter,
          catalog1,
          catalog3,
          catalog4,
          status,
          clickedMonth,
          page,
          pageSize,
          totalCreatedRequests
        ),
      };
      const responseforPresentQuarter = await usageMetricsService.customGraphQL<MetricApiServiceResponse>(payloadforPresentQuarter);

      setRequestListData(responseforPresentQuarter && responseforPresentQuarter.data.data.getRequestList);
      setTotalCount(responseforPresentQuarter.data.data.getRequestList.metaData.totalCount);

      setisLoading(false);
    } catch (error) {
      setisLoading(false);
      console.log(error);
    }
  };

  return (
    <NblGridContainer>
      <NblGridItem>
        <RequestListTable
          payload={requestListData}
          catalogData={catalogData}
          isLoading={isLoading}
          count={totalCount}
          handlePageChange={(page: number, pageSize: number) => fetchData(page, pageSize)}
          rowperPage={pageSize}
          page={page}
        />
      </NblGridItem>
    </NblGridContainer>
  );
};

export default RequestOverview;
