import { act, render, screen } from '@testing-library/react';
import ProjectTableDetails from './index';
import ThemeProvider from 'mock/ThemeProvider';

jest.mock('react-toastify');

describe('Project Table Details', () => {
  test('Should render Project Table Details', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <ProjectTableDetails name={'Network'} dataCenter={'SDA_7'} vlan={'VLAN_50'} />
        </ThemeProvider>
      )
    );
    expect(screen.getByText('Network')).toBeInTheDocument();
    expect(screen.getByText('SDA_7')).toBeInTheDocument();
    expect(screen.getByText('VLAN_50')).toBeInTheDocument();
  });
});
