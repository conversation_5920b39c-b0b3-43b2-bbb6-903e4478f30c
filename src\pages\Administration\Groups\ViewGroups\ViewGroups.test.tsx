import { render, act } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import { BrowserRouter as Router } from 'react-router-dom';

import ViewGroups from '..';

describe('ViewGroups component', () => {
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <ThemeProvider>
            <Router>
              <ViewGroups />
            </Router>
          </ThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
