import React, { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import useNblNavigate from 'hooks/useNblNavigate';
import SearchService from 'api/ApiService/SearchService';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import NblSearchResults from 'sharedComponents/NblSearchResults';

const GenericSearch = () => {
  const theme = useTheme<NebulaTheme>();
  const searchService = new SearchService();
  const navigate = useNblNavigate();
  const [results, setResults] = useState({});
  const [searchValue, setSearchValue] = useState<string>('');
  const [isLoading, setIsLoading] = useState<Boolean>(true);
  const [searchTriggered, setSearchTriggered] = useState<Boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    const query = searchParams.get('query');
    if (query) {
      setSearchValue(query);
      setIsLoading(true);
      setSearchTriggered(true);
      getResults(query).then((res) => {
        setResults({ ...res });
        setIsLoading(false);
      });
    } else {
      setIsLoading(false);
    }
  }, []);

  const getResults = async (query: string): Promise<any> => {
    const searchResults: any = await searchService.genericSearch(query);
    const formattedData = searchResults.data.reduce((acc: any, curr: any) => {
      for (let key in curr) {
        if (Array.isArray(curr[key]) && curr[key].length === 0) {
          continue; // skip empty arrays
        }
        acc[key] = curr[key];
      }
      return acc;
    }, {});
    return formattedData;
  };

  const handleSubmitSearch = async () => {
    setIsLoading(true);
    setSearchTriggered(true);
    setResults({});
    setSearchParams({ query: searchValue });
    const response = await getResults(searchValue);
    setResults(response);
    setIsLoading(false);
  };

  const handleClearSearch = () => {
    setSearchValue('');
    setSearchParams();
    setResults({});
    setSearchTriggered(false);
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setSearchValue(newValue);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.code === 'Enter' && searchValue !== '') {
      handleSubmitSearch();
    }
  };

  const renderResults = useMemo(
    () => <NblSearchResults results={results} isLoading={isLoading} searchTriggered={searchTriggered} />,
    [results, isLoading, searchTriggered]
  );

  return (
    <>
      <NblBorderContainer backgroundColor={theme.palette.common.white} height="auto" margin="0 0 1rem">
        <NblFlexContainer spacing={3} direction="column" alignItems="center" justifyContent="flex-start">
          <NblTypography variant="h2" color="shade1" margin="1rem 0 0">
            Search
          </NblTypography>
          <NblFlexContainer width="250px" direction="column" alignItems="center">
            <NblTextField
              name="genericSearch"
              type="text"
              handleChange={handleChange}
              onKeyDown={handleKeyDown}
              value={searchValue}
              placeholder="Search Nebula"
            />
            <NblFlexContainer width="400px" direction="row" margin="1rem 0 0" justifyContent="center">
              <NblButton type="button" buttonID="clear filters" color="primary" variant="outlined" onClick={handleClearSearch}>
                Clear
              </NblButton>
              <NblButton
                type="button"
                disabled={searchValue === ''}
                buttonID="apply filters"
                color="primary"
                variant="contained"
                onClick={handleSubmitSearch}
              >
                Search
              </NblButton>
            </NblFlexContainer>
          </NblFlexContainer>
          <NblButton buttonID={'advanced-search-btn'} variant="text" onClick={() => navigate('/advanced-search')} margin="0 0 1rem">
            Advanced Search
          </NblButton>
        </NblFlexContainer>
      </NblBorderContainer>
      {renderResults}
    </>
  );
};

export default GenericSearch;
