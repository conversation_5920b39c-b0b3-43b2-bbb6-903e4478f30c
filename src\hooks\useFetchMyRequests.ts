import { useState } from 'react';
import { useSelector } from 'react-redux';

import { useApiService } from 'api/ApiService/context';
import { PageInfoData } from 'types/Interfaces/PaginationResponse';
import useCatalogRequests from './useCatalogRequests';
import { extractCatalogItemsByLevel, formatMetricQueryArray } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { CatalogItem, MyRequests } from 'types';

export interface MyRequestList {
  getRequestList: {
    metaData: {
      totalCount: number;
    };
    data: {
      id: string;
      status: string;
      requestType: string;
      catalogName: string;
      projectName: string;
      serviceRequestId: string;
      createdBy: string;
      date: string;
      name: string;
    }[];
  };
}

interface Query {
  createdBy: string;
  page: number;
  pageSize: number;
  level1: CatalogItem[];
  level3: CatalogItem[];
  level4: CatalogItem[];
}

const getMyRequestsQuery = ({ createdBy, page, pageSize, level1, level3, level4 }: Query) => {
  return {
    query: `{
    getRequestList(
    createdBy:"${createdBy}"
    page: ${page},
    pageSize: ${pageSize},
    status:[],
    catalogs1: [${formatMetricQueryArray(level1)}],
    catalogs3: [${formatMetricQueryArray(level3)}],
    catalogs4: [${formatMetricQueryArray(level4)}],
    totalCreatedRequests: false)
    {
        metaData{
            totalCount
        }
        data{
            id
            status
            requestType
            catalogName
            projectName
            date
            serviceRequestId
        }
    }
}`,
  };
};

const useFetchMyRequests = () => {
  //Hooks
  const { apiUsageMetricsService } = useApiService();
  const { groupRequestType } = useCatalogRequests();
  const { allCatalogItems } = useSelector((state: State) => state.catalogs);

  //States
  const [loadingRequestList, setLoadingRequestList] = useState(true);
  const [requestList, setRequestList] = useState<MyRequests[]>();
  const [pageInfo, setPageInfo] = useState<PageInfoData>();

  //Local
  const level4Group = groupRequestType(4);
  const { level1, level3, level4 } = extractCatalogItemsByLevel(allCatalogItems);

  //Utils
  function getCatalogName(requestType: string = ''): string {
    for (const catalogName in level4Group) {
      if (level4Group[catalogName].includes(requestType)) return catalogName;
    }
    return '-';
  }

  const fetchRequestList = async ({ page, pageSize, createdBy }: Omit<Query, 'level1' | 'level3' | 'level4'>) => {
    const queryPayload = {
      page,
      pageSize,
      createdBy,
      level1,
      level3,
      level4,
    };

    setLoadingRequestList(true);

    const myRequestResponse = await apiUsageMetricsService.getMyRequests(getMyRequestsQuery(queryPayload));
    if (myRequestResponse.status) {
      setRequestList(
        myRequestResponse.data.data.getRequestList.data.map((data, index) => ({
          sNo: index + 1,
          catalogName: data.catalogName,
          id: data.serviceRequestId || data.id,
          status: data.status,
          projectName: data.projectName || '-',
          name: getCatalogName(data.requestType),
        }))
      );
      const totalDocs = myRequestResponse.data.data.getRequestList.metaData.totalCount;
      const totalPages = Math.round(totalDocs / pageSize);
      setPageInfo({
        page: page,
        nextPage: page + 1,
        limit: pageSize,
        totalDocs,
        totalPages,
        pagingCounter: (page - 1) * pageSize + 1,
        prevPage: page - 1 || 0,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      });
      setLoadingRequestList(false);
    }
  };

  return {
    requestList,
    pageInfo,
    fetchRequestList,
    loadingRequestList,
  };
};

export default useFetchMyRequests;
