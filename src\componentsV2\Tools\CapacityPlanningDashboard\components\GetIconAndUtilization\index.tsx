import { Grid } from '@mui/material';
import { getUsageColor } from '../../utils/colors';
import { getStatusLabel } from '../../utils/status';
import NblTypography from 'sharedComponents/NblTypography';

const GetIconAndUtilization: React.FC<{ status: number; isIndividualUtilization?: boolean }> = ({ status, isIndividualUtilization }) => {
  const color = getUsageColor(status);
  return (
    <Grid container direction={'row'}>
      <Grid item>
        <svg width="15" height="15" viewBox="0 0 15 15">
          <rect width="12" height="5" fill={color} rx="2" ry="2" y="40%" transform="translate(0,-1.5)" />
        </svg>
      </Grid>
      <Grid item>
        <NblTypography variant="subtitle1" color="shade1">
          {isIndividualUtilization ? `${Math.round(status)}` : getStatusLabel(status)}
        </NblTypography>
      </Grid>
    </Grid>
  );
};

export default GetIconAndUtilization;
