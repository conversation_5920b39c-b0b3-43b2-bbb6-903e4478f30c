import React, { useEffect, useState } from 'react';
import DataGridTable from 'components/DataGridTable';
import { ApprovalDetails } from 'types';
import AddApprovalDetailsDialog from '../AddApprovalDetailsDialog';
import ActionsColumn from 'components/Administration/ActionsColumn';
// eslint-disable-next-line
import { GridColDef } from '@mui/x-data-grid';
import { Box } from '@mui/material';

interface AddApprovalDetailsTableProps {
  data: ApprovalDetails[];
  setApprovalDetails: (values: ApprovalDetails[]) => void;
}

interface EditTableRow {
  level: number;
  groupId: string;
  approverCount: number;
  id: number;
  approvalGroupName: string;
}

const AddApprovalDetailsTable: React.FunctionComponent<AddApprovalDetailsTableProps> = ({ data, setApprovalDetails }) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [approvalDetailsData, setApprovalDetailsData] = useState({
    updateApprovalDetails: {
      groupId: '',
      approvalGroupName: '',
      approverCount: 0,
      level: 0,
      id: 0,
    },
  });

  useEffect(() => {
    if (data && data.length > 0) {
      data.map((val) => {

        setApprovalDetailsData({
          updateApprovalDetails: {
            groupId: val.groupId,
            approvalGroupName: val.approvalGroupName,
            approverCount: val.approverCount,
            level: val.level,
            id: val.id,
          },
        });
      });
    }
  }, [data]);

  const handleEdit = (row: EditTableRow) => {
    setDialogOpen(true);
    setApprovalDetailsData({
      updateApprovalDetails: row,
    });
  };

  const handleDelete = (rowId: number) => {
    const deletedRow = data.filter((item: ApprovalDetails) => item.id !== rowId);
    setApprovalDetails(deletedRow);
  };

  const columns: GridColDef[] = [
    {
      field: 'approvalGroupName',
      headerName: 'Approval Group',
      flex: 1,
      width: 700,
    },
    {
      field: 'level',
      headerName: 'Approver Level',
      flex: 1,
      width: 700,
    },
    {
      field: 'approverCount',
      headerName: 'Approver Count',
      flex: 1,
      width: 700,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: any) => {
        return (
          <ActionsColumn
            permissions={{ canUpdate: true, canDelete: true }}
            onEditHandler={() => handleEdit(params.row)}
            onDeleteHandler={() => handleDelete(params.row.id)}
            showConfirmWarning={false}
          />
        );
      },
    },
  ];

  const handleCloseDialog = () => {
    setApprovalDetailsData({
      ...approvalDetailsData,
    });
    setDialogOpen(false);
  };

  const fetchSavedData = (values: ApprovalDetails) => {
    setApprovalDetailsData({
      ...approvalDetailsData,
    });
    setApprovalDetails(
      data.map((currRow: ApprovalDetails, id: number) => (id !== approvalDetailsData.updateApprovalDetails.id ? currRow : values))
    );
  };

  const renderForm = () => {
    return (
      <AddApprovalDetailsDialog
        onClose={handleCloseDialog}
        setApprovalData={fetchSavedData}
        approvalDetails={approvalDetailsData.updateApprovalDetails}
        selectedGroupData={data}
        open={dialogOpen}
      />
    );
  };

  return (
    <Box sx={{ mt: 2 }}>
      {data.length ? <DataGridTable columns={columns} rows={data} /> : null}
      {renderForm()}
    </Box>
  );
};

export default AddApprovalDetailsTable;
