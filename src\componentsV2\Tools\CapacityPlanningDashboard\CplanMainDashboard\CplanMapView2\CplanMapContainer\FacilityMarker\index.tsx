import { CalculatedFacility } from '../../../../utils/types';
import { Marker } from 'react-simple-maps';
import MapCard from './MapCard';
import { getUsageColor } from '../../../../utils/colors';
import { useMemo } from 'react';
import { dispatch } from 'store';
import { setOverviewResource } from 'store/reducers/capacityplanning';

interface FacilityMarkerProps {
  facility: CalculatedFacility;
  isSelected: boolean;
  onMarkerClick: (facilityId: number) => void;
}

const FacilityMarker: React.FunctionComponent<FacilityMarkerProps> = ({ facility, isSelected, onMarkerClick }) => {
  const { maxCpuUtilized, maxMemUtilized, maxDiskUtilized } = useMemo(() => {
    let maxCpuUtilized = 0,
      maxMemUtilized = 0,
      maxDiskUtilized = 0;
    facility.resources.forEach((calcRes) => {
      maxCpuUtilized = Math.max(maxCpuUtilized, calcRes.cpuUtilized);
      maxMemUtilized = Math.max(maxMemUtilized, calcRes.memoryUtilized);
      maxDiskUtilized = Math.max(maxDiskUtilized, calcRes.storageUtilized);
    });
    return { maxCpuUtilized, maxMemUtilized, maxDiskUtilized };
  }, [facility.resources]);

  const handleMarkerClick = () => {
    onMarkerClick(facility.facilityid);
    facility?.resources?.length && dispatch(setOverviewResource(facility.resources[0]));
  };

  return facility?.resources?.length ? (
    <Marker coordinates={[facility.longitude, facility.latitude]}>
      <svg id={String(facility?.facilityid)} onClick={handleMarkerClick} width="13" height="13" viewBox="0 0 13 13">
        <rect x="0.5" y="1" width="4" height="9" fill={getUsageColor(maxCpuUtilized)} stroke="white" strokeWidth={'1.2'} rx="1" ry="1" />
        <rect x="4.5" y="1" width="4" height="9" fill={getUsageColor(maxMemUtilized)} stroke="white" strokeWidth={'1.2'} rx="1" ry="1" />
        <rect x="8.5" y="1" width="4" height="9" fill={getUsageColor(maxDiskUtilized)} stroke="white" strokeWidth={'1.2'} rx="1" ry="1" />
      </svg>
      {isSelected && <MapCard facility={facility} />}
    </Marker>
  ) : (
    <></>
  );
};

export default FacilityMarker;
