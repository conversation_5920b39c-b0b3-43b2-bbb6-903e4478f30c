import React from 'react';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import '../.././NebulaDashboard.css';
import { StyledUMCard, StyledUMCardReqUpArrow, StyledUMCardReqDownArrow } from '../../styled';
import { useDispatch } from 'react-redux';
import { updateRequestOverviewType } from 'store/reducers/requestOverview';
import NblTypography from 'sharedComponents/NblTypography';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import useNblNavigate from 'hooks/useNblNavigate';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import NblTooltip from 'sharedComponents/NblTooltip';

export interface requestCardProps {
  totalRequests: number;
  previousRequests: number | undefined;
  title: string;
  loading: boolean;
  catalogData: any;
  statusList: string[];
}
const RequestCard = ({ totalRequests, previousRequests, title, catalogData, loading, statusList }: requestCardProps) => {
  const getPercentage = (): number | null => {
    if (!isNaN(previousRequests!) && !isNaN(totalRequests) && previousRequests! > 0) {
      return Math.round(((totalRequests - previousRequests!) / previousRequests!) * 100);
    } else return null;
  };
  const dispatch = useDispatch();
  const requestPercentage = getPercentage();
  const navigate = useNblNavigate();
  const handleClick = () => {
    dispatch(updateRequestOverviewType(catalogData));
    navigate('requestoverview');
  };
  const theme = useTheme<NebulaTheme>();
  return (
    <NblTooltip tooltipMessage={statusList.join('\n')} key={title}>
      <StyledUMCard onClick={handleClick} style={{ cursor: 'pointer' }}>
        <NblFlexContainer flexType={'flex'} alignItems="center" justifyContent="space-between" margin="0 0 15px 0">
          {!loading ? (
            <NblTypography variant="h3" color={'shade1'} weight="bold">
              {totalRequests}
            </NblTypography>
          ) : (
            'Loading...'
          )}
          {requestPercentage && requestPercentage > 0 ? (
            <NblTypography weight="medium" variant="body2" color={'shade8'}>
              <NblFlexContainer
                flexType="flex"
                alignItems="center"
                borderRadius="13px"
                padding="0 5px 0 5px"
                backgroundColor={theme.palette.tertiary.shade2.light}
                spacing={0}
              >
                <StyledUMCardReqUpArrow>
                  <PlayArrowIcon />
                </StyledUMCardReqUpArrow>
                {requestPercentage}%
              </NblFlexContainer>
            </NblTypography>
          ) : (
            ''
          )}
          {requestPercentage && requestPercentage < 0 ? (
            <NblTypography weight="medium" variant="body2" color={'shade9'}>
              <NblFlexContainer
                flexType="flex"
                alignItems="center"
                borderRadius="13px"
                padding="0 5px 0 5px"
                backgroundColor={theme.palette.tertiary.shade3.light}
                spacing={0}
              >
                <StyledUMCardReqDownArrow>
                  <PlayArrowIcon />
                </StyledUMCardReqDownArrow>
                {Math.abs(requestPercentage)}%
              </NblFlexContainer>
            </NblTypography>
          ) : (
            ''
          )}
        </NblFlexContainer>
        <NblTypography variant="subtitle2" weight="bold" color={'shade1'}>
          {title}
        </NblTypography>
        {previousRequests !== undefined && previousRequests >= 0 && (
          <NblTypography variant="body3" color={'shade1'}>
            vs previous = {previousRequests}
          </NblTypography>
        )}
      </StyledUMCard>
    </NblTooltip>
  );
};

export default RequestCard;
