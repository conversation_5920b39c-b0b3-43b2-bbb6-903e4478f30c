//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblSelect from 'sharedComponents/NblFormInputs/NblSelect';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

type StoryProps = ComponentProps<typeof NblSelect>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblSelect',
  component: NblSelect,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { control: 'text' },
    name: { control: 'text' },
    placeholder: { control: 'text' },
    options: { control: 'object' },
    helperText: { control: 'text' },
    variant: { options: ['contained', 'outlined'] },
  },
};

export default meta;

export const Select: Story = {
  args: {
    label: 'Label',
    name: 'name',
    placeholder: 'Select an option',
    options: [
      { label: 'John', value: 'opt1' },
      { label: 'Steven', value: 'opt2' },
      { label: 'Alex', value: 'opt3' },
      { label: 'Jack', value: 'opt4' },
      { label: 'Tom', value: 'opt5' },
      { label: 'Jane', value: 'opt6' },
    ],

    helperText: 'helper text',
    disabled: false,
    error: false,
    maxLength: 0,
    mandatory: false,
    variant: 'outlined',
    datatestid: {},
  },
  render: (args) => {
    const WrapperComponent = () => {
      const [selectedValue, setSelectedValue] = useState<string | number>('');

      const handleChange = (event: React.ChangeEvent<{ value: unknown }>) => {
        setSelectedValue(event.target.value as string | number);
      };
      return (
        <NebulaTheme>
          <NblFlexContainer width="300px">
            <NblSelect {...args} handleChange={handleChange} value={selectedValue} />
          </NblFlexContainer>
        </NebulaTheme>
      );
    };

    return <WrapperComponent />;
  },
};
