import { useEffect, useState } from 'react';
import { StyledTimeBox } from '../../styled';
import NblTypography from 'sharedComponents/NblTypography';
import { useApiService } from 'api/ApiService/context';
import { useSelector } from 'react-redux';
// eslint-disable-next-line
import { State } from 'store/reducers/type';
import { TimeInterval } from '../../utils/constant';

const TimeStamp: React.FunctionComponent = () => {
  const [timestamp, setTimestamp] = useState<string | null>(null);
  const [formattedDate, setFormattedDate] = useState<string | null>(null);
  const hours = useSelector((state: State) => state.capacityPlanning.hours);
  const sunburstLevel = useSelector((state: State) => state.capacityPlanning.sunburstLevel);
  const { apiCapacityPlanningService } = useApiService();
  const domainIds = useSelector((state: State) => state.capacityPlanning.selectedDomainIds);
  const getLatestTimeStamp = async (interval: string) => {
    const Apiresponse = await apiCapacityPlanningService.getLatestTimeStamp(interval);
    const timestamp = Apiresponse.data.filter((d) => domainIds?.includes(d.domainid)).map((d) => Number(d.lasttimestamp));
    setTimestamp(String(Math.max(...timestamp)));
  };

  const options: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
    day: '2-digit',
    month: 'long',
    year: 'numeric',
  };

  useEffect(() => {
    const interval = hours > 24 ? TimeInterval.Daily : TimeInterval.Hourly;
    getLatestTimeStamp(interval);
  }, [domainIds, hours, sunburstLevel]);

  useEffect(() => {
    if (timestamp) {
      const date = new Date(Number(timestamp));
      const formatted = date.toLocaleDateString('en-US', options);
      setFormattedDate(formatted);
    }
  }, [timestamp]);

  return formattedDate ? (
    <StyledTimeBox>
      <NblTypography variant="subtitle2" textAlign="right" color="shade1">
        {domainIds.length ? `Last Updated on ${formattedDate}` : 'Please Select Domain'}
      </NblTypography>
    </StyledTimeBox>
  ) : (
    <>Loading...</>
  );
};

export default TimeStamp;
