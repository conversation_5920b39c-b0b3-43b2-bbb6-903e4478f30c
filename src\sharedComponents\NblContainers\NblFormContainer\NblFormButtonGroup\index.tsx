import React from 'react';
import ComputerRoundedIcon from '@mui/icons-material/ComputerRounded';
import ArrowBackIosNewRoundedIcon from '@mui/icons-material/ArrowBackIosNewRounded';
import ArrowForwardIosRoundedIcon from '@mui/icons-material/ArrowForwardIosRounded';
import { NblFormProps, useNblForms } from '..';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblButton from 'sharedComponents/Buttons/NblButton';
import useNblNavigate from 'hooks/useNblNavigate';

export interface NblFormButtonGroupProps {
  showPrevious?: boolean;
  showNext?: boolean;
  showSubmit?: boolean;
  showPreview?: boolean;
  showCancel?: boolean;
  canCreate?: boolean;
  canUpdate?: boolean;
  readOnly?: boolean;
  disablePrevious?: boolean;
  disableNext?: boolean;
  onSuccess?: boolean;
  submitText?: string;
}

interface NblFormButtonGroupPropsPrivate<Values> extends NblFormButtonGroupProps {
  nblFormProps: NblFormProps<Values>;
  onNextClickHandler: (v: NblFormProps<Values>) => void;
  onPreviousClickHandler: (v: NblFormProps<Values>) => void;
  onPreviewClickHandler: (v: NblFormProps<Values>) => void;
  isValid: boolean;
  onCancel?: (values?: Values, nblFormProps?: NblFormProps<Values>) => void;
}

//Button Group
const NblFormButtonGroup = <Values,>({
  nblFormProps,
  onNextClickHandler,
  onPreviousClickHandler,
  isValid,
  onPreviewClickHandler,
  onCancel,
  showCancel,
  showNext,
  showPreview,
  showPrevious,
  showSubmit,
  canCreate,
  canUpdate,
  readOnly,
  disablePrevious,
  disableNext,
  onSuccess,
  submitText = 'Submit',
}: NblFormButtonGroupPropsPrivate<Values>) => {
  //Hooks
  const nblFormValues = useNblForms().nblFormValues as Values;
  const navigate = useNblNavigate();
  const homepath = '/';

  //JSX
  return (
    <NblFlexContainer width="auto" alignSelf="flex-end" spacing={3}>
      {onSuccess ? (
        // If Success is true, show homepage button
        <NblButton buttonID={`form-go-to-homepage-btn`} onClick={() => navigate(homepath)} variant="contained" color="primary" disabled={false}>
          Go to Homepage
        </NblButton>
      ) : (
        <>
          {showPrevious && (
            <NblButton
              buttonID={`form-previous-btn`}
              onClick={() => onPreviousClickHandler(nblFormProps)}
              variant="outlined"
              color="primary"
              disabled={disablePrevious}
              startIcon={<ArrowBackIosNewRoundedIcon />}
            >
              Previous
            </NblButton>
          )}
          {showCancel && !readOnly && !nblFormProps.isSubmitting && (
            <NblButton buttonID={`form-cancel-btn`} onClick={() => onCancel?.(nblFormValues, nblFormProps)} variant="outlined" color="primary">
              Cancel
            </NblButton>
          )}
          {showPreview && (
            <NblButton
              buttonID={`form-preview-btn`}
              onClick={() => onPreviewClickHandler(nblFormProps)}
              variant="outlined"
              color="primary"
              startIcon={<ComputerRoundedIcon />}
              disabled={!nblFormProps.isValid || nblFormProps.isSubmitting}
            >
              Preview
            </NblButton>
          )}
          {showNext && (
            <NblButton
              buttonID={`form-next-btn`}
              onClick={() => onNextClickHandler(nblFormProps)}
              variant="contained"
              color="primary"
              disabled={!isValid || disableNext}
              endIcon={<ArrowForwardIosRoundedIcon />}
            >
              Next
            </NblButton>
          )}
          {showSubmit && !readOnly && (
            <NblButton
              buttonID={`form-${submitText}-btn`}
              type="submit"
              variant="contained"
              color="primary"
              disabled={!nblFormProps.isValid || nblFormProps.isSubmitting || !canCreate || !canUpdate}
            >
              {submitText}
            </NblButton>
          )}
        </>
      )}
    </NblFlexContainer>
  );
};

export default React.memo(NblFormButtonGroup) as <Values>(props: NblFormButtonGroupPropsPrivate<Values>) => React.ReactElement;
