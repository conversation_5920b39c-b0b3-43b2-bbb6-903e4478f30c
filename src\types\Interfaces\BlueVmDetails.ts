import RefDataOptions from './ReferenceDataOptions';

type BlueVmdetails = {
  domain: string;
  name: string;
  size: string;
  projectName: string;
  application: string;
  environment: {
    name: string;
    value: string;
  };
  layouts: { shortName: string };
  network: { displayName: string };
  datacenter: { name: string; id: number };
  cloudId: number;
  vmCount: number;
  description: string;
  resourcePool: { id: number; name: string };
  projectId: string;
  catalogId: string;
  applicationSearch: string;
  appId: string;
  driveOwner: string;
  driveGroup: string;
  corpnetEnvironment: RefDataOptions;
  appTier: RefDataOptions;
  addDisks?: { diskName: string; diskValue: string }[];
  patchCycle: RefDataOptions;
  backupOptions: RefDataOptions;
  requestPayloadVersion: number;
  appInstance: RefDataOptions;
  adGroup?: string[];
  applicationRef: string;
  shortName: string;
  database?: RefDataOptions;
  platformContext: {
    catalogId: string;
    domainId: string;
    envId: string;
    applicationName: string;
    environmentName: string;
    domainName: string;
  };
};

export default BlueVmdetails;
