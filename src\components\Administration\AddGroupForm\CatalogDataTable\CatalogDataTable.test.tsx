import { act, render, fireEvent, waitFor } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import CatalogDataTable from '.';
import ThemeProvider from 'mock/ThemeProvider';
import { GroupTableData } from 'mock/GroupsCatalogData';
import { CatalogPermissions } from 'mock/Groups';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});

describe('Render Catalog permission table', () => {
  const setSelectedCatalogPermissions = jest.fn();

  test('Should render the catalog permissions', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <CatalogDataTable
              catalogPermissions={CatalogPermissions}
              setSelectedCatalogPermissions={setSelectedCatalogPermissions}
              data={GroupTableData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    expect(getByText('Catalogs')).toBeInTheDocument();
    expect(getByText('Catalog Roles')).toBeInTheDocument();
    expect(getByText('Actions')).toBeInTheDocument();
    expect(getByText(GroupTableData[0]['catalogName'])).toBeInTheDocument();
    expect(getByText(GroupTableData[0]['roles'][0]['roleName'])).toBeInTheDocument();
  });

  test('Should close the Edit Catalog Permissions Dialog on clicking Cancel button', async () => {
    const { getByText, getByTestId, queryByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <CatalogDataTable
              catalogPermissions={CatalogPermissions}
              setSelectedCatalogPermissions={setSelectedCatalogPermissions}
              data={GroupTableData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    const editIcon = getByTestId(`edit-icon-${GroupTableData[0]['id']}`);
    act(() => {
      fireEvent.click(editIcon);
    });
    await waitFor(() => {
      expect(getByText('Edit Catalog Permissions')).toBeInTheDocument();
    });

    act(() => {
      fireEvent.click(getByText('Cancel'));
    });
    await waitFor(() => {
      expect(queryByText('Edit Catalog Permissions')).toBeNull();
    });
  });

  test('Should remove the deleted row from the table', async () => {
    const { getByText, getByTestId, queryByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <CatalogDataTable
              catalogPermissions={CatalogPermissions}
              setSelectedCatalogPermissions={setSelectedCatalogPermissions}
              data={GroupTableData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    expect(getByText(GroupTableData[0]['catalogName'])).toBeInTheDocument();
    const deleteIcon = getByTestId(`delete-icon-${GroupTableData[0]['id']}`);
    await act(() => {
      fireEvent.click(deleteIcon);
    });
    waitFor(() => {
      expect(queryByText(GroupTableData[0]['catalogName'])).toBeNull();
    });
  });

  // test('updates Catalogs dropdown value when changed', async () => {
  //   const { debug, container, getByText, getByTestId, queryByText, getByLabelText, } = await act(async () =>
  //     render(
  //       <ThemeProvider>
  //         <CatalogDataTable
  //           catalogPermissions={CatalogPermissions}
  //           setSelectedCatalogPermissions={setSelectedCatalogPermissions}
  //           data={GroupTableData}
  //         />
  //       </ThemeProvider>
  //     )
  //   );

  //   fireEvent.click(getByTestId('edit-icon-0')); // Assuming this icon corresponds to the first item in the data array
  //   await waitFor(() => {
  //     expect(getByText('Edit Catalog Permissions')).toBeInTheDocument();
  //     expect(getByText('Catalogs *')).toBeInTheDocument();
  //   })

  //   const catalogsDropdown = container.querySelector('#catalogId') as HTMLDivElement;
  //   fireEvent.mouseDown(catalogsDropdown);
  //   fireEvent.click(getByText(CatalogPermissions.catalogNames[1].name));
  // });
});
