import { act, render, fireEvent } from '@testing-library/react';

import NblButton from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('Render Nbl Button', () => {
  const mockResetHandler = jest.fn();
  const buttonId = 'generic';
  test('Should Render Nbl Button and trigger onClick Fn. upon click', async () => {
    const { getByText } = await act(async () =>
      render(
        <NebulaThemeProvider>
          <NblButton buttonID={buttonId} onClick={mockResetHandler}>
            Test
          </NblButton>
        </NebulaThemeProvider>
      )
    );
    const button = getByText('Test');
    fireEvent.mouseDown(button);
    expect(mockResetHandler).toHaveBeenCalled();
  });
  test('Should not trigger onClick Fn. upon click when disabled', async () => {
    const { getByText } = await act(async () =>
      render(
        <NebulaThemeProvider>
          <NblButton buttonID={buttonId} onClick={mockResetHandler} disabled={true}>
            Test
          </NblButton>
        </NebulaThemeProvider>
      )
    );
    const button = getByText('Test');
    fireEvent.mouseDown(button);
    expect(mockResetHandler).not.toHaveBeenCalled();
  });
});
