//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import NblButton from 'sharedComponents/Buttons/NblButton';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';

type StoryProps = ComponentProps<typeof NblButton>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Buttons/NblButton',
  component: NblButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: { control: 'radio', options: ['contained', 'outlined'], type: 'string' },
    color: { control: 'radio', options: ['primary', 'info', 'error'] },
    onClick: { action: 'onClick', type: 'function' },
    disabled: { control: 'boolean' },
    buttonID: { type: 'string' },
    type: { options: ['submit', 'button'] },
  },
};

export default meta;

export const Button: Story = {
  args: {
    disabled: false,
    variant: 'contained',
    color: 'primary',
    startIcon: <SearchOutlinedIcon />,
    endIcon: undefined,
  },
  render: (args) => (
    <NebulaTheme>
      <NblButton buttonID={'Nblbutton'} {...args} >Default</NblButton>
    </NebulaTheme>
  ),
};
