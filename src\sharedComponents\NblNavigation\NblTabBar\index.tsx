import React, { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import NblChip from 'sharedComponents/NblChip';
import { StyledTab, StyledTabs } from './styled';
import { formatId } from 'utils/common';

export type TabProp = { label: string; chip?: React.ReactElement | string | number; disabled?: boolean; tooltipMessage?: string };

export type TabsProp = TabProp[];

export interface NblTabBarProps {
  tabs: TabsProp;
  tabBarIndicator?: boolean;
  activeTab?: number;
  onTabChange?: (tab: string) => void;
}

const NblTabBar: React.FC<NblTabBarProps> = ({ tabs, activeTab, onTabChange, tabBarIndicator = false }) => {
  // States
  const [active, setActive] = useState<number>(0);

  useEffect(() => {
    if (typeof activeTab === 'number') {
      setActive(activeTab);
    } else if (!activeTab) {
      setActive(tabs.findIndex((tab) => !tab.disabled));
    }
  }, [activeTab]);

  // Handlers
  function handleTabChange(event: React.SyntheticEvent, tab: number) {
    setActive(tab);
    onTabChange?.(tabs[tab].label);
  }

  // Side Effects
  useEffect(() => {
    if (tabs.length !== [...new Set(tabs.map((tab) => tab.label))].length) throw Error('All tabs should be unique. Found duplicate tab');
  }, [tabs]);

  // JSX
  return (
    <Box sx={{ ...(tabBarIndicator && { borderBottom: 0.5, borderColor: 'divider' }) }}>
      <StyledTabs value={active} onChange={handleTabChange} variant="standard" textColor="primary">
        {tabs.map((tab, index) => {
          return (
            <StyledTab
              key={tab.label}
              {...(tab?.chip && {
                icon: (
                  <NblChip
                    id="Nbl Tab Chip"
                    borderRadius="lg"
                    label={tab.chip}
                    disabled={tab?.disabled}
                    extracolor={active == index ? 'primary' : undefined}
                  ></NblChip>
                ),
                iconPosition: 'end',
              })}
              label={tab.label}
              id={formatId(`tab-${tab.label}`)}
              disabled={tab?.disabled}
            />
          );
        })}
      </StyledTabs>
    </Box>
  );
};

export { NblTabBar };
