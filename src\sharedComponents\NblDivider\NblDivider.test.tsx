import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblDivider from '.';

describe('NblDivider component', () => {
  const props = {
    borderRadius: 1,
    mt: 0,
    mb: 0,
    positionAbsolute: false,
    strokeWidth: 2,
    length: '100%',
    color: '#33dbdc',
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblDivider {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
