import NblInputLabel from 'sharedComponents/NblFormInputs/NblInputLabel';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblInputHelperText from 'sharedComponents/NblFormInputs/NblInputHelperText';
import { StyledMuiTelInput } from './styled';

interface PhoneInputFieldProps {
  error?: boolean | string;
  helperText?: string;
  label: string;
  name: string;
  placeholder?: string;
  value: string;
  handleChange: (event: any) => void;
  handleBlur: (event: any) => void;
  restrictCountries?: boolean;
  mandatory?: boolean;
  disabled?: boolean;
}

const PhoneInputField = ({
  error,
  label,
  name,
  placeholder,
  value,
  handleBlur,
  handleChange,
  restrictCountries = true,
  helperText,
  mandatory,
  disabled,
}: PhoneInputFieldProps) => {
  const defaultCountry = 'US';
  return (
    <NblFlexContainer direction="column" position="relative">
      <NblInputLabel label={label} name={name} mandatory={mandatory} disabled={disabled} />
      <StyledMuiTelInput
        defaultCountry={defaultCountry}
        disableFormatting
        id={name}
        name={name}
        placeholder={placeholder}
        disabled={disabled}
        fullWidth
        value={value}
        error={Boolean(error)}
        onBlur={handleBlur}
        onChange={handleChange}
        {...(restrictCountries && { onlyCountries: ['US'] })}
        disableDropdown={defaultCountry === 'US'}
        forceCallingCode={defaultCountry === 'US'}
      />
      {helperText && <NblInputHelperText error={Boolean(error)} helperText={helperText} />}
    </NblFlexContainer>
  );
};

export default PhoneInputField;
