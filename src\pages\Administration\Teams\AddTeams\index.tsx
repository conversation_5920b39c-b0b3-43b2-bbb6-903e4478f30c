import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import AddTeamForm from 'components/Administration/AddTeamForm';
import useNblNavigate from 'hooks/useNblNavigate';

const AddTeam = () => {
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const navigateToTeamDetails = () => {
    navigate('/administration/teams');
  };

  return <AddTeamForm title="Add Team" onSuccess={navigateToTeamDetails} onClose={navigateToTeamDetails} />;
};

export default AddTeam;
