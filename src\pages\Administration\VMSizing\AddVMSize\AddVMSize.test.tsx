import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import WrappedVMSizeForm from 'componentsV2/Administration/VMSize/VMSizeForm';

const mockNavigate = jest.fn();
jest.mock('hooks/useNblNavigate', () => ({
  __esModule: true,
  default: () => mockNavigate,
}));

jest.mock('hooks/useShowNavigationWarning', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('componentsV2/Administration/VMSize/VMSizeForm', () => {
  return {
    __esModule: true,
    default: ({ onSuccess, onClose }: any) => (
      <div>
        Mocked WrappedVMSizeForm
        <button onClick={onSuccess}>Trigger Success</button>
        <button onClick={onClose}>Trigger Close</button>
      </div>
    ),
  };
});

describe('AddVMSize', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders WrappedVMSizeForm component', () => {
    const { getByText } = render(<WrappedVMSizeForm onClose={() => {}} permissions={{}} />);
    expect(getByText('Mocked WrappedVMSizeForm')).toBeInTheDocument();
  });
});
