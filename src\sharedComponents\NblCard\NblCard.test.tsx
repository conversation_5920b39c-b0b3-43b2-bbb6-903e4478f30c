import { render, screen, fireEvent } from '@testing-library/react';
import NblCard from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('NblCard Component', () => {
  const mandatoryProps = {
    id: 'test',
    shortName: 'testshortname',
    onClickCard: jest.fn(),
    title: 'Test Title',
    onFavourite: jest.fn(),
  };

  const label = 'Test Label';
  const chip = 'Test Chip';
  const description = 'Test Description';

  describe('Title Element', () => {
    test('renders the component with title', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} /></NebulaThemeProvider>);
      expect(screen.getByText(mandatoryProps.title)).toBeInTheDocument();
    });
  });

  describe('Label Element', () => {
    test('renders the label when expanded', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} expanded={true} label={label} /></NebulaThemeProvider>);
      expect(screen.getByText(label)).toBeInTheDocument();
    });

    test('does not render the label,chip,description when not expanded', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} expanded={false} label={label} description={description} chip={chip} /></NebulaThemeProvider>);
      expect(screen.queryByText(label)).not.toBeInTheDocument();
      expect(screen.queryByText(description)).not.toBeInTheDocument();
      expect(screen.queryByText(chip)).not.toBeInTheDocument();
    });
  });

  describe('Chip Element', () => {
    test('renders the description when expanded', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} expanded={true} description={description} /></NebulaThemeProvider>);
      expect(screen.getByText(description)).toBeInTheDocument();
    });

    test('does not render the description when not expanded', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} expanded={false} description={description} /></NebulaThemeProvider>);
      expect(screen.queryByText(description)).not.toBeInTheDocument();
    });
    test('renders the chip with correct styles based on variant', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} expanded={true} chip="Test Chip" color="primary" /></NebulaThemeProvider>);
      const chip = screen.getByText('Test Chip').parentElement;
      expect(chip).toBeInTheDocument();
      expect(chip).toHaveStyle(`background-color:rgb(203, 233, 247); color:rgb(14, 153, 216)`);
    });
  });

  describe('Icon Element', () => {
    const icon = 'CloudOutlined';
    const testId = 'mui-icon';

    test('renders the icon when provided', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} icon={icon} /></NebulaThemeProvider>);
      const Icon = screen.getByTestId(testId);
      expect(Icon).toBeInTheDocument();
    });
    test('Does not render the icon when not provided', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} /></NebulaThemeProvider>);
      const Icon = screen.queryByTestId(testId);
      expect(Icon).not.toBeInTheDocument();
    });
    test('Does not render the icon when invalid icon provided', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} icon="InvalidIcon" /></NebulaThemeProvider>);
      const Icon = screen.queryByTestId(testId);
      expect(Icon).not.toBeInTheDocument();
    });
  });

  describe('Card Interaction', () => {
    test('calls onClick when card is clicked', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} /></NebulaThemeProvider>);
      fireEvent.click(screen.getByTestId('card'));
      expect(mandatoryProps.onClickCard).toHaveBeenCalledTimes(1);
      expect(mandatoryProps.onClickCard).toHaveBeenCalledWith(mandatoryProps.shortName);
    });

    test('calls onClick when Enter key is pressed', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} /></NebulaThemeProvider>);
      fireEvent.keyDown(screen.getByTestId('card'), { key: 'Enter', code: 'Enter' });
      expect(mandatoryProps.onClickCard).toHaveBeenCalledTimes(1);
      expect(mandatoryProps.onClickCard).toHaveBeenCalledWith(mandatoryProps.shortName);
    });

    test('calls onFavourite when favourite icon is clicked', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} /></NebulaThemeProvider>);
      fireEvent.click(screen.getByTestId(/fav-button/i));
      expect(mandatoryProps.onFavourite).toHaveBeenCalledTimes(1);
      expect(mandatoryProps.onFavourite).toHaveBeenCalledWith(mandatoryProps.id);
    });
    test('should not call onFavourite when disabled and favourite icon is clicked', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} disabled={true} /></NebulaThemeProvider>);
      fireEvent.click(screen.getByTestId(/fav-button/i));
      expect(mandatoryProps.onFavourite).not.toHaveBeenCalled();
    });
    test('disables interaction when disabled prop is true', () => {
      render(<NebulaThemeProvider><NblCard {...mandatoryProps} disabled={true} /></NebulaThemeProvider>);
      const card = screen.getByTestId('card');
      expect(card).toHaveAttribute('aria-disabled', 'true');
      fireEvent.click(card);
      expect(mandatoryProps.onClickCard).not.toHaveBeenCalled();
    });
  });
});
