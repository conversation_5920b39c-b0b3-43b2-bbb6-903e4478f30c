import { styled } from '@mui/material/styles';
import { Autocomplete, TextField, MenuItem, Typography, Paper, Popper, List } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { getOutlinedInputStyles, getScrollbarStyles } from '../common';

export const StyledAutocomplete = styled(Autocomplete)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { palette, typography } = theme;
  const { textfield, autoComplete } = palette;
  return {
    width: '100%',
    '& .MuiAutocomplete-inputRoot': {
      padding: 0,
    },
    '& .MuiOutlinedInput-root': {
      ...getOutlinedInputStyles(theme),
    },
    '& .MuiInputBase-input::placeholder': {
      ...typography.subtitle1,
      color: textfield.placeholderColor,
    },
    '& .MuiInputBase-input': {
      ...typography.subtitle1,
      ...typography.medium,
      color: autoComplete.color,
    },
  };
});

export const StyledTextField = styled(TextField)<{ theme?: NebulaTheme }>(() => {
  return {
    width: '100%',
    '& .MuiInputBase-root': {
      padding: '0.5px 8px',
    },
    '& .Mui-disabled .MuiInputBase-input::placeholder': {
      opacity: '0.3',
    },
  };
});

export const StyledMenuItem = styled(MenuItem)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography, palette } = theme;
  const { select } = palette;

  return {
    ...typography.subtitle1,
    display: 'flex',
    alignItems: 'center',
    padding: '8px 16px',
    textWrap: 'wrap',
    wordBreak: 'break-word',
    '&.menuItem': {
      borderBottom: `1px solid ${select.optionsBorderColor}`,
      color: select.color,
      lineHeight: '3',
    },
  };
});

export const StyledTypography = styled(Typography)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography, palette } = theme;
  const { autoComplete } = palette;
  return {
    ...typography.subtitle1,
    color: autoComplete.color,
  };
});

export const StyledPaper = styled(Paper)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography, palette } = theme;
  const { autoComplete, secondary } = palette;
  return {
    ...typography.subtitle2,
    '&.MuiAutocomplete-paper': {
      ...getScrollbarStyles(theme),
      border: autoComplete.optionsContainerBorderColor,
      boxShadow: 'none',
    },
    '& .MuiAutocomplete-listbox': {
      maxHeight: '208px',
      overflow: 'auto',
    },
    '& .MuiAutocomplete-option': {
      margin: '0px 6px !important',
      borderBottom: `1px solid ${autoComplete.optionsBorderColor}`,
      lineHeight: '3',
      '&:hover': {
        background: `${secondary.shade2} 0% 0% no-repeat padding-box`,
      },
      '&.MuiMenuItem-root': {
        minHeight: '40px',
      },
    },
  };
});

export const StyledPopper = styled(Popper)<{ theme?: NebulaTheme }>(() => {
  return {
    marginTop: '0px !important',
  };
});

export const CustomListbox = styled(List)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { palette } = theme;
  const { autoComplete } = palette;
  return {
    border: `1px solid ${autoComplete.optionsBorderColor}`,
    borderRadius: '4px',
    '&': {
      ...getScrollbarStyles(theme),
    },
  };
});
