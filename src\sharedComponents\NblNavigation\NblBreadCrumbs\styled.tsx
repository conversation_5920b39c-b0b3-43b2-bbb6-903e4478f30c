import { Breadcrumbs, Link } from '@mui/material';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledBreadCrumbs = styled(Breadcrumbs)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { palette } = theme;

  return {
    '.MuiBreadcrumbs-ol li': {
      color: palette.typography.shade1,
      opacity: 0.4,
    },
    '.MuiBreadcrumbs-ol li:nth-last-of-type(-n+2)': {
      opacity: 1,
    },
  };
});

export const StyledLink = styled(Link)<{ theme?: NebulaTheme }>(({ theme, variant }) => {
  const { typography } = theme;

  return {
    ...typography.body3,
    fontWeight: typography.medium.fontWeight,
    textTransform: 'capitalize',
    cursor: variant === 'button' ? 'pointer' : 'default',
  };
});
