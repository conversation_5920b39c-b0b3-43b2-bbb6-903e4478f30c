import React, { useEffect, useState } from 'react';
import { useTheme } from '@mui/material';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { FormValues } from '..';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import useGetDomainData from 'hooks/useGetDomainData';
import NblAccordion from 'sharedComponents/Accordion/NblAccordion';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblAutoComplete from 'sharedComponents/NblFormInputs/NblAutoComplete';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblFieldWrapper from 'sharedComponents/NblFormInputs/NblFieldWrapper';
import NblTypography from 'sharedComponents/NblTypography';
import useVCenterFields from './useVCenterCommonFields';
import { CloudData, LookupPayload } from 'types/Interfaces/LookUpVCenterResponse';

interface VCenterDetailsProps {
  responseData: CloudData;
  cloudDatacenter: string;
  setCloudDatacenter: (value: string) => void;
  setActiveCloudIndex?: (value: number) => void;
  payload: LookupPayload;
}

const VCenterDetails: React.FC<VCenterDetailsProps> = ({
  responseData,
  cloudDatacenter,
  setCloudDatacenter,
  setActiveCloudIndex,
  payload,
}) => {
  const { domainData } = useGetDomainData();
  const {
    palette: { secondary },
  }: NebulaTheme = useTheme();

  const { nblFormProps, nblFormValues } = useNblForms<FormValues>();
  const cloudDetails = responseData.cloudDetails;
  const [isEnabled, setIsEnabled] = useState<boolean>(true);
  const [cloud, setCloud] = useState<any[]>([]);
  const vcenterFields = useVCenterFields(payload, domainData);
  const splitIndex = vcenterFields.findIndex((f) => f.title === 'VCenter Host');
  const initialFields = vcenterFields.slice(0, splitIndex + 1);
  const remainingFields = vcenterFields.slice(splitIndex + 1);

  useEffect(() => {
    if (payload) {
      nblFormProps.setFieldValue('vcenterHost', payload.vCenterHost || '');
      nblFormProps.setFieldValue('vcenterName', payload.vCenterName || '');
    }

    if (Array.isArray(cloudDetails) && cloudDetails.length > 0) {
      setCloud(
        cloudDetails.map((cloud: any) => ({
          label: cloud.cloudName,
          value: cloud.cloudId,
        }))
      );
      const encodedUsername = btoa(payload?.vCenterUser || '');
      const encodedPassword = btoa(payload?.vCenterPassword || '');
      const initializedClouds = cloudDetails.map((cloud: any) => ({
        ...cloud,
        vcenterHost: payload?.vCenterHost,
        domain: payload?.domain || '',
        datacenter: payload?.cloudDatacenter || '',
        hostname: payload?.vCenterHost || '',
        password: encodedPassword,
        port: payload?.vCenterPort,
        protocol: payload?.vCenterProtocol,
        username: encodedUsername,
      }));

      nblFormProps.setFieldValue('cloudDetails', initializedClouds);
    }
  }, [payload, cloudDetails]);

  useEffect(() => {
    if (!cloudDatacenter || !nblFormValues.cloudDetails) return;

    const index = nblFormValues.cloudDetails.findIndex((cloud) => `${cloud.cloudId}` === `${cloudDatacenter}`);

    if (index !== -1 && setActiveCloudIndex) {
      setActiveCloudIndex(index);
    }
  }, [cloudDatacenter, nblFormValues.cloudDetails]);

  return (
    <NblAccordion
      border={`1px solid ${secondary.shade4}`}
      summary="VCenter Details"
      hasDivider
      bgColor={secondary.main}
      expandIconSize="large"
    >
      <NblGridContainer columns={5} spacingX={5} spacingY={2}>
        {initialFields.map((field) => (
          <NblGridItem key={field.title} colspan={field.span}>
            <NblFieldWrapper mandatory={false} name={field.title} disabled label={field.title} error={false} helperText="">
              <NblTypography
                variant="subtitle1"
                padding={field.value ? '10px 0 5px 0' : '0'}
                weight="medium"
                color="shade1"
                whiteSpace="pre-line"
                wordBreak="break-word"
              >
                {field.value}
              </NblTypography>
            </NblFieldWrapper>
          </NblGridItem>
        ))}

        <NblGridItem>
          <NblAutoComplete
            name="cloud"
            value={cloudDatacenter}
            onChange={(selectedOption) => {
              setCloudDatacenter(selectedOption);
            }}
            options={cloud}
            placeholder="Select"
            label="Cloud"
          />
        </NblGridItem>

        {remainingFields.map((field) => (
          <NblGridItem key={field.title} colspan={field.span}>
            <NblFieldWrapper mandatory={false} name={field.title} disabled label={field.title} error={false} helperText="">
              <NblTypography
                variant="subtitle1"
                padding={field.value ? '10px 0 5px 0' : '0'}
                weight="medium"
                color="shade1"
                whiteSpace="pre-line"
                wordBreak="break-word"
              >
                {field.value}
              </NblTypography>
            </NblFieldWrapper>
          </NblGridItem>
        ))}

        <NblGridItem>
          <NblCheckBox
            name="enable"
            label="Enable"
            checked={isEnabled}
            onChange={(e) => setIsEnabled(e.target.checked)}
            onBlur={() => {}}
          />
        </NblGridItem>
      </NblGridContainer>
    </NblAccordion>
  );
};

export default VCenterDetails;
