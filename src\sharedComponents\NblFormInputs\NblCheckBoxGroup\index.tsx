import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblInputLabel from '../NblInputLabel';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import React from 'react';
import NblInputHelperText from '../NblInputHelperText';

interface NblCheckBoxGroup {
  label: string;
  name: string;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>, index: number, checked: boolean) => void;
  onMouseEnter?: (event: React.MouseEvent<HTMLLabelElement>, index: number) => void;
  onMouseLeave?: (event: React.MouseEvent<HTMLLabelElement>, index: number) => void;
  onBlur?: (event: React.FocusEvent<HTMLButtonElement>, index: number) => void;
  options: { name: string; label: string; checked: boolean; disabled?: boolean }[];
  mandatory?: boolean;
}

const NblCheckBoxGroup: React.FC<NblCheckBoxGroup> = ({
  label,
  name,
  error,
  disabled,
  onChange,
  onMouseEnter,
  onMouseLeave,
  onBlur,
  options,
  helperText = '',
  mandatory = false,
}) => {
  //Handlers
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>, index: number, checked: boolean) => {
    onChange?.(event, index, checked);
  };
  const handleBlur = (event: React.FocusEvent<HTMLButtonElement>, index: number) => {
    onBlur?.(event, index);
  };
  const handleMouseEnter = (event: React.MouseEvent<HTMLLabelElement>, index: number) => {
    onMouseEnter?.(event, index);
  };
  const handleMouseLeave = (event: React.MouseEvent<HTMLLabelElement>, index: number) => {
    onMouseLeave?.(event, index);
  };

  //JSX
  return (
    <NblFlexContainer direction="column" position="relative" spacing={0}>
      <NblInputLabel label={label} name={name} disabled={disabled} mandatory={mandatory} />
      <NblFlexContainer alignItems="center">
        {options.map((option, index) => (
          <NblCheckBox
            key={option.label}
            name={option.name}
            checked={option.checked}
            onBlur={(e) => handleBlur(e, index)}
            onChange={(e, checked) => handleChange(e, index, checked)}
            label={option.label}
            disabled={disabled || option.disabled}
            error={Boolean(error)}
            labelPlacement="end"
            onMouseEnter={(e) => handleMouseEnter(e, index)}
            onMouseLeave={(e) => handleMouseLeave(e, index)}
            type="regular"
          />
        ))}
      </NblFlexContainer>
      <NblInputHelperText error={Boolean(error)} helperText={helperText} />
    </NblFlexContainer>
  );
};

export default NblCheckBoxGroup;
