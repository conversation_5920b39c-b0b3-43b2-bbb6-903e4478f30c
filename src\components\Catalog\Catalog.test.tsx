import { render } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import Catalog from '.';
import ThemeProvider from 'mock/ThemeProvider';
import CatalogItem from './CatalogItem';
jest.mock('./CatalogItem');

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    exposureParams: [],
  },
});

describe('Catalog component', () => {
  const mockCatalogItems = [
    {
      name: 'Create Monitoring Organization',
      description: 'Create a new departmental container in Science Logic',
      button: 'start',
      path: '/IaaS/network/monitoring/create-organization',
      icon: 'FoundationOutlined',
    },
    {
      name: 'Mock Catalog item',
      description: 'Mock descripion',
      button: 'start',
      path: '/IaaS/network/monitoring/create-organization',
      icon: 'FoundationOutlined',
    },
  ];

  test('should render correct number of catalog items ', () => {
    render(
      <ReduxProvider store={store}>
        <ThemeProvider>
          <Catalog catalogItems={mockCatalogItems} />
        </ThemeProvider>
      </ReduxProvider>
    );
    expect(CatalogItem).toHaveBeenCalledTimes(mockCatalogItems.length);
  });

  test('should pass correct props to all Catalog items ', () => {
    render(
      <ReduxProvider store={store}>
        <ThemeProvider>
          <Catalog catalogItems={mockCatalogItems} />
        </ThemeProvider>
      </ReduxProvider>
    );
    for (const index in mockCatalogItems) {
      expect(CatalogItem).toHaveBeenCalledWith(expect.objectContaining(mockCatalogItems[index]), expect.anything());
    }
  });
});
