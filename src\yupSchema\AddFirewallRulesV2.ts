import * as yup from 'yup';
import { Address4, Address6 } from 'ip-address';
import { yupMatchesParams, isPortEntryValid } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { FirewallSourceDestination } from 'types';
import { IPvType } from 'types/Enums';
import { ProtocolTypes } from 'types/Enums';

type ValidationSchema = {
  setIpSourceCorrection: (params: FirewallSourceDestination['source']) => void;
  setIpDestinationCorrection: (params: FirewallSourceDestination['destination']) => void;
  setIpVersion: (params: string) => void;
};

const validateSourcePort = (value?: string, ctx?: yup.TestContext): any => {
  if (value !== '' && value?.toUpperCase() !== 'ANY') {
    const result = isPortEntryValid(value);
    if (result.errors) {
      return ctx?.createError({ message: result.errors }) ?? '';
    }
    if (result.security) {
      return ctx?.createError({ message: result.security }) ?? '';
    }
  }
  return true;
};

const validateDestinationPort = (value?: string, ctx?: yup.TestContext): any => {
  // @ts-ignore
  let protocolValue = ctx?.options.context.protocol;
  if (
    protocolValue &&
    (protocolValue === ProtocolTypes.TCP ||
      protocolValue === ProtocolTypes.UDP ||
      protocolValue === ProtocolTypes.SNMP ||
      protocolValue === ProtocolTypes.HTTP) &&
    !value
  ) {
    return ctx?.createError({ message: 'Destination Port is mandatory for TCP, UDP, SNMP and HTTP protocol' });
  }
  if (value !== '' && value?.toUpperCase() !== 'ANY') {
    const result = isPortEntryValid(value);
    if (result.errors) {
      return ctx?.createError({ message: result.errors }) ?? '';
    }
    if (result.security) {
      return ctx?.createError({ message: result.security }) ?? '';
    }
  }
  return true;
};

export const validateIPAddress = (value: any, createError: any, setIpVersion: ValidationSchema['setIpVersion']) => {
  try {
    if (/^,|,$/.test(value)) {
      return createError({ message: 'Cannot start or end with a comma' });
    }

    if (/,,/.test(value)) {
      return createError({ message: 'Cannot have two commas in a row' });
    }

    if (value.trim() == '::0/0' || value.trim() == '0::0/0' || value.trim() == '::/0') {
      setIpVersion(IPvType.v6);
      return true;
    }

    const seenIps = new Set();
    let correctedValue = value;
    let correctionMade = false;

    const ipSegments = value.split(',').map((segment: any) => segment.trim());
    if (ipSegments.length > 32) {
      return createError({ message: 'Cannot have morethan 32 IP addresses or ranges' });
    }

    for (let value of ipSegments) {
      if (value.includes('-')) {
        const [startIp, endIp] = value.split('-').map((ip: any) => ip.trim());

        // IPV6 validation for range limit
        const startParts = startIp.split('.').map(Number);
        const endParts = endIp.split('.').map(Number);

        const ipToInt = (ipParts: any) => (ipParts[0] << 24) | (ipParts[1] << 16) | (ipParts[2] << 8) | ipParts[3];

        const startInt = ipToInt(startParts);
        const endInt = ipToInt(endParts);
        const ipv4RangeSize = endInt - startInt;

        if (value.includes('/')) {
          return createError({ message: "Please don't enter mask for range" });
        }

        if (startInt == endInt) {
          return createError({ message: 'End IP range should greater than start IP' });
        }
        if (ipv4RangeSize > 64) {
          return createError({ message: 'The IPV4 range must not exceed 64 addresses' });
        }

        // IPV6 validation for range limit
        const ip6ToBigInt = (ip: any) => {
          const parts = ip.split(':');
          let value = BigInt(0);
          parts.forEach((part: any) => {
            value = (value << BigInt(16)) + BigInt(parseInt(part, 16) || 0);
          });
          return value;
        };

        const startBigInt = ip6ToBigInt(startIp);
        const endBigInt = ip6ToBigInt(endIp);
        const ipv6RangeSize = endBigInt - startBigInt;

        if (ipv6RangeSize > BigInt(64)) {
          return createError({ message: 'The IPV6 range must not exceed 64 addresses' });
        }

        // Validation IPv4 range
        if (Address4.isValid(startIp) && Address4.isValid(endIp)) {
          const start = new Address4(startIp);
          const end = new Address4(endIp);

          if (start.correctForm() > end.correctForm()) {
            return createError({ message: 'Invalid IPv4 range' });
          }

          setIpVersion(IPvType.v4);

          // Validation IPv6 range
        } else if (Address6.isValid(startIp) && Address6.isValid(endIp)) {
          const start = new Address6(startIp);
          const end = new Address6(endIp);

          if (start.correctForm() > end.correctForm()) {
            return createError({ message: 'Invalid IPv6 range' });
          }
          setIpVersion(IPvType.v6);
        } else {
          return createError({ message: 'Please enter valid range' });
        }
        return true;
      } else {
        // Validation for duplicates
        if (seenIps.has(value)) {
          return createError({ message: `Duplicate IP address: ${value}` });
        }

        // Validation IPv4 with mask
        if (Address4.isValid(value)) {
          const parts = value.split('/');
          if (parts.length === 2 && (parseInt(parts[1], 10) < 9 || parseInt(parts[1], 10) > 32)) {
            return createError({ message: 'IPv4 subnet mask or prefix must be between /9 to /32' });
          }

          const address = new Address4(value);
          const networkAddress = `${address.startAddress().correctForm()}/${address.subnetMask}`;
          if (address.startAddress().correctForm() !== value.split('/')[0]) {
            correctedValue = correctedValue.replace(value, networkAddress);

            correctionMade = true;
          }

          seenIps.add(networkAddress);
          setIpVersion(IPvType.v4);
        }
        // Validation IPv6 with mask
        else if (Address6.isValid(value)) {
          if (!/^.+\/\d{1,3}$/.test(value)) {
            return createError({ message: `${value} is missing a subnet mask or prefix, e.g., /128` });
          }
          const address = new Address6(value);
          const newIPV6Address = address.parsedAddress.join(':');
          const networkAddress = `${address.startAddress().correctForm()}/${address.subnetMask}`;

          if (address.startAddress().correctForm() !== newIPV6Address) {
            correctedValue = correctedValue.replace(value, networkAddress);
            correctionMade = true;
          }
          setIpVersion(IPvType.v6);
          seenIps.add(networkAddress);
        } else {
          return createError({ message: `Invalid IP Address: ${value}` });
        }
      }
    }
    return { correctedValue, correctionMade };
  } catch (error) {
    return createError({ message: 'Please enter valid IP addresses or range' });
  }
};

const addFirewallRuleSchemaV2 = ({ setIpSourceCorrection, setIpDestinationCorrection, setIpVersion }: ValidationSchema) =>
  yup.object().shape({
    source: yup.object().shape({
      location: yup
        .string()
        .trim()
        .min(2, 'Please enter at least a two-character state code')
        .matches(yupMatchesParams.firewallLocation.pattern, yupMatchesParams.firewallLocation.errorMessage),
      hostName: yup.string().trim().matches(yupMatchesParams.fqdn.pattern, yupMatchesParams.fqdn.hostErrorMessage),
      ipAddress: yup
        .string()
        .required('Source IP Address is required')
        .test('valid-ip-address', 'Please enter valid IP addresses', function (value) {
          const validIPAddress = validateIPAddress(value, this.createError, setIpVersion);
          if (validIPAddress && validIPAddress.correctionMade && validIPAddress.correctedValue !== value) {
            setIpSourceCorrection(validIPAddress.correctedValue);
            return false;
          }
          setIpSourceCorrection('');
          return validIPAddress;
        }),

      port: yup.string().test('sourceValidate', 'Source Port should be valid', function (this: yup.TestContext, value) {
        return validateSourcePort(value, this);
      }),
    }),
    destination: yup.object().shape({
      location: yup
        .string()
        .trim()
        .min(2, 'Please enter at least a two-character state code')
        .matches(yupMatchesParams.firewallLocation.pattern, yupMatchesParams.firewallLocation.errorMessage),
      hostName: yup.string().trim().matches(yupMatchesParams.fqdn.pattern, yupMatchesParams.fqdn.hostErrorMessage),
      ipAddress: yup
        .string()
        .required('Destination IP Address is required')
        .test('valid-ip-address', 'Please enter valid IP addresses', function (value) {
          const validIPAddress = validateIPAddress(value, this.createError, setIpVersion);
          if (validIPAddress && validIPAddress.correctionMade && validIPAddress.correctedValue !== value) {
            setIpDestinationCorrection(validIPAddress.correctedValue);
            return false;
          }
          setIpDestinationCorrection('');
          return validIPAddress;
        })
        .test('SourceIPAddress-DestinationIPAddress', 'Source and Destination IP Address cannot be the same', function (value) {
          if (!this.options.context) return true;
          const sourceIPs = this.options.context.source?.ipAddress?.split(',').map((ip: string) => ip.trim()) || [];
          const destinationIPs = value?.split(',').map((ip: string) => ip.trim()) || [];
          return sourceIPs.every((sourceIP: string) => !destinationIPs.includes(sourceIP));
        })
        .test('SourceIPAddress-DestinationIPAddress', 'Source and Destination must have matching IP versions (v4/v6)', function (value) {
          const sourceIPaddress = this.options.context?.source.ipAddress;
          if (sourceIPaddress && value) {
            if (sourceIPaddress.includes(':') === value.includes(':') || sourceIPaddress.includes('.') === value.includes('.')) {
              return true;
            } else {
              return false;
            }
          }
          return true;
        }),
      port: yup.string().test('validateDestinationPort', 'Destination Port should be valid', function (this: yup.TestContext, value) {
        return validateDestinationPort(value, this);
      }),
    }),
    protocol: yup
      .string()
      .required('Protocol is required')
      .oneOf(Object.values(ProtocolTypes), 'Please select the valid protocol from the options'),
    notes: yup.string(),
  });

export default addFirewallRuleSchemaV2;
