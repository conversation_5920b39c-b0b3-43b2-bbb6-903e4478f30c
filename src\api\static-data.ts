import axios from 'axios';
import { constructRoute, isProductionBuild } from 'utils/namespace-routing';

async function getStaticData(fileName: string) {
  const config = {
    method: 'GET',
    baseURL: `${window.location.protocol}//${window.location.host}`,
  };
  var staticDataRoute = `staticData/${fileName}`;
  return (await axios.get(isProductionBuild() ? constructRoute(staticDataRoute) : staticDataRoute, config)).data;
}

export async function getVMSizeCores() {
  return getStaticData('noOfCores.json');
}

export async function getVMSizeMemory() {
  return getStaticData('memory.json');
}

export async function getNavigationBarData() {
  return getStaticData('navigationBarData.json');
}

export async function getAllAdministrationData() {
  return getStaticData('administrationData.json');
}
export async function getAdministrationCatalogListItems() {
  return getStaticData('administrationCatalogList.json');
}

export async function getAdministrationCatalogItems() {
  const catalogs = await getStaticData('administrationCatalogGroupData.json');
  return catalogs.content;
}

export async function getServiceCatalogData() {
  const catalogs = await getAdministrationCatalogItems();
  return catalogs.serviceCatalog;
}

export async function getProjectData() {
  const catalogs = await getAdministrationCatalogItems();
  return catalogs.project;
}

export async function getGroupsData() {
  const catalogs = await getAdministrationCatalogItems();
  return catalogs.groups;
}

export async function getTagsData() {
  const catalogs = await getAdministrationCatalogItems();
  return catalogs.tags;
}

export async function getFirewallProtocolItems() {
  const catalogs = (await axios.get('/staticData/firewallProtocolData.json')).data;
  return catalogs.content;
}

export async function getVMSizingData() {
  const catalogs = await getAdministrationCatalogItems();
  return catalogs.vmsizing;
}

export async function getPermissionCatalogItems() {
  const catalogs = await getAdministrationCatalogItems();
  return catalogs.permission;
}

export async function getPermissionKeyItems() {
  const permission = await getStaticData('permissionKeyData.json');
  return permission.content;
}

export async function getPermissionNames() {
  const permission = await getStaticData('permissionNameData.json');
  return permission.permissions;
}

export async function getProjectDataCenterItems() {
  const projectDataCenter = await getStaticData('projectDataCenterData.json');
  return projectDataCenter.content;
}

export async function getProjectNetworkConfigItems() {
  const projectNetworkConfig = await getStaticData('projectNetworkConfigData.json');
  return projectNetworkConfig.dataCenter;
}

export async function getProjectTagItems() {
  const projectTag = await getStaticData('projectTagData.json');
  return projectTag.content;
}

export async function getRolesCatalogItems() {
  const catalogs = await getAdministrationCatalogItems();
  return catalogs.role;
}

export async function getTeamItems() {
  const catalogs = await getAdministrationCatalogItems();
  return catalogs.team;
}

export async function getGroupNames() {
  const groups = await getStaticData('groupNameData.json');
  return groups.content;
}

export async function getApprovalItems() {
  const catalogs = await getAdministrationCatalogItems();
  return catalogs.approval;
}

export async function getCapacityTableData() {
  const tableData = await getStaticData('capacityPlanningtableData.json');
  return tableData.content;
}
export async function getVcenterDataOne() {
  const tableData = await getStaticData('capacityPlanningtableData.json');
  return tableData.vcenterDataOne;
}
export async function getVcenterDataTwo() {
  const tableData = await getStaticData('capacityPlanningtableData.json');
  return tableData.vcenterDataTwo;
}
export async function getVcenterDataThree() {
  const tableData = await getStaticData('capacityPlanningtableData.json');
  return tableData.datacenterDataOne;
}
export async function getVcenterDataFour() {
  const tableData = await getStaticData('capacityPlanningtableData.json');
  return tableData.datacenterDataTwo;
}

export async function getFacilitiesData() {
  const facilitiesData = await getStaticData('capacityPlanningtableData.json');
  return facilitiesData.mapContent;
}
export async function getResourcesData() {
  const facilitiesData = await getStaticData('capacityPlanningtableData.json');
  return facilitiesData.tableContent;
}
export async function getVcenterData() {
  const data = await getStaticData('capacityPlanningtableData.json');
  return data.vcenterdata;
}
export async function getDatacenterData() {
  const data = await getStaticData('capacityPlanningtableData.json');
  return data.datacenterdata;
}
