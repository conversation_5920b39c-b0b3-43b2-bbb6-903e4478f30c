// eslint-disable-next-line no-unused-vars
import { ColumnData, NblTable } from 'sharedComponents/NblTable';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import useDataGridUtils from 'hooks/useDataGridUtils';
// eslint-disable-next-line no-unused-vars
import { GridRenderEditCellParams, GridRowSelectionModel } from '@mui/x-data-grid';
import { useEffect, useState } from 'react';
import { Credentials } from '..';

interface CredentialsFormTableProps {
  data: Credentials[];
  editedCredData: (values: Credentials[]) => void;
}

const CredentialsFormTable: React.FunctionComponent<CredentialsFormTableProps> = ({ data, editedCredData }) => {
  //Hooks
  const canEdit = useDataGridUtils();

  //States
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [credData, setCredData] = useState<Credentials[]>(data);

  //sideEffect
  useEffect(() => {
    const dataLength = Array.from({ length: data.length }, (_, index) => index);
    setSelectedRows(dataLength);
    setCredData(data);
  }, [data]);

  //utility
  const handleEditCellChange = (params: GridRenderEditCellParams, event: any) => {
    const rowToUpdate = [...credData];

    if (rowToUpdate[params.id as number]) {
      rowToUpdate[params.id as number][params.field as keyof Credentials] = event.target.value;
      setCredData(rowToUpdate);
    }
    params.api.setEditCellValue({ id: params.id, field: params.field, value: event.target.value });
  };

  const handleRow = (data: Credentials[]) => {
    return data?.map((item: Credentials, mapId: number) => ({
      id: mapId,
      name: item.name,
      path: item.path,
      provider: item.provider,
    }));
  };

  const columns: ColumnData[] = [
    {
      field: 'name',
      headerName: 'Name',
      flex: 1,
      width: 700,
      editable: true,
      renderCell: (params) =>
        canEdit?.canEditRow(canEdit.selectedRows, params.id, canEdit.isEditMode) ? (
          <NblTextField disabled value={params.value} type={'text'} label={''} name={''} />
        ) : (
          params.value
        ),

      renderEditCell: (params) =>
        canEdit?.canEditRow(canEdit.selectedRows, params.id, canEdit.isEditMode) ? (
          <NblTextField
            value={params.value}
            handleChange={(event) => handleEditCellChange(params, event)}
            type={'text'}
            label={''}
            name={''}
          />
        ) : (
          params.value
        ),
    },
    {
      field: 'path',
      headerName: 'Path',
      flex: 1,
      width: 700,
      editable: true,
      renderCell: (params) =>
        canEdit?.canEditRow(canEdit.selectedRows, params.id, canEdit.isEditMode) ? (
          <NblTextField
            value={params.value}
            type={'text'}
            label={''}
            name={''}
            handleChange={(event) => handleEditCellChange(params, event)}
          />
        ) : (
          params.value
        ),

      renderEditCell: (params) =>
        canEdit?.canEditRow(canEdit.selectedRows, params.id, canEdit.isEditMode) ? (
          <NblTextField
            value={params.value}
            handleChange={(event) => handleEditCellChange(params, event)}
            type={'text'}
            label={''}
            name={''}
          />
        ) : (
          params.value
        ),
    },
    {
      field: 'provider',
      headerName: 'Provider',
      flex: 1,
      width: 700,
      editable: true,
      renderCell: (params) =>
        canEdit?.canEditRow(canEdit.selectedRows, params.id, canEdit.isEditMode) ? (
          <NblTextField disabled value={params.value} type={'text'} label={''} name={''} />
        ) : (
          params.value
        ),

      renderEditCell: (params) =>
        canEdit?.canEditRow(canEdit.selectedRows, params.id, canEdit.isEditMode) ? (
          <NblTextField
            value={params.value}
            handleChange={(event) => handleEditCellChange(params, event)}
            type={'text'}
            label={''}
            name={''}
          />
        ) : (
          params.value
        ),
    },
  ];

  const saveHandler = () => {
    editedCredData(credData);
  };

  const deleteHandler = () => {
    const updatedData = data.filter((_, index) => !selectedRows.includes(index));
    editedCredData(updatedData);
  };

  const rowSelectionHandler = (selected: GridRowSelectionModel) => {
    setSelectedRows(selected as number[]);
  };

  return (
    <NblTable
      setEditMode={canEdit.setEditMode}
      selectedRows={canEdit.selectedRows}
      setSelectedRows={canEdit.setSelectedRows}
      rows={handleRow(credData)}
      columns={columns}
      checkboxSelection={true}
      showEditButton={true}
      saveDataHandler={saveHandler}
      deleteHandler={deleteHandler}
      onRowSelectionModelChange={rowSelectionHandler}
      rowSize={'100'}
      showResetFilter={false}
      hideFooterAndPagination={true}
    />
  );
};

export default CredentialsFormTable;
