export default interface OnboardGroupRequest {
  groupName: string;
  emailDistribution: string[];
  description: string;
  nebulaClientId: string;
  organizationId: string;
  verticalId: string;
  departmentId: string;
  catalogPermissions: CatalogPermission[];
}
export type CatalogPermission = {
  catalogId: string;
  domain: {
    domainId: string | null;
    roles: {
      roleId: string;
    }[];
  }[];
};
