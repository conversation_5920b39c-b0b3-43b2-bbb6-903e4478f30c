import React, { useEffect, useState } from 'react';
// eslint-disable-next-line no-unused-vars
import { Credentials } from '..';
import NblFormContainer, { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import { FormValues } from '..';
import NblTypography from 'sharedComponents/NblTypography';
import { generateEnum } from 'utils/common';
import NblChipComponent from 'sharedComponents/NblFormInputs/NblChipComponent';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import NblDivider from 'sharedComponents/NblDivider';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import CredentialsValidationSchema from 'yupSchema/CredentialsValidationSchema';
import CredentialsFormTable from '../ViewCredentialsTable';
import AddCredentialsForm from '../AddCredentialsForm';
import ProjectAppEnvDropdown from 'componentsV2/Administration/ProjectAppEnvDropdown';
import { MultiENVProjectsResponse } from 'types/Interfaces/MultiENVProjectsResponse';

interface CreateSpecFormEKSProps {
  projectData?: MultiENVProjectsResponse;
  catalogShortName?: string;
}

const CreateSpecFormEKS: React.FunctionComponent<CreateSpecFormEKSProps> = ({ projectData, catalogShortName }) => {
  const [showTagForm, setShowCredForm] = useState<boolean>(false);
  const { nblFormProps, nblFormValues } = useNblForms<FormValues>();
  const FIELDNAMES = generateEnum<FormValues>(nblFormValues);
  const initialCredValues: Credentials = {
    name: '',
    path: '',
    provider: 'ssm',
  };
  const credentialsValidationSchema = CredentialsValidationSchema();
  const theme = useTheme<NebulaTheme>();

  const handleMultiInput = async (event: any, newValue: string[], field: string) => {
    const { value } = event.target;
    if (value) {
      nblFormProps.setFieldValue(field, newValue);
    }
  };

  const getProjectSettings = () => {
    return projectData?.map((project) => ({ label: project.name, value: project.id }));
  };

  useEffect(() => {
    const selectedProject = projectData?.find((project) => project.id === nblFormValues.projectName);
    if (selectedProject) {
      nblFormProps.setFieldValue(FIELDNAMES.project, selectedProject.id);
    }
  }, [nblFormValues.projectName, projectData]);

  const handleCredData = (values: Credentials) => {
    nblFormProps.setFieldValue('credentials', [...nblFormValues.credentials, values]);
    setShowCredForm(false);
  };

  const handleEditedCredData = (values: Credentials[]) => {
    nblFormProps.setFieldValue('credentials', values);
  };

  const projectAppEnvValues = {
    domain: nblFormValues.domain,
    projectName: nblFormValues.projectName,
    application: nblFormValues.application,
    environment: nblFormValues.environment,
  };

  const formErrors = {
    domain: nblFormProps.errors.domain,
    projectName: nblFormProps.errors.projectName,
    application: nblFormProps.errors.application,
    environment: nblFormProps.errors.environment,
  };

  const formTouched = {
    domain: nblFormProps.touched.domain,
    projectName: nblFormProps.touched.projectName,
    application: nblFormProps.touched.application,
    environment: nblFormProps.touched.environment,
  };
  const renderOrganizationDetails = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'Project Details'}</NblTypography>
        <NblGridContainer columns={3} spacing={3}>
          <ProjectAppEnvDropdown
            values={projectAppEnvValues}
            formErrors={formErrors}
            formTouched={formTouched}
            handleChange={(field, value) => {
              nblFormProps.setFieldValue(field, value);
            }}
            handleBlur={(field) => nblFormProps.setFieldTouched(field, true)}
            defaultProjectOptions={getProjectSettings()}
            showDomain={false}
            catalogShortName={catalogShortName}
          />
        </NblGridContainer>
        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              label={'IAC Repo Name'}
              mandatory
              name={FIELDNAMES.iacProjectName}
              placeholder="Enter Repo Name"
              value={nblFormValues.iacProjectName}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.iacProjectName}
              error={Boolean(nblFormProps.touched.iacProjectName && nblFormProps.errors.iacProjectName)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'number'}
              label={'Namespace Id'}
              name={FIELDNAMES.namespaceId}
              placeholder={'Enter namespaceId'}
              value={nblFormValues.namespaceId}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.namespaceId}
              error={Boolean(nblFormProps.touched.namespaceId && nblFormProps.errors.namespaceId)}
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };

  const renderEKSDetails = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'EKS Details'}</NblTypography>

        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.cluster_name}
              label={'Cluster Name'}
              placeholder="Enter Cluster Name"
              value={nblFormValues.cluster_name}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.cluster_name}
              error={Boolean(nblFormProps.touched.cluster_name && nblFormProps.errors.cluster_name)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.cluster_version}
              label={'Cluster Version'}
              placeholder="Enter Cluster Version"
              value={nblFormValues.cluster_version}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.cluster_version}
              error={Boolean(nblFormProps.touched.cluster_version && nblFormProps.errors.cluster_version)}
            />
          </NblGridItem>
        </NblGridContainer>
        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.ami_id}
              label={'AMI Id'}
              placeholder="Enter AMI Id"
              value={nblFormValues.ami_id}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.ami_id}
              error={Boolean(nblFormProps.touched.ami_id && nblFormProps.errors.ami_id)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.vpc_id}
              label={'VPC ID'}
              placeholder="Enter VPC Id"
              value={nblFormValues.vpc_id}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.vpc_id}
              error={Boolean(nblFormProps.touched.vpc_id && nblFormProps.errors.vpc_id)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.zone_id}
              label={'Zone ID'}
              placeholder="Enter Zone Id"
              value={nblFormValues.zone_id}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.zone_id}
              error={Boolean(nblFormProps.touched.zone_id && nblFormProps.errors.zone_id)}
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };

  const renderEKSSizeDetails = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'EKS Size Details'}</NblTypography>

        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'number'}
              name={FIELDNAMES.min_size}
              label={'Min Size'}
              placeholder="Enter Min Size"
              value={nblFormValues.min_size}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.min_size}
              error={Boolean(nblFormProps.touched.min_size && nblFormProps.errors.min_size)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'number'}
              name={FIELDNAMES.max_size}
              label={'Max Size'}
              placeholder="Enter Max Size"
              value={nblFormValues.max_size}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.max_size}
              error={Boolean(nblFormProps.touched.max_size && nblFormProps.errors.max_size)}
            />
          </NblGridItem>
        </NblGridContainer>
        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'number'}
              name={FIELDNAMES.desired_size}
              label={'Desired Size'}
              placeholder="Enter Desired Size"
              value={nblFormValues.desired_size}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.desired_size}
              error={Boolean(nblFormProps.touched.desired_size && nblFormProps.errors.desired_size)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'number'}
              name={FIELDNAMES.disk_size}
              label={'Disk Size'}
              placeholder="Enter Disk Size"
              value={nblFormValues.disk_size}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.disk_size}
              error={Boolean(nblFormProps.touched.disk_size && nblFormProps.errors.disk_size)}
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };

  const renderEKSNetworkDetails = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'Network Details'}</NblTypography>

        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.aws_region}
              label={'AWS Region'}
              placeholder="Enter AWS Region"
              value={nblFormValues.aws_region}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.aws_region}
              error={Boolean(nblFormProps.touched.aws_region && nblFormProps.errors.aws_region)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblChipComponent
              label="Control Plane Subnets"
              name={FIELDNAMES.control_plane_subnets}
              value={nblFormValues.control_plane_subnets}
              handleChange={(event, newValue) => {
                handleMultiInput(event, newValue, FIELDNAMES.control_plane_subnets);
              }}
              handleBlur={nblFormProps.handleBlur}
              error={nblFormProps.touched.control_plane_subnets && Boolean(nblFormProps.errors.control_plane_subnets)}
              options={[]}
              mandatory
            />
          </NblGridItem>
        </NblGridContainer>
        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblChipComponent
              label="Public Subnets"
              name={FIELDNAMES.public_subnets}
              value={nblFormValues.public_subnets}
              handleChange={(event, newValue) => {
                handleMultiInput(event, newValue, FIELDNAMES.public_subnets);
              }}
              handleBlur={nblFormProps.handleBlur}
              error={nblFormProps.touched.public_subnets && Boolean(nblFormProps.errors.public_subnets)}
              options={[]}
              mandatory
            />
          </NblGridItem>
          <NblGridItem>
            <NblChipComponent
              label="Node Group Subnets"
              name={FIELDNAMES.node_group_subnets}
              value={nblFormValues.node_group_subnets}
              handleChange={(event, newValue) => {
                handleMultiInput(event, newValue, FIELDNAMES.node_group_subnets);
              }}
              handleBlur={nblFormProps.handleBlur}
              error={nblFormProps.touched.node_group_subnets && Boolean(nblFormProps.errors.node_group_subnets)}
              options={[]}
              mandatory
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };

  const renderSecurityDetails = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'Security Details'}</NblTypography>

        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.falcon_cid}
              label={'Falcon CID'}
              placeholder="Enter Falcon CID"
              value={nblFormValues.falcon_cid}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.falcon_cid}
              error={Boolean(nblFormProps.touched.falcon_cid && nblFormProps.errors.falcon_cid)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.falcon_client}
              label={'Falcon Client'}
              placeholder="Enter Falcon Client"
              value={nblFormValues.falcon_client}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.falcon_client}
              error={Boolean(nblFormProps.touched.falcon_client && nblFormProps.errors.falcon_client)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.falcon_secret}
              label={'Falcon Secret'}
              placeholder="Enter Falcon Secret"
              value={nblFormValues.falcon_secret}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.falcon_secret}
              error={Boolean(nblFormProps.touched.falcon_secret && nblFormProps.errors.falcon_secret)}
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };

  const renderDomainDetails = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'Domain Details'}</NblTypography>

        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.waf_name}
              label={'WAF Name'}
              placeholder="Enter Waf Name"
              value={nblFormValues.waf_name}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.waf_name}
              error={Boolean(nblFormProps.touched.waf_name && nblFormProps.errors.waf_name)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.default_action}
              label={'Default Action'}
              placeholder="Enter Default Action"
              value={nblFormValues.default_action}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.default_action}
              error={Boolean(nblFormProps.touched.default_action && nblFormProps.errors.default_action)}
            />
          </NblGridItem>
        </NblGridContainer>
        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.acm_domain_name}
              label={'ACM Domain Name'}
              placeholder="Enter Domain name"
              value={nblFormValues.acm_domain_name}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.acm_domain_name}
              error={Boolean(nblFormProps.touched.acm_domain_name && nblFormProps.errors.acm_domain_name)}
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };

  const renderCredentials = () => {
    return (
      <>
        <NblGridContainer>
          <NblGridItem>
            <NblGridContainer>
              <NblDivider length={'100%'} strokeWidth={4} opacity={1} mb={'10px'} />
              <NblGridItem width="80%" padding="5px">
                <NblFormContainer<Credentials>
                  title={'Add Credentials'}
                  caption={''}
                  formInitialValues={initialCredValues}
                  formValidationSchema={credentialsValidationSchema}
                  steps={[
                    {
                      caption: '',
                      errorFields: ['name', 'path', 'provider'],
                      icon: '',
                      status: 'current',
                      title: '',
                    },
                  ]}
                  formType="subform"
                >
                  <NblGridItem width="100%">
                    {nblFormValues.credentials?.length > 0 && (
                      <CredentialsFormTable data={nblFormValues.credentials} editedCredData={handleEditedCredData} />
                    )}
                  </NblGridItem>
                  <NblGridItem padding="20px">
                    <br />
                    <NblButton
                      buttonID="Add Credential"
                      color="primary"
                      onClick={() => {
                        setShowCredForm(true);
                      }}
                      variant="outlined"
                    >
                      Add Credential
                    </NblButton>
                  </NblGridItem>
                  {showTagForm && (
                    <NblGridItem rowspan={2} colspan={4}>
                      <NblBorderContainer
                        backgroundColor={theme.palette.border.backgroundColor2}
                        border={`1px solid ${theme.palette.border.backgroundColor2}`}
                        borderRadius="10px"
                        padding="25px"
                      >
                        <AddCredentialsForm
                          onClose={() => {
                            setShowCredForm(false);
                          }}
                          setCredData={handleCredData}
                        />
                      </NblBorderContainer>
                    </NblGridItem>
                  )}
                </NblFormContainer>
              </NblGridItem>
            </NblGridContainer>
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };

  return (
    <NblGridContainer>
      {renderOrganizationDetails()}
      {renderEKSDetails()}
      {renderEKSSizeDetails()}
      {renderEKSNetworkDetails()}
      {renderDomainDetails()}
      {renderSecurityDetails()}
      {renderCredentials()}
    </NblGridContainer>
  );
};

export default CreateSpecFormEKS;
