import React, { createContext, useContext, useMemo } from 'react';

import AdministrationService from 'api/ApiService/AdministrationService';
import FirewallService from '../ApiService/SecurityService';
import UsageMetricsService from 'api/ApiService/UsageMetricsService';
import UserService from './UserService';
import AssetService from './AssetService';
import PermissionService from './PermissionService';
import ComputeService from './ComputeService';
import IPAMService from './IPAMService';
import MonitoringService from './MonitoringService';
import ItentialService from './ItentialService';
import StorageService from './StorageService';
import onBoardService from './OnBoardService';
import CapacityPlanningService from './CapacityPlanningService';
import SiteMapService from './SiteMapService';
import AdminstrationAuthService from './AdminstrationAuthService';
import CatalogAuthService from './CatalogAuthService';
import CatalogService from './CatalogService';
import ProjectCreationService from './ProjectCreationService';
import DbaasService from './DbaasService';
import SpecflowService from './SpecflowService';
import ServiceRequestService from './ServiceRequestService';
import SecretsManagementService from './SecretsManagementService';
import VMSizeService from './VMSizeService';
import ZTPService from './ZTP';

interface ApiServiceProviderProps {
  children: React.ReactElement;
}

class APIServicesContext {
  apiAdministrationService: AdministrationService;
  apiFirewallService: FirewallService;
  apiUsageMetricsService: UsageMetricsService;
  apiUserService: UserService;
  apiAssetService: AssetService;
  apiComputeService: ComputeService;
  apiDbaasService: DbaasService;
  apiPermissionService: PermissionService;
  apiIpamService: IPAMService;
  apiMonitoringService: MonitoringService;
  apiItentialService: ItentialService;
  apiStorageService: StorageService;
  apiOnBoardingService: onBoardService;
  apiCapacityPlanningService: CapacityPlanningService;
  apiSiteMapService: SiteMapService;
  apiAdministrationAuthService: AdminstrationAuthService;
  apiCatalogAuthService: CatalogAuthService;
  apiCatalogService: CatalogService;
  apiProjectCreationService: ProjectCreationService;
  apiSpecFlowService: SpecflowService;
  apiServiceRequestService: ServiceRequestService;
  apiSecretsManagement: SecretsManagementService;
  apiVMSizeService: VMSizeService;
  apiZTPService: ZTPService;

  constructor() {
    this.apiAdministrationService = new AdministrationService();
    this.apiFirewallService = new FirewallService();
    this.apiUsageMetricsService = new UsageMetricsService();
    this.apiUserService = new UserService();
    this.apiAssetService = new AssetService();
    this.apiComputeService = new ComputeService();
    this.apiDbaasService = new DbaasService();
    this.apiPermissionService = new PermissionService();
    this.apiIpamService = new IPAMService();
    this.apiMonitoringService = new MonitoringService();
    this.apiItentialService = new ItentialService();
    this.apiStorageService = new StorageService();
    this.apiOnBoardingService = new onBoardService();
    this.apiCapacityPlanningService = new CapacityPlanningService();
    this.apiSiteMapService = new SiteMapService();
    this.apiAdministrationAuthService = new AdminstrationAuthService();
    this.apiCatalogAuthService = new CatalogAuthService();
    this.apiCatalogService = new CatalogService();
    this.apiProjectCreationService = new ProjectCreationService();
    this.apiSpecFlowService = new SpecflowService();
    this.apiServiceRequestService = new ServiceRequestService();
    this.apiSecretsManagement = new SecretsManagementService();
    this.apiVMSizeService = new VMSizeService();
    this.apiZTPService = new ZTPService();
  }
}

// @ts-ignore
export const useApiService = (): APIServicesContext => useContext(ApiServiceContext);

export const ApiServiceContext = createContext<APIServicesContext | undefined>(undefined);

const ApiServiceProvider: React.FunctionComponent<ApiServiceProviderProps> = ({ children }: ApiServiceProviderProps) => {
  const apiServices = useMemo(() => new APIServicesContext(), []); // Memoize the context value
  return <ApiServiceContext.Provider value={apiServices}>{children}</ApiServiceContext.Provider>;
};

// Memoize ApiServiceProvider to avoid unnecessary rerenders unless accessToken changes
const MemoizedApiServiceProvider = React.memo(ApiServiceProvider, (prevProps, nextProps) => {
  // @ts-ignore
  return prevProps.accessToken === nextProps.accessToken;
});

export default MemoizedApiServiceProvider;
