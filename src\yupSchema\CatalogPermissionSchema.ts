import * as yup from 'yup';
import { yupMatchesParams } from '../utils/common';

const PermissionSchema = yup.object().shape({
  groupName: yup.string().required('Group Name is required'),
  domain: yup.mixed().when('$domainApplicable', {
    is: true,
    then: yup.string().required('Domain is required').nullable(true),
    otherwise: yup.string().nullable(),
  }),
  role: yup.string().required('Role is required'),
});

const TagsSchema = yup.object().shape({
  key: yup.string().required('Key is required'),
  value: yup.string().required('Value is required'),
  active: yup.string().required('Active is required'),
  description: yup
    .string()
    .required('Description is required')
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
  overridable: yup.string().required('Override is required'),
});

const CatalogPermissionSchema = yup.object().shape({
  level01: yup.string().required('Level01 is required'),
  level02: yup.string().required('Level02 is required'),
  level03: yup.string().required('Level03 is required'),
  applicableDomains: yup.mixed().when('domainApplicable', {
    is: true,
    then: yup.array().of(yup.string()).min(1, 'At least one domain should be provided').required('Domain is required').nullable(true),
    otherwise: yup.array().nullable(),
  }),
  serviceCatalogName: yup
    .string()
    .trim()
    .required('Name is required')
    .matches(yupMatchesParams.catalogName.pattern, yupMatchesParams.catalogName.errorMessage),
  description: yup
    .string()
    .required('Description is required')
    .matches(yupMatchesParams.catalogDescription.pattern, yupMatchesParams.catalogDescription.errorMessage),
  icon: yup
    .string()
    .required('Icon is required')
    .matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),
  component: yup.string().required('Component is required'),
  catalogPermissions: yup
    .array()
    .of(PermissionSchema)
    .test('unique-permission-combo', 'Duplicate permissions are not allowed', (permissions) => {
      if (!permissions) return true;
      const seen = new Set();
      for (const { groupName, domain, role } of permissions) {
        const key = `${groupName}-${domain}-${role}`;
        if (seen.has(key)) return false;
        seen.add(key);
      }
      return true;
    }),
  catalogTags: yup.array().of(TagsSchema),
});

export default CatalogPermissionSchema;
