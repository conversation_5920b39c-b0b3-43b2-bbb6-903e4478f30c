import { CapacityPlanningMapViewIcon, CapacityPlanningTableViewIcon, CapacityPlanningToolViewIcon } from 'assets/images/icons/custom-icons';
import { useEffect, useState } from 'react';
import IconToggleGroup from '../components/IconToggleGroup';
import CplanTableView2 from './CplanTableView2';
import CplanCardView from './CplanCardview';
import TimeSliceDropdown from '../components/TimeSliceDropdown';
import CplanMapView2 from './CplanMapView2';
import { useLocation, useParams } from 'react-router';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import ResourceOverview from '../components/ResourceOverview';
import TimeStamp from '../components/TimeStamp';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import useNblNavigate from 'hooks/useNblNavigate';
import { useDispatch } from 'react-redux';
import { setSunburstLevel, setSelectedDomainIds } from 'store/reducers/capacityplanning';

const CplanMainDashboard: React.FunctionComponent = () => {
  const { catalogItem } = useParams();
  const location = useLocation();
  const navigate = useNblNavigate();
  const [selectedView, setSelectedView] = useState<string | undefined>(catalogItem);
  const defaultToggleIcons = [
    { value: 'tableview', label: 'Table View', component: <CapacityPlanningTableViewIcon /> },
    { value: 'cardview', label: 'Card View', component: <CapacityPlanningToolViewIcon /> },
    { value: 'mapview', label: 'Map View', component: <CapacityPlanningMapViewIcon /> },
  ];
  const dispatch = useDispatch();

  useEffect(() => {
    setSelectedView(catalogItem);
  }, [catalogItem]);
  useEffect(() => {
    dispatch(setSunburstLevel(1));
    dispatch(setSelectedDomainIds([1, 2]));
  }, []);

  const handleViewChange = (value: string) => {
    if (selectedView) {
      setSelectedView(value);
      const newPath = location.pathname.replace(selectedView, value);
      navigate(newPath);
    }
  };

  return (
    <NblGridContainer columns={20} spacing={2}>
      <NblGridItem colspan={14}>
        {selectedView === 'tableview' && <CplanTableView2 />}
        {selectedView === 'cardview' && <CplanCardView />}
        {selectedView === 'mapview' && <CplanMapView2 />}
      </NblGridItem>
      <NblGridItem colspan={6} overflowY="hidden">
        <NblFlexContainer justifyContent="flex-end" position="static" top="60" right="30" height="auto">
          <TimeStamp />
        </NblFlexContainer>
        <NblFlexContainer direction="column" height="auto">
          <NblFlexContainer justifyContent="end" height="auto" alignItems="center">
            <TimeSliceDropdown />
            <IconToggleGroup icons={defaultToggleIcons} selected={selectedView} onChange={handleViewChange}></IconToggleGroup>
          </NblFlexContainer>
          <NblGridItem height="auto">
            <ResourceOverview />
          </NblGridItem>
        </NblFlexContainer>
      </NblGridItem>
    </NblGridContainer>
  );
};

export default CplanMainDashboard;
