import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import * as api from 'api/static-data';
import Catalog from 'components/Catalog';
import VCenters from '.';

jest.mock('api/static-data', () => ({
  getVCenterData: jest.fn(),
}));

jest.mock('components/Catalog', () => ({
  __esModule: true,
  default: jest.fn(() => <div>Mocked Catalog</div>),
}));

describe('VMSizing', () => {
  it('renders a "No items to display" message when no data is fetched', async () => {
    (api.getVCenterData as jest.Mock).mockResolvedValue([]);

    render(<VCenters />);

    await waitFor(() => expect(screen.getByText('No items to display')).toBeInTheDocument());
  });

  it('handles error case gracefully', async () => {
    (api.getVCenterData as jest.Mock).mockRejectedValue(new Error('API error'));

    render(<VCenters />);

    await waitFor(() => expect(screen.getByText('No items to display')).toBeInTheDocument());
  });
});
