import React from 'react';

import useExposureParams from 'hooks/useExposureParams';
import { checkCanCreatePermission } from 'utils/common';
import CatalogItem from '../index';
import { PERMISSION_MESSAGES } from 'utils/constant';
import { CatalogTilesData } from 'types';

interface ServiceCatalogItemProps extends CatalogTilesData {}

const ServiceCatalogItem: React.FC<ServiceCatalogItemProps> = (props: ServiceCatalogItemProps) => {
  const { exposureParamsEnabled } = useExposureParams();
  const canCreate = checkCanCreatePermission(props.permissionsPath);

  const isDisabled = (props.disabled && !exposureParamsEnabled(props.name)) || !canCreate;

  return (
    <CatalogItem
      description={props.description}
      disabled={isDisabled}
      canAccess={canCreate}
      accessDeniedMessage={PERMISSION_MESSAGES.serviceCatalogItem}
      path={props.path || ''}
      id={props.id || ''}
      icon={props.icon}
      title={props.name}
    />
  );
};

export default ServiceCatalogItem;
