// types
import { createSlice } from '@reduxjs/toolkit';

export type MetricsData = {
  totalRequests: number;
  approvedRequests: number;
  processingRequests: number;
  completedRequests: number;
  groupedRequests?: any;
  failedRequests: number;
  rejectedRequests: number;
  cancelledRequests: number;
};

export type RequestList = {
  catalogName: string;
  createdBy: string;
  date: string;
  id: string;
  projectName: string;
  requestType: string;
  serviceRequestId: string;
  status: string;
};

export type GetRequestList = {
  data: RequestList[];
  metaData: {
    totalCount: number;
  };
};

export type TotalRequests = {
  month: string;
  requests: {
    status: string;
    catalogName: string;
    date: string;
  }[];
}[];

export type CatalogList = {
  id: string;
  name: string;
  value: string;
}[];

export type ResourceList = {
  id: string;
  name: string;
  value: string;
}[];

export type UsageMetricsRequestType = {
  metricsData: MetricsData;
  totalRequests: TotalRequests;
  catalogList: CatalogList;
  resourceList: ResourceList;
};

// initial state
const initialState: UsageMetricsRequestType = {
  metricsData: {
    approvedRequests: 0,
    completedRequests: 0,
    failedRequests: 0,
    processingRequests: 0,
    rejectedRequests: 0,
    totalRequests: 0,
    cancelledRequests: 0,
  },
  totalRequests: [],
  catalogList: [],
  resourceList: [],
};

// ==============================|| SLICE - MENU ||============================== //

const UsageMetricsRequestsSlice = createSlice({
  name: 'UsageMetricsRequest',
  initialState,
  reducers: {
    updateUsageMetricsRequest(state, action) {
      state.metricsData = action.payload.metricsData;
      state.totalRequests = action.payload.metricsData.groupedRequests;
      state.catalogList = action.payload.catalogs;
      state.resourceList = action.payload.resources;
    },
  },
});

export default UsageMetricsRequestsSlice.reducer;

export const { updateUsageMetricsRequest } = UsageMetricsRequestsSlice.actions;
