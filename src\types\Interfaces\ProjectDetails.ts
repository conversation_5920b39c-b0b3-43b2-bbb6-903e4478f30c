import NetworkData from './NetworkData';

type ProjectDetails = Array<{
  projectName: string;
  applications?: any;
  id: string;
  name: string;
  projectShortName: string;
  appId: string;
  projectSettings: {
    dataCenters: Array<{ name: string; description: string; networks: NetworkData[] }>;
    tags: Array<{ name: string; value: string }>;
  };
  description: string;
  createdAt: string;
  updatedAt: string;
}>;

export default ProjectDetails;
