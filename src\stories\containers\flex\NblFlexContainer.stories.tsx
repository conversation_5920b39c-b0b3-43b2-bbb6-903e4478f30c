//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NblButton from 'sharedComponents/Buttons/NblButton';

import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

type StoryProps = ComponentProps<typeof NblFlexContainer>;

export default {
  title: 'Containers/Flex/NblFlex Container',
  tags: ['autodocs'],
  component: NblFlexContainer,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    center: { type: 'boolean' },
    flex: { type: 'string' },
    overflowX: { type: 'string', control: 'radio', options: ['hidden', 'auto', 'scroll'] },
    overflowY: { type: 'string', control: 'radio', options: ['hidden', 'auto', 'scroll'] },
    height: { type: 'string' },
    alignItems: {
      type: 'string',
      control: 'select',
      options: ['normal', 'stretch', 'center', 'start', 'end', 'self-start', 'self-end', 'baseline', 'first baseline', 'last baseline'],
    },
    alignContent: {
      type: 'string',
      control: 'select',
      options: [
        'start',
        'end',
        'center',
        'stretch',
        'space-around',
        'space-between',
        'space-evenly',
        'baseline',
        'first baseline',
        'last baseline',
      ],
    },
    justifyContent: {
      type: 'string',
      control: 'select',
      options: ['start', 'end', 'center', 'stretch', 'space-around', 'space-between', 'space-evenly', 'left', 'right'],
    },
    padding: { type: 'string' },
    spacing: { type: 'number' },
    width: { type: 'string' },
    backgroundColor: { control: 'color' },
    position: { options: ['relative', 'absolute', 'static', 'fixed', 'sticky'] },
    direction: { options: ['row', 'column', 'row-reverse', 'column-reverse'] },
    scrollbar: { type: 'boolean' },
  },
} as Meta<StoryProps>;

type Story = StoryObj<StoryProps>;
const Template = (props: StoryProps) => {
  //States
  const [number, setNumber] = useState(5);
  //Handlers
  function handler(v: number) {
    if (number > 1 || v == 1) setNumber((prev) => prev + v);
  }

  //Jsx
  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: '10px',
        display: 'flex',
        flexDirection: 'column',
        gap: '10px',
        flexWrap: 'wrap',
        overflowX: 'hidden',
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <NblButton buttonID={'reduce-flex-item-btn'} variant="contained" color="info" onClick={() => handler(-1)}>
          Reduce Flex Item
        </NblButton>
        <NblButton buttonID={'add-flex-item-btn'} variant="contained" color="primary" onClick={() => handler(1)}>
          Add Flex Item
        </NblButton>
      </div>
      <NblFlexContainer {...props}>
        {Array.from({ length: number }).map(() => (
          <div
            key={length}
            style={{
              display: 'flex',
              alignItems: 'initial',
              justifyContent: 'initial',
              backgroundColor: '#0099D8',
              width: '200px',
              height: '200px',
              color: 'white',
              fontSize: '1.3rem',
            }}
          ></div>
        ))}
      </NblFlexContainer>
    </div>
  );
};

export const Default: Story = {
  args: {
    center: true,
    alignItems: 'normal',
    justifyContent: 'initial',
    alignContent: 'stretch',
    spacing: 2,
    flex: '1',
    width: '100%',
    height: '100%',
    overflowX: 'hidden',
    overflowY: 'scroll',
    padding: '0',
    backgroundColor: '#ffff',
    direction: 'row',
    position: 'static',
    wrap: 'wrap',
  },
  render: (args) => (
    <NebulaTheme>
      <Template {...args} />
    </NebulaTheme>
  ),
};
