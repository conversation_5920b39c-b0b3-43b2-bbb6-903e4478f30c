import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

export const AddRoleSchema = yup.object().shape({
  roleName: yup.string().trim().required('Role Name is required'),
  roleType: yup.string().required('Role Type is required'),
  permissionName: yup.array().of(yup.string()).min(1, 'Please select atleast one permission').required('Permision Name is required'),
  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage).required('Description is required'),
});