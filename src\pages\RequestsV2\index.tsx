import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { NblTabBar } from 'sharedComponents/NblNavigation/NblTabBar';
/* eslint-disable no-unused-vars */
import { State } from 'store/reducers/type';
import { PageInfoData } from 'types/Interfaces/PaginationResponse';
import { dateFormatter } from 'utils/common';
import { useApiService } from 'api/ApiService/context';
import MyRequests from 'types/Interfaces/MyRequests';
import MyRequestsGridV2 from 'componentsV2/Requests';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { DownStreamError } from 'types';
import { RequestStatus } from 'types/Enums';
import { useSearchParams } from 'react-router-dom';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import useNblNavigate from 'hooks/useNblNavigate';

export enum TABS {
  myRequests = 'My Requests',
  allRequests = 'All Requests',
}

const Requests: React.FC = () => {
  //Hooks
  const { userId } = useSelector((state: State) => state.user.userDetails);
  const [myFilter, setMyFilter] = useState<string | undefined>('');
  const apiAssetService = useApiService();
  const [searchParams] = useSearchParams();
  const theme = useTheme<NebulaTheme>();
  const navigate = useNblNavigate();

  //States
  const [filteredRequestsData, setFilteredRequestsData] = useState<MyRequests[]>([]);
  const [isRequestsLoading, setIsRequestsLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<number | null>(); // Default to "MyRequest" tab
  // Separate pageInfo states for both tabs
  const [myRequestsPageInfo, setMyRequestsPageInfo] = useState<PageInfoData>();
  const [allRequestsPageInfo, setAllRequestsPageInfo] = useState<PageInfoData>();

  //Local
  const { secondary } = theme.palette;

  //Side Effects
  useEffect(() => {
    if (searchParams.get('tab') === TABS.allRequests) setActiveTab(1);
    else if (searchParams) setActiveTab(0);
  }, [searchParams]);

  useEffect(() => {
    fetchRequestsListPaginated(1, 10, '', myFilter); // Fetch the first page with a default pageSize of 10
  }, [activeTab, userId, myFilter]);

  const getError = (downstreamError: DownStreamError[]) => {
    return downstreamError.map((error: DownStreamError) => error?.data?.message || '').join(', ');
  };

  const fetchRequestsListPaginated = (page: number, pageSize: number, sort?: string, filter?: string) => {
    if (userId.length === 0 || !searchParams || typeof activeTab !== 'number') {
      return;
    }
    const statusFilter = searchParams.get('requeststatus');
    setIsRequestsLoading(true);
    setMyFilter(filter);
    let parsedFilterObj = filter ? JSON.parse(filter) : {};
    if (activeTab === 0) {
      parsedFilterObj = {
        ...parsedFilterObj,
        createdBy: {
          contains: userId,
        },
        ...(statusFilter && {
          status: {
            contains: statusFilter,
          },
        }),
      };
    } else {
      parsedFilterObj = {
        ...parsedFilterObj,
        ...(statusFilter && {
          status: {
            contains: statusFilter,
          },
        }),
      };
    }

    filter = JSON.stringify(parsedFilterObj);
    const catalogName = 'metadata.serviceCatalog.catalogName';

    apiAssetService.apiAssetService
      .getMyRequestsv3(page, pageSize, sort, filter)
      .then((res) => {
        if (res.status) {
          const formattedData = res.data.items.map((asset, index) => ({
            sNo: (page - 1) * pageSize + index + 1,
            [catalogName]: asset.metadata?.serviceCatalog?.catalogName,
            id: asset?.serviceRequestId || asset?._id,
            serviceRequestId: asset?.serviceRequestId || asset?._id,
            status: asset?.status,
            createdBy: asset?.createdBy,
            createdAt: asset?.createdAt ? dateFormatter(asset.createdAt) : '',
            startedAt: asset?.startedAt ? dateFormatter(asset.startedAt) : '',
            completedAt: asset?.completedAt ? dateFormatter(asset.completedAt) : '',
            approvalStatus: asset?.approvalStatus,
            topsTicket: asset?.netopsaskTicket || '-',
            error:
              (asset?.status === RequestStatus.FAILED ||
                asset?.status === RequestStatus.PARTIAL ||
                asset?.status === RequestStatus.TIMED_OUT) &&
              asset?.downstreamError?.length
                ? getError(asset.downstreamError)
                : '',
          }));

          if (activeTab === 0) {
            setFilteredRequestsData(formattedData);
            setMyRequestsPageInfo(res.data.pageInfo);
          } else {
            setFilteredRequestsData(formattedData);
            setAllRequestsPageInfo(res.data.pageInfo);
          }
        } else {
          setFilteredRequestsData([]);
        }
      })
      .finally(() => {
        setIsRequestsLoading(false);
      });
  };

  const handleTabChange = (tab: string) => {
    if (tab === TABS.myRequests) {
      if (searchParams.get('tab')) navigate('');
      setActiveTab(0);
    }
    if (tab === TABS.allRequests) {
      setActiveTab(1);
    }
  };

  //Renders
  const tab = () => {
    return (
      <NblTabBar
        tabs={[
          {
            chip: myRequestsPageInfo?.totalDocs ?? 0,
            label: TABS.myRequests,
          },
          {
            chip: allRequestsPageInfo?.totalDocs ?? 0,
            label: TABS.allRequests,
          },
        ]}
        activeTab={activeTab || 0}
        onTabChange={handleTabChange}
      />
    );
  };

  const handleRefresh = () => {
    const currentPage = activeTab === 0 ? myRequestsPageInfo?.page : allRequestsPageInfo?.page;
    fetchRequestsListPaginated(currentPage ?? 1, 10, '', myFilter);
  };

  return (
    <NblBorderContainer padding="10px" backgroundColor={secondary.main}>
      {searchParams && typeof activeTab === 'number' && (
        <MyRequestsGridV2
          loading={isRequestsLoading}
          rows={filteredRequestsData}
          serverPaginationFn={fetchRequestsListPaginated}
          pageInfo={activeTab === 0 ? myRequestsPageInfo : allRequestsPageInfo}
          pageSizeOptions={['10', '20', '30', '40']}
          renderElement={tab()}
          onRefreshHandler={handleRefresh}
        />
      )}
    </NblBorderContainer>
  );
};

export default Requests;
