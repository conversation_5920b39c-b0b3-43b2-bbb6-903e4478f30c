import { validateDiskValue, yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

const AddDiskBlueSchema = () => {
  return yup.object().shape({
    diskCount: yup
      .number()
      .transform((value) => (value === '' || value === 0 ? undefined : value))
      .required('Disk Count is required') 
      .typeError('Disk Count must be a number'),
    addDisks: yup
      .array()
      .of(
        yup.object().shape({
          diskValue: yup
            .number()
            .required('Disk Value is required')
            .test('validate-disk', 'Disk value should be of min 10 GB and Max 4 TB', function (value) {
              const { diskUnit } = this.parent;
              const maxLimit = Number(process.env.REACT_APP_CORPNET_DISK_MAX_GB) || 4096;
              const minLimit = Number(process.env.REACT_APP_CORPNET_DISK_MIN_GB) || 10;
              return validateDiskValue(Number(value), diskUnit, maxLimit, minLimit);
            })
            .typeError('DiskValue must be a number')
            .transform((value, originalValue) => {
              if (originalValue === '' || isNaN(originalValue)) return null;
              return Number(value);
            })
            .nullable(),

          diskName: yup
            .string()
            .trim()
            .max(255, 'string must be at max 255 character')
            .required('Disk Name is required')
            .matches(yupMatchesParams.alphaNumericWithHyphenAndSlash.pattern, yupMatchesParams.alphaNumericWithHyphenAndSlash.errorMessage),
          diskUnit: yup.string().required('Unit is required'),
        })
      )
      .test('unique-disk-name', 'Disk Mount Points must be unique', function (addDisks) {
        if (addDisks && addDisks.length > 1) {
          const diskNames = addDisks?.map((disk) => disk.diskName);
          const uniqueDiskNames = new Set(diskNames);
          if (uniqueDiskNames.size !== diskNames?.length) {
            return this.createError({
              message: 'Disk Mount Point must be unique',
              path: `${this.path}[${addDisks.findIndex(
                (disk) => diskNames.indexOf(disk.diskName) !== diskNames.lastIndexOf(disk.diskName)
              )}].diskName`,
            });
          }
        }
        return true;
      }),
  });
};

export default AddDiskBlueSchema;
