import { render, screen, fireEvent, waitFor } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { useApiService } from 'api/ApiService/context';
import SecretValueField from './index';

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

const mockValidateSecretPassword = jest.fn();

const defaultProps = {
  isPasswordPolicyRequired: true,
  policyId: '123',
  policyDescription: 'Password must be strong',
  label: 'Secret',
  value: 'MySecret123!',
  name: 'secret',
  helperText: '',
  error: false,
  handleChange: jest.fn(),
  handleBlur: jest.fn(),
  setPasswordValidStatus: jest.fn(),
};

describe('SecretValueField', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useApiService as jest.Mock).mockReturnValue({
      apiSecretsManagement: {
        validateSecretPassword: mockValidateSecretPassword,
        getPasswordPolicies: jest.fn().mockResolvedValue({
          status: true,
          data: [
            {
              policyId: 'Test',
              policyName: 'Test',
              description: 'Test',
            },
          ],
        }),
      },
    });
  });

  it('renders the input field with label', () => {
    render(
      <NebulaThemeProvider>
        <SecretValueField {...defaultProps} />
      </NebulaThemeProvider>
    );
    expect(screen.getByLabelText(/Secret/i)).toBeInTheDocument();
  });

  it('toggles password visibility', () => {
    render(
      <NebulaThemeProvider>
        <SecretValueField {...defaultProps} />
      </NebulaThemeProvider>
    );
    const toggleButton = screen.getByRole('button');
    const input = screen.getByLabelText(/Secret/i);

    // Initially password type
    expect(input).toHaveAttribute('type', 'password');

    fireEvent.click(toggleButton);
    expect(input).toHaveAttribute('type', 'text');

    fireEvent.click(toggleButton);
    expect(input).toHaveAttribute('type', 'password');
  });

  it('disables input if password policy is required but no policyId', () => {
    render(
      <NebulaThemeProvider>
        <SecretValueField {...defaultProps} policyId="" />
      </NebulaThemeProvider>
    );
    expect(screen.getByLabelText(/Secret/i)).toBeDisabled();
  });

  it('shows helper text when no policyId is provided', () => {
    render(
      <NebulaThemeProvider>
        <SecretValueField {...defaultProps} policyId="" />
      </NebulaThemeProvider>
    );
    expect(screen.getByText(/Please select Secret Policy/i)).toBeInTheDocument();
  });

  it('calls validateSecretPassword API when value changes and policy is required', async () => {
    mockValidateSecretPassword.mockResolvedValue({
      status: true,
      data: { valid: true },
    });

    render(
      <NebulaThemeProvider>
        <SecretValueField {...defaultProps} />
      </NebulaThemeProvider>
    );

    await waitFor(() => {
      expect(mockValidateSecretPassword).toHaveBeenCalledWith({
        policyId: '123',
        secret: 'MySecret123!',
      });
    });
  });

  it('shows error message if password is invalid', async () => {
    jest.useFakeTimers(); // control timers to flush debounce
    mockValidateSecretPassword.mockResolvedValue({
      status: true,
      data: { valid: false, message: ['Too short', 'Missing symbol'] },
    });

    render(
      <NebulaThemeProvider>
        <SecretValueField {...defaultProps} />
      </NebulaThemeProvider>
    );

    jest.advanceTimersByTime(1000);
    await waitFor(() => {
      expect(mockValidateSecretPassword).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(screen.getByText(/Too short/i)).toBeInTheDocument();
    });

    jest.useRealTimers();
  });

  it('shows generic error if API fails', async () => {
    jest.useFakeTimers(); // control timers to flush debounce

    mockValidateSecretPassword.mockResolvedValue({
      status: false,
    });

    const { rerender } = render(
      <NebulaThemeProvider>
        <SecretValueField {...defaultProps} />
      </NebulaThemeProvider>
    );

    // Simulate input change
    const input = screen.getByLabelText(/Secret/i);
    fireEvent.change(input, { target: { value: 'NewSecret123!' } });

    // Rerender with updated value (simulate controlled input behavior)
    rerender(
      <NebulaThemeProvider>
        <SecretValueField {...defaultProps} value="NewSecret123!" />
      </NebulaThemeProvider>
    );

    // Advance timers to flush debounce
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(mockValidateSecretPassword).toHaveBeenCalledWith({
        policyId: '123',
        secret: 'NewSecret123!',
      });
    });

    await waitFor(() => {
      expect(screen.getByText(/Password not meeting the Password policy criteria/i)).toBeInTheDocument();
    });

    jest.useRealTimers(); // restore real timers
  });
});
