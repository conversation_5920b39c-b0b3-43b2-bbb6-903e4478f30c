import { Autocomplete, Chip, TextField } from '@mui/material';
//eslint-disable-next-line no-unused-vars
import Tooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';
import { NebulaTheme } from 'NebulaTheme/type';
import { getOutlinedBaseStyles, getOutlinedInputStyles } from '../common';

export const StyledTooltip = styled(({ className, ...props }: TooltipProps) => <Tooltip {...props} classes={{ popper: className }} />)<{
  theme?: NebulaTheme;
  isError?: boolean;
}>(({ theme, isError }) => {
  const { palette, typography } = theme;
    const { chipComponent } = palette;
  return {
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: isError ? chipComponent.errorBgColor : chipComponent.normalBgColor,
      color: isError ? chipComponent.errorFontColor : chipComponent.normalFontColor,
      ...typography.body2,
      ...typography.medium,
    },
    [`& .${tooltipClasses.arrow}`]: {
      color: isError ? chipComponent.errorBgColor : chipComponent.normalBgColor,
    },
  };
});

export const StyledChip = styled(Chip)<{ theme?: NebulaTheme; isError?: boolean }>(({ theme, isError }) => {
  const { palette, typography } = theme;
  const { chipComponent } = palette;
  return {
    backgroundColor: isError ? chipComponent.errorBgColor : chipComponent.normalBgColor,
    color: isError ? chipComponent.errorFontColor : chipComponent.normalFontColor,
    padding: '4px 0px',
    height: 'auto',
    margin: 0,
    borderRadius: '4px',
    border: 'none',
    '& .MuiChip-label': {
      fontWeight: 'bold',
    },
    '& .MuiChip-deleteIcon': {
      ...typography.body2,
      color: isError ? chipComponent.errorFontColor : chipComponent.normalCrossIconColor,
      '&:hover': {
        color: isError ? chipComponent.iconHoverErrorColor : chipComponent.iconHoverNormalColor,
      },
    },
  };
});

export const StyledTextField = styled(TextField)<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    '& .MuiOutlinedInput-root': {
      ...getOutlinedBaseStyles(theme),
      ...getOutlinedInputStyles(theme),
    },
  };
});

export const StyledAutoComplete = styled(Autocomplete)<{ theme?: NebulaTheme }>(() => ({
  '& .MuiAutocomplete-inputRoot': {
    padding: '0px !important',
  },
  '& .MuiOutlinedInput-root': {
    padding: '0px 4px !important',
  },
}));
