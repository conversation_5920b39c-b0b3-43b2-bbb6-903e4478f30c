import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblAutoComplete from '.';

describe('NblAutoComplete component', () => {
  const props = {
    label: 'Label',
    value: '1',
    name: 'name',
    placeholder: 'Select an option',
    options: [
      { label: '<PERSON>', value: '1' },
      { label: '<PERSON>', value: '2' },
      { label: '<PERSON>', value: '3' },
      { label: 'Jack', value: '4' },
      { label: 'Tom', value: '5' },
      { label: '<PERSON>', value: '6' },
    ],
    helperText: 'helper text',
    error: false,
    disabled: false,
    onChange: jest.fn()
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblAutoComplete {...props}>Info</NblAutoComplete>
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
