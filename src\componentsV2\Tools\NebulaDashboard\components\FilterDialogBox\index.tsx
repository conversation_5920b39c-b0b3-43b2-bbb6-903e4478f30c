import { useEffect, useState, useRef } from 'react';
import {
  Grid,
  Box,
  Divider,
  Badge,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  OutlinedInput,
  // eslint-disable-next-line
  SelectChangeEvent,
  ListItemIcon,
  Chip,
  useTheme,
} from '@mui/material';
import { FilterAlt } from '@mui/icons-material';
import MetricsDialogBox from './DialogBox';
import UsageMetricsService from 'api/ApiService/UsageMetricsService';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line
import { State } from 'store/reducers/type';
import { isDeepEqual } from '@mui/x-data-grid/internals';
import { RequestFilterConfigType, updateFilterConfigSlice, updateLevelWiseList } from 'store/reducers/filterDialogRequests';
import { GENERATE_QUERY_FOR_CATALOG } from '../../queries';
import { getQuarter } from '../../utility';
import { StyledApplyFilterBtn, StyledClearAllBtn } from '../../styled';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import NblButton from 'sharedComponents/Buttons/NblButton';
import { NblGridContainer } from 'sharedComponents/NblContainers/NblGridContainer';
import NblDivider from 'sharedComponents/NblDivider';
import { NebulaTheme } from 'NebulaTheme/type';
import NblBox from 'sharedComponents/NblContainers/NblBox';
// import NblChip from 'sharedComponents/NblChip';
import { StyledIconButton } from 'sharedComponents/NblDataViewer/styled';
import { PrevArrowIcon, NextArrowIcon } from 'assets/images/icons/custom-icons';
import NblChip from 'sharedComponents/NblChip';
import master from 'NebulaTheme/bundle/light/master';
import NblSelect from 'sharedComponents/NblFormInputs/NblSelect';
import NblMultiSelect from 'sharedComponents/NblFormInputs/NblMultiSelect';
import useMediaQuery from 'hooks/useMediaQuery';
// eslint-disable-next-line
import { selectedCatalogs } from 'api/ApiService/type';
interface UsageMetricsDashboardProps {
  //children: ReactElement;
}
function createYearArray(startYear: number, endYear: number): number[] {
  const arr = [];
  for (let year = startYear; year >= endYear; year--) {
    arr.push(year);
  }
  return arr;
}

const UsageMetricsDashboardHeader: React.FunctionComponent<UsageMetricsDashboardProps> = () => {
  const [showFilterBox, setShowFilterBox] = useState<boolean>(false);
  const [selectedFiltersCount] = useState<number>(0);
  const handleToggleFilter = () => setShowFilterBox((prev) => !prev);
  const usageMetricsService = new UsageMetricsService();
  const [quickfilterHandler, setQuickFilterHandler] = useState<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const currentYear = new Date().getFullYear();
  const lastYear = 2023;
  const years = createYearArray(currentYear, lastYear);
  const quarters = [1, 2, 3, 4];
  const currentMonth = new Date().getMonth();
  const currentQuarter = Math.floor(currentMonth / 3) + 1;
  const RequestFilterConfig = useSelector((state: State) => state.RequestFilterConfig);

  const [filterConfigState, setFilterConfigState] = useState<RequestFilterConfigType>(RequestFilterConfig);
  const {
    allCatalog1,
    allCatalog2,
    allCatalog3,
    allCatalog4,
    selectedCatalog1,
    selectedCatalog2,
    selectedCatalog3,
    selectedCatalog4,
    isFilterApplied,
  } = filterConfigState;
  //checkers
  const isAllQuartersSelected = filterConfigState.quarters.length === 4;
  const currentCatalog1 = allCatalog1;
  const currentCatalog2 = allCatalog2.filter(({ parentId }) => selectedCatalog1.includes(parentId));
  const currentCatalog3 = allCatalog3.filter(({ parentId }) => selectedCatalog2.includes(parentId));
  const currentCatalog4 = allCatalog4.filter(({ parentId }) => selectedCatalog3.includes(parentId));

  const isAllCatalog_1_Selected = currentCatalog1.length === selectedCatalog1.length;
  const isAllCatalog_2_Selected = currentCatalog2.length === selectedCatalog2.length;
  const isAllCatalog_3_Selected = currentCatalog3.length === selectedCatalog3.length;
  const isAllCatalog_4_Selected = currentCatalog4.length === selectedCatalog4.length;

  const dispatch = useDispatch();
  const { secondary } = master;

  const getCatalogsAndResources = async () => {
    const payload = { query: GENERATE_QUERY_FOR_CATALOG() };
    const response = await usageMetricsService.getCatalogsAndResources(payload);
    if (response.status) {
      const { catalogs } = response.data.data;
      let cat_1 = [];
      let cat_2: any[] = [];
      let cat_3: any[] = [];
      let cat_4: any[] = [];
      cat_1 = catalogs.map(({ id, value, name, subCatalogs }) => {
        cat_2 = cat_2.concat(
          subCatalogs.map((item2) => {
            cat_3 = cat_3.concat(
              item2.subCatalogs.map((item3) => {
                cat_4 = cat_4.concat(
                  item3.subCatalogs.map((item4) => {
                    return {
                      id: item4.id,
                      name: item4.name,
                      value: item4.value,
                      parentId: item3.id,
                      requestType: item4.requestType,
                    };
                  })
                );
                return {
                  id: item3.id,
                  name: item3.name,
                  value: item3.value,
                  parentId: item2.id,
                };
              })
            );
            return {
              id: item2.id,
              name: item2.name,
              value: item2.value,
              parentId: id,
            };
          })
        );

        return {
          id,
          value,
          name,
          parentId: '',
        };
      });

      let filterConfig: any;
      if (isFilterApplied) {
        filterConfig = {
          ...filterConfigState,
          catalogsList: catalogs,
          allCatalog1: cat_1,
          allCatalog2: cat_2,
          allCatalog3: cat_3,
          allCatalog4: cat_4,
          selectedCatalog1: cat_1.map(({ id }) => id).filter((id) => selectedCatalog1.includes(id)),
          selectedCatalog2: cat_2.map(({ id }) => id).filter((id) => selectedCatalog2.includes(id)),
          selectedCatalog3: cat_3.map(({ id }) => id).filter((id) => selectedCatalog3.includes(id)),
          selectedCatalog4: cat_4.map(({ id }) => id).filter((id) => selectedCatalog4.includes(id)),
        };
      } else {
        filterConfig = {
          ...filterConfigState,
          catalogsList: catalogs,
          allCatalog1: cat_1,
          allCatalog2: cat_2,
          allCatalog3: cat_3,
          allCatalog4: cat_4,
          selectedCatalog1: cat_1.map(({ id }) => id),
          selectedCatalog2: cat_2.map(({ id }) => id),
          selectedCatalog3: cat_3.map(({ id }) => id),
          selectedCatalog4: cat_4.map(({ id }) => id),
          levelSelected: 1,
        };
      }

      setFilterConfigState(filterConfig);
      dispatch(updateFilterConfigSlice(filterConfig));
      dispatch(updateLevelWiseList(filterConfig));
    }
  };

  useEffect(() => {
    getCatalogsAndResources();
  }, []);

  useEffect(() => {
    if (quickfilterHandler) {
      handleApplyFilter();

      setQuickFilterHandler(false);
    }
  }, [quickfilterHandler]);

  const handleYearChange = (event: SelectChangeEvent<string>) => {
    let newFilter = { ...filterConfigState };

    newFilter.year = Number(event.target.value);

    setFilterConfigState(newFilter);
  };
  const handleQuickFilterYearChange = (event: SelectChangeEvent<string>) => {
    let newFilter = { ...filterConfigState };

    newFilter.year = Number(event.target.value);

    setFilterConfigState(newFilter);

    setQuickFilterHandler(true);
  };
  type NumberOrString = number | string;
  const handleQuarterChange = (event: SelectChangeEvent<NumberOrString[]>) => {
    const isAllClicked = event.target.value.includes('ALL');
    const uncheckAll = isAllClicked && isAllQuartersSelected;
    if (uncheckAll) {
      setFilterConfigState((prev) => ({ ...prev, quarters: [] }));
      return;
    }
    isAllClicked
      ? setFilterConfigState((prev) => ({ ...prev, quarters: quarters }))
      : setFilterConfigState((prev) => ({ ...prev, quarters: event.target.value as number[] }));
  };

  const handleQuickFilterQuarterChange = (event: SelectChangeEvent<NumberOrString[]>) => {
    if (event.target.value.length === 0) {
      return null;
    }
    handleQuarterChange(event);

    if (event.target.value.length && event.target.value.length < 5) {
      setQuickFilterHandler(true);
    }
  };

  const handleQuickFilterCatalogLevelChange = (level: number, id: string) => {
    let newFilter = filterConfigState;
    let modifiedIds: string[];
    let isIdPresent: number;
    switch (level) {
      case 1:
        modifiedIds = filterConfigState.selectedCatalog1;
        isIdPresent = modifiedIds.findIndex((item) => item === id);
        if (!isNaN(isIdPresent) && isIdPresent >= 0) modifiedIds = modifiedIds.filter((_id) => _id !== id);
        newFilter.selectedCatalog1 = modifiedIds;
        newFilter.selectedCatalog2 = allCatalog2
          .filter(({ parentId, id }) => modifiedIds.includes(parentId) && filterConfigState.selectedCatalog2.includes(id))
          .map((item) => item.id);
        newFilter.selectedCatalog3 = allCatalog3
          .filter(({ parentId, id }) => newFilter.selectedCatalog2.includes(parentId) && filterConfigState.selectedCatalog3.includes(id))
          .map((item) => item.id);
        newFilter.selectedCatalog4 = allCatalog4
          .filter(({ parentId, id }) => newFilter.selectedCatalog3.includes(parentId) && filterConfigState.selectedCatalog4.includes(id))
          .map((item) => item.id);
        break;
      case 2:
        modifiedIds = filterConfigState.selectedCatalog2;
        isIdPresent = modifiedIds.findIndex((item) => item === id);
        if (!isNaN(isIdPresent) && isIdPresent >= 0) modifiedIds = modifiedIds.filter((_id) => _id !== id);
        newFilter.selectedCatalog2 = modifiedIds;
        newFilter.selectedCatalog3 = allCatalog3
          .filter(({ parentId, id }) => modifiedIds.includes(parentId) && filterConfigState.selectedCatalog3.includes(id))
          .map((item) => item.id);
        newFilter.selectedCatalog4 = allCatalog4
          .filter(({ parentId, id }) => newFilter.selectedCatalog3.includes(parentId) && filterConfigState.selectedCatalog4.includes(id))
          .map((item) => item.id);
        break;
      case 3:
        modifiedIds = filterConfigState.selectedCatalog3;
        isIdPresent = modifiedIds.findIndex((item) => item === id);
        if (!isNaN(isIdPresent) && isIdPresent >= 0) modifiedIds = modifiedIds.filter((_id) => _id !== id);
        newFilter.selectedCatalog3 = modifiedIds;
        newFilter.selectedCatalog4 = allCatalog4
          .filter(({ parentId, id }) => newFilter.selectedCatalog3.includes(parentId) && filterConfigState.selectedCatalog4.includes(id))
          .map((item) => item.id);
        break;
      case 4:
        modifiedIds = filterConfigState.selectedCatalog4;
        isIdPresent = modifiedIds.findIndex((item) => item === id);
        if (!isNaN(isIdPresent) && isIdPresent >= 0) modifiedIds = modifiedIds.filter((_id) => _id !== id);
        newFilter.selectedCatalog4 = modifiedIds;
        break;
    }
    setFilterConfigState(newFilter);
    handleApplyFilter();
  };

  const handleCatalogChange = (value: string, catalogNo: number = 0) => {
    const isAllClicked = value.includes('ALL');
    let levelSelected = catalogNo;
    switch (catalogNo) {
      case 1:
        if (isAllClicked)
          setFilterConfigState((prev) => ({
            ...prev,
            selectedCatalog1: allCatalog1.map((item) => item.id),
            selectedCatalog2: allCatalog2.map((item) => item.id),
            selectedCatalog3: allCatalog3.map((item) => item.id),
            selectedCatalog4: allCatalog4.map((item) => item.id),
            levelSelected,
          }));
        else {
          let selectedCatalog1 = isAllCatalog_1_Selected ? [value] : checkIdPresent(filterConfigState.selectedCatalog1, value);
          if (selectedCatalog1.length === 0) {
            selectedCatalog1 = allCatalog1.map((item) => item.id);
          }
          let selectedCatalog2 = allCatalog2
            .filter(
              ({ parentId, id }) =>
                selectedCatalog1.includes(parentId) && (isAllCatalog_2_Selected || filterConfigState.selectedCatalog2.includes(id))
            )
            .map((item) => item.id);
          let selectedCatalog3 = allCatalog3
            .filter(
              ({ parentId, id }) =>
                selectedCatalog2.includes(parentId) && (isAllCatalog_3_Selected || filterConfigState.selectedCatalog3.includes(id))
            )
            .map((item) => item.id);
          let selectedCatalog4 = allCatalog4
            .filter(
              ({ parentId, id }) =>
                selectedCatalog3.includes(parentId) && (isAllCatalog_4_Selected || filterConfigState.selectedCatalog4.includes(id))
            )
            .map((item) => item.id);
          setFilterConfigState((prev) => ({
            ...prev,
            selectedCatalog1,
            selectedCatalog2,
            selectedCatalog3,
            selectedCatalog4,
            levelSelected,
          }));
        }
        break;
      case 2:
        if (isAllClicked) {
          let selectedCatalogsLevel2: string[] = [];
          allCatalog2.forEach((item) => {
            if (selectedCatalog1.includes(item.parentId)) selectedCatalogsLevel2.push(item.id);
          });
          let selectedCatalogsLevel3: string[] = [];
          allCatalog3.forEach((item) => {
            if (selectedCatalogsLevel2.includes(item.parentId)) selectedCatalogsLevel3.push(item.id);
          });
          let selectedCatalogsLevel4: string[] = [];
          allCatalog4.forEach((item) => {
            if (selectedCatalogsLevel3.includes(item.parentId)) selectedCatalogsLevel4.push(item.id);
          });
          setFilterConfigState((prev) => ({
            ...prev,

            selectedCatalog2: selectedCatalogsLevel2,
            selectedCatalog3: selectedCatalogsLevel3,
            selectedCatalog4: selectedCatalogsLevel4,
            levelSelected,
          }));
        } else {
          let selectedCatalog2 = isAllCatalog_2_Selected ? [value] : checkIdPresent(filterConfigState.selectedCatalog2, value);
          if (selectedCatalog2.length === 0) {
            selectedCatalog2 = allCatalog2.map((item) => item.id);
          }
          let selectedCatalog3 = allCatalog3
            .filter(
              ({ parentId, id }) =>
                selectedCatalog2.includes(parentId) && (isAllCatalog_3_Selected || filterConfigState.selectedCatalog3.includes(id))
            )
            .map((item) => item.id);

          let selectedCatalog4 = allCatalog4
            .filter(
              ({ parentId, id }) =>
                selectedCatalog3.includes(parentId) && (isAllCatalog_4_Selected || filterConfigState.selectedCatalog4.includes(id))
            )
            .map((item) => item.id);
          setFilterConfigState((prev) => ({ ...prev, selectedCatalog2, selectedCatalog3, selectedCatalog4, levelSelected }));
        }
        break;
      case 3:
        if (isAllClicked) {
          let selectedCatalogsLevel3: string[] = [];
          allCatalog3.forEach((item) => {
            if (selectedCatalog2.includes(item.parentId)) selectedCatalogsLevel3.push(item.id);
          });
          let selectedCatalogsLevel4: string[] = [];
          allCatalog4.forEach((item) => {
            if (selectedCatalogsLevel3.includes(item.parentId)) selectedCatalogsLevel4.push(item.id);
          });
          setFilterConfigState((prev) => ({
            ...prev,
            selectedCatalog3: selectedCatalogsLevel3,
            selectedCatalog4: selectedCatalogsLevel4,
            levelSelected,
          }));
        } else {
          let selectedCatalog3 = isAllCatalog_3_Selected ? [value] : checkIdPresent(filterConfigState.selectedCatalog3, value);
          if (selectedCatalog3.length === 0) {
            selectedCatalog3 = allCatalog3.map((item) => item.id);
          }
          let selectedCatalog4 = allCatalog4
            .filter(
              ({ parentId, id }) =>
                selectedCatalog3.includes(parentId) && (isAllCatalog_4_Selected || filterConfigState.selectedCatalog4.includes(id))
            )
            .map((item) => item.id);

          setFilterConfigState((prev) => ({ ...prev, selectedCatalog3, selectedCatalog4, levelSelected }));
        }
        break;
      case 4:
        if (isAllClicked) {
          let selectedCatalogsLevel4: string[] = [];
          allCatalog4.forEach((item) => {
            if (selectedCatalog3.includes(item.parentId)) selectedCatalogsLevel4.push(item.id);
          });
          setFilterConfigState((prev) => ({
            ...prev,

            selectedCatalog4: selectedCatalogsLevel4,
          }));
        } else {
          let selectedCatalog4 = isAllCatalog_4_Selected ? [value] : checkIdPresent(filterConfigState.selectedCatalog4, value);
          if (selectedCatalog4.length === 0) {
            selectedCatalog4 = allCatalog4.map((item) => item.id);
          }
          setFilterConfigState((prev) => ({ ...prev, selectedCatalog4, levelSelected }));
        }
        break;
      default:
        return;
    }
  };

  const getAvailableQuarters = (year: number): number[] => {
    if (year === currentYear) {
      return quarters.filter((q) => q <= currentQuarter);
    }
    return quarters;
  };

  const isFilterBtnDisabled = () => {
    if (!filterConfigState.quarters.length) {
      return true;
    } else {
      return false;
    }
  };

  const checkIdPresent = (ids: string[], id: string) => {
    let modifiedIds = ids;
    const isIdPresent = modifiedIds.findIndex((item) => item === id);
    if (!isNaN(isIdPresent) && isIdPresent >= 0) {
      return modifiedIds.filter((_id) => _id !== id);
    } else return modifiedIds.concat(id);
  };

  const handleApplyFilter = () => {
    //Filtering the truly selected catalogs on parent level and handling if any selection becomes empty
    let newFilter = filterConfigState;
    if (filterConfigState.selectedCatalog4.length <= 0) {
      if (filterConfigState.selectedCatalog3.length <= 0) {
        if (filterConfigState.selectedCatalog2.length <= 0) {
          if (filterConfigState.selectedCatalog1.length <= 0) {
            allCatalog1.forEach(({ id }) => newFilter.selectedCatalog1.push(id));
            allCatalog2.forEach(({ id }) => newFilter.selectedCatalog2.push(id));
            allCatalog3.forEach(({ id }) => newFilter.selectedCatalog3.push(id));
            allCatalog4.forEach(({ id }) => newFilter.selectedCatalog4.push(id));
          } else {
            let selectedCatalogs2 = allCatalog2.filter(({ parentId }) => newFilter.selectedCatalog1.includes(parentId));
            selectedCatalogs2.forEach(({ id }) => newFilter.selectedCatalog2.push(id));
            let selectedCatalogs3 = allCatalog3.filter(({ parentId }) => newFilter.selectedCatalog2.includes(parentId));
            selectedCatalogs3.forEach(({ id }) => newFilter.selectedCatalog3.push(id));
            let selectedCatalogs4 = allCatalog4.filter(({ parentId }) => filterConfigState.selectedCatalog3.includes(parentId));
            selectedCatalogs4.forEach(({ id }) => newFilter.selectedCatalog4.push(id));
          }
        } else {
          let selectedCatalogs3 = allCatalog3.filter(({ parentId }) => newFilter.selectedCatalog2.includes(parentId));
          selectedCatalogs3.forEach(({ id }) => newFilter.selectedCatalog3.push(id));
          let selectedCatalogs4 = allCatalog4.filter(({ parentId }) => filterConfigState.selectedCatalog3.includes(parentId));
          selectedCatalogs4.forEach(({ id }) => newFilter.selectedCatalog4.push(id));
          let selectedCatalogs2 = allCatalog2.filter(({ id }) => newFilter.selectedCatalog2.includes(id));
          newFilter.selectedCatalog1 = allCatalog1
            .filter(({ id }) => selectedCatalogs2.findIndex((cat2) => cat2.parentId == id) >= 0 && newFilter.selectedCatalog1.includes(id))
            .map((item) => item.id);
        }
      } else {
        let selectedCatalogs4 = allCatalog4.filter(({ parentId }) => filterConfigState.selectedCatalog3.includes(parentId));
        selectedCatalogs4.forEach(({ id }) => newFilter.selectedCatalog4.push(id));
        let selectedCatalogs3 = allCatalog3.filter(({ id }) => newFilter.selectedCatalog3.includes(id));
        newFilter.selectedCatalog2 = allCatalog2
          .filter(({ id }) => selectedCatalogs3.findIndex((cat3) => cat3.parentId == id) >= 0 && newFilter.selectedCatalog2.includes(id))
          .map((item) => item.id);
        let selectedCatalogs2 = allCatalog2.filter(({ id }) => newFilter.selectedCatalog2.includes(id));
        newFilter.selectedCatalog1 = allCatalog1
          .filter(({ id }) => selectedCatalogs2.findIndex((cat2) => cat2.parentId == id) >= 0 && newFilter.selectedCatalog1.includes(id))
          .map((item) => item.id);
      }
    } else {
      let selectedCatalogs4 = allCatalog4.filter(({ id }) => filterConfigState.selectedCatalog4.includes(id));
      newFilter.selectedCatalog3 = allCatalog3
        .filter(
          ({ id }) => selectedCatalogs4.findIndex((cat4) => cat4.parentId == id) >= 0 && filterConfigState.selectedCatalog3.includes(id)
        )
        .map((item) => item.id);
      let selectedCatalogs3 = allCatalog3.filter(({ id }) => newFilter.selectedCatalog3.includes(id));
      newFilter.selectedCatalog2 = allCatalog2
        .filter(({ id }) => selectedCatalogs3.findIndex((cat3) => cat3.parentId == id) >= 0 && newFilter.selectedCatalog2.includes(id))
        .map((item) => item.id);
      let selectedCatalogs2 = allCatalog2.filter(({ id }) => newFilter.selectedCatalog2.includes(id));
      newFilter.selectedCatalog1 = allCatalog1
        .filter(({ id }) => selectedCatalogs2.findIndex((cat2) => cat2.parentId == id) >= 0 && newFilter.selectedCatalog1.includes(id))
        .map((item) => item.id);
    }
    setFilterConfigState(newFilter);

    if (!isDeepEqual(filterConfigState, RequestFilterConfig)) {
      dispatch(updateFilterConfigSlice(filterConfigState));
    }
    setShowFilterBox(false);
  };
  const clearFilter = () => {
    setFilterConfigState((prev) => {
      return {
        ...prev,
        year: new Date().getFullYear(),
        quarters: [getQuarter(new Date())],
        selectedCatalog1: allCatalog1.map((item) => item.id),
        selectedCatalog2: allCatalog2.map((item) => item.id),
        selectedCatalog3: allCatalog3.map((item) => item.id),
        selectedCatalog4: allCatalog4.map((item) => item.id),
        levelSelected: 1,
      };
    });
  };
  const theme = useTheme<NebulaTheme>();
  const horizontalScroll = function (direction: string) {
    const container = containerRef.current;

    if (direction == 'prev') container?.scrollBy({ left: 150, behavior: 'smooth' });
    else container?.scrollBy({ left: -150, behavior: 'smooth' });
  };
  const DropdownStyles2K = {
    minWidth: '110px',
  };

  const DropdownStylesFullHD = {
    minWidth: '150px',
  };
  const styles = useMediaQuery(DropdownStylesFullHD, DropdownStyles2K);

  const quickFilterChips = (level: number, selectedCatalogs: string[], allCatalogs: selectedCatalogs[]) => {
    let label = 'Catalog Level ' + level + ' - All';
    return selectedCatalogs.length == allCatalogs.length ? (
      <Chip
        key={'All' + level}
        label={label}
        sx={{
          backgroundColor: theme.palette.secondary.shade1,
          borderColor: theme.palette.secondary.shade5,
          border: '1px solid',
          color: theme.palette.primary.main,
          margin: '0 5px 0 5px',
        }}
      />
    ) : selectedCatalogs.length == 1 ? (
      selectedCatalogs.map((id) => {
        return (
          <Chip
            key={id}
            label={allCatalogs.find((cat) => cat.id == id)?.name}
            sx={{
              backgroundColor: theme.palette.secondary.shade1,
              borderColor: theme.palette.secondary.shade5,
              border: '1px solid',
              color: theme.palette.primary.main,
              margin: '0 5px 0 5px',
            }}
          />
        );
      })
    ) : (
      selectedCatalogs.map((id) => {
        return (
          <Chip
            key={id}
            label={allCatalogs.find((cat) => cat.id == id)?.name}
            onDelete={() => handleQuickFilterCatalogLevelChange(level, id)}
            sx={{
              backgroundColor: theme.palette.secondary.shade1,
              borderColor: theme.palette.secondary.shade5,
              border: '1px solid',
              color: theme.palette.primary.main,
              margin: '0 5px 0 5px',
            }}
          />
        );
      })
    );
  };

  return (
    <>
      <NblFlexContainer alignItems="center">
        <NblFlexContainer width="100px" height="auto" alignSelf="center">
          <NblTypography variant="subtitle2" color="shade1" weight="medium">
            Quick Filters:
          </NblTypography>
        </NblFlexContainer>
        <NblFlexContainer minWidth={styles.minWidth} width="auto" height="auto" flex="1 0 0">
          <NblSelect
            value={filterConfigState.year}
            name="Year"
            handleChange={handleQuickFilterYearChange}
            options={years.map((val) => ({ value: val, label: String(val) }))}
            placeholder={filterConfigState.year.toString()}
          />
        </NblFlexContainer>
        <NblFlexContainer margin="2px 0 0 0" minWidth={styles.minWidth} width="auto" height="auto" flex="1 0 0">
          <NblMultiSelect
            value={filterConfigState.quarters}
            label=""
            name="Quarter"
            handleChange={handleQuickFilterQuarterChange}
            options={getAvailableQuarters(filterConfigState.year).map((val) => ({ value: val, label: String(`Q${val}`) }))}
            placeholder={'Select Quarters'}
            maxLength={150}
          />
        </NblFlexContainer>

        <NblFlexContainer width="120px" height="auto" alignSelf="center">
          {RequestFilterConfig.selectedCatalog4.length != allCatalog4.length && (
            <NblTypography variant="subtitle2" color="shade1" weight="medium">
              Applied Filters:
            </NblTypography>
          )}
        </NblFlexContainer>

        <NblFlexContainer alignItems="center" width="55%">
          {RequestFilterConfig.selectedCatalog4.length != allCatalog4.length && (
            <>
              <StyledIconButton onClick={() => horizontalScroll('next')}>
                <PrevArrowIcon sx={{ fontSize: 'inherit' }} />
              </StyledIconButton>

              <NblFlexContainer height="auto" overflowX="auto" ref={containerRef}>
                {quickFilterChips(1, RequestFilterConfig.selectedCatalog1, allCatalog1)}
                {quickFilterChips(2, RequestFilterConfig.selectedCatalog2, allCatalog2)}
                {quickFilterChips(3, RequestFilterConfig.selectedCatalog3, allCatalog3)}
                {quickFilterChips(4, RequestFilterConfig.selectedCatalog4, allCatalog4)}
              </NblFlexContainer>

              <StyledIconButton onClick={() => horizontalScroll('prev')}>
                <NextArrowIcon sx={{ fontSize: 'inherit', color: '#03213B', marginRight: '5px' }} />
              </StyledIconButton>
            </>
          )}
        </NblFlexContainer>

        <NblFlexContainer width="auto" height="auto">
          <NblButton
            buttonID={'dashboard-filter-btn'}
            variant="contained"
            color={'primary'}
            onClick={handleToggleFilter}
            startIcon={<FilterAlt />}
          >
            Filter
          </NblButton>
        </NblFlexContainer>
        {showFilterBox && (
          <MetricsDialogBox open={showFilterBox} onClose={handleToggleFilter}>
            <NblFlexContainer direction={'column'} height={'100%'}>
              <NblGridContainer>
                <NblTypography variant="h3" color={'shade4'}>
                  Filters
                </NblTypography>
                <NblDivider
                  orientation="horizontal"
                  length="100%"
                  color={theme.palette.nbldivider.variant2}
                  strokeWidth={0.5}
                  opacity={0.1}
                />
                <Grid container>
                  <Grid item xs={6}>
                    <NblBox>
                      <NblTypography variant="subtitle1" color={'shade4'}>
                        Year
                      </NblTypography>
                    </NblBox>
                    <Select
                      labelId="year-select-label"
                      id="year-select"
                      value={filterConfigState.year.toString()}
                      onChange={handleYearChange}
                      sx={{
                        width: '80%',
                        backgroundColor: 'transparent',
                        border: '1px solid #E2F4FC',
                        color: '#E2F4FC',
                        mb: 0.5,
                        mt: 0.5,
                      }}
                    >
                      {years.map((year: number) => (
                        <MenuItem key={year} value={year}>
                          {year}
                        </MenuItem>
                      ))}
                    </Select>
                    {/* <NblSelect
                        value={filterConfigState.year}
                        name="Year"
                        handleChange={handleYearChange}
                        options={years.map((val) => ({ value: val, label: String(val) }))}
                        placeholder={filterConfigState.year.toString()}
                      /> */}
                  </Grid>
                  <Grid item xs={6}>
                    <NblBox>
                      <NblTypography variant="subtitle1" color={'shade4'}>
                        Quarter
                      </NblTypography>
                    </NblBox>
                    <Select
                      labelId="quarter-select-label"
                      id="quarter-checkbox"
                      multiple
                      value={filterConfigState.quarters}
                      onChange={handleQuarterChange}
                      input={<OutlinedInput label="Tag" />}
                      renderValue={(selected) => (selected.length === 4 ? 'All Quarters' : selected.map((item) => `Q${item}`).join(', '))}
                      sx={{
                        width: '80%',
                        backgroundColor: 'transparent',
                        border: '1px solid #E2F4FC',
                        color: '#E2F4FC',
                        mb: 0.5,
                        mt: 0.5,
                      }}
                    >
                      <MenuItem value={'ALL'}>
                        <ListItemIcon>
                          <Checkbox checked={isAllQuartersSelected} />
                        </ListItemIcon>
                        <ListItemText primary="Select All" />
                      </MenuItem>
                      {getAvailableQuarters(filterConfigState.year).map((quarter) => (
                        <MenuItem key={quarter} value={quarter}>
                          <Checkbox checked={filterConfigState.quarters.includes(quarter)} />
                          <ListItemText primary={`Q${quarter}`} />
                        </MenuItem>
                      ))}
                    </Select>
                  </Grid>
                </Grid>

                <NblDivider
                  orientation="horizontal"
                  length="100%"
                  color={theme.palette.nbldivider.variant2}
                  strokeWidth={0.5}
                  opacity={0.1}
                />
                <NblFlexContainer direction="column">
                  <NblTypography variant="subtitle1" color={'shade4'}>
                    Catalog 1
                  </NblTypography>
                  <NblFlexContainer spacing={0} wrap="wrap">
                    <NblBox>
                      <NblTypography variant="subtitle1" color={'shade4'}>
                        <NblBox margin="0 0 8px 0">
                          <NblChip
                            label={'All'}
                            onClick={() => handleCatalogChange('ALL', 1)}
                            id={'All'}
                            borderRadius="lg"
                            {...(isAllCatalog_1_Selected
                              ? {
                                  color: 'primary',
                                }
                              : {
                                  extracolor: 'transparent',
                                  borderColor: secondary.main,
                                })}
                          />
                        </NblBox>
                      </NblTypography>
                    </NblBox>
                    {currentCatalog1.map(({ id, name }) => {
                      return (
                        <NblBox key={id} margin="0 0 8px 0">
                          <NblTypography variant="body2" color={'shade4'}>
                            <NblChip
                              label={name}
                              onClick={() => handleCatalogChange(id, 1)}
                              id={id}
                              borderRadius="lg"
                              {...(isAllCatalog_1_Selected
                                ? {
                                    extracolor: 'transparent',
                                    borderColor: secondary.main,
                                  }
                                : selectedCatalog1.includes(id)
                                ? {
                                    color: 'primary',
                                  }
                                : {
                                    extracolor: 'transparent',
                                    borderColor: secondary.main,
                                  })}
                            />
                          </NblTypography>
                        </NblBox>
                      );
                    })}
                  </NblFlexContainer>
                </NblFlexContainer>
                <NblDivider
                  orientation="horizontal"
                  length="100%"
                  color={theme.palette.nbldivider.variant2}
                  strokeWidth={0.5}
                  opacity={0.1}
                />

                <NblFlexContainer direction="column">
                  <NblTypography variant="subtitle1" color={'shade4'}>
                    Catalog 2
                  </NblTypography>
                  <NblFlexContainer spacing={0} wrap="wrap">
                    <NblBox margin="0 0 8px 0">
                      <NblTypography variant="subtitle1" color={'shade4'}>
                        <NblChip
                          label={'All'}
                          onClick={() => handleCatalogChange('ALL', 2)}
                          id={'All'}
                          borderRadius="lg"
                          {...(isAllCatalog_2_Selected
                            ? {
                                color: 'primary',
                              }
                            : {
                                extracolor: 'transparent',
                                borderColor: secondary.main,
                              })}
                        />
                      </NblTypography>
                    </NblBox>
                    {currentCatalog2.map(({ id, name }) => {
                      return (
                        <NblBox key={id} margin="0 0 8px 0">
                          <NblChip
                            label={name}
                            onClick={() => handleCatalogChange(id, 2)}
                            id={id}
                            borderRadius="lg"
                            {...(isAllCatalog_2_Selected
                              ? {
                                  extracolor: 'transparent',
                                  borderColor: secondary.main,
                                }
                              : selectedCatalog2.includes(id)
                              ? {
                                  color: 'primary',
                                }
                              : {
                                  extracolor: 'transparent',
                                  borderColor: secondary.main,
                                })}
                          />
                        </NblBox>
                      );
                    })}
                  </NblFlexContainer>
                </NblFlexContainer>

                <NblDivider
                  orientation="horizontal"
                  length="100%"
                  color={theme.palette.nbldivider.variant2}
                  strokeWidth={0.5}
                  opacity={0.1}
                />
                <NblFlexContainer direction="column">
                  <NblTypography variant="subtitle1" color={'shade4'}>
                    Catalog 3
                  </NblTypography>
                  <NblFlexContainer spacing={0} wrap="wrap">
                    <NblBox margin="0 0 8px 0">
                      <NblTypography variant="subtitle1" color={'shade4'}>
                        <NblChip
                          label={'All'}
                          onClick={() => handleCatalogChange('ALL', 3)}
                          id={'All'}
                          borderRadius="lg"
                          {...(isAllCatalog_3_Selected
                            ? {
                                color: 'primary',
                              }
                            : {
                                extracolor: 'transparent',
                                borderColor: secondary.main,
                              })}
                        />
                      </NblTypography>
                    </NblBox>
                    {currentCatalog3.map(({ id, name }) => {
                      return (
                        <NblBox key={id} margin="0 0 8px 0">
                          <NblChip
                            label={name}
                            onClick={() => handleCatalogChange(id, 3)}
                            id={id}
                            borderRadius="lg"
                            {...(isAllCatalog_3_Selected
                              ? {
                                  extracolor: 'transparent',
                                  borderColor: secondary.main,
                                }
                              : selectedCatalog3.includes(id)
                              ? {
                                  color: 'primary',
                                }
                              : {
                                  extracolor: 'transparent',
                                  borderColor: secondary.main,
                                })}
                          />
                        </NblBox>
                      );
                    })}
                  </NblFlexContainer>
                </NblFlexContainer>

                <NblDivider
                  orientation="horizontal"
                  length="100%"
                  color={theme.palette.nbldivider.variant2}
                  strokeWidth={0.5}
                  opacity={0.1}
                />
                <NblFlexContainer direction="column">
                  <NblTypography variant="subtitle1" color={'shade4'}>
                    Catalog 4
                  </NblTypography>
                  <NblFlexContainer spacing={0} wrap="wrap">
                    <NblBox margin="0 0 8px 0">
                      <NblTypography variant="subtitle1" color={'shade4'}>
                        <NblChip
                          label={'All'}
                          onClick={() => handleCatalogChange('ALL', 4)}
                          id={'All'}
                          borderRadius="lg"
                          {...(isAllCatalog_4_Selected
                            ? {
                                color: 'primary',
                              }
                            : {
                                extracolor: 'transparent',
                                borderColor: secondary.main,
                              })}
                        />
                      </NblTypography>
                    </NblBox>
                    {currentCatalog4.map(({ id, name }) => {
                      return (
                        <NblBox key={id} margin="0 0 8px 0">
                          <NblChip
                            label={name}
                            onClick={() => handleCatalogChange(id, 4)}
                            id={id}
                            borderRadius="lg"
                            {...(isAllCatalog_4_Selected
                              ? {
                                  extracolor: 'transparent',
                                  borderColor: secondary.main,
                                }
                              : selectedCatalog4.includes(id)
                              ? {
                                  color: 'primary',
                                }
                              : {
                                  extracolor: 'transparent',
                                  borderColor: secondary.main,
                                })}
                          />
                        </NblBox>
                      );
                    })}
                  </NblFlexContainer>
                </NblFlexContainer>
              </NblGridContainer>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', padding: '20px 0' }}>
                <StyledClearAllBtn onClick={clearFilter} id={'form-cancel-btn'}>
                  Clear All
                </StyledClearAllBtn>
                <StyledApplyFilterBtn
                  id={'form-submit-btn'}
                  variant="contained"
                  onClick={handleApplyFilter}
                  disabled={isFilterBtnDisabled()}
                >
                  Apply Filters <Badge badgeContent={selectedFiltersCount} overlap="rectangular" color="secondary" />
                </StyledApplyFilterBtn>
              </Box>
            </NblFlexContainer>
          </MetricsDialogBox>
        )}
      </NblFlexContainer>
      <Divider orientation="horizontal" flexItem color="000000" sx={{ mt: 1 }} />
    </>
  );
};
export default UsageMetricsDashboardHeader;
