import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import ProjectSettingConfig from './index';
import ThemeProvider from 'mock/ThemeProvider';
import { BrowserRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

jest.mock('react-toastify');

describe('Create ProjectSettingConfig new request form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  const handleProjectSettingConfig = jest.fn();
  const props = { id: 0, settingName: 'test', settingValue: 'test1,test2' };

  const initialState = {
    projectNetworks: {
      projectValues: {
        id: '',
        settingName: '',
        settingValue: [],
      },
    },
    projectTags: {
      ProjectTagValues: {
        settingName: '',
        id: '',
        tagKey: '',
        tagValue: '',
        destination: [{ id: '', name: '' }],
        description: '',
        tagKeyId: '',
        tagValueName: '',
      },
    },
    common: { exposureParams: [] },
  };

  const mockStore = configureMockStore();
  const store = mockStore(initialState);
  const dataCenter = [
    {
      id: '66216af38b22d42be179edde',
      name: 'NCW',
    },
    {
      id: '66216ae28b22d42be179eddc',
      name: 'NCE',
    },
    {
      id: '660ff0b92188d2daadf7f458',
      name: 'STAMP',
    },
  ];

  test('Should render the form with all the field', async () => {
    const { getByText, getByLabelText } = await act(async () =>
      render(
        <Router>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <ProjectSettingConfig
                editTagValue={props}
                projectSettingsData={handleProjectSettingConfig}
                openDialog={true}
                onClose={handleClose}
                onSuccess={handleSuccess}
                dataCenter={dataCenter}
              />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    await waitFor(async () => {
      expect(screen.getByText('Add Network Config')).toBeInTheDocument();
      expect(screen.getByText('Add Tag')).toBeInTheDocument();
      const cancelButton = getByLabelText('close');
      const addMISCConfigButton = getByText('Add MISC Config');
      expect(cancelButton).toBeEnabled();
      expect(addMISCConfigButton).toBeDisabled();
    });
  });

  test('Should render the popup after clicking Project Setting COnfig Buttons', async () => {
    const { getByText, getByTestId } = await act(async () =>
      render(
        <Router>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <ProjectSettingConfig
                editTagValue={props}
                projectSettingsData={handleProjectSettingConfig}
                openDialog={true}
                onClose={handleClose}
                onSuccess={handleSuccess}
                dataCenter={dataCenter}
              />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );

    fireEvent.click(getByTestId('add-network-config-btn'));
    await waitFor(async () => {
      expect(getByText('Network Setting Config')).toBeInTheDocument();
    });
  });
});
