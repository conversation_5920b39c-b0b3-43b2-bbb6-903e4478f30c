import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react';
import MultiSelectAutoComplete from '.';
import ThemeProvider from 'mock/ThemeProvider';

const OPTIONS = [
  { label: 'Backbone', value: 'Backbone' },
  { label: 'Carolinas', value: 'Carolinas' },
  { label: 'Central', value: 'Central' },
];

describe('MultiSelectAutoComplete Component', () => {
  test('Should render multi-select dropdown with options', async () => {
    const onChange = jest.fn();
    const { getByText, container } = await act(async () =>
      render(
        <ThemeProvider>
          <MultiSelectAutoComplete value={[]} label="Test" options={OPTIONS} name="test" handleChange={onChange} />
        </ThemeProvider>
      )
    );

    let multiSelectTextField = container.querySelector('#test') as HTMLDivElement;

    fireEvent.mouseDown(multiSelectTextField);

    await waitFor(() => {
      OPTIONS.forEach((option) => {
        expect(getByText(option.label)).toBeInTheDocument();
      });
    });

    fireEvent.click(getByText('Backbone'));
    fireEvent.click(getByText('Carolinas'));
    fireEvent.click(getByText('Central'));

    await waitFor(() => {
      expect(onChange).toHaveBeenCalledTimes(3);
    });
  });
});
