// eslint-disable-next-line no-unused-vars
import { validateDiskValue } from 'utils/common';

export const CatalogForms = [
  { label: 'Reserve IP Address Block', value: 'ReserveIPBlockForm' },
  { label: 'Update IP Address Block', value: 'UpdateIPBlockForm' },
  { label: 'Release IP Address Block', value: 'ReleaseIPBlockForm' },
  { label: 'Create Monitoring Organization', value: 'CreateOrganizationForm' },
  { label: 'Onboard Device', value: 'OnboardDeviceForm' },
  { label: 'Offboard Device', value: 'OffboardDeviceForm' },
  { label: 'Do Not Page', value: 'DNP' },
  { label: 'Virtual Machine', value: 'CreateVirtualMachine' },
  { label: 'Add Firewall Rules', value: 'FirewallNewRequestForm' },
  { label: 'Database', value: 'CreateDatabase' },
  { label: 'DatabaseV2', value: 'CreateDatabaseV2' },
  { label: 'Common firewall Rule', value: 'CommonFirewallRuleForm' },
  { label: 'Capacity Planning Map view', value: 'CapacityPlanningMapview' },
  { label: 'Nebula Dashboard', value: 'NebulaDashboard' },
  { label: 'Add SiteMap', value: 'AddSiteMapForm' },
  { label: 'Manage SiteMap', value: 'ManageSiteMap' },
  { label: 'Add Firewall Rules V2', value: 'FirewallNewRequestFormV2' },
  { label: 'Capacity Planning Table view', value: 'CapacityPlanningTableview' },
  { label: 'CreateStorageS3', value: 'CreateStorageS3' },
  { label: 'CreateStorageNFS', value: 'CreateStorageNFS' },
  { label: 'CreateCorpnetVM', value: 'CreateCorpnetVM' },
  { label: 'Create Sub Account', value: 'CreateSubAccountForm' },
  { label: 'Manage Sub Account Group', value: 'ManageSubAccountGroups' },
  { label: 'Capacity Planning Card view', value: 'CapacityPlanningCardview' },
  { label: 'Manage AWS Sub Account', value: 'ManageAwsSubAccount' },
  { label: 'Path Analysis', value: 'PathAnalysisForm' },
  { label: 'Manage Sub Account Groups And Users', value: 'ManageSubAccountGroupsAndUsers' },
  { label: 'Internal Certificate', value: 'InternalCertificateForm' },
  { label: 'F5 - CaaS', value: 'LoadBalancerF5' },
  { label: 'Manage Permission Set', value: 'ManagePermissionSet' },
  { label: 'Create Permission Set', value: 'CreatePermissionSet' },
  { label: 'Bulk Import VM', value: 'BulkImportVM' },
  { label: 'PACE VirtualMachine', value: 'CreatePaceVM' },
  { label: 'Capacity Planning Dashboard', value: 'CapacityPlanningDashboard' },
  { label: 'Device Config Lookup', value: 'DeviceConfigLookupForm' },
  {
    label: 'Migrate V1 Firewall Requests',
    value: 'MigrateV1FirewallRequests',
  },
  { label: 'Add Site', value: 'AddSite' },
  { label: 'View Sites', value: 'ViewSites' },
  { label: 'Onboard New Project', value: 'CreateNewProject' },
  { label: 'Onboard New Group', value: 'OnboardNewGroup' },
  { label: 'Catalog Access Request', value: 'CatalogAccessRequest' },
  { label: 'Spec Flow S3', value: 'SpecFormS3' },
  { label: 'Spec Flow EC2', value: 'SpecFormEC2' },
  { label: 'Spec Flow EKS', value: 'SpecFormEKS' },
  { label: 'VPC', value: 'CreateVPC' },
  { label: 'CreateVmwareVM', value: 'CreateVmwareVM' },
  { label: 'CreateVmwareVMAdmin', value: 'CreateVmwareVMAdmin' },
  { label: 'Create Namespace', value: 'CreateNamespace' },
  { label: 'Secret Device Association', value: 'SecretDeviceAssociation' },
  { label: 'Reconfigure VMware', value: 'ReconfigureVMwareVM' },
  { label: 'Single Device', value: 'ZTPSingleDeviceForm' },
];
