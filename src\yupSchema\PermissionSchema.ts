import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

export const permission_validationSchema = yup.object().shape({
  permissionName: yup
    .string()
    .required('Permission Name is required')
    .matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),
  permissionKey: yup.string().required('Permission Key is required'),
  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});
