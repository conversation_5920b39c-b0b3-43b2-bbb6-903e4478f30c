import React, { useState, useEffect } from 'react';
// eslint-disable-next-line no-unused-vars
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

import ActionsColumn from 'components/Administration/ActionsColumn';
import RequestsGrid from 'components/RequestsGrid';
import AdministrationService from 'api/ApiService/AdministrationService';
import { ViewPermissionCatalogPayload } from 'types';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'mantis/themes/type';
import { AdministrationDialogData } from 'types';
import { useMediaQuery, useTheme } from '@mui/material';
import { getAdminColumnWidth } from 'utils/common';

interface ViewServiceCatalogProps {}

const DIALOG_DATA: AdministrationDialogData = {
  initialState: { open: false, type: '', title: '', content: '', confirmationText: '', data: {} },
  deleteDialog: {
    title: 'Confirmation',
    content: 'Shall we proceed with this delete request?',
    confirmationText: 'Delete',
  },
  editDialog: {
    title: 'Confirmation',
    content: 'Yet to be implemented',
    confirmationText: 'Edit',
  },
};

const ViewPermissionTable: React.FunctionComponent<ViewServiceCatalogProps> = () => {
  const apiAdministrationService = new AdministrationService();
  const [confirmDialog, setConfirmDialog] = useState<AdministrationDialogData['initialState']>(DIALOG_DATA.initialState);
  const [viewPermissionCatalogData, setPermissionCatalogData] = useState<ViewPermissionCatalogPayload[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [viewDetailsDialog, setViewDetailsDialog] = useState<{ open: boolean; id: string }>({ open: false, id: '' });
  const theme: NebulaTheme = useTheme();
  const isSmaller = useMediaQuery(theme.breakpoints.down('xl'));
  const fetchServiceRequestCatalogList = () => {
    setIsLoading(true);
    apiAdministrationService
      .getPermissionRequestCatalogs()
      .then((res) => {
        if (res.status) {
          setPermissionCatalogData(
            res.data.map((viewData) => ({
              permissionName: viewData?.permissionName,
              id: viewData?.id,
              description: viewData?.description,
            }))
          );
        } else {
          setPermissionCatalogData([]);
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  useEffect(() => {
    fetchServiceRequestCatalogList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: GridColDef[] = [
    {
      field: 'permissionName',
      headerName: 'Permission Name',
      flex: 1,
    },
    { field: 'description', headerName: 'Description', flex: 1 },
    {
      field: 'actionPending',
      headerName: 'Action',
      width: getAdminColumnWidth(isSmaller),
      headerAlign: 'center',
      align: 'center',
      disableColumnMenu: true,
      filterable: false,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => {
        const deleteClickHandler = () => {
          setConfirmDialog({ open: true, type: 'DELETED', ...DIALOG_DATA.deleteDialog, data: params });
        };
        return (
          <ActionsColumn
            permissions={{ canUpdate: false, canDelete: false }}
            onEditHandler={() => {}}
            onDeleteHandler={deleteClickHandler}
          />
        );
      },
    },
  ];

  const dialogConfirmHandler = () => {
    const {
      row: { id },
    } = confirmDialog.data;
    setPermissionCatalogData(viewPermissionCatalogData.filter((obj) => obj.id !== id));
    setConfirmDialog(DIALOG_DATA.initialState);
  };

  const dialogCloseHandler = (): void => {
    setConfirmDialog(DIALOG_DATA.initialState);
  };

  return (
    <RequestsGrid
      isLoading={isLoading}
      rows={viewPermissionCatalogData}
      columns={columns}
      viewDetailsDialog={viewDetailsDialog}
      setViewDetailsDialog={setViewDetailsDialog}
      confirmDialog={confirmDialog}
      dialogConfirmHandler={dialogConfirmHandler}
      dialogCloseHandler={dialogCloseHandler}
      fetchData={fetchServiceRequestCatalogList}
    />
  );
};

export default ViewPermissionTable;
