export const GetApprovalsSuccessResponse = {
  status: true,
  data: [
    {
      id: '659e8bb34fc49491faccb69f',
      metadata: {
        serviceCatalog: {
          catalogName: 'IaaS-Naas-Create-IP-Pool',
        },
      },
      createdAt: '2024-01-10T12:21:07.628Z',
    },
    {
      id: '789e8bb34fc49491faccb69f',
      metadata: {
        serviceCatalog: {
          catalogName: 'IaaS-Naas-Create-IP-Pool',
        },
      },
      createdAt: '2024-01-12T11:14:03.618Z',
    },
  ],
};

export const UpdateApprovalsRequestData = {
  status: true,
  data: [
    {
      id: '659e8bb34fc49491faccb69f',
      metadata: {
        serviceCatalog: {
          catalogName: 'IaaS-Naas-Create-IP-Pool',
        },
      },
      createdAt: '2024-01-10T12:21:07.628Z',
    },
  ],
};

export const UpdateApprovalsRequestResponse = {
  status: true,
  data: {
    id: '',
    serviceRequestId: '',
    message: '',
  },
};
