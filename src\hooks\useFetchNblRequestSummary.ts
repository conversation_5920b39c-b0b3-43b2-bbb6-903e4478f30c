import { useState } from 'react';

import { useApiService } from 'api/ApiService/context';
import { formatMetricQueryArray } from 'utils/common';
import { CatalogItem } from 'types';

interface NebulaRequestStats {
  totalRequests: number;
  approvedRequests: number;
  failedRequests: number;
  processingRequests: number;
}

export interface CatalogItemsQuery {
  year: number;
  quarter: number[];
  month?: number;
  level1: CatalogItem[];
  level3: CatalogItem[];
  level4: CatalogItem[];
  createdBy: string;
}

const getNebulaRequestSummaryQuery = ({ createdBy, year, quarter, month, level1, level3, level4 }: CatalogItemsQuery) => {
  return {
    query: `{
        getRequestSummary(
        year: ${year}
        quarter: [${quarter}]
        ${month ? 'month: ' + month : ''}
        createdBy:"${createdBy}"
        catalogs1: [${formatMetricQueryArray(level1)}]
        catalogs3: [${formatMetricQueryArray(level3)}]
        catalogs4: [${formatMetricQueryArray(level4)}] 
        ) {
            totalRequests
            approvedRequests
            processingRequests
            failedRequests
        }
  }
    `,
  };
};

const useFetchNebulaRequestSummary = () => {
  const { apiUsageMetricsService } = useApiService();
  const [loadingRequestSummary, setLoadingRequestSummary] = useState(true);
  const [nblRequestSummary, setNebulaRequestSummary] = useState<NebulaRequestStats>({
    totalRequests: 0,
    approvedRequests: 0,
    failedRequests: 0,
    processingRequests: 0,
  });

  const fetchNblRequestSummary = async ({ year, quarter, month, level1, level3, level4, createdBy }: CatalogItemsQuery) => {
    const queryPayload = {
      year,
      quarter,
      month,
      level1,
      level3,
      level4,
      createdBy,
    };

    setLoadingRequestSummary(true);

    const nblRequestSummaryResponse = await apiUsageMetricsService.getLandingPageNblRequestSummary(
      getNebulaRequestSummaryQuery(queryPayload)
    );
    if (nblRequestSummaryResponse.status) {
      const { totalRequests, approvedRequests, failedRequests, processingRequests } =
        nblRequestSummaryResponse.data.data.getRequestSummary;
      setNebulaRequestSummary({
        totalRequests,
        approvedRequests,
        failedRequests,
        processingRequests,
      });
      setLoadingRequestSummary(false);
    }
  };

  return {
    nblRequestSummary,
    fetchNblRequestSummary,
    loadingRequestSummary,
  };
};

export default useFetchNebulaRequestSummary;
