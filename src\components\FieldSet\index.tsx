import React from 'react';

import { Box, Grid, Typography } from '@mui/material';

interface FieldSetProps {
  children: React.ReactElement;
  isLast?: boolean;
  title: string;
}

const FieldSet: React.FunctionComponent<FieldSetProps> = ({ children, isLast, title }: FieldSetProps) => {
  return (
    <>
      <Grid container gap={1.75} alignItems={'center'} mb={isLast ? 0 : 0.5}>
        <Typography variant={'h4'} color={'primary.main'} mb={2}>
          {title}
        </Typography>
      </Grid>
      <Box
        component="fieldset"
        sx={{
          mb: 2,
          p: 0,
          border: 'none',
        }}
      >
        {children}
      </Box>
    </>
  );
};

export default FieldSet;
