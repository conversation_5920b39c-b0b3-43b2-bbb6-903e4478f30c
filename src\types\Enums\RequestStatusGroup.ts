import RequestStatus from './RequestStatus';

export enum RequestStatusGroupIdentifier {
  APPROVED = 'APPROVED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  TOTALREQUESTS = 'TOTALREQUESTS',
}

export const getRequestStatusGroup = (type: RequestStatusGroupIdentifier): RequestStatus[] => {
  switch (type) {
    case RequestStatusGroupIdentifier.APPROVED:
      return [RequestStatus.APPROVED, RequestStatus.AUTO_APPROVED, RequestStatus.PARTIALLY_APPROVED];
    case RequestStatusGroupIdentifier.PENDING:
      return [
        RequestStatus.PROCESSING,
        RequestStatus.PENDING_APPROVAL,
        RequestStatus.PENDING_RISK_ANALYSIS,
        RequestStatus.PENDING_DESIGNER_RESULTS,
        RequestStatus.SUBMITTED,
        RequestStatus.PENDING_DEPLOYMENT,
        RequestStatus.CREATED,
        RequestStatus.PENDING_PIPELINE_CHECK,
        RequestStatus.PENDING_DAP_PROCESS,
        RequestStatus.RETRYING,
      ];
    case RequestStatusGroupIdentifier.REJECTED:
      return [RequestStatus.REJECTED, RequestStatus.PARTIALLY_REJECTED];
    case RequestStatusGroupIdentifier.COMPLETED:
      return [RequestStatus.COMPLETED, RequestStatus.PARTIAL_SUCCESS, RequestStatus.PARTIAL, RequestStatus.CLOSED, RequestStatus.SUCCESS];
    case RequestStatusGroupIdentifier.FAILED:
      return [RequestStatus.FAILED, RequestStatus.TIMED_OUT];
    case RequestStatusGroupIdentifier.CANCELLED:
      return [RequestStatus.CANCELLED, RequestStatus.NO_CHANGE_REQUIRED];
    case RequestStatusGroupIdentifier.TOTALREQUESTS:
      return Array.from(
        new Set([
          ...getRequestStatusGroup(RequestStatusGroupIdentifier.APPROVED),
          ...getRequestStatusGroup(RequestStatusGroupIdentifier.PENDING),
          ...getRequestStatusGroup(RequestStatusGroupIdentifier.REJECTED),
          ...getRequestStatusGroup(RequestStatusGroupIdentifier.CANCELLED),
          ...getRequestStatusGroup(RequestStatusGroupIdentifier.COMPLETED),
          ...getRequestStatusGroup(RequestStatusGroupIdentifier.FAILED),
        ])
      );
    default:
      return [];
  }
};
