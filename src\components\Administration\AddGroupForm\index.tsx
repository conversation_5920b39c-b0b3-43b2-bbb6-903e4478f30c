import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid, Typography, Button, Box, Tabs, Tab, FormHelperText } from '@mui/material';

import withAdminPermissions from 'hoc/withAdminPermissions';
import { formWrapperError, yupMatchesParams } from 'utils/common';
import PermissionService from 'api/ApiService/PermissionService';
import TextField from 'components/TextField';
import FormWrapper from 'components/FormWrapper';
import icons from 'assets/images/icons';
// eslint-disable-next-line no-unused-vars
import { GroupPermissionsPayload, AdminComponent } from 'types';
import { FormProps, GroupsPermissions, SelectedGroupPermissions, Roles, GroupDataProps } from 'types';
import ProjectPermissionForm from './ProjectPermission';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';

import CatalogPermissionForm from './CatalogPermission';
import AdminPermissionForm from './AdminPermission';
import ProjectDataTable from './ProjectDataTable';
import CatalogDataTable from './CatalogDataTable';
import ChipComponent from 'components/ChipComponent';
import AdminDataTable from './AdminDataTable';
import OnBoardService from '../../../api/ApiService/OnBoardService';
import MultiSelect from 'components/MultiSelect';
import useNblNavigate from 'hooks/useNblNavigate';

interface AddGroupFormProps extends FormProps {
  editGroupDetails?: GroupDataProps;
}

const emailSchema = yup.string().email('Invalid email address');

const TABS: { [key: string]: string } = {
  projectPermissions: 'Project',
  catalogPermissions: 'Catalog',
  adminTilePermissions: 'Admin',
};

const validationSchema = yup.object().shape({
  groupName: yup
    .string()
    .matches(yupMatchesParams.groupName.pattern, yupMatchesParams.groupName.errorMessage)
    .required('Group Name is required'),
  emailDistribution: yup
    .array()
    .of(yup.string().email('Please enter valid email addresses'))
    .required('Email Dl is required')
    .min(1, 'Please enter atleast one email address')
    .test('unique', 'Duplicate emails are not allowed', function (value) {
      const emails = value || [];
      const uniqueEmails = new Set(emails);
      return emails.length === uniqueEmails.size;
    }),

  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});

enum DIALOGS {
  PROJECT = 'PROJECT',
  CATALOG = 'CATALOG',
  ADMIN = 'ADMIN',
}

const AddGroupForm: AdminComponent<AddGroupFormProps> = ({
  title,
  editGroupDetails,
  onClose,
  permissions: { canUpdate, canCreate },
}: AddGroupFormProps) => {
  const dispatch = useDispatch();
  const navigate = useNblNavigate();

  const permissionService = new PermissionService();

  const [groupPermissions, setGroupPermissions] = useState<GroupsPermissions>({
    projectPermissions: { projectNames: [], projectRoles: [] },
    catalogPermissions: { catalogNames: [], catalogRoles: [] },
    adminTilePermissions: { adminNames: [], adminRoles: [] },
  });
  const [adminTiles, setAdminTiles] = useState<{ id: string; tileName: string }[]>([{ id: '', tileName: '' }]);
  const [adminTileRoles, setAdminTileRoles] = useState<Roles[]>([{ _id: '', roleName: '' }]);
  const [organizations, setOrganizations] = useState<{}>();

  const [isDialogOpen, setIsDialogOpen] = useState<{ [DIALOGS.PROJECT]: boolean; [DIALOGS.CATALOG]: boolean; [DIALOGS.ADMIN]: boolean }>({
    [DIALOGS.PROJECT]: false,
    [DIALOGS.CATALOG]: false,
    [DIALOGS.ADMIN]: false,
  });
  const [selectedPermissions, setSelectedPermissions] = useState<SelectedGroupPermissions>({
    projectPermissions: [],
    catalogPermissions: [],
    adminTilePermissions: [],
  });
  const [isFormSubmitting, setIsFormSubmitting] = useState<boolean>(false);
  const [selectedTabIndex, setSelectedTabIndex] = React.useState(0);
  const [tabs, setTabs] = React.useState<string[]>(['']);
  const IS_AWS_SUB_ACCOUNT_ENABLED = process.env?.REACT_APP_AWS_SUB_ACCOUNT === 'enabled';
  const IS_PINXT_INTEGRATION_ENABLED = process.env?.REACT_APP_PINXT_INTEGRATION === 'enabled';

  useEffect(() => {
    let showTabs: string[] = [];
    Object.keys(selectedPermissions).map((item) => {
      if (selectedPermissions[item as keyof SelectedGroupPermissions]?.length > 0) {
        showTabs.push(TABS[item]);
      }
    });
    setTabs(showTabs);
  }, [selectedPermissions]);

  const fetchPermissions = () => {
    dispatch(showSpinner({ id: SPINNER_IDS.groupPermissions, status: true, message: 'Loading project and catalog permissions...' }));
    permissionService
      .getGroupsPermissions()
      .then((res) => {
        if (res.status) {
          setGroupPermissions({
            ...groupPermissions,
            catalogPermissions: res.data.catalogPermissions,
            projectPermissions: res.data.projectPermissions,
          });
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: SPINNER_IDS.groupPermissions, status: false, message: '' }));
      });
  };

  useEffect(() => {
    setGroupPermissions({
      ...groupPermissions,
      adminTilePermissions: { adminNames: adminTiles, adminRoles: adminTileRoles },
    });
  }, [adminTiles, adminTileRoles, groupPermissions.catalogPermissions, groupPermissions.projectPermissions]);

  const fetchAdminTiles = () => {
    dispatch(showSpinner({ id: SPINNER_IDS.adminTiles, status: true, message: 'Loading Admin Tiles...' }));
    permissionService
      .getAdminTiles()
      .then((res) => {
        if (res.status) {
          setAdminTiles(res.data);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: SPINNER_IDS.adminTiles, status: false, message: '' }));
      });
  };

  const fetchAdminRoles = () => {
    dispatch(showSpinner({ id: SPINNER_IDS.adminTileRoles, status: true, message: 'Loading Admin Tile Roles...' }));
    permissionService
      .getAdminTileRoles()
      .then((res) => {
        if (res.status) {
          setAdminTileRoles(res.data);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: SPINNER_IDS.adminTileRoles, status: false, message: '' }));
      });
  };
  useEffect(() => {
    fetchPermissions();
    fetchAdminTiles();
    fetchAdminRoles();
    fetchOrganizations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const addGroupHandler = (payload: GroupPermissionsPayload) => {
    setIsFormSubmitting(true);
    permissionService
      .addGroupPermissions(payload)
      .then((res) => {
        if (res.status) {
          toast.success(res.data?.message || 'Request submitted successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
          navigate('/administration/groups/manage-registered-ad-groups');
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        setIsFormSubmitting(false);
      });
  };

  const editGroupHandler = (payload: GroupPermissionsPayload) => {
    setIsFormSubmitting(true);
    const existingGroupName = editGroupDetails?.groupName || '';
    permissionService
      .editGroupPermissions(payload, existingGroupName)
      .then((res) => {
        if (res.status) {
          toast.success(res.data?.message || 'Request submitted successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
          navigate('/administration/groups/manage-registered-ad-groups');
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        setIsFormSubmitting(false);
      });
  };
  const [errorMessage, setErrorMessage] = useState<{ value?: any; error: boolean; message: string }[]>([]);

  const formik = useFormik({
    initialValues: {
      groupName: editGroupDetails?.groupName || '',
      description: editGroupDetails?.description || '',
      orgId: [],
      clientIds: editGroupDetails?.clientIds || [],
      emailDistribution: editGroupDetails?.emailDistribution || [],
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const { groupName, description, orgId, clientIds } = values;
      const { projectPermissions, catalogPermissions, adminTilePermissions } = selectedPermissions;
      const payload: GroupPermissionsPayload = {
        groupName,
        emailDistribution: values.emailDistribution,
        description,
        orgId,
        clientIds: clientIds,
        projectPermissions: [],
        adminTilePermissions: [],
        catalogPermissions: [],
      };

      payload.projectPermissions = projectPermissions.map((permission) => ({
        projectId: permission.projectId,
        roles: permission.roles.map((roles) => ({ roleId: roles._id })),
      }));

      payload.catalogPermissions = catalogPermissions.map((permission) => ({
        catalogId: permission.catalogId,
        roles: permission.roles.map((roles) => ({ roleId: roles._id })),
      }));

      payload.adminTilePermissions = adminTilePermissions.map((permission) => ({
        adminTileId: permission.adminTileId,
        roles: permission.roles.map((roles) => ({ roleId: roles._id })),
      }));

      editGroupDetails ? editGroupHandler(payload) : addGroupHandler(payload);
    },
  });

  const updateChipMessage = (value: string, isValid: boolean, message: string, newValue: string[]) => {
    setErrorMessage((prevErrors: { value?: any; error: boolean; message: string }[]) => {
      const newErrors = [...prevErrors];
      const index = newErrors.findIndex((err) => err.value === value);
      if (index >= 0) {
        newErrors[index] = { error: !isValid, message, value };
      } else {
        newErrors.push({ error: !isValid, message, value });
      }
      return newErrors.filter((err) => newValue.includes(err.value));
    });
  };

  const handleEmailDl = (event: any, newValue: string[]) => {
    const { value } = event.target;
    if (value) {
      emailSchema
        .validate(value)
        .then(() => {
          updateChipMessage(value, true, 'Valid email address', newValue);
        })
        .catch((err: any) => {
          updateChipMessage(value, false, err.message, newValue);
        });
    }
    if (newValue.length < formik.values.emailDistribution.length) {
      const removedIndex = formik.values.emailDistribution.findIndex((val: any) => !newValue.includes(val));
      setErrorMessage((prev) => prev.filter((_, index) => index !== removedIndex));
    }
  };

  useEffect(() => {
    if (editGroupDetails) {
      formik.setFieldValue('groupName', editGroupDetails.groupName);
      formik.setFieldValue('emailDistribution', editGroupDetails.emailDistribution || []);
      formik.setFieldValue('description', editGroupDetails.description);
      formik.setFieldValue('orgId', editGroupDetails.orgId || []);
      formik.setFieldValue('clientIds', editGroupDetails.clientIds);
      setSelectedPermissions({
        catalogPermissions: editGroupDetails.catalogPermissions,
        projectPermissions: editGroupDetails.projectPermissions,
        adminTilePermissions: editGroupDetails.adminTilePermissions,
      });
    }
    // eslint-disable-next-line
  }, [editGroupDetails]);

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const dialogHandler = (dialogType: DIALOGS.PROJECT | DIALOGS.CATALOG | DIALOGS.ADMIN, isOpened: boolean) => {
    setIsDialogOpen({
      ...isDialogOpen,
      [dialogType]: isOpened,
    });
  };
  const setSelectedProjectPermissions = (values: SelectedGroupPermissions['projectPermissions']) => {
    setSelectedPermissions({ ...selectedPermissions, projectPermissions: values });
  };

  const setProjectData = (values: { projectId: string; projectName: string; roles: Roles[] }) => {
    setSelectedProjectPermissions([...selectedPermissions.projectPermissions, values]);
    dialogHandler(DIALOGS.PROJECT, false);
  };

  const setSelectedCatalogPermissions = (values: SelectedGroupPermissions['catalogPermissions']) => {
    setSelectedPermissions({ ...selectedPermissions, catalogPermissions: values });
  };

  const setCatalogData = (values: { catalogId: string; catalogName: string; roles: Roles[] }) => {
    setSelectedPermissions({ ...selectedPermissions, catalogPermissions: [...selectedPermissions.catalogPermissions, values] });

    dialogHandler(DIALOGS.CATALOG, false);
  };

  const setSelectedAdminPermissions = (values: SelectedGroupPermissions['adminTilePermissions']) => {
    setSelectedPermissions({ ...selectedPermissions, adminTilePermissions: values });
  };

  const setAdminData = (values: { adminTileId: string; tileName: string; roles: Roles[] }) => {
    setSelectedPermissions({ ...selectedPermissions, adminTilePermissions: [...selectedPermissions.adminTilePermissions, values] });

    dialogHandler(DIALOGS.ADMIN, false);
  };

  const dialogCloseHandler = () => {
    isDialogOpen[DIALOGS.PROJECT] && dialogHandler(DIALOGS.PROJECT, false);
    isDialogOpen[DIALOGS.CATALOG] && dialogHandler(DIALOGS.CATALOG, false);
    isDialogOpen[DIALOGS.ADMIN] && dialogHandler(DIALOGS.ADMIN, false);
  };

  const isAddButtonDisabled = () => {
    const {
      projectPermissions: { projectNames },
      catalogPermissions: { catalogNames },
    } = groupPermissions;
    const addProjectPermissions = Boolean(
      selectedPermissions.projectPermissions.length && selectedPermissions.projectPermissions.length === projectNames.length
    );
    const addCatalogPermissions = Boolean(
      selectedPermissions.catalogPermissions.length && selectedPermissions.catalogPermissions.length === catalogNames.length
    );
    return {
      addProjectPermissions,
      addCatalogPermissions,
    };
  };

  const onBoardService = new OnBoardService();
  const fetchOrganizations = () => {
    onBoardService.getOrganizations().then((res) => {
      if (res.status) {
        setOrganizations(res.data);
      }
    });
  };

  const renderFormFields = () => {
    return (
      <>
        <Grid container spacing={1}>
          <Grid container item justifyContent={'center'} spacing={3}>
            <Grid item xs={6}>
              <TextField
                type="text"
                value={formik.values.groupName}
                label="Group Name *"
                name="groupName"
                placeholder="Please enter"
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.groupName && formik.errors.groupName}
              />
            </Grid>
            <Grid item xs={6}>
              <ChipComponent
                options={[]}
                label="Email DL(s) *"
                name="emailDistribution"
                value={formik.values.emailDistribution}
                handleChange={(event, newValue) => {
                  formik.setFieldValue('emailDistribution', newValue);
                  handleEmailDl(event, newValue);
                }}
                handleBlur={(event: any) => formik.handleBlur(event)}
                error={formik.touched.emailDistribution && formik.errors.emailDistribution}
                errorMessage={errorMessage}
              />
              {!formik.errors.emailDistribution && (
                <FormHelperText sx={{ color: 'forms.titleColor' }}>Press enter after each valid email address or DL</FormHelperText>
              )}
            </Grid>
            {IS_AWS_SUB_ACCOUNT_ENABLED && (
              <Grid item xs={12}>
                {organizations && (
                  <MultiSelect
                    value={formik.values.orgId}
                    label="Organization"
                    placeholder="Select"
                    name="orgId"
                    handleChange={formik.handleChange}
                    options={organizations}
                  />
                )}
              </Grid>
            )}
            {IS_PINXT_INTEGRATION_ENABLED && (
              <Grid item xs={12}>
                <ChipComponent
                  options={[]}
                  label="ClientId's"
                  name="clientIds"
                  value={formik.values.clientIds}
                  handleChange={(event, newValue) => {
                    formik.setFieldValue('clientIds', newValue);
                  }}
                  handleBlur={(event: any) => formik.handleBlur(event)}
                  error={formik.touched.clientIds && formik.errors.clientIds}
                />
                {!formik.errors.clientIds && (
                  <FormHelperText sx={{ color: 'forms.titleColor' }}>Press enter after each clientId</FormHelperText>
                )}
              </Grid>
            )}
            <Grid item xs={12}>
              <TextField
                type="text"
                value={formik.values.description}
                label="Description"
                name="description"
                placeholder="Please enter"
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.description && formik.errors.description}
              />
            </Grid>
            <Grid item xs={6}>
              <Typography mb={1} variant="h4" color={'text.primary'}>
                Project Permissions *
              </Typography>
              <Button
                data-testid={'add-project-permissions'}
                variant="outlined"
                disabled={isAddButtonDisabled().addProjectPermissions}
                onClick={() => dialogHandler(DIALOGS.PROJECT, true)}
                sx={{
                  ':hover': {
                    bgcolor: '#03213B',
                    color: '#ffffff',
                    borderColor: '#03213B',
                  },
                }}
              >
                Add
              </Button>
            </Grid>
            <Grid item xs={4}>
              <Typography mb={1} variant="h4" color={'text.primary'}>
                Catalog Permissions *
              </Typography>
              <Button
                data-testid={'add-catalog-permissions'}
                variant="outlined"
                disabled={isAddButtonDisabled().addCatalogPermissions}
                onClick={() => dialogHandler(DIALOGS.CATALOG, true)}
                sx={{
                  ':hover': {
                    bgcolor: '#03213B',
                    color: '#ffffff',
                    borderColor: '#03213B',
                  },
                }}
              >
                Add
              </Button>
            </Grid>
            <Grid item xs={2}>
              <Typography mb={1} variant="h4" color={'text.primary'}>
                Admin Permissions *
              </Typography>
              <Button
                data-testid={'add-admin-permissions'}
                variant="outlined"
                disabled={isAddButtonDisabled().addCatalogPermissions}
                onClick={() => dialogHandler(DIALOGS.ADMIN, true)}
                sx={{
                  ':hover': {
                    bgcolor: '#03213B',
                    color: '#ffffff',
                    borderColor: '#03213B',
                  },
                }}
              >
                Add
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </>
    );
  };

  const renderDailogs = () => {
    return (
      <>
        {isDialogOpen[DIALOGS.PROJECT] && (
          <ProjectPermissionForm
            open={true}
            projectPermissions={groupPermissions.projectPermissions}
            selectedProjectPermissions={selectedPermissions.projectPermissions}
            onClose={dialogCloseHandler}
            setProjectData={setProjectData}
          />
        )}
        {isDialogOpen[DIALOGS.CATALOG] && (
          <CatalogPermissionForm
            open={true}
            catalogPermissions={groupPermissions.catalogPermissions}
            selectedCatalogPermissions={selectedPermissions.catalogPermissions}
            onClose={dialogCloseHandler}
            setCatalogData={setCatalogData}
          />
        )}
        {isDialogOpen[DIALOGS.ADMIN] && (
          <AdminPermissionForm
            open={true}
            adminTilePermissions={groupPermissions.adminTilePermissions}
            selectedAdminPermissions={selectedPermissions.adminTilePermissions}
            onClose={dialogCloseHandler}
            setAdminData={setAdminData}
          />
        )}
      </>
    );
  };

  const handleChange = (event: any, newValue: any) => {
    setSelectedTabIndex(newValue);
  };

  const renderSelectedTab = () => {
    switch (tabs[selectedTabIndex]) {
      case 'Project':
        return (
          <Grid item xs={10} sx={{ mx: 2 }}>
            <ProjectDataTable
              data={selectedPermissions.projectPermissions?.map((item, index) => ({ ...item, id: index }))}
              projectPermissions={groupPermissions.projectPermissions}
              setSelectedProjectPermissions={setSelectedProjectPermissions}
            />
          </Grid>
        );

      case 'Catalog':
        return (
          <Grid item xs={10} sx={{ mx: 2 }}>
            <CatalogDataTable
              data={selectedPermissions.catalogPermissions?.map((item, index) => ({ ...item, id: index }))}
              catalogPermissions={groupPermissions.catalogPermissions}
              setSelectedCatalogPermissions={setSelectedCatalogPermissions}
            />
          </Grid>
        );

      case 'Admin':
        return (
          <Grid item xs={10} sx={{ mx: 2 }}>
            <AdminDataTable
              data={selectedPermissions.adminTilePermissions?.map((item, index) => ({ ...item, id: index }))}
              adminTilePermissions={groupPermissions.adminTilePermissions}
              setSelectedAdminPermissions={setSelectedAdminPermissions}
            />
          </Grid>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <FormWrapper
        title={title}
        isSubmitting={
          isFormSubmitting ||
          (!selectedPermissions.projectPermissions.length &&
            !selectedPermissions.catalogPermissions.length &&
            !selectedPermissions.adminTilePermissions.length)
        }
        errors={formWrapperError(formik)}
        submitText={'Submit'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        Icon={icons.AddOutlined}
        canCreate={canCreate}
        canUpdate={canUpdate}
      >
        <>
          {renderFormFields()}
          <Box sx={{ flexGrow: 1, display: 'flex', mt: 3 }}>
            <Tabs
              orientation="vertical"
              variant="scrollable"
              value={selectedTabIndex}
              onChange={handleChange}
              aria-label="Vertical-tabs"
              sx={{
                mt: 3,
                '.MuiTabs-indicator': {
                  left: 0,
                },
              }}
            >
              {tabs.map((item: string, index: number) => {
                return <Tab label={item} key={`tab-${index}`} sx={{ borderLeft: 1, borderColor: 'divider' }} />;
              })}
            </Tabs>
            {renderSelectedTab()}
          </Box>
        </>
      </FormWrapper>
      {renderDailogs()}
    </>
  );
};

AddGroupForm.type = ADMIN_TILE_PERMISSION_TYPE.form;

export default withAdminPermissions(AddGroupForm);
