import React from 'react';
import { Grid, SxProps } from '@mui/material';
import { usageColors } from './utils/colors';
import NblTypography from 'sharedComponents/NblTypography';

interface UtilizationProps {
  sx?: SxProps;
  textColor?: string;
  typographySx?: SxProps;
}
const UtilizationStatus: React.FC<UtilizationProps> = ({ textColor }) => {
  const utilizationStatus = [
    { color: `${usageColors.lowUsage}`, percentage: '20%' },
    { color: `${usageColors.mediumUsage}`, percentage: '40%' },
    { color: `${usageColors.highUsage}`, percentage: '60%' },
    { color: `${usageColors.warningUsage}`, percentage: '80%' },
    { color: `${usageColors.criticalUsage}`, percentage: '90%' },
    { color: `${usageColors.deadUsage}`, percentage: '100%' },
  ];

  return (
    <Grid display="flex" flexDirection="column" alignItems="self-end">
      <NblTypography variant="body1" color={textColor ? 'shade4' : 'shade1'}>
        Utilization Status
      </NblTypography>

      <Grid display="flex">
        {utilizationStatus.map((item, index) => (
          <Grid key={index} display="flex">
            <Grid
              width={43}
              height={7}
              bgcolor={item.color}
              sx={{
                borderRadius: '10px',
                opacity: 1,
                mt: 1,
                ml: 1,
              }}
            />
          </Grid>
        ))}
      </Grid>
      <Grid display="flex" marginTop={1}>
        {utilizationStatus.map((item, index) => (
          <Grid key={index} display="flex" sx={{ ml: 1 }}>
            <NblTypography variant="subtitle1" color="shade1" textAlign="center" width="43px">
              {item.percentage}
            </NblTypography>
          </Grid>
        ))}
      </Grid>
    </Grid>
  );
};

export default UtilizationStatus;
