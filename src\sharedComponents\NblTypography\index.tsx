import React from 'react';

import { TypographyPalette } from 'NebulaTheme/type/typographyPalette';
import { StyledTypography, TypographyVariant, TypographyWeight } from './styled';

export interface NblTypographyProps {
  variant: TypographyVariant;
  weight?: TypographyWeight;
  color?: keyof TypographyPalette['typography'];
  margin?: string;
  padding?: string;
  children: React.ReactNode;
  textAlign?: React.CSSProperties['textAlign'];
  textTransform?: React.CSSProperties['textTransform'];
  display?: React.CSSProperties['display'];
  whiteSpace?: React.CSSProperties['whiteSpace'];
  wordBreak?: React.CSSProperties['wordBreak'];
  showEllipsis?: boolean;
  opacity?: number;
  width?: string;
  onClick?: (event?: React.MouseEvent<HTMLElement>) => void;
  cursor?: React.CSSProperties['cursor'];
  textDecoration?: React.CSSProperties['textDecoration'];
}

const NblTypography: React.FC<NblTypographyProps> = ({
  variant,
  weight,
  color,
  margin,
  padding,
  children,
  textAlign,
  display,
  whiteSpace,
  opacity,
  textTransform,
  width,
  cursor,
  onClick,
  wordBreak,
  showEllipsis,
  textDecoration,
}) => (
  <StyledTypography
    variant={variant}
    weight={weight}
    color={color}
    margin={margin}
    padding={padding}
    textAlign={textAlign}
    display={display}
    whiteSpace={whiteSpace}
    opacity={opacity}
    textTransform={textTransform}
    width={width}
    onClick={onClick}
    cursor={cursor}
    showEllipsis={showEllipsis}
    wordBreak={wordBreak}
    textDecoration={textDecoration}
  >
    {children}
  </StyledTypography>
);

export default NblTypography;
