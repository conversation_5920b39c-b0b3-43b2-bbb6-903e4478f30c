import React, { useEffect } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid } from '@mui/material';

import { formWrapperError, filterSelectedOptions } from 'utils/common';
import Select from 'components/Select';
import MultiSelect from 'components/MultiSelect';
import FormWrapper from 'components/FormWrapper';
import { ProjectPermissions, SelectedGroupPermissions } from 'types';
import DialogBox from 'components/DialogBox/Dialog';

interface ProjectPermissionProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  projectPermissions: ProjectPermissions;
  selectedProjectPermissions: SelectedGroupPermissions['projectPermissions'];
  setProjectData: (values: any) => void;
  projectDetails?: { projectId: string; roles: string[] };
}

const validationSchema = yup.object().shape({
  projectId: yup.string().trim().required('Projects is required'),
  projectRoles: yup.array().of(yup.string()).min(1, 'Please select atleast one project role').required('Project Roles is required'),
});

const ProjectPermissionComponent: React.FunctionComponent<ProjectPermissionProps> = ({
  open,
  onClose,
  projectPermissions,
  selectedProjectPermissions,
  setProjectData,
  projectDetails,
}: ProjectPermissionProps) => {
  const formik = useFormik({
    initialValues: {
      projectId: '',
      projectRoles: [],
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const selectedProject = projectPermissions.projectNames.find((project) => project._id === values.projectId);
      setProjectData({
        projectId: selectedProject?._id,
        projectName: selectedProject?.projectName,
        roles: values.projectRoles.map((id) => projectPermissions.projectRoles.find((roles) => roles._id === id)),
      });
    },
  });

  useEffect(() => {
    if (projectDetails) {
      formik.setValues({
        projectId: projectDetails.projectId,
        /* @ts-ignore */
        projectRoles: projectDetails.roles,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectDetails]);

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const getProjectOptions = () => {
    const projectNames = projectPermissions.projectNames.map((project) => ({ value: project._id, label: project.projectName }));
    const selectedProjectIds = selectedProjectPermissions.map((selectedPermissions) => selectedPermissions.projectId);
    return filterSelectedOptions(projectNames, selectedProjectIds, projectDetails?.projectId);
  };

  const renderFormFields = () => {
    return (
      <Grid container spacing={1}>
        <Grid container item justifyContent={'center'} spacing={3}>
          <Grid item xs={12}>
            <Select
              value={formik.values.projectId}
              label="Projects *"
              placeholder="Select"
              name="projectId"
              handleChange={formik.handleChange}
              error={formik.touched.projectId && formik.errors.projectId}
              options={getProjectOptions()}
            />
          </Grid>
          <Grid item xs={12}>
            <MultiSelect
              value={formik.values.projectRoles}
              label="Project Roles *"
              placeholder="Select"
              name="projectRoles"
              handleChange={formik.handleChange}
              error={formik.touched.projectRoles && formik.errors.projectRoles}
              options={projectPermissions.projectRoles.map((roles) => ({ label: roles.roleName, value: roles._id }))}
            />
          </Grid>
        </Grid>
      </Grid>
    );
  };

  return (
    <DialogBox fullWidth maxWidth={'sm'} open={open} onClose={onCancel}>
      <FormWrapper
        title={`${projectDetails?.projectId ? 'Edit' : 'Add'} Project Permissions`}
        errors={formWrapperError(formik)}
        submitText={'Save'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        isPopUpView
      >
        {renderFormFields()}
      </FormWrapper>
    </DialogBox>
  );
};

export default ProjectPermissionComponent;
