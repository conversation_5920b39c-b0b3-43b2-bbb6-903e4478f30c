import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblInputHelperText from '.';

describe('NblInputHelperText component', () => {
  const props = {
    helperText: 'Centrify Installation is disabled if only IPv6 selected'
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblInputHelperText {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
