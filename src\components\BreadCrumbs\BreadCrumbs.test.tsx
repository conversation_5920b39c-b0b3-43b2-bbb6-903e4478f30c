import { act, render, screen } from '@testing-library/react';
import { MemoryRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';
import ThemeProvider from 'mock/ThemeProvider';

import BreadCrumbs from './';
import { Catalogs } from 'mock/Catalogs';

const BASE_ROUTE = '';

describe('BreadCrumbs component', () => {
  test('Should render the breadcrumbs', async () => {
    const mockStore = configureMockStore();
    const store = mockStore({
      catalogs: Catalogs,
    });

    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={[`${BASE_ROUTE}/IaaS/network/IPAM/reserve-ip-block`]}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <BreadCrumbs />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );

    const breadcrumbs = screen.getAllByRole('link');
    expect(breadcrumbs).toHaveLength(3);

    expect(breadcrumbs[0]).toHaveTextContent(/IaaS/i);
    expect(breadcrumbs[1]).toHaveTextContent(/Network/i);
    expect(breadcrumbs[2]).toHaveTextContent(/IPAM/i);

    expect(getByText(/Home/i)).toBeInTheDocument();
    expect(getByText(/Reserve IP Block/i)).toBeInTheDocument();

    expect(breadcrumbs[0]).toHaveAttribute('href', `${BASE_ROUTE}/IaaS`);
    expect(breadcrumbs[1]).toHaveAttribute('href', `${BASE_ROUTE}/IaaS/network`);
    expect(breadcrumbs[2]).toHaveAttribute('href', `${BASE_ROUTE}/IaaS/network/IPAM`);
  });
});
