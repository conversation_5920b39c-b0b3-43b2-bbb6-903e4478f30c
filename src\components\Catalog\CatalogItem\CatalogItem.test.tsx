import { render } from '@testing-library/react';
import CatalogItem from '.';
import ThemeProvider from 'mock/ThemeProvider';
import { BrowserRouter } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

describe('CatalogItem component', () => {
  test('Should render the catalog item', async () => {
    const item = {
      title: 'Create Monitoring Organization',
      description: 'Create a new departmental container in Science Logic',
      button: 'start',
      path: '/IaaS/network/monitoring/create-organization',
      icon: 'FoundationOutlined',
      canAccess: true,
      disabled: false,
      id: 'createMonitoring',
    };

    const mockStore = configureMockStore();
    const store = mockStore({
      authorization: {
        permissions: {
          iaas: { ipam: { reserveIPAddressBlock: { Permissions: ['CREATE'] } } },
        },
      },
      common: {
        exposureParams: [],
      },
    });

    const { getByText } = render(
      <BrowserRouter>
        <ReduxProvider store={store}>
          <ThemeProvider>
            <CatalogItem {...item} />
          </ThemeProvider>
        </ReduxProvider>
      </BrowserRouter>
    );
    expect(getByText(item.title)).toBeInTheDocument();
  });
});
