import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTheme } from '@mui/material/styles';
import { Alert, Button, Grid, SvgIcon, styled } from '@mui/material';
import { LoadingButton } from '@mui/lab';
// eslint-disable-next-line no-unused-vars
import { AlertColor } from '@mui/material';

import FormTabs from 'components/FormTabs';
import ContentViewport from '../ContentViewport';
import FormTitle from '../FormTitle';
import HexaIconWrapper from '../HexaIconWrapper';
import { setDialogContentScrollOff } from 'store/reducers/common';
import { getDialogMaxHeight } from 'utils/common';
import { PERMISSION_MESSAGES } from 'utils/constant';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'mantis/themes/type';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';

interface FormWrapperProps {
  children: React.ReactElement;
  canCreate?: boolean;
  canUpdate?: boolean;
  errors: boolean;
  fullWidth?: boolean;
  isReadOnlyMode?: boolean;
  isPopUpView?: boolean;
  isSubmitting?: boolean;
  showSubmittingSpinner?: boolean;
  onSubmit?: (e?: React.FormEvent<HTMLFormElement> | undefined) => void;
  onCancel: () => void;
  onAdd?: () => void;
  submitText?: string;
  cancelText?: string;
  addText?: string; 
  title?: string;
  Icon?: React.ElementType;
  tabs?: {
    currentTab: number;
    onTabChangeHandler: (param: number) => void;
    tabsList: Array<{ id: string; label: string; errorFields: string[]; icon: null | typeof SvgIcon }>;
  };
}

const ResponsiveFormIcon = styled('div')(({ theme }) => ({
  [theme.breakpoints.down('xl')]: {
    '& .hexagon-svg-icon': {
      width: 50,
      height: 44,
    },
    '& .hexagon-svg-icon-children': {
      '& .MuiSvgIcon-root': {
        width: 20.84,
        height: 20.84,
      },
    },
  },
}));

const FormWrapper: React.FunctionComponent<FormWrapperProps> = ({
  children,
  canCreate = true,
  canUpdate = true,
  errors,
  fullWidth = false,
  isReadOnlyMode = false,
  isPopUpView = false,
  isSubmitting = false,
  showSubmittingSpinner = false,
  onSubmit,
  onCancel,
  onAdd, 
  submitText = 'Submit',
  cancelText = 'Cancel',
  addText = 'Add',
  title = '',
  tabs,
  Icon,
}: FormWrapperProps) => {
  const theme: NebulaTheme = useTheme();
  const dispatch = useDispatch();
  const { isDialogMaximized } = useSelector((state: State) => state.common);

  const {
    palette: { forms },
  } = theme;

  const showIcon = title && Icon;
  const showActions = !isReadOnlyMode;

  useEffect(() => {
    showActions && dispatch(setDialogContentScrollOff(true));
    return () => {
      dispatch(setDialogContentScrollOff(false));
    };
  }, []);

  const renderFormAlert = (message: string, color: AlertColor) => {
    return (
      <Grid container item>
        <Alert
          color={color}
          sx={{
            mt: 2,
            mx: 'auto',
            [theme.breakpoints.down('xl')]: {
              fontSize: '0.75rem',
              '& .MuiAlert-icon': {
                fontSize: '1rem',
              },
            },
          }}
        >
          {message}
        </Alert>
      </Grid>
    );
  };

  return (
    <ContentViewport
      sx={{
        ...((isReadOnlyMode || isPopUpView) && { backgroundColor: forms.viewportBgColor, borderColor: '#bcbbbb' }),
        ...(isPopUpView && { border: 'none', backdropFilter: 'none' }),
        ...(tabs && { borderColor: forms.tabs.borderColor, pt: 0, px: 0 }),
      }}
    >
      <Grid sx={{ maxWidth: fullWidth ? '100%' : 1445, width: '100%', mx: 'auto' }}>
        <form onSubmit={onSubmit}>
          <Grid sx={{ ...(isPopUpView && showActions && { maxHeight: getDialogMaxHeight(isDialogMaximized), overflowY: 'auto', px: 2 }) }}>
            {tabs && <FormTabs currentTab={tabs.currentTab} tabsList={tabs.tabsList} onTabChangeHandler={tabs.onTabChangeHandler} />}
            <ResponsiveFormIcon>
              {(showIcon || title) && (
                <Grid display={'flex'} justifyContent={'center'} alignItems={'center'} columnGap={5} mb={6.25}>
                  {showIcon && (
                    <HexaIconWrapper>
                      <Icon sx={{ color: forms.titleIconColor }} />
                    </HexaIconWrapper>
                  )}
                  {title && <FormTitle title={title}></FormTitle>}
                </Grid>
              )}
            </ResponsiveFormIcon>
            {!canCreate && !canUpdate && (
              <Grid container position={'relative'} top={'-20px'} mb={2}>
                {renderFormAlert(PERMISSION_MESSAGES.formSubmitRequest, 'error')}
              </Grid>
            )}
            {children}
            {!isReadOnlyMode && errors && renderFormAlert('Invalid form, please check the fields again.', 'error')}
          </Grid>
          <Grid container spacing={2}>
            {showActions && (
              <Grid display={'flex'} justifyContent={'center'} item xs={12} columnGap={5} sx={{ mt: 4 }}>
                <Button id={'form-cancel-btn'} variant="outlined" onClick={onCancel}>
                  {cancelText}
                </Button>
                {onAdd && (
                  <Button id={'form-add-btn'} variant="contained" onClick={onAdd} disabled={isSubmitting || (!canCreate && !canUpdate)}>
                    {addText}
                  </Button>
                )}
                <LoadingButton loading={showSubmittingSpinner && isSubmitting} id={'form-submit-btn'} variant="contained" type="submit" disabled={isSubmitting || (!canCreate && !canUpdate)} sx={{minWidth: '72px'}}>
                  <span>{submitText}</span>
                </LoadingButton>
              </Grid>
            )}
          </Grid>
        </form>
      </Grid>
    </ContentViewport>
  );
};

export default FormWrapper;
