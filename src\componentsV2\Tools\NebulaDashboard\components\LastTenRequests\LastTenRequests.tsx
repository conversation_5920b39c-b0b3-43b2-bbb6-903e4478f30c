import React from 'react';
import { StyledDot, StyledStatusTooltip } from '../../styled';
import { statusStyles } from '../../utility';
import { TableData } from '../../utils/types';

interface LastTenRequestsProps {
  catalogData: TableData;
}
const getStatusStyle = (status: string) => statusStyles(status);

const LastTenRequests: React.FC<LastTenRequestsProps> = ({ catalogData }) => {
  return (
    <>
      {catalogData?.allRequests?.length > 0 ? (
        catalogData?.allRequests?.map((request: any, index: number) => {
          const { color, label } = getStatusStyle(request?.status);
          return (
            <StyledDot style={{ backgroundColor: color }} key={index}>
              <StyledStatusTooltip className="tootip-text">{label}</StyledStatusTooltip>
            </StyledDot>
          );
        })
      ) : (
        <p style={{ paddingLeft: '50px' }}>--</p>
      )}
    </>
  );
};

export default LastTenRequests;
