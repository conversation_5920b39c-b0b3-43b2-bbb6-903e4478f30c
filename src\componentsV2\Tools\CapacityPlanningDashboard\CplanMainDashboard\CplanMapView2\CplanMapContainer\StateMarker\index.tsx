import { getUsageColor } from '../../../../utils/colors';
import { Marker } from 'react-simple-maps';

interface StateMarkerProps {
  statename: string;
  statecode: string;
  latitude: number;
  longitude: number;
  maxUtilized: number;
  onMarkerClick?: (statename: string, [longitude, latitude]: [number, number]) => void;
}

const StateMarker: React.FunctionComponent<StateMarkerProps> = ({ latitude, longitude, maxUtilized, statename, onMarkerClick }) => {
  const maxUtilizedColor = getUsageColor(maxUtilized);

  return (
    <Marker coordinates={[longitude, latitude]} onClick={() => onMarkerClick?.(statename, [longitude, latitude])}>
      <circle r={5} fill={maxUtilizedColor} stroke="#fff" strokeWidth={2} />
    </Marker>
  );
};

export default StateMarker;
