import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblCheckboxCard from '.';

describe('NblCheckboxCard component', () => {
  const props = {
    disabled: false,
    icon: 'CloudOutlined',
    id: 'test',
    name: 'Qualys',
    onCardClickHandler: () => { },
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblCheckboxCard {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
