import { Tabs, Tab } from '@mui/material';

interface VerticalTabsProps {
  currentTab: number;
  onTabChangeHandler: (param: number) => void;
  tabsList: Array<{ id: string; label: string }>;
}

const VerticalTabs: React.FunctionComponent<VerticalTabsProps> = ({ currentTab, onTabChangeHandler, tabsList }) => {
  return (
    <Tabs
      orientation="vertical"
      variant="scrollable"
      value={currentTab}
      aria-label="Vertical-tabs"
      sx={{
        '.MuiTabs-indicator': {
          left: 0,
        },
      }}
    >
      {tabsList.map((tab, index: number) => (
        <Tab
          onClick={() => onTabChangeHandler(index)}
          key={`${tab.id}-tab`}
          {...tab}
          id={`${tab.id}-tab`}
          sx={{ borderLeft: 1, borderColor: 'divider', paddingLeft: 0 }}
        />
      ))}
    </Tabs>
  );
};

export default VerticalTabs;
