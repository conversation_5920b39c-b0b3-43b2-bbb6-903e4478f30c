import React from 'react';
import { Grid, Typography, Stack, styled, useTheme, Box } from '@mui/material';

import useExposureParams from 'hooks/useExposureParams';
import CatalogCard from 'components/Catalog/CatalogCard';
import HexaIconWrapper from 'components/HexaIconWrapper';
import icons from 'assets/images/icons';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import { CatalogTilesData } from 'types';
import useNblNavigate from 'hooks/useNblNavigate';

interface catalogTilesProps {
  name: string;
  items: CatalogTilesData[];
}

interface StyledCatalogCardProps {
  theme: NebulaTheme;
  isDisabled: boolean;
}

const StyledCatalogCard = styled(CatalogCard, { shouldForwardProp: (prop) => prop !== 'isDisabled' })(
  ({ theme, isDisabled }: StyledCatalogCardProps) => {
    const {
      palette: { contentCard },
    } = theme;
    return {
      ...(isDisabled && {
        '& .hexagon-svg-icon': {
          color: contentCard.disabledColor,
        },
        '& .MuiTypography-root': {
          color: contentCard.disabledColor,
        },
      }),
    };
  }
);

const CatalogTiles: React.FunctionComponent<catalogTilesProps> = ({ name, items }: catalogTilesProps) => {
  const navigate = useNblNavigate();
  const { exposureParamsEnabled } = useExposureParams();
  const theme: NebulaTheme = useTheme();

  const { typography, palette } = theme;

  const renderIcon = (icon: any) => {
    /* @ts-ignore */
    const Icon = icons[icon];
    return (
      <Grid container mb={1}>
        <HexaIconWrapper>
          <Icon
            style={{
              fontSize: '1.5rem',
            }}
          />
        </HexaIconWrapper>
      </Grid>
    );
  };

  const renderCatalogTile = (data: CatalogTilesData, index: number) => {
    const isDisabled = data.disabled && !exposureParamsEnabled(data.name);

    return (
      <Grid key={`${name}-content-${index}`} title={isDisabled ? 'Coming soon...' : ''}>
        {/* @ts-ignore */}
        <StyledCatalogCard
          id={`${data.id}-card`}
          className={isDisabled ? 'isDisabled' : ''}
          isDisabled={Boolean(isDisabled)}
          onClick={() => !isDisabled && data.path && navigate(data.path)}
          sx={{
            '&:hover': {
              border: `1px solid black`,
            },
            border: `1px solid #D8DADC`,
            borderRadius: '10px',
            [theme.breakpoints.down('xl')]: {
              minWidth: 252,
              minHeight: 191,
              width: 268,
              height: 191,
            },
          }}
        >
          <Stack spacing={5}>
            <Grid textAlign={'left'}>
              <Box display="flex" alignItems="center" marginTop={0.5}>
                {data.icon && React.cloneElement(renderIcon(data.icon), { sx: { width: 'auto' } })}
                <Typography
                  variant="h3"
                  title={data.name}
                  sx={{
                    marginBottom: 3,
                    marginLeft: 2,
                    marginTop: 1,
                    ...typography.medium,
                    color: palette.contentCard.textPrimaryColor,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    [theme.breakpoints.down('xl')]: {
                      marginBottom: 1.06,
                    },
                  }}
                >
                  {data.name}
                </Typography>
              </Box>
              <Typography
                variant="body1"
                sx={{
                  ...typography.regular,
                  color: palette.contentCard.textSecondaryColor,
                  marginTop: data.description.length > 24 ? 1.5 : 1.5,
                }}
              >
                {data.description}
              </Typography>
            </Grid>
          </Stack>
        </StyledCatalogCard>
      </Grid>
    );
  };

  return (
    <Grid
      className="CatalogTiles-Grid"
      container
      columnGap={5.3}
      rowGap={7.5}
      mt={3}
      sx={{ [theme.breakpoints.down('xl')]: { columnGap: '3.3', rowGap: '3.5' } }}
    >
      {items?.map((data: CatalogTilesData, index: number) => renderCatalogTile(data, index))}
    </Grid>
  );
};

export default CatalogTiles;
