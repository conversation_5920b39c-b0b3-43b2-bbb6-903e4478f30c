import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

export const CreatePasswordPolicySchema = yup
  .object()
  .shape({
    splChars: yup
      .string()
      .matches(/^[^ ,]+$/, 'No spaces or commas are allowed')
      .test('no-repeated-characters', 'Special characters must not contain duplicates', function (value) {
        if (!value) return true;
        const charSet = new Set(value);
        return charSet.size === value.length;
      })
      .required('Special characters are required'),
    policyname: yup
      .string()
      .required('Policy Name is required')
      .matches(yupMatchesParams.passwordPolicyName.pattern, yupMatchesParams.passwordPolicyName.errorMessage),
    description: yup.string().required('Description is required'),
    totalchars: yup
      .number()
      .typeError('Character Length must be a number')
      .min(12, 'Total length must be at least 12')
      .max(30, 'Total length cannot exceed 30')
      .required('Total character length is required'),
    smallAlphabets: yup
      .number()
      .typeError('Small Letters must be a number')
      .min(0, 'Length must be at least 0')
      .max(30, 'Total length cannot exceed 30')
      .required('Number of Small Letters is required'),
    bigAlphabets: yup
      .number()
      .typeError('Capital Letters must be a number')
      .min(0, 'Length must be at least 0')
      .max(30, 'Length cannot exceed 30')
      .required('Number of Capital Letters is required'),
    noOfSplChars: yup
      .number()
      .typeError('Special Characters must be a number')
      .min(0, 'Length must be at least 0')
      .max(30, 'Length cannot exceed 30')
      .required('Number of special Characters is required'),
    numbers: yup
      .number()
      .typeError('Numbers must be a number')
      .min(0, 'Length must be at least 0')
      .max(30, 'Length cannot exceed 30')
      .required('Numbers is needed'),
  })
  .test('sum-equals-total', 'Sum of character types must equal total character length', function (values) {
    const { totalchars, smallAlphabets, bigAlphabets, noOfSplChars, numbers } = values;

    if (
      typeof totalchars === 'number' &&
      typeof smallAlphabets === 'number' &&
      typeof bigAlphabets === 'number' &&
      typeof noOfSplChars === 'number' &&
      typeof numbers === 'number'
    ) {
      const totalCount = smallAlphabets + bigAlphabets + noOfSplChars + numbers;

      return totalCount === totalchars;
    }

    return true; // Skip if values are not yet available (avoids false fail during typing)
  })

  // Each character type must not exceed total length
  .test('individual-max-check', 'Each character type must not exceed total character length', function (values) {
    const { totalchars, smallAlphabets, bigAlphabets, noOfSplChars, numbers } = values;

    if (
      typeof totalchars !== 'number' ||
      typeof smallAlphabets !== 'number' ||
      typeof bigAlphabets !== 'number' ||
      typeof noOfSplChars !== 'number' ||
      typeof numbers !== 'number'
    ) {
      return true; // skip this check if any value is not filled yet
    }

    return smallAlphabets <= totalchars && bigAlphabets <= totalchars && noOfSplChars <= totalchars && numbers <= totalchars;
  });
