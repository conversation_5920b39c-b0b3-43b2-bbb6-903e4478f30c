import React, { useState, useEffect, forwardRef, createContext, useContext } from 'react';
import { useDispatch } from 'react-redux';

import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { RequestType } from 'types/Enums';
import { useApiService } from 'api/ApiService/context';
// eslint-disable-next-line no-unused-vars
import { MultiENVProjectsResponse } from 'types/Interfaces/MultiENVProjectsResponse';

const {
  COMMON_FIREWALL_POLICY,
  DYNAMIC_ACCESS_POLICIES,
  FIREWALL_V2,
  CREATE_LB_F5,
  CREATE_LINUX_CORPNET,
  CREATE_WINDOWS_CORPNET,
  CREATE_VM_LINUX89,
  CREATE_VM_WINDOWS,
  CREATE_VM_UBUNTU,
  INTERNAL_CERTIFICATE,
} = RequestType;

const ExcludedCatalogItems = [
  FIREWALL_V2,
  COMMON_FIREWALL_POLICY,
  INTERNAL_CERTIFICATE,
  DYNAMIC_ACCESS_POLICIES,
  CREATE_LINUX_CORPNET,
  CREATE_WINDOWS_CORPNET,
];
const CatalogItemsWithVersion2 = [CREATE_VM_LINUX89, CREATE_VM_WINDOWS, CREATE_VM_UBUNTU, CREATE_LB_F5];

interface MultiENVProjectsContextProps {
  projectData: MultiENVProjectsResponse | null;
}

//Context
const MultiENVProjectsContext = createContext<MultiENVProjectsContextProps>({
  projectData: null,
});

//Custom Hook
export const useMultiENVProjectsData = (): MultiENVProjectsContextProps => {
  return useContext(MultiENVProjectsContext);
};

const withFetchMultiENVProjects = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  const withFetchMultiENVProjects = forwardRef((props: P & { isReadOnlyMode?: boolean; data?: any }, ref) => {
    //Hooks
    const { apiComputeService } = useApiService();
    const dispatch = useDispatch();

    //STATES
    const [projectData, setProjectData] = useState<MultiENVProjectsResponse | null>(null);

    //Side effects
    useEffect(() => {
      if (
        !(props?.isReadOnlyMode && ExcludedCatalogItems.includes(props?.data?.requestType)) &&
        !(
          CatalogItemsWithVersion2.includes(props?.data?.requestType) &&
          props?.data?.payload?.requestPayloadVersion >= 2 &&
          props?.isReadOnlyMode
        )
      ) {
        dispatch(showSpinner({ id: SPINNER_IDS.vmProjectDetails, status: true, message: 'Loading project details...' }));
        if (props && props?.isReadOnlyMode) {
          //Placeholder for get projects including deleted
        } else {
          apiComputeService
            .getMultiENVProjectDetails()
            .then((res) => {
              if (res.status) {
                setProjectData(res.data);
              }
            })
            .finally(() => {
              dispatch(showSpinner({ id: SPINNER_IDS.vmProjectDetails, status: false, message: '' }));
            });
        }
      }
    }, []);

    return (
      <MultiENVProjectsContext.Provider value={{ projectData }}>
        <WrappedComponent {...props} projectData={projectData} ref={ref} />
      </MultiENVProjectsContext.Provider>
    );
  });

  return withFetchMultiENVProjects;
};

export default withFetchMultiENVProjects;
