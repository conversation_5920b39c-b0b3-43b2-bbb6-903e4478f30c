import React, { useState, useEffect } from 'react';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblDataViewer from 'sharedComponents/NblDataViewer';
import { Cluster } from '../..';
import AddProject from '../AddProject';

interface ClusterOverviewProps {
  selectedCluster: Cluster;
  onClusterChange: (updatedCluster: Cluster) => void;
}

const ClusterOverview: React.FC<ClusterOverviewProps> = ({ selectedCluster, onClusterChange }) => {
  const { palette } = useTheme<NebulaTheme>();

  const [disabled, setDisabled] = useState(selectedCluster.disabled);
  const [restricted, setRestricted] = useState(selectedCluster.restricted);
  const [projectRows, setProjectRows] = useState<{ id: string; project: string }[]>([]);

  useEffect(() => {
    setDisabled(selectedCluster.disabled);
    setRestricted(selectedCluster.restricted);

    if (selectedCluster.restricted) {
      const initialProjects = selectedCluster.projects?.length
        ? selectedCluster.projects.map((p, i) => ({ id: `${i}`, project: p }))
        : [{ id: '0', project: '' }];
      setProjectRows(initialProjects);
    } else {
      setProjectRows([]);
    }
  }, [selectedCluster.clusterMor, selectedCluster.restricted]);

  const Cluster_Details = [
    { name: 'Cluster Name', value: selectedCluster.name },
    { name: 'Cluster ID', value: selectedCluster.clusterMor },
  ];

  const handleDisabledChange = () => {
    const updated = { ...selectedCluster, disabled: !disabled };
    setDisabled(!disabled);
    onClusterChange(updated);
  };

  const handleRestrictedChange = () => {
    const newRestricted = !restricted;
    setRestricted(newRestricted);

    const updatedCluster: Cluster = newRestricted
      ? {
          ...selectedCluster,
          restricted: true,
          projects: selectedCluster.projects ?? [],
        }
      : (() => {
          // eslint-disable-next-line no-unused-vars
          const { projects, ...rest } = selectedCluster;
          return { ...rest, restricted: false };
        })();

    onClusterChange(updatedCluster);
  };

  const handleProjectsChange = (projects: string[]) => {
    const existing = selectedCluster.projects ?? [];
    if (JSON.stringify(projects) !== JSON.stringify(existing)) {
      onClusterChange({ ...selectedCluster, projects });
    }
  };

  return (
    <NblFlexContainer padding="0px 14px 0px 0px">
      <NblFlexContainer direction="column">
        <NblTypography variant="h5" color="shade1" weight="bold">
          VMware: {selectedCluster.name}
        </NblTypography>

        <NblGridContainer columns={12} height="auto">
          <NblGridItem colspan={12}>
            <NblDataViewer data={Cluster_Details} columns={2} />
          </NblGridItem>

          <NblGridItem colspan={6}>
            <NblCheckBox
              checked={selectedCluster.haEnabled}
              label="HA Enabled"
              name="haEnabled"
              onChange={() => {}}
              onBlur={() => {}}
              disabled
            />
          </NblGridItem>

          <NblGridItem colspan={6}>
            <NblCheckBox
              checked={selectedCluster.drsEnabled !== null}
              label="DRS Enabled"
              name="drsEnabled"
              onChange={() => {}}
              onBlur={() => {}}
              disabled
            />
          </NblGridItem>

          <NblGridItem colspan={6}>
            <NblCheckBox checked={disabled} label="Disabled" name="disabled" onChange={handleDisabledChange} onBlur={() => {}} />
          </NblGridItem>

          <NblGridItem colspan={6}>
            <NblCheckBox
              checked={restricted}
              label="Restricted"
              name="restrictionsApplied"
              onChange={handleRestrictedChange}
              onBlur={() => {}}
            />
          </NblGridItem>
        </NblGridContainer>
      </NblFlexContainer>

      {restricted ? (
        <NblGridItem colspan={6} height="auto">
          <AddProject rows={projectRows} setRows={setProjectRows} onProjectsChange={handleProjectsChange} />
        </NblGridItem>
      ) : (
        <NblFlexContainer backgroundColor={palette.secondary.shade1} height="auto" center>
          <NblTypography variant="h6" color="shade6" textAlign="center" weight="semiBold">
            Enable restricted to add projects.
          </NblTypography>
        </NblFlexContainer>
      )}
    </NblFlexContainer>
  );
};

export default ClusterOverview;
