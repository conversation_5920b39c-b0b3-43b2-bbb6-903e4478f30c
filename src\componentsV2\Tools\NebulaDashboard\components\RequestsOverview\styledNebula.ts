import { styled } from '@mui/material/styles';
import { Grid, Typography } from '@mui/material';

export const StyledRequestSummaryCount = styled(Typography)(({ theme }) => {
  return {
    fontSize: '40px',
    fontWeight: 'bold',
    fontFamily: 'Gotham',
    letterSpacing: '1.6px',
    color: '#003057',
    [theme.breakpoints.down('xl')]: {
      fontSize: '34px',
    },
  };
});

export const StyledRequestSummaryPercentage = styled(Grid)(({ theme }) => {
  return {
    // marginTop: 1,
    marginLeft: 3,
    [theme.breakpoints.down('xl')]: {
      marginTop: 1,
      marginLeft: 1,
    },
  };
});

export const StyledRequestSummaryStatus = styled(Typography)(({ theme }) => {
  return {
    fontSize: '22px',
    fontWeight: 'medium',
    fontFamily: 'Gotham',
    letterSpacing: '0.88px',
    color: '#003057',
    [theme.breakpoints.down('xl')]: {
      fontSize: '15px',
    },
  };
});
export const StyledPreviousRequest = styled(Typography)(({ theme }) => {
  return {
    fontSize: '18px',
    fontWeight: 'normal',
    fontFamily: 'Gotham',
    letterSpacing: '0.72px',
    color: '#003057',
    [theme.breakpoints.down('xl')]: {
      fontSize: '11px',
    },
  };
});
export const StyledRequestHeader = styled(Typography)(({ theme }) => {
  return {
    fontSize: '20px',
    fontWeight: 'bold',
    fontFamily: 'Gotham',
    color: '#102F54',
    [theme.breakpoints.down('xl')]: {
      fontSize: '15px',
    },
  };
});
