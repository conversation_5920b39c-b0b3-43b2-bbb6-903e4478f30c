import { Dayjs } from 'dayjs';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { Stack, FormHelperText, InputLabel } from '@mui/material';

interface DatePickerProps {
  disabled?: boolean;
  error?: boolean | string;
  label: string;
  name: string;
  placeholder?: string;
  value: Dayjs | null;
  minDate?: Dayjs;
  handleChange: (event: any) => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

const DatePickerField: React.FunctionComponent<DatePickerProps> = ({
  label,
  name,
  value,
  disabled,
  error,
  minDate,
  handleChange,
  onMouseEnter,
  onMouseLeave
}: DatePickerProps) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Stack spacing={1} >
        <InputLabel htmlFor={name}>{label}</InputLabel>
        <DatePicker
          value={value}
          name={name}
          disabled={disabled}
          minDate={minDate}
          onChange={(newValue) => handleChange(newValue)}
          slotProps={{ textField: { variant: 'outlined', onMouseEnter: onMouseEnter, onMouseLeave: onMouseLeave } }}
        />
        <FormHelperText error sx={{ whiteSpace: 'break-spaces' }}>
          {error}
        </FormHelperText>
      </Stack>
    </LocalizationProvider>
  );
};

export default DatePickerField;
