import { useCallback, useEffect, useState } from 'react';
import { useApiService } from 'api/ApiService/context';
import { MultiEnvMyProjects } from 'types/Interfaces/MultiEnvMyProjects';
import { useParams } from 'react-router';
import { AdminEditRouteParams } from '../../types/Enums';
import ManageProjects from 'componentsV2/Administration/Projects/ManageProjects';
import NonAuthorizedPage from '../../components/Authentication/NonAuthorizedPage';
import { showSpinner } from '../../store/reducers/spinner';
import { useDispatch } from 'react-redux';

const ManageMyProjects: React.FC = () => {
  const { apiComputeService } = useApiService();
  const [projectDetails, setProjectDetails] = useState<MultiEnvMyProjects>([]);
  const { [AdminEditRouteParams.projects]: projectName } = useParams();
  const projectCanBeUpdated = projectDetails.find((project) => project.projectShortName === projectName)?.canUpdate;
  const dispatch = useDispatch();
  const fetchAllProjectDetails = useCallback(() => {
    dispatch(showSpinner({ status: true, message: 'Fetching Project Details' }));
    apiComputeService
      .getMultiENVMyProjects()
      .then((res) => {
        if (res.status) {
          setProjectDetails(res.data);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ status: false, message: '' }));
      });
  }, []);

  //Side effects
  useEffect(() => {
    fetchAllProjectDetails();
  }, [fetchAllProjectDetails]);

  if (projectDetails.length) {
    return projectCanBeUpdated ? <ManageProjects permissions={{}} onClose={() => { }} /> : <NonAuthorizedPage />
  }

  return null;
};

export default ManageMyProjects;
