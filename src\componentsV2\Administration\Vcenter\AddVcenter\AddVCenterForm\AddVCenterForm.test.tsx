import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import AddVCenterForm from '../AddVCenterForm';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import useGetDomainData from 'hooks/useGetDomainData';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { BrowserRouter as Router } from 'react-router-dom';
import MemoizedApiServiceProvider from 'api/ApiService/context';
import { Provider as ReduxProvider } from 'react-redux';
import { store } from 'store';

export const mockFormValues = {
  domain: '',
  cloudDatacenter: '',
  vCenterName: '',
  vCenterHost: '',
  vCenterPort: '',
  vCenterProtocol: '',
  vCenterUser: '',
  vCenterPassword: '',
};

export const mockFormProps = {
  setFieldValue: jest.fn(),
  handleChange: jest.fn(),
  handleBlur: jest.fn(),
  touched: {},
  errors: {},
};

export const mockDomainData = [
  {
    domainName: 'domain1.com',
    dataCenters: [
      { name: 'dc1', id: '1' },
      { name: 'dc2', id: '2' },
    ],
  },
];

jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  useNblForms: jest.fn(),
}));

jest.mock('hooks/useGetDomainData', () => ({
  __esModule: true,
  default: jest.fn(),
}));

describe('AddVCenterForm', () => {
  beforeEach(() => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockFormProps,
      nblFormValues: mockFormValues,
    });

    (useGetDomainData as jest.Mock).mockReturnValue({
      domainData: mockDomainData,
    });
  });

  const renderComponent = () =>
    render(
      <Router>
        <ReduxProvider store={store}>
          <NebulaThemeProvider>
            <MemoizedApiServiceProvider>
              <AddVCenterForm />
            </MemoizedApiServiceProvider>
          </NebulaThemeProvider>
        </ReduxProvider>
      </Router>
    );

  test('renders all required fields', () => {
    renderComponent();
    expect(screen.getByLabelText(/Domain/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Cloud Datacenter/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/VCenter Host/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/VCenter Port/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Vcenter Protocol/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/VCenter User/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/VCenter Password/i)).toBeInTheDocument();
  });

  test('disables datacenter field when no domain selected', () => {
    renderComponent();
    const datacenterField = screen.getByLabelText(/Cloud Datacenter/i);
    expect(datacenterField).toBeDisabled();
  });

  test('calls setFieldValue when domain is selected', async () => {
    renderComponent();

    const domainInput = screen.getByLabelText(/Domain/i);
    userEvent.click(domainInput);
    fireEvent.change(domainInput, { target: { value: 'domain1.com' } });
    userEvent.tab();

    expect(mockFormProps.setFieldValue).toHaveBeenCalledWith(expect.any(String), 'domain1.com');
  });

  test('shows datacenter options when a domain is selected', () => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockFormProps,
      nblFormValues: { ...mockFormValues, domain: 'domain1.com' },
    });

    renderComponent();
    const datacenterField = screen.getByLabelText(/Cloud Datacenter/i);
    expect(datacenterField).not.toBeDisabled();
  });
});
