import { styled } from '@mui/material/styles';
import { MenuItem, Select, Typography, ListItemIcon, Box } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import {
  getContainedVariantStylesForMuiSelect,
  getDisabledInputStylesForMuiSelect,
  getMuiSelectPlaceholderStyles,
  getOutlinedBaseStyles,
  getOutlinedInputStyles,
} from '../common';

export const StyledSelect = styled(Select)<{ theme?: NebulaTheme; type: 'contained' | 'outlined' }>(({ theme, type }) => {
  return {
    '& .MuiSelect-select.MuiInputBase-input.MuiOutlinedInput-input': {
      ...getOutlinedBaseStyles(theme),
      ...getMuiSelectPlaceholderStyles(),
    },
    ...getDisabledInputStylesForMuiSelect(),
    ...getOutlinedInputStyles(theme),
    ...(type === 'contained' && getContainedVariantStylesForMuiSelect(theme)),
    '& .MuiSelect-icon': {
      transition: 'none',
      transform: 'none',
    },
  };
});

export const StyledTypography = styled(Typography)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography } = theme;
  return {
    ...typography.subtitle1,
    opacity: 0.4,
  };
});

export const StyledMenuItem = styled(MenuItem)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography, palette } = theme;
  const { select } = palette;

  return {
    ...typography.subtitle1,
    '&.menuItem': {
      borderBottom: `1px solid ${select.optionsBorderColor}`,
      color: select.color,
      lineHeight: '2',
      textTransform: 'capitalize',
    },
  };
});

export const StyledListItemIcon = styled(ListItemIcon)(() => ({}));

export const StyledSelectWrapper = styled(Box)({
  display: 'block', 
  textTransform: 'capitalize', 
  textOverflow: 'ellipsis',
  overflow: 'hidden',
  whiteSpace: 'nowrap', 
  width: 'calc(100% - 4%)'
});