import React from 'react';
import { FormHelperText } from '@mui/material';

interface HoverHelperTextProps {
  text: string;
  bottom?: number;
}
const HoverHelperText: React.FunctionComponent<HoverHelperTextProps> = ({ text, bottom = -5 }) => {
  return <FormHelperText sx={{ position: 'absolute', bottom: bottom, zIndex: 2, color: 'forms.titleColor' }}>{text}</FormHelperText>;
};

export default HoverHelperText;
