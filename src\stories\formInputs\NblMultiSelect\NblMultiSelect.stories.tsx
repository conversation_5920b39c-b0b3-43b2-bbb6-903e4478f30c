// eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblMultiSelect from 'sharedComponents/NblFormInputs/NblMultiSelect';

type StoryProps = ComponentProps<typeof NblMultiSelect>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblMultiSelect',
  component: NblMultiSelect,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { control: 'text' },
    name: { control: 'text' },
    placeholder: { control: 'text' },
    options: { control: 'object' },
    disabled: { control: 'boolean' },
    helperText: { control: 'text' }, 
    error: { control: 'boolean' },
    maxLength: { control: 'number' },
    mandatory: { control: 'boolean' },
    contained: { control: 'boolean' },
  },
};

export default meta;

export const MultiSelect: Story = {
  args: {
    label: 'Label',
    name: 'name',
    placeholder: 'Select options',
    options: [
      { label: 'John', value: 'opt1' },
      { label: 'Steven', value: 'opt2' },
      { label: 'Alex', value: 'opt3' },
      { label: 'Jack', value: 'opt4' },
      { label: 'Tom', value: 'opt5' },
      { label: 'Jane', value: 'opt6' },
    ],
    helperText: 'helper text',
    error: false,
    disabled: false,
    maxLength: 10,
    mandatory: false,
    contained: false,
  },
  render: (args) => {
    const WrapperComponent = () => {
       const [selectedValues, setSelectedValues] = useState<(string | number)[]>([]);
      
        const handleChange = (event: any) => {
          setSelectedValues(event.target.value);
        };

      return (
        <NebulaTheme>
          <NblFlexContainer width="300px">
            <NblMultiSelect {...args} handleChange={handleChange} value={selectedValues}/>
          </NblFlexContainer>
        </NebulaTheme>
      );
    };

    return <WrapperComponent />;
  },
};