import { Route, Routes } from 'react-router-dom';
import WorkInProgress from 'components/WorkInProgress';
import AdministrationDetails from './Details';
import Permissions from './Permission';
import AddPermission from './Permission/AddPermissions';
import ViewPermission from './Permission/ViewPermissions';
import ServiceCatalogGroup from './ServiceCatalogGroups';
import Groups from './Groups';
import AddGroupForm from './Groups/AddGroup';
import EditGroup from './Groups/EditGroup';
import Roles from './Roles';
import ViewRole from 'componentsV2/Administration/Roles/ViewRoles/ViewRoleTable';
import AddRole from 'componentsV2/Administration/Roles/AddRoles';
import Project from './Projects';
import VMSizing from './VMSizing';
import Teams from './Teams';
import AddTeam from './Teams/AddTeams';
import ViewTeam from './Teams/ViewTeams';
import ViewGroup from './Groups/ViewGroups';
import withAdminAccess from 'hoc/withAdminAccess';
import EditRole from 'componentsV2/Administration/Roles/EditRole';
import { AdminEditRouteParams } from 'types/Enums';
import { ManageProjectsWithAdminPermission } from 'componentsV2/Administration/Projects/ManageProjects';
import MultiViewServiceCatalog from './ServiceCatalogGroups/MultiViewServiceCatalog';
import NebulaTheme from 'NebulaTheme';
import { ProjectPermissionWithAdminPermission } from 'componentsV2/Administration/Projects/ProjectPermission';
import ViewMultiENVProject from 'componentsV2/Administration/Projects/ViewProjects';
import ManageApproval from 'componentsV2/Administration/Catalogs/ManageApproval';
import ViewCatalogList from 'componentsV2/Administration/Catalogs/ViewCatalogList';
import AddCatalogForm from 'componentsV2/Administration/Catalogs/AddCatalogs/AddCatalogForm';
import EditMultiEnvServiceCatalog from 'componentsV2/Administration/EditMultiEnvServiceCatalog';
import AddMultiEnvServiceCatalog from 'componentsV2/Administration/AddMultiEnvServiceCatalog';

import AddVMSize from './VMSizing/AddVMSize';
import ViewVMSize from './VMSizing/ViewVMSize';
import EditVMSize from './VMSizing/EditVMSize';

const Administration = () => {
  return (
    <>
      <Routes>
        <Route path={'/'} element={<AdministrationDetails />} />

        {/* ServiceCatalog Routes */}
        <Route path={'/service-catalog'}>
          <Route path="" element={<ServiceCatalogGroup />} />
          <Route
            path={'add-service-catalog-group'}
            element={
              <NebulaTheme>
                <AddMultiEnvServiceCatalog title={'Add Service Catalog Group'} onClose={() => {}} permissions={{}} />
              </NebulaTheme>
            }
          />
          <Route path="manage-service-catalog-groups">
            <Route
              path=""
              element={
                <NebulaTheme>
                  <MultiViewServiceCatalog permissions={{}} />
                </NebulaTheme>
              }
            />
            <Route
              path={`:${AdminEditRouteParams.serviceCatalogGroup}`}
              element={
                <NebulaTheme>
                  <EditMultiEnvServiceCatalog />
                </NebulaTheme>
              }
            />
          </Route>
          <Route
            path={'add-service-catalog-list'}
            element={
              <NebulaTheme>
                <AddCatalogForm onClose={() => {}} permissions={{}} />
              </NebulaTheme>
            }
          />
          <Route path="manage-service-catalog-lists">
            <Route
              path=""
              element={
                <NebulaTheme>
                  <ViewCatalogList permissions={{}} />
                </NebulaTheme>
              }
            />
            <Route path={`:${AdminEditRouteParams.catalogs}`}>
              <Route
                path=""
                element={
                  <NebulaTheme>
                    <AddCatalogForm onClose={() => {}} permissions={{}} />
                  </NebulaTheme>
                }
              />
              <Route
                path="catalog-approval"
                element={
                  <NebulaTheme>
                    <ManageApproval permissions={{}} />
                  </NebulaTheme>
                }
              />
            </Route>
          </Route>
        </Route>

        {/* Permission Routes  */}
        <Route path="/permissions">
          <Route path={''} element={<Permissions />} />
          <Route
            path={'add-permission'}
            element={
              <NebulaTheme>
                <AddPermission />
              </NebulaTheme>
            }
          />
          <Route
            path={'view-permissions'}
            element={
              <NebulaTheme>
                <ViewPermission />
              </NebulaTheme>
            }
          />
        </Route>

        {/* Service Catalog List Route */}
        <Route path={'/service-catalog-list'} element={<ServiceCatalogGroup />} />

        {/* Groups Routes */}
        <Route path="/groups">
          <Route path={''} element={<Groups />} />
          <Route path={'register-ad-group'} element={<AddGroupForm />} />
          <Route path={`:${AdminEditRouteParams.groups}`} element={<EditGroup />} />
          <Route path={'manage-registered-ad-groups'} element={<ViewGroup />} />
        </Route>

        {/* Roles Routes */}
        <Route path="/roles">
          <Route path={''} element={<Roles />} />
          <Route
            path={'add-role'}
            element={
              <NebulaTheme>
                <AddRole title={'Add Role'} onClose={() => {}} permissions={{}} />
              </NebulaTheme>
            }
          />
          <Route
            path={'view-roles'}
            element={
              <NebulaTheme>
                <ViewRole permissions={{}} />
              </NebulaTheme>
            }
          />
          <Route
            path={`:${AdminEditRouteParams.roles}`}
            element={
              <NebulaTheme>
                <EditRole />
              </NebulaTheme>
            }
          />
        </Route>

        {/*Project Routes*/}
        <Route path="/project">
          <Route path={''} element={<Project />} />
          <Route
            path={'add-project'}
            element={
              <NebulaTheme>
                <ManageProjectsWithAdminPermission permissions={{}} onClose={() => {}} />
              </NebulaTheme>
            }
          />
          <Route path={'view-projects'}>
            <Route
              path=""
              element={
                <NebulaTheme>
                  <ViewMultiENVProject permissions={{}} />
                </NebulaTheme>
              }
            />
            <Route path={`:${AdminEditRouteParams.projects}`}>
              <Route
                path=""
                element={
                  <NebulaTheme>
                    <ManageProjectsWithAdminPermission permissions={{}} onClose={() => {}} />
                  </NebulaTheme>
                }
              />
              <Route
                path={`project-permissions`}
                element={
                  <NebulaTheme>
                    <ProjectPermissionWithAdminPermission permissions={{}} onClose={() => {}} />
                  </NebulaTheme>
                }
              />
            </Route>
          </Route>
        </Route>

        {/* Team Routes */}
        <Route path="/teams">
          <Route path={''} element={<Teams />} />
          <Route path={'add-team'} element={<AddTeam />} />
          <Route path={'view-teams'} element={<ViewTeam />} />
        </Route>

        {/* VM-Sizing Route */}
        <Route path="/VM-sizing">
          <Route path={''} element={<VMSizing />} />
          <Route
            path={'add-VM-sizing'}
            element={
              <NebulaTheme>
                <AddVMSize />
              </NebulaTheme>
            }
          />
          <Route
            path={'view-VM-sizing'}
            element={
              <NebulaTheme>
                <ViewVMSize />
              </NebulaTheme>
            }
          />
          <Route
            path={`:${AdminEditRouteParams.vmSize}`}
            element={
              <NebulaTheme>
                <EditVMSize />
              </NebulaTheme>
            }
          />
        </Route>

        {/* Catch all route */}
        <Route path={'/*'} element={<WorkInProgress />} />
      </Routes>
    </>
  );
};

export default withAdminAccess(Administration);
