import { useEffect, useMemo, useState } from 'react';
import { GENERATE_REQUEST_BY_MONTH_QUERY } from '../../queries';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line
import { State } from 'store/reducers/type';
// eslint-disable-next-line
import { RequestSummaryByMonthResponse, RequestSummaryMonthlyData } from 'api/ApiService/type';
import { addSummaryToCatalog } from '../../utils/utility';
// eslint-disable-next-line
import { CatalogWithSummary } from '../../utils/types';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { updateRequestOverviewType } from 'store/reducers/requestOverview';
import useNblNavigate from 'hooks/useNblNavigate';
import { useApiService } from 'api/ApiService/context';
import { getPieSeriesData } from './utility';
import DonutChart from './DonutChart';
import NblBreadCrumbs, { NblBreadcrumbItem } from 'sharedComponents/NblNavigation/NblBreadCrumbs';

interface RequestByMonthChartProps {}

const RequestTrendByMonth: React.FunctionComponent<RequestByMonthChartProps> = () => {
  const {
    allCatalog1,
    allCatalog3,
    allCatalog4,
    allCatalog2,
    year,
    selectedCatalog3,
    selectedCatalog1,
    selectedCatalog2,
    selectedCatalog4,
    isFilterApplied,
    quarters,
    levelSelected,
  } = useSelector((state: State) => state.RequestFilterConfig);
  const navigate = useNblNavigate();
  const dispatch = useDispatch();
  const { apiUsageMetricsService } = useApiService();
  const [drillLevel, setDrillLevel] = useState(levelSelected);
  const [breadcrumbItems, setBreadcrumbItems] = useState<NblBreadcrumbItem[]>([{ label: 'Catalog-1', route: '' }]);
  const [requestSummary, setRequestSummary] = useState([] as RequestSummaryMonthlyData[]);
  const [barSeries1, setBarSeries1] = useState([] as { id: string; label: string; value: number }[]);

  // API call to get request summary count
  const fetchRequestSummaryByMonth = async () => {
    const allCatalog1Names = selectedCatalogs[1].map(({ value }) => value);
    const allCatalog3Names = selectedCatalogs[3].map(({ value }) => value);
    const allCatalog4Names = selectedCatalogs[4].map(({ value }) => value);
    const payload = {
      query: GENERATE_REQUEST_BY_MONTH_QUERY(year, quarters, allCatalog1Names, allCatalog3Names, allCatalog4Names),
    };
    const response = await apiUsageMetricsService.customGraphQL<RequestSummaryByMonthResponse>(payload);
    const requestSummaryData: RequestSummaryMonthlyData[] = response.data.data.getRequestSummaryByMonth;
    setRequestSummary(requestSummaryData);
  };

  // UseEffect for API call
  useEffect(() => {
    if (allCatalog1.length) {
      fetchRequestSummaryByMonth();
    }
  }, [year, quarters, selectedCatalog1, selectedCatalog2, selectedCatalog3, selectedCatalog4, allCatalog1]);

  // Applying Dialog Filters to data.
  const selectedCatalogs: CatalogWithSummary[][] = useMemo(() => {
    const filteredCatalog1 = allCatalog1.filter((item) => selectedCatalog1.includes(item.id));
    const filteredCatalog2 = allCatalog2.filter((item) => selectedCatalog2.includes(item.id));
    const filteredCatalog3 = allCatalog3.filter((item) => selectedCatalog3.includes(item.id));
    const filteredCatalog4 = allCatalog4.filter((item) => selectedCatalog4.includes(item.id));
    return [[], filteredCatalog1, filteredCatalog2, filteredCatalog3, filteredCatalog4];
  }, [selectedCatalog1, selectedCatalog2, selectedCatalog3, selectedCatalog4, requestSummary]);

  // Sum up the request count from level4 to level1 filtered data
  const catalogWithSummary: CatalogWithSummary[][] = useMemo(() => {
    const Catalog4WithSummary = selectedCatalogs[4].map((item) => {
      const summary = requestSummary.find((res) => res.requestType === item.requestType)?.summary;
      const newItem = { ...item, summary };
      return newItem;
    });
    const Catalog3WithSummary = addSummaryToCatalog(selectedCatalogs[3], Catalog4WithSummary);
    const Catalog2WithSummary = addSummaryToCatalog(selectedCatalogs[2], Catalog3WithSummary);
    const Catalog1WithSummary = addSummaryToCatalog(selectedCatalogs[1], Catalog2WithSummary);
    return [[], Catalog1WithSummary, Catalog2WithSummary, Catalog3WithSummary, Catalog4WithSummary];
  }, [selectedCatalogs]);

  useEffect(() => {
    setBarSeries1(getPieSeriesData(catalogWithSummary[levelSelected]));
    let newItems = [] as NblBreadcrumbItem[];
    for (let i = 1; i <= levelSelected; i++) {
      newItems.push({ label: 'Catalog-' + i, route: '' });
    }
    setBreadcrumbItems(newItems);
    setDrillLevel(levelSelected);
  }, [catalogWithSummary, levelSelected]);

  const handleStackedBarClick = (index: number) => {
    if (drillLevel < 4) {
      setDrillLevel((prev) => prev + 1);
      setBreadcrumbItems((prevItems) => [...prevItems, { label: 'Catalog-' + (drillLevel + 1), route: barSeries1[index].id }]);
      setBarSeries1(getPieSeriesData(catalogWithSummary[drillLevel + 1].filter((item) => item.parentId === barSeries1[index].id)));
    } else {
      // navigate to request overview
      dispatch(
        updateRequestOverviewType({
          catalog1: [],
          catalog3: [],
          catalog4: [barSeries1[index].label],
          year,
          quarters,
        })
      );
      navigate('requestoverview');
    }
  };

  const handleBreadcrumbItemClick = (item: NblBreadcrumbItem, index: number) => {
    setDrillLevel(index + 1);
    if (index === 0 || isFilterApplied) {
      setBarSeries1(getPieSeriesData(catalogWithSummary[index + 1]));
    }
    if (item.route) {
      setBarSeries1(getPieSeriesData(catalogWithSummary[index + 1].filter((i) => i.parentId === item.route)));
    }
    setBreadcrumbItems((prevItems) => prevItems.slice(0, index + 1));
  };

  return (
    <NblFlexContainer direction="column" spacing={2}>
      <NblBreadCrumbs breadCrumbs={breadcrumbItems} onClick={handleBreadcrumbItemClick} />
      <DonutChart data={barSeries1} onSliceClick={handleStackedBarClick} />
    </NblFlexContainer>
  );
};

export default RequestTrendByMonth;
