import * as yup from 'yup';
import { Address4 } from 'ip-address';
import { yupMatchesParams } from 'utils/common';

const isValidIpv4NetworkAddress = (value: any) => {
  if (!Address4.isValid(value)) {
    return 'Enter a valid IP';
  }
  const ipObj = new Address4(value);
  if (!ipObj.address.includes('/')) {
    return 'Mask must be added';
  }
  const startIp = ipObj.startAddress();
  if (startIp.addressMinusSuffix !== ipObj.addressMinusSuffix) {
    return 'Invalid network address';
  }

  return true;
};

export const validationSchema = (isReadOnly: boolean | undefined) =>
  yup.object().shape({
    organization: yup.string().required('Organization is required'),
    subAccount: yup.string().required('Sub Account is required'),
    baseSubnet: yup
      .number()
      .required('Number of base Subnet is required')
      .typeError('Must be a number')
      .positive('Must be a positive number')
      .integer('Must be an integer'),
    region: yup.string().required('Region is required'),
    description: yup
      .string()
      .trim()
      .required('Description is required')
      .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
    vpcBASE: yup
      .string()
      .test('valid-ipv4-address', 'Please enter valid IPv4 address', function (value) {
        if (!value) {
          return true;
        }
        const validationResult = isValidIpv4NetworkAddress(value);
        if (validationResult !== true) {
          return this.createError({ message: validationResult });
        }
        return true;
      })
      .when([], {
        is: () => isReadOnly,
        then: (schema) => schema.required('VPC CIDR BASE is required'),
        otherwise: (schema) => schema,
      }),
    vpcGUA: yup.string().test('valid-ipv4-address', 'Please enter valid IPv4 address', function (value) {
      if (!value) {
        return true;
      }
      const validationResult = isValidIpv4NetworkAddress(value);
      if (validationResult !== true) {
        return this.createError({ message: validationResult });
      }
      return true;
    }),
  });
