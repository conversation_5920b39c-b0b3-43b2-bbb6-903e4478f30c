import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';
import { matchIsValidTel } from 'mui-tel-input';

export const validationSchema = yup.object().shape({
  firstname: yup.string().matches(yupMatchesParams.alphaChars.pattern, yupMatchesParams.alphaChars.errorMessage),
  lastname: yup.string().matches(yupMatchesParams.alphaChars.pattern, yupMatchesParams.alphaChars.errorMessage),
  address: yup.string().matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),
  city: yup.string().matches(yupMatchesParams.alphaChars.pattern, yupMatchesParams.alphaChars.errorMessage),
  state: yup.string(),
  country: yup.string(),
  company: yup
    .string()
    .trim()
    .required('Organization is required')
    .matches(yupMatchesParams.company.pattern, yupMatchesParams.company.errorMessage),
  dept: yup.string().matches(yupMatchesParams.alphaChars.pattern, yupMatchesParams.alphaChars.errorMessage),
  phone: yup
    .number()
    .test(
      'Is valid phone number',
      'Please enter valid phone number',
      (value) => !value || Boolean(value && matchIsValidTel(`+${value.toString()}`))
    ),
  email: yup.string().email('Please enter valid email'),
  title: yup.string().matches(yupMatchesParams.alphaChars.pattern, yupMatchesParams.alphaChars.errorMessage),
  fax: yup
    .string()
    .matches(yupMatchesParams.faxTollFreeChars.pattern, yupMatchesParams.faxTollFreeChars.errorMessage)
    .min(10, 'Fax must be at least 10 digits')
    .max(12, 'Fax must be at most 12 digits'),
  tollfree: yup
    .string()
    .matches(yupMatchesParams.faxTollFreeChars.pattern, yupMatchesParams.faxTollFreeChars.errorMessage)
    .min(10, 'Tollfree number must be at least 10 digits')
    .max(12, 'Tollfree number must be at most 12 digits'),
  billingId: yup.string().matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),
  crmId: yup.string().matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),
  latitude: yup.string().matches(yupMatchesParams.latitudeRange.pattern, yupMatchesParams.latitudeRange.errorMessage),
  longitude: yup.string().matches(yupMatchesParams.longitudeRange.pattern, yupMatchesParams.longitudeRange.errorMessage),
});
