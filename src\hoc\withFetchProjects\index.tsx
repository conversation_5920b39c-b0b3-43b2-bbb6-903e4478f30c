import React, { useState, useEffect, forwardRef } from 'react';
import { useDispatch } from 'react-redux';

import ComputeService from 'api/ApiService/ComputeService';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { ProjectDetails } from 'types';
import { RequestType } from 'types/Enums';

const {
  COMMON_FIREWALL_POLICY,
  DYNAMIC_ACCESS_POLICIES,
  FIREWALL_V2,
  CREATE_LB_F5,
  CREATE_LINUX_CORPNET,
  CREATE_WINDOWS_CORPNET,
  CREATE_VM_LINUX89,
  CREATE_VM_WINDOWS,
  CREATE_VM_UBUNTU,
  INTERNAL_CERTIFICATE,
} = RequestType;

const ExcludedCatalogItems = [
  FIREWALL_V2,
  COMMON_FIREWALL_POLICY,
  INTERNAL_CERTIFICATE,
  DYNAMIC_ACCESS_POLICIES,
  CREATE_LINUX_CORPNET,
  CREATE_WINDOWS_CORPNET,
];
const CatalogItemsWithVersion2 = [CREATE_VM_LINUX89, CREATE_VM_WINDOWS, CREATE_VM_UBUNTU, CREATE_LB_F5];

const withFetchProjects = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  const WithFetchProjects = forwardRef((props: P & { isReadOnlyMode?: boolean; data?: any }, ref) => {
    const apiComputeService = new ComputeService();
    const dispatch = useDispatch();

    const [projectData, setProjectData] = useState<ProjectDetails | null>(null);

    const fetchProjectDetails = () => {
      dispatch(showSpinner({ id: SPINNER_IDS.vmProjectDetails, status: true, message: 'Loading project details...' }));
      apiComputeService
        .getProjectDetails()
        .then((res) => {
          if (res.status) {
            setProjectData(res.data);
          }
        })
        .finally(() => {
          dispatch(showSpinner({ id: SPINNER_IDS.vmProjectDetails, status: false, message: '' }));
        });
    };

    const fetchAllProjects = () => {
      dispatch(showSpinner({ id: SPINNER_IDS.vmProjectDetails, status: true, message: 'Loading project details...' }));
      apiComputeService
        .getProjectsIncludingDeleted()
        .then((res) => {
          if (res.status) {
            setProjectData(res.data);
          }
        })
        .finally(() => {
          dispatch(showSpinner({ id: SPINNER_IDS.vmProjectDetails, status: false, message: '' }));
        });
    };

    useEffect(() => {
      if (
        !(props?.isReadOnlyMode && ExcludedCatalogItems.includes(props?.data?.requestType)) &&
        !(
          CatalogItemsWithVersion2.includes(props?.data?.requestType) &&
          props?.data?.payload?.requestPayloadVersion >= 2 &&
          props?.isReadOnlyMode
        )
      ) {
        if (props && props?.isReadOnlyMode) {
          fetchAllProjects();
        } else {
          fetchProjectDetails();
        }
      }

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return <WrappedComponent {...props} projectData={projectData} ref={ref} />;
  });

  return WithFetchProjects;
};

export default withFetchProjects;
