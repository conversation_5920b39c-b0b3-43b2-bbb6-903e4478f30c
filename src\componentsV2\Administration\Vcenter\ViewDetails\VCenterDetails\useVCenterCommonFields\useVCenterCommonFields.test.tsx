import React from 'react';
import { render, renderHook } from '@testing-library/react';
// eslint-disable-next-line no-unused-vars
import MaskedValueCell from 'componentsV2/MaskedValueCell';
import { LookupPayload } from 'types/Interfaces/LookUpVCenterResponse';
import useVCenterFields from '.';

jest.mock('componentsV2/MaskedValueCell', () => ({
  __esModule: true,
  default: ({ value }: { value: string }) => <span>{`***${value}***`}</span>,
}));

describe('useVCenterFields', () => {
  const domainData: any[] = [];

  it('returns correct fields with full data', () => {
    const dataFromLookup: LookupPayload = {
      domain: 'corp.example.com',
      cloudDatacenter: 'DC-1',
      vCenterName: 'VC-Prod',
      vCenterHost: 'vc.example.com',
      vCenterPort: '443',
      vCenterProtocol: 'https',
      vCenterUser: 'admin@corp',
      vCenterPassword: 'secret123',
    };

    const { result } = renderHook(() => useVCenterFields(dataFromLookup, domainData));

    expect(result.current).toHaveLength(8);
    expect(result.current[0]).toEqual({ title: 'Domain', value: 'corp.example.com' });
    expect(result.current[1]).toEqual({ title: 'Cloud Datacenter', value: 'DC-1' });
    expect(result.current[6]).toEqual({ title: 'VCenter User', value: 'admin@corp' });

    const PasswordCell = result.current[7].value;
    const { getByText } = render(<>{PasswordCell}</>);
    expect(getByText('***secret123***')).toBeInTheDocument();
  });

  it('uses fallback "-" for missing fields', () => {
    const dataFromLookup: Partial<LookupPayload> = {
      vCenterName: '',
      vCenterPassword: '',
    };

    const { result } = renderHook(() => useVCenterFields(dataFromLookup as LookupPayload, domainData));

    expect(result.current[2]).toEqual({ title: 'VCenter Name', value: '-' });
    const PasswordCell = result.current[7].value;
    const { getByText } = render(<>{PasswordCell}</>);
    expect(getByText('***-***')).toBeInTheDocument();
  });

  it('returns empty array if dataFromLookup is null', () => {
    const { result } = renderHook(() => useVCenterFields(null as any, domainData));
    expect(result.current).toEqual([]);
  });
});
