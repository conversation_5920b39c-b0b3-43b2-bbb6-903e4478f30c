import * as yup from 'yup';
import dayjs from 'dayjs';
import { yupMatchesParams } from 'utils/common';

const MIN_DATE = dayjs().add(20, 'day');

const FirewallBuildRequestSchema = yup.object().shape({
  projectName: yup
    .string()
    .required('Summary Name is required')
    .max(60, 'Summary Name must be at most 60 characters')
    .matches(yupMatchesParams.firewallProjectName.pattern, yupMatchesParams.firewallProjectName.errorMessage),
  projectCreator: yup.string(),
  date: yup.string(),
  supportOrganization: yup.string().required('Support Organization is required'),
  region: yup.array().of(yup.string()).min(1, 'Please select atleast one region').required('Region is required'),
  netopsaskTicket: yup.string().matches(yupMatchesParams.numericChars.pattern, yupMatchesParams.numericChars.errorMessage),
  nebulaProject: yup.string().required('Project is required'),
  jiraIssueLink: yup.array().of(yup.string().matches(yupMatchesParams.jiraIssueLink.pattern, yupMatchesParams.jiraIssueLink.errorMessage)),
  businessRequestDate: yup
    .date()
    .nullable(true)
    .typeError('Business Request Date must be a valid date in MM/DD/YYYY format')
    .test('min-date', 'Business Request Date should be minimum 20 days from current date', function (value) {
      if (value) {
        return !dayjs(value).isBefore(MIN_DATE, 'day');
      }
      return true;
    }),
});

export default FirewallBuildRequestSchema;
