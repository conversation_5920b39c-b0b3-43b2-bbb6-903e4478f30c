import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import TagConfigDialog from '.';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

const projectTags = {
  ProjectTagValues: {
    settingName: '',
    id: '',
    tagKey: '',
    tagValue: '',
    destination: [{ id: '', name: '' }],
    description: '',
    tagKeyId: '',
    tagValueName: '',
  },
};
const mockStore = configureMockStore();
const store = mockStore({
  projectTags: {
    ProjectTagValues: {
      settingName: '',
      id: '',
      tagKey: '',
      tagValue: '',
      destination: [{ id: '', name: '' }],
      description: '',
      tagKeyId: '',
      tagValueName: '',
    },
  },
  common: {
    disableDialogContentScroll: false,
  },
});
jest.mock('react-toastify');

describe('Render Project Permission form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  const handleData = jest.fn();
  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <TagConfigDialog onClose={handleClose} onSuccess={handleSuccess} setTagData={handleData} openTagDialog={true} />
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(screen.getByText('Tag Key *')).toBeInTheDocument();
    expect(screen.getByText('Tag Value *')).toBeInTheDocument();

    expect(screen.getByText('Destination *')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    const saveButton = getByText('Save');
    const cancelButton = getByText('Cancel');
    expect(saveButton).toBeEnabled();
    expect(cancelButton).toBeEnabled();
  });
});
