import React, { useEffect, useState } from 'react';
import { AdministrationTabsData } from 'types';
import { getGroupsData } from 'api/static-data';
import Catalog from 'components/Catalog';

interface GroupsProps {}

const Groups: React.FunctionComponent<GroupsProps> = () => {
  const [content, setContent] = useState<AdministrationTabsData[]>([]);
  useEffect(() => {
    async function getCatalogItems() {
      try {
        const data = await getGroupsData();
        console.log(data);
        setContent(data);
      } catch (error) {
        console.log(error);
      }
    }
    getCatalogItems();
  }, []);
  return content?.length ? <Catalog catalogItems={content} isAdminTiles /> : <div>No items to display</div>;
};

export default Groups;
