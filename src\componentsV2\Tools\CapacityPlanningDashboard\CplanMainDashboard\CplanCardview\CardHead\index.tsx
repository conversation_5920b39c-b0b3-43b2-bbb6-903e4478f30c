import { Box, Grid, Tooltip } from '@mui/material';
import { Facility, CalculatedVropsResource } from '../../../utils/types';
import { useState, useEffect } from 'react';
import { getUsageColor } from '../../../utils/colors';
import { addOpacityToColor } from '../../../utils/utilization';
import NblTypography from 'sharedComponents/NblTypography';
import { useApiService } from 'api/ApiService/context';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import ColorIndicatorIcon from '../../../components/ColorIndicationIcon';
import NblFlexContainer from '../../../../../../sharedComponents/NblContainers/NblFlexContainer';

export interface CardHeadProps {
  data: CalculatedVropsResource;
}
const CardHead: React.FunctionComponent<CardHeadProps> = ({ data }) => {
  const [facilityList, setFacilityList] = useState([] as Facility[]);
  const { apiCapacityPlanningService } = useApiService();
  const getFacilities = async () => {
    const Apiresponse = await apiCapacityPlanningService.getFacilities();
    const response = Apiresponse.data;
    setFacilityList(response);
  };
  useEffect(() => {
    getFacilities();
  }, []);
  const matchingFacility = facilityList.find((facility: Facility) => {
    return facility?.resources?.some((res) => res.resourceid === data.resourceid);
  });
  const facilityName = matchingFacility?.facilityname;
  const truncatedFacilityName = facilityName && facilityName?.substring(0, 15) + '...';
  const truncatedLabel = data.resourcelabel ? data.resourcelabel.substring(0, 15) + '...' : data.resourcename.substring(0, 15) + '...';
  const maxUtilization = Math.max(data.cpuUtilized, data.memoryUtilized, data.storageUtilized);
  const color = getUsageColor(maxUtilization);
  return (
    <>
      <NblFlexContainer>
        <NblGridContainer justifyContent="center" width="60%" alignContent="center" overflowY="hidden">
          <NblGridItem >
            <Tooltip title={facilityName}>
              <span>
                <NblTypography variant="body1" color="shade6" textAlign="left">
                  {truncatedFacilityName}
                </NblTypography>
              </span>
            </Tooltip>
          </NblGridItem>
          <NblGridItem overflowY="hidden">
            <Tooltip title={data.resourcelabel ? data.resourcelabel : data.resourcename}>
              <span>
                <NblTypography variant="subtitle1" color="shade1" textAlign="left" textTransform="uppercase">
                  {truncatedLabel}
                </NblTypography>
              </span>
            </Tooltip>
          </NblGridItem>
        </NblGridContainer>
        <NblGridContainer
          backgroundColor={addOpacityToColor(getUsageColor(maxUtilization), 0.26)}
          borderRadius="0 10px 0 0"
          justifyContent="center"
          alignItems="center"
          padding="2rem 0 2rem 0"
          width="40%"
          overflowY="hidden"
        >
          <Grid sx={{ opacity: 1 }}>
            <Box display="flex" flexDirection="row" alignItems="center" height="100%" justifyContent="center">
              <ColorIndicatorIcon color={color} />
              <NblTypography variant="body1" color="shade1">
                {data.status}
              </NblTypography>
            </Box>
          </Grid>
        </NblGridContainer>
      </NblFlexContainer>
    </>
  );
};
export default CardHead;
