enum RequestType {
  CREATE_AVAILABLE_NETWORK = 'CREATE_AVAILABLE_NETWORK',
  CREATE_ORGANIZATION = 'CREATE_ORGANIZATION',
  ADD_DEVICE = 'ADD_DEVICE',
  RELEASE_IP = 'RELEASE_IP',
  RESERVE_IP = 'RESERVE_IP',
  DNP = 'DNP',
  REMOVE_DEVICE = 'REMOVE_DEVICE',
  FIREWALL = 'FIREWALL',
  FIREWALL_V2 = 'FIREWALL_V2',
  COMMON_FIREWALL_POLICY = 'COMMON_FIREWALL_POLICY',
  CREATE_VM_LINUX89 = 'CREATE_VM_LINUX89',
  CREATE_VM_UBUNTU = 'CREATE_VM_UBUNTU',
  CREATE_VM_WINDOWS = 'CREATE_VM_WINDOWS',
  CREATE_LINUX_CORPNET = 'CREATE_LINUX_CORPNET',
  CREATE_WINDOWS_CORPNET = 'CREATE_WINDOWS_CORPNET',
  CREATE_DB_MONGODB6 = 'CREATE_DB_MONGODB6',
  CREATE_DB_MONGODB7 = 'CREATE_DB_MONGODB7',
  CREATE_DB_POSTGRESDB15 = 'CREATE_DB_POSTGRESDB15',
  CREATE_DB_POSTGRESDB16 = 'CREATE_DB_POSTGRESDB16',
  CREATE_DB_REDISDB6 = 'CREATE_DB_REDISDB6',
  CREATE_DB_REDISDB7 = 'CREATE_DB_REDISDB7',
  CREATE_DB_ORACLEDB19C = 'CREATE_DB_ORACLEDB19C',
  CREATE_DB_ORACLEDB21C = 'CREATE_DB_ORACLEDB21C',
  CREATE_STORAGE_S3 = 'CREATE_STORAGE_S3',
  CREATE_STORAGE_NFS = 'CREATE_STORAGE_NFS',
  DYNAMIC_ACCESS_POLICIES = 'DYNAMIC_ACCESS_POLICIES',
  AWS_CREATE_SUBACCOUNT = 'AWS_CREATE_SUBACCOUNT',
  MANAGE_PERMISSION_SET_CREATE = 'MANAGE_PERMISSION_SET_CREATE',
  CREATE_LB_F5 = 'CREATE_LB_F5',
  INTERNAL_CERTIFICATE = 'CREATE_INTERNAL_CERTIFICATE',
  AWS_CREATE_USER = 'AWS_CREATE_USER',
  AWS_REMOVE_USER = 'AWS_REMOVE_USER',
  MANAGE_PERMISSION_SET_REMOVE = 'MANAGE_PERMISSION_SET_REMOVE',
  FIREWALL_V1_MIGRATE = 'FIREWALL_V1_MIGRATE',
  AWS_SUB_ACCOUNT_GROUPS = 'AWS_SUB_ACCOUNT_GROUPS',
  DEVICE_CONFIG_LOOKUP = 'DEVICE_CONFIG_LOOKUP',
  MANAGE_SUB_ACCOUNT_GROUP_PERMISSION = 'MANAGE_SUB_ACCOUNT_GROUP_PERMISSION',
  BULK_VM_IMPORT = 'BULK_VM_IMPORT',
  DELETE_VM = 'DELETE_VM',
  DELETE_DB = 'DELETE_DB',
  CATALOG_ACCESS_REQUEST = 'CATALOG_ACCESS_REQUEST',
  ONBOARD_PROJECT = 'ONBOARD_PROJECT',
  ONBOARD_GROUP = 'ONBOARD_GROUP',
  SPEC_FLOW_S3 = 'SPEC_FLOW_S3',
  SPEC_FLOW_EC2 = 'SPEC_FLOW_EC2',
  SPEC_FLOW_EKS = 'SPEC_FLOW_EKS',
  CREATE_VM_LINUX89_V3 = 'CREATE_VM_LINUX89_V3',
  CREATE_NAMESPACE = 'CREATE_NAMESPACE',
  PUBLIC_CLOUD_VPC = 'PUBLIC_CLOUD_VPC',
  RECONFIGURE_VM = 'RECONFIGURE_VM',
  ZERO_TOUCH_PROVISIONING = 'ZERO_TOUCH_PROVISIONING',
  CREATE_VM_LINUX89_VMWARE = 'CREATE_VM_LINUX89_VMWARE',
  CREATE_VM_LINUX89_ADMIN_VMWARE = 'CREATE_VM_LINUX89_ADMIN_VMWARE',
  CREATE_VM_UBUNTU_VMWARE = 'CREATE_VM_UBUNTU_VMWARE',
  CREATE_VM_UBUNTU_ADMIN_VMWARE = 'CREATE_VM_UBUNTU_ADMIN_VMWARE',
  CREATE_VM_WINDOWS_VMWARE = 'CREATE_VM_WINDOWS_VMWARE',
  CREATE_VM_WINDOWS_ADMIN_VMWARE = 'CREATE_VM_WINDOWS_ADMIN_VMWARE',
}

export default RequestType;
