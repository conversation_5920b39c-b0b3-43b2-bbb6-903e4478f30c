import React, { useState } from 'react';
import { Stack, useTheme } from '@mui/material';
import { VisibilityOutlined, EditOutlined, DeleteOutlineOutlined } from '@mui/icons-material';

import { PERMISSION_MESSAGES } from 'utils/constant';
import ConfirmationDialog from 'components/ConfirmationDialog';
import CustomIconButton from 'components/CustomIconButton';
import { Permissions } from 'types';
// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import useNblNavigate from 'hooks/useNblNavigate';

interface ActionsColumnProps {
  editUrl?: string;
  showConfirmWarning?: boolean;
  onEditHandler?: () => void;
  onDeleteHandler?: () => void;
  permissions: Permissions;
  disableDelete?: boolean;
  disableEdit?: boolean;
  vmSizeDetails?: { vmSize: string; vmType: string };
  readOnly?: boolean;
}

const ActionsColumn: React.FunctionComponent<ActionsColumnProps> = ({
  editUrl,
  onEditHandler,
  onDeleteHandler,
  showConfirmWarning = true,
  permissions,
  disableDelete,
  disableEdit,
  vmSizeDetails,
  readOnly,
}) => {
  const DIALOG_DATA = {
    title: 'Confirmation',
    content: vmSizeDetails
      ? `Shall we proceed to delete VM size ${vmSizeDetails.vmSize} for ${vmSizeDetails.vmType}?`
      : 'Shall we proceed with deleting this request?',
    confirmationText: 'Delete',
  };
  const { canDelete, canUpdate, canRead } = permissions;

  const [showDeleteDialog, setDeleteDialog] = useState<boolean>(false);

  const theme: NebulaTheme = useTheme();
  const navigate = useNblNavigate();

  const {
    palette: { table },
  } = theme;

  const onConfirmHandler = () => {
    onDeleteHandler?.();
    setDeleteDialog(false);
  };

  const renderViewIcon = () => {
    return editUrl || onEditHandler ? (
      <CustomIconButton
        icon={VisibilityOutlined}
        iconButtonId="view-icon"
        isDisabled={!canRead}
        tooltipMessage={!canRead ? PERMISSION_MESSAGES.readRequest : ''}
        onClickHandler={() => canRead && (editUrl ? navigate(editUrl) : onEditHandler?.())}
      />
    ) : null;
  };

  const renderEditIcon = () => {
    return editUrl || onEditHandler ? (
      <CustomIconButton
        icon={EditOutlined}
        iconButtonId="update-icon"
        isDisabled={!canUpdate || readOnly}
        tooltipMessage={!canUpdate ? PERMISSION_MESSAGES.updateRequest : readOnly ? 'Read Only Mode' : ''}
        onClickHandler={() => canUpdate && !readOnly && (editUrl ? navigate(editUrl) : onEditHandler?.())}
      />
    ) : null;
  };

  const renderDeleteIcon = () => {
    const disableDeleteIcon = disableDelete || !canDelete;
    return (
      <CustomIconButton
        icon={DeleteOutlineOutlined}
        iconButtonId="delete-icon"
        isDisabled={disableDeleteIcon || readOnly}
        activeColor={table.deleteIconColor}
        tooltipMessage={
          !canDelete ? PERMISSION_MESSAGES.deleteRequest : readOnly ? 'Read Only Mode' : disableDelete ? 'Coming soon...' : ''
        }
        onClickHandler={() => !disableDeleteIcon && !readOnly && (showConfirmWarning ? setDeleteDialog(true) : onDeleteHandler?.())}
      />
    );
  };

  return (
    <Stack spacing={0} direction="row" alignItems={'center'}>
      {(!canUpdate && canRead) || disableEdit ? renderViewIcon() : renderEditIcon()}
      {onDeleteHandler && renderDeleteIcon()}
      {showDeleteDialog && onDeleteHandler && canDelete && (
        <ConfirmationDialog
          open={showDeleteDialog}
          title={DIALOG_DATA.title}
          content={DIALOG_DATA.content}
          confirmationText={DIALOG_DATA.confirmationText}
          confirmationTextId={'admin-deleteConfirm-btn'}
          onConfirm={onConfirmHandler}
          onClose={() => setDeleteDialog(false)}
          canMaximize={false}
        />
      )}
    </Stack>
  );
};

export default ActionsColumn;
