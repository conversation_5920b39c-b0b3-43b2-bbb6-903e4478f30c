import { act, render } from '@testing-library/react';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';
import { MemoryRouter as Router } from 'react-router-dom';

import ProjectTable from '.';
import ThemeProvider from 'mock/ThemeProvider';
import { ProjectTableData } from 'mock/ProjectTableData';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});
describe('Render Catalog permission table', () => {
  const setProjectData = jest.fn();
  const editProjectTags = jest.fn();
  test('Should render the table with data ', async () => {
    const { getByText } = await act(async () =>
      render(
        <Router>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <ProjectTable editProjectTags={editProjectTags} setProjectData={setProjectData} data={ProjectTableData} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );

    expect(getByText('Actions')).toBeInTheDocument();
    expect(getByText('Details')).toBeInTheDocument();
    expect(getByText('Setting Name')).toBeInTheDocument();
    expect(getByText('Setting Value')).toBeInTheDocument();
  });
});
