//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import ColorPalette from 'pages/ColorPalette';

type StoryProps = ComponentProps<typeof ColorPalette>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'ColorPalette/NblColorPalette',
  component: ColorPalette,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
  },
};

export default meta;

const Template = (args: StoryProps) => {
  return <ColorPalette {...args} />;
};

export const NblColorPalette: Story = {
  args: {
  },
  render: (args) => {
    return (
      <NebulaTheme>
        <Template {...args} />
      </NebulaTheme>
    );
  },
};
