import React from 'react';
import { StyledSpinner } from './styled';

import NblTypography from 'sharedComponents/NblTypography';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { getHexColorWithOpacity } from 'utils/common';

export interface NblSpinnerProps {
  spinnerData: Array<{
    id: string;
    status: boolean;
    message: string | React.ReactElement;
  }>;
  display?: 'inline' | 'block';
}

const NblSpinner: React.FunctionComponent<NblSpinnerProps> = ({ spinnerData, display = 'block' }) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();
  //Renders
  const renderMessage = () => {
    return (
      <NblFlexContainer width="auto" height="auto">
        {spinnerData.map((data, index) => {
          if (typeof data.message === 'string')
            return (
              <NblTypography key={data.message} variant="body1" color={display === 'block' ? 'shade4' : 'shade1'} weight="medium">
                {data.message + (spinnerData.length > 0 && index < spinnerData.length - 1 ? ', ' : '')}
              </NblTypography>
            );
          else return data.message;
        })}
      </NblFlexContainer>
    );
  };

  //JSX
  if (spinnerData.length)
    return (
      <NblFlexContainer
        data-testid="spinner"
        center
        {...(display === 'block' && {
          width: '100vw',
          height: '100vh',
          position: 'fixed',
          zIndex: 99999,
          backgroundColor: getHexColorWithOpacity(theme.palette.secondary.shade6, 90),
        })}
        direction="column"
        spacing={3}
      >
        <StyledSpinner display={display} />
        {renderMessage()}
      </NblFlexContainer>
    );
  else return null;
};

export default NblSpinner;
