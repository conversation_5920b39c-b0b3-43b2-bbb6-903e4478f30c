import { Typography } from '@mui/material';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';
import { textOverflowStyles } from 'utils/common';

interface StyledCard {
  theme?: NebulaTheme;
  expanded: boolean;
  disabled: boolean;
  width: string | undefined;
  active: boolean;
  maxWidth: string | undefined;
}
interface StyledHeader {
  theme?: NebulaTheme;
  expanded: boolean;
}

export const StyledCard = styled('div', {
  shouldForwardProp: (prop) => prop !== 'expanded' && prop !== 'active' && prop !== 'maxWidth',
})<StyledCard>(({ theme, expanded, disabled, width, active, maxWidth }) => {
  const { card } = theme.palette;
  return {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    borderRadius: '10px',
    boxSizing: 'border-box',

    '*': {
      boxSizing: 'border-box',
    },
    '&:hover': !disabled && {
      ...(expanded
        ? { borderColor: card.expanded.borderHoverColor, boxShadow: `6px 6px 12px ${card.expanded.boxShadowHover}` }
        : { backgroundColor: card.minimized.bgHoverColor, color: `${card.titleTextColor} !important` }),
    },
    ...(expanded
      ? {
          backgroundColor: card.expanded.bgColor,
          boxshadow: `6px 6px 12px ${card.expanded.boxShadow}`,
          border: `1px solid ${card.expanded.borderColor}`,
          minHeight: '175px',
        }
      : {
          display: 'inline-flex',
          justifyContent: 'center',
          height: '40px',
          flexShrink: 0,
          color: card.minimized.color,
          backgroundColor: card.minimized.bgColor,
        }),
    ...(disabled ? { opacity: 0.5, cursor: 'not-allowed' } : { cursor: 'pointer' }),
    ...(width && { width }),
    ...(maxWidth && { maxWidth }),
    ...(active && {
      ...(expanded
        ? { borderColor: card.expanded.borderHoverColor, boxShadow: `6px 6px 12px ${card.expanded.boxShadowHover}` }
        : { backgroundColor: card.minimized.bgHoverColor }),
    }),
  };
});

export const StyledHeader = styled('div', {
  shouldForwardProp: (prop) => prop !== 'expanded',
})<StyledHeader>(({ expanded }) => ({
  position: 'relative',
  gap: '8px',
  margin: '16px',
  ...(expanded
    ? { display: 'flex', flexDirection: 'column', justifyContent: 'flex-start', height: '60px' }
    : { display: 'inline-flex', justifyContent: 'center', height: '100%' }),
}));

export const StyledTypography = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'expanded',
})<{ theme?: NebulaTheme; expanded?: boolean }>(({ theme, expanded }) => {
  const {
    typography,
    palette: { card },
  } = theme;
  return {
    '&.MuiTypography-body3': {
      color: card.headerTextColor,
      ...typography.body3,
      ...textOverflowStyles('60%', 1),
      fontWeight: typography.medium.fontWeight,
    },
    '&.MuiTypography-subtitle2': {
      color: card.footerTextColor,
      ...typography.subtitle2,
      fontWeight: typography.regular.fontWeight,
      ...textOverflowStyles('90%', 2),
    },
    '&.MuiTypography-subtitle1': {
      ...(expanded ? { ...textOverflowStyles('50%', 2), color: card.titleTextColor } : { width: 'auto', color: card.headerTextColor }),
      '&:hover': !expanded && { color: card.titleTextColor },
      ...typography.subtitle1,
      fontWeight: typography.bold.fontWeight,
    },
  };
});

export const StyledTitle = styled('span', {
  shouldForwardProp: (prop) => prop !== 'expanded',
})<StyledHeader>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  height: '39px',
  color: theme.palette.card.titleTextColor,
}));

export const StyledFooter = styled('div')<{ theme?: NebulaTheme }>(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  height: '66px',
  width: '100%',
  padding: '0 16px',
  backgroundColor: theme.palette.card.footerBgColor,
  borderRadius: '0px 0px 9px 9px',
  opacity: 1,
  color: theme.palette.card.footerTextColor,
  '& button': {
    border: 'none',
    cursor: 'pointer',
    background: 'none',
    '&:disabled': {
      cursor: 'not-allowed',
    },
  },
}));
