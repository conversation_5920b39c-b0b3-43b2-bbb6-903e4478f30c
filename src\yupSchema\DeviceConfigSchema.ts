// eslint-disable-next-line no-unused-vars
import { Address4, Address6 } from 'ip-address';
import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

const DeviceConfigSchema = () =>
  yup.object().shape({
    option: yup.string().required('Option is required'),
    ipAddress: yup
      .string()
      .required('IP Address is required')
      .test('is-valid-ip', 'Invalid IP address', (value) => {
        if (!value) return false;
        const trimmedValue = value.trim();
        if (trimmedValue.includes('/')) {
          return new yup.ValidationError('Subnet mask is not allowed for IP address', trimmedValue, 'ipAddress');
        }
        return Address4.isValid(trimmedValue) || Address6.isValid(trimmedValue);
      }),
    deviceHost: yup.string().matches(yupMatchesParams.fqdn.pattern, yupMatchesParams.fqdn.hostErrorMessage),
    interface: yup
      .string()
      .matches(yupMatchesParams.alphaNumericWithHyphenUnderscrore.pattern, yupMatchesParams.alphaNumericWithHyphenUnderscrore.errorMessage),
    asn: yup
      .string()
      .matches(yupMatchesParams.alphaNumericWithHyphenUnderscrore.pattern, yupMatchesParams.alphaNumericWithHyphenUnderscrore.errorMessage),
    kma: yup.string(),
    market: yup.string(),
  });

export default DeviceConfigSchema;
