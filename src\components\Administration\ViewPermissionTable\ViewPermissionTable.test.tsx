import { act, render, waitFor } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';

import ViewPermissionTable from '.';
import AdministrationService from 'api/ApiService/AdministrationService';

describe('ViewPermission Component', () => {
  let getPendingApprovalsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getPendingApprovalsSpy = jest.spyOn(AdministrationService.prototype, 'getPermissionRequestCatalogs');
  });

  afterEach(() => {
    getPendingApprovalsSpy.mockRestore();
  });

  test('Should display the No Record Found message', async () => {
    getPendingApprovalsSpy.mockResolvedValue({ status: false, data: [] });
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <ViewPermissionTable />
        </ThemeProvider>
      )
    );
    await waitFor(() => {
      expect(getPendingApprovalsSpy).toHaveBeenCalled();
    });
    expect(getByText('No Record Found')).toBeInTheDocument();
  });
});
