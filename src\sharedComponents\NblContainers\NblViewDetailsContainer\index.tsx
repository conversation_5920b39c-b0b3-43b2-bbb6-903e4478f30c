import { useEffect, useMemo } from 'react';
import { UnfoldLess, UnfoldMore } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';

import useLoadComponentDetails from 'hooks/useLoadComponentDetails';
import NblFlexContainer from '../NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblViewDetailsOverview from './NblViewDetailsOverview';
import NblChip from 'sharedComponents/NblChip';
import { getResourceStatusChipColor } from 'utils/common';
import { ResourcesDetails, LoadComponentDetails } from 'types';
import { DefaultAccordions, setAccordions } from 'store/reducers/common';
import { isViewDetailsAccordionsExpanded } from 'componentsV2/NblViewDetailsAccordion';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';

export type ViewDetailsFields = {
  title: string;
  value: React.ReactNode;
  reason?: string;
  subValue?: string;
};

interface NblViewDetailsContainerProps<Data> {
  title: string;
  componentName: string;
  data: Data;
  viewDetailsFields?: ViewDetailsFields[];
  loading: boolean;
  Actions?: React.ReactNode;
  folder: LoadComponentDetails;
}

const NblViewDetailsContainer = <Data,>({
  componentName,
  data,
  title,
  viewDetailsFields,
  loading,
  Actions,
  folder,
}: NblViewDetailsContainerProps<Data>) => {
  //Hooks
  const { Component, comingSoon } = useLoadComponentDetails(componentName, folder);
  const dispatch = useDispatch();
  const { expandedAccordions } = useSelector((state: State) => state.common);
  const expanded = useMemo(() => isViewDetailsAccordionsExpanded(), [expandedAccordions]);

  //Local
  const resourceDataTemp = data as ResourcesDetails;
  const resourceDataBasicDetails = [
    {
      title: 'Request ID',
      value: resourceDataTemp?.requestId,
    },
    {
      title: 'Resource ID',
      value: resourceDataTemp?.resourceId,
    },
    {
      title: 'Catalog Item',
      value: resourceDataTemp?.catalogType,
    },
    {
      title: 'Project Name',
      value: resourceDataTemp?.projectName,
    },
    {
      title: 'Resource Status',
      value: (
        <NblChip
          id="ResourceStatus"
          borderRadius="lg"
          color={getResourceStatusChipColor(resourceDataTemp?.status)}
          label={resourceDataTemp?.status?.toUpperCase()}
        />
      ),
    },
  ];

  //Utils
  const toggleExpandCollapse = () => {
    dispatch(setAccordions(expanded ? DefaultAccordions.NONE : DefaultAccordions.ALL));
  };

  //Side effcets
  useEffect(() => {
    dispatch(setAccordions(DefaultAccordions.NONE));
  }, []);

  //JSX
  return (
    <NblFlexContainer direction="column" spacing={2}>
      <NblFlexContainer alignItems="center" height="auto" justifyContent="space-between">
        <NblFlexContainer alignItems="center">
          <NblTypography variant="h3" color="shade1" weight="bold">
            {title}
          </NblTypography>
          <NblButton
            buttonID={'expand-collapse-btn'}
            color="primary"
            onClick={toggleExpandCollapse}
            startIcon={expanded ? <UnfoldLess /> : <UnfoldMore />}
          >
            <NblTypography variant="subtitle2">{expanded ? 'Collapse All' : 'Expand All'}</NblTypography>
          </NblButton>
        </NblFlexContainer>
        {Actions}
      </NblFlexContainer>
      <NblViewDetailsOverview loading={loading} viewDetailsFields={viewDetailsFields} />
      {Component && <Component data={data} loading={loading} />}
      {comingSoon && folder === 'ResourceDetails' && (
        <>
          <NblViewDetailsOverview viewDetailsFields={resourceDataBasicDetails} />
          <NblTypography variant="h4" textAlign="center" color="shade1" weight="bold">
            More details Comming soon.
          </NblTypography>
        </>
      )}
    </NblFlexContainer>
  );
};

export default NblViewDetailsContainer;
