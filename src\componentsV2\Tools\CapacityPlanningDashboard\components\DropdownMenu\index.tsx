import { Box, FormControl, MenuItem, Typography, useTheme } from '@mui/material';
import { ReactComponent as CapacityPlanningDropdownIcon } from 'assets/images/icons/dropdownIcon.svg';
import { StyledSelect } from 'components/CapacityPlanning/VCenterView/styled';

interface DropdownMenuProps {
  disabled?: boolean;
  error?: boolean | string;
  label: string;
  name: string;
  options: any;
  placeholder?: string;
  type?: string;
  value: string | number;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  handleChange: (event: any) => void;
  defaultValue?: string | number;
}

const DropdownMenu: React.FunctionComponent<DropdownMenuProps> = ({
  label,
  options = [],
  defaultValue,
  handleChange,
  name,
  value,
}: DropdownMenuProps) => {
  const theme = useTheme();

  //keeping constant here
  const fixedWidth = '160px';

  const DropdownStyles = {
    borderRadius: '8px !important',
    border: '1px solid #28609795',
    '& .MuiMenuItem-root': {
      color: '#717171 !important',
      border: 'none !important',
      fontSize: '14px !important',
      lineHeight: '1.25px',
      [theme.breakpoints.down('xl')]: {
        fontSize: '11px !important',
      },
      '&.Mui-selected': {
        background: 'none !important',
        color: '#286097 !important',
        fontWeight: '450 !important',
      },
    },
  };

  return (
    <Box display="flex" alignItems="center">
      <Box display="flex" alignItems="center">
        <Typography sx={{ mr: 3, whiteSpace: 'nowrap', overflow: 'visible', color: '#286097' }}>{label}</Typography>
        <FormControl fullWidth variant="standard">
          <StyledSelect
            label={label}
            IconComponent={CapacityPlanningDropdownIcon}
            defaultValue={defaultValue}
            onChange={handleChange}
            disableUnderline
            name={name}
            value={value}
            sx={{
              width: fixedWidth, // Fix the width of the select box
            }}
            MenuProps={{
              PaperProps: {
                sx: {
                  width: fixedWidth, // Keeping the dropdown menu's width the same as the select box
                  maxWidth: '100%', // to Prevent the menu from growing larger than its trigger width
                  ...DropdownStyles,
                },
              },
            }}
          >
            {options.map((option: any, index: any) => (
              <MenuItem key={`${name}-option-${index}`} value={option.value ? option.value : option}>
                {option.value ? option.value : option}
              </MenuItem>
            ))}
          </StyledSelect>
        </FormControl>
      </Box>
    </Box>
  );
};

export default DropdownMenu;
