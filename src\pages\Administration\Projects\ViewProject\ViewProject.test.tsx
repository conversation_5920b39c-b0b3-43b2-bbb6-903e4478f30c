import { act, render, waitFor } from '@testing-library/react';
import { MemoryRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import ThemeProvider from 'mock/ThemeProvider';
import ViewProject from '.';
import AdministrationService from 'api/ApiService/AdministrationService';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'groups', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/groups/manage-registered-ad-groups'];

describe('ViewProject Component', () => {
  let getViewProjectSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);

    getViewProjectSpy = jest.spyOn(AdministrationService.prototype, 'getServiceRequestCatalogs');
  });

  afterEach(() => {
    getViewProjectSpy.mockRestore();
  });

  test('Should display the No Record Found message', async () => {
    getViewProjectSpy.mockResolvedValue({ status: false, data: [] });
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <ViewProject permissions={{ canRead: true, canUpdate: true }} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    await waitFor(() => {
      expect(getViewProjectSpy).toHaveBeenCalled();
    });
    expect(getByText('No Record Found')).toBeInTheDocument();
  });
});
