type Namespace = {
  name: string;
  shortName: string;
  projectName: string;
  projectDL: string;
  projectId: string;
  catalogId: string;
  namespaceName: string;
  platformContext: {
    projectName: string;
    applicationName: string;
    environmentName: string;
    domainName: string;
  };
  vaultPolicies: {
    policyName: string;
    role: string;
    enable: boolean;
  }[];
  vaultAppRoles: {
    policyName: string;
    appRoleName: string;
  }[];
  tokenTTL: string;
  autoRenewTokenOnExpiry: boolean;
  notifyBeforeTokenExpiry: boolean;
  addDisks: any[];
  deeplinkUrl: string;
};
export default Namespace;
