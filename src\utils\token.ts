import { jwtDecode } from 'jwt-decode';
import jwtEncode from 'jwt-encode';

export const modifyTokenGroups = (token: string, groups: string[]) => {
  let decoded_token: any = null;
  decoded_token = jwtDecode(token);
  const values = token?.split('.');
  const sign = values?.[2];
  const new_filtered_groups = decoded_token.groups?.filter((group: any) => groups.includes(group));
  const updatedToken = {
    ...decoded_token,
    groups: new_filtered_groups,
  };
  const newToken = jwtEncode(updatedToken, sign);
  return newToken;
};
