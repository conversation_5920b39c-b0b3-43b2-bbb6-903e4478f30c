import React, { useState } from 'react';
import { Box, IconButton, Stack, Typography, useTheme } from '@mui/material';
import { EditOutlined, DeleteOutlineOutlined } from '@mui/icons-material';
// eslint-disable-next-line no-unused-vars
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import DataGridTable from 'components/DataGridTable';
// eslint-disable-next-line
import { ProjectPermissions, SelectedGroupPermissions, Roles } from 'types';
import ProjectPermissionForm from '../ProjectPermission';

interface ProjectDataTableProps {
  data: SelectedGroupPermissions['projectPermissions'];
  setSelectedProjectPermissions: (rules: SelectedGroupPermissions['projectPermissions']) => void;
  projectPermissions: ProjectPermissions;
}

const ProjectDataTable: React.FunctionComponent<ProjectDataTableProps> = ({ data, setSelectedProjectPermissions, projectPermissions }) => {
  const [projectPermissionData, setProjectPermissionData] = useState({
    open: false,
    updateProjectPermission: {
      projectId: '',
      roles: [],
      id: null,
    },
  });
  const theme: NebulaTheme = useTheme();
  const {
    palette: { table },
  } = theme;

  const handleEdit = (row: any) => {
    setProjectPermissionData({
      open: true,
      updateProjectPermission: {
        id: row.id,
        projectId: row.projectId,
        roles: row.roles.map((role: { _id: string }) => role._id),
      },
    });
  };

  const handleDelete = (id: number) => {
    const deletedRow = data.filter((item) => item.id !== id);
    setSelectedProjectPermissions(deletedRow);
  };

  const columns: GridColDef[] = [
    {
      field: 'projectName',
      headerName: 'Projects',
      flex: 1,
      width: 500,
    },
    {
      field: 'roles',
      headerName: 'Project Roles',
      flex: 1,
      width: 700,
      sortable: true,
      valueGetter: (params: GridRenderCellParams) => {
        const {
          row: { roles },
        } = params;
        return roles?.map((role: { roleName: string }) => role.roleName).join(', ');
      },
      renderCell: (params: GridRenderCellParams) => {
        const {
          row: { roles },
        } = params;
        const roleNames = roles?.map((role: { roleName: string }) => role.roleName).join(', ');
        return (
          <Typography title={roleNames} sx={{ width: '100%', textOverflow: 'ellipsis', overflow: 'hidden' }}>
            {roleNames}
          </Typography>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      flex: 0.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        return (
          <Stack spacing={0} direction="row" alignItems={'center'}>
            <IconButton
              data-testid={`edit-icon-${params.id}`}
              onClick={() => {
                handleEdit(params.row);
              }}
            >
              <EditOutlined style={{ color: table.editIconColor }} />
            </IconButton>
            <IconButton
              data-testid={`delete-icon-${params.id}`}
              onClick={() => {
                handleDelete(params.row.id);
              }}
            >
              <DeleteOutlineOutlined style={{ color: table.deleteIconColor }} />
            </IconButton>
          </Stack>
        );
      },
    },
  ];

  const handleCloseDialog = () => {
    setProjectPermissionData({
      ...projectPermissionData,
      open: false,
    });
  };

  const setProjectData = (values: { projectId: string; projectName: string; roles: Roles[] }) => {
    setProjectPermissionData({
      ...projectPermissionData,
      open: false,
    });
    setSelectedProjectPermissions(
      data.map((currRow, id: number) => (id !== projectPermissionData.updateProjectPermission.id ? currRow : values))
    );
  };

  const renderForm = () => {
    return (
      <ProjectPermissionForm
        open={projectPermissionData.open}
        onClose={handleCloseDialog}
        projectPermissions={projectPermissions}
        selectedProjectPermissions={data}
        setProjectData={setProjectData}
        projectDetails={projectPermissionData.updateProjectPermission}
      />
    );
  };

  return (
    <Box sx={{ mt: 2 }}>
      {data.length ? <DataGridTable columns={columns} rows={data} pageSize={5} showResetFilter={false} /> : null}
      {renderForm()}
    </Box>
  );
};

export default ProjectDataTable;
