import React, { useState } from 'react';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
// eslint-disable-next-line no-unused-vars
import { ColumnData, NblTable } from 'sharedComponents/NblTable';
import OperatingSystemDetails from './OperatingSystemDetails';
import { Link } from '@mui/material';
import NblTypography from 'sharedComponents/NblTypography';

export type OSLayout = {
  disabled: boolean;
  imageName: string;
  imagePath: string | null;
  layoutMor: string;
  layoutName: string;
  osName: string;
  restricted: boolean;
  shortName: string;
  projects?: string[];
};

interface OSLayoutProps {
  osLayouts: OSLayout[];
  onChange: (updatedOSLayouts: OSLayout[]) => void;
}

const OperatingSystems: React.FC<OSLayoutProps> = ({ osLayouts, onChange }) => {
  const [selectedOS, setSelectedOS] = useState<OSLayout | null>(null);

  const handleChecked = (layoutMor: string, fieldName: keyof OSLayout) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const updated = osLayouts.map((os) => {
      if (os.layoutMor === layoutMor) {
        const updatedOS: OSLayout = {
          ...os,
          [fieldName]: e.target.checked,
        };

        if (fieldName === 'restricted' && !e.target.checked) {
          delete updatedOS.projects;
        }

        return updatedOS;
      }
      return os;
    });

    onChange(updated);

    const updatedSelected = updated.find((os) => os.layoutMor === selectedOS?.layoutMor);
    if (updatedSelected) {
      setSelectedOS(updatedSelected);
    }

    if (fieldName === 'restricted' && e.target.checked) {
      const newlyRestricted = updated.find((os) => os.layoutMor === layoutMor);
      if (newlyRestricted) {
        setSelectedOS(newlyRestricted);
      }
    }
  };

  const columns: ColumnData[] = [
    {
      field: 'layoutName',
      headerName: 'Layout Name',
      flex: 1,
      renderCell: (params) => (
        <Link
          sx={{ cursor: 'pointer' }}
          onClick={() => {
            const selected = osLayouts.find((layout) => layout.layoutMor === params.row.layoutMor);
            setSelectedOS(selected || null);
          }}
        >
          <NblTypography variant="subtitle2" color="shade7">
            {params.value}
          </NblTypography>
        </Link>
      ),
    },
    { field: 'layoutMor', headerName: 'Layout ID', flex: 1 },
    { field: 'osName', headerName: 'OS Name', flex: 1 },
    { field: 'shortName', headerName: 'Short Name', flex: 1 },
    { field: 'imageName', headerName: 'Image Name', flex: 1 },
    { field: 'imagePath', headerName: 'Image Path', flex: 1 },
    {
      field: 'disabled',
      headerName: 'Disabled',
      flex: 1,
      renderCell: (params) => (
        <NblFlexContainer center>
          <NblCheckBox
            checked={!!params.row.disabled}
            label=""
            name=""
            onChange={handleChecked(params.row.layoutMor, 'disabled')}
            onBlur={() => {}}
          />
        </NblFlexContainer>
      ),
    },
    {
      field: 'restricted',
      headerName: 'Restricted',
      flex: 1,
      renderCell: (params) => (
        <NblFlexContainer center>
          <NblCheckBox
            checked={!!params.row.restricted}
            label=""
            name=""
            onChange={handleChecked(params.row.layoutMor, 'restricted')}
            onBlur={() => {}}
          />
        </NblFlexContainer>
      ),
    },
  ];

  const handleOSChange = (updatedOS: OSLayout) => {
    const existingOS = osLayouts.find((os) => os.layoutMor === updatedOS.layoutMor);

    if (!existingOS || JSON.stringify(existingOS) !== JSON.stringify(updatedOS)) {
      const updatedList = osLayouts.map((os) => (os.layoutMor === updatedOS.layoutMor ? updatedOS : os));
      onChange(updatedList);
      setSelectedOS(updatedOS);
    }
  };

  return (
    <NblFlexContainer direction="column">
      {selectedOS && <OperatingSystemDetails selectedOS={selectedOS} onOSChange={handleOSChange} onClose={() => setSelectedOS(null)} />}
      <NblTable
        columns={columns}
        rows={osLayouts.map((layout) => ({ id: layout.layoutMor, ...layout }))}
        rowsOverlayMessage="No OS Layouts Found"
        showResetFilter={false}
        hideFooterAndPagination
      />
    </NblFlexContainer>
  );
};

export default OperatingSystems;
