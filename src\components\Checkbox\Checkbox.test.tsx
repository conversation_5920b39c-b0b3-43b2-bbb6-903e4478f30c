import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import Checkbox from '.';

describe('Checkbox Component', () => {
  const handleChange = jest.fn();
  const handleBlur = jest.fn();

  test('renders with props', () => {
    const { getByLabelText } = render(
      <Checkbox name="checkbox" label="Test Checkbox" labelPlacement="end" checked handleChange={handleChange} handleBlur={handleBlur} />
    );
    const checkbox = getByLabelText('Test Checkbox');
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).toBeChecked();
  });

  test('calls handleChange when checkbox is clicked', () => {
    const { getByLabelText } = render(
      <Checkbox name="checkbox" label="Test Checkbox" handleChange={handleChange} handleBlur={handleBlur} />
    );
    const checkbox = getByLabelText('Test Checkbox');
    fireEvent.click(checkbox);
    expect(handleChange).toHaveBeenCalledTimes(1);
  });
});
