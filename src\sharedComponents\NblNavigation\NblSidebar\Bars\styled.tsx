import { Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { NebulaTheme } from 'NebulaTheme/type';

interface StyledSideBarProps {
  expanded: boolean;
  theme?: NebulaTheme;
}

interface StyledLogoContainerProps {
  expanded: boolean;
  theme?: NebulaTheme;
}
interface StyledItemListContainerProps {
  bar: number;
  listStartPosition: number;
}

const StyledBar = styled('aside')<StyledSideBarProps>(({ theme, expanded }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'space-between',
  height: '100%',
  boxSizing: 'border-box',
  transition: 'width 0.2s ease-out',

  '&.bar1': {
    padding: '6px',
    boxShadow: '0px 8px 24px #00000026',
    backgroundColor: theme.palette.primary.main,
    borderRadius: expanded ? '34px' : '64px',
    width: expanded ? '340px' : '80px',
  },
  '&.bar2,&.bar3': {
    position: 'relative',
    padding: '20px',
    width: '340px',
    borderRadius: '0 34px 34px 0',
  },
  '&.bar2': {
    borderRight: '2px solid #70d7ff33',
  },

  '.MuiTypography-h3': {
    fontWeight: theme.typography.fontWeightBold,
  },
  '.MuiTypography-h5': {
    color: theme.palette.typography.shade3,
  },
  [theme.breakpoints.down('2K')]: {
    '&.bar1': {
      borderRadius: expanded ? '29px' : '64px',
      width: expanded ? '270px' : '70px',
    },
    '&.bar2,&.bar3': {
      width: '236px',
      borderRadius: '0 14px 14px 0',
    },
  },
}));

const StyledBarTitle = styled(Typography)<{ theme?: NebulaTheme }>(({ theme }) => ({
  all: 'unset',
  display: 'inline',
  alignSelf: 'flex-start',
  texttransform: 'capitalize',
  color: theme.palette.typography.shade3,
  ...theme.typography.h3,
}));

const StyledH4 = styled(Typography)<{ theme?: NebulaTheme }>(({ theme }) => ({
  all: 'unset',
  display: 'inline',
  alignSelf: 'flex-start',
  textTransform: 'uppercase',
  letterSpacing: '0px',
  ...theme.typography.h4,
  fontWeight: theme.typography.fontWeightBold,
  color: `${theme.palette.typography.shade3} !important`,
}));

const StyledLogoContainer = styled('span')<StyledLogoContainerProps>(({ theme, expanded }) => ({
  display: 'flex',
  alignitems: 'center',
  height: '68px',
  width: '68px',
  backgroundColor: theme.palette.secondary.main,
  borderRadius: '100%',
  ...(expanded && { alignSelf: 'flex-start', width: '197px', borderRadius: '34px', transition: 'width 0.2s ease-out' }),
  '.logoImage': {
    width: '68px',
    height: '68px',
    borderRadius: '100%',
    padding: '10px',
    boxSizing: 'border-box',
  },
  '.logoText': {
    alignSelf: 'center',
    display: expanded ? 'inine' : 'none',
    transition: 'opacity 0.2s ease-out',
    letterSpacing: '-0.6px',
    color: theme.palette.typography.shade1,
    marginLeft: '-5px',
  },

  [theme.breakpoints.down('2K')]: {
    width: '58px',
    height: '58px',
    ...(expanded && { width: '120px', borderRadius: '29px' }),
    '.logoImage': {
      width: '58px',
      height: '58px',
    },
  },
}));

const StyledItemListContainer = styled('div')<StyledItemListContainerProps>(({ theme, bar, listStartPosition }) => ({
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  alignItems: 'center',
  gap: '5px',
  marginTop: listStartPosition + 'px',
  width: '100%',
  height: 'calc(100% - 88px)' /*68px+20px*/,
  overflowY: 'auto',
  scrollBehavior: 'smooth',
  padding: bar === 1 ? '10px' : 0,
  '&::-webkit-scrollbar': {
    display: 'none',
  },
  [theme.breakpoints.down('2K')]: {
    height: 'calc(100% - 63px)' /*53px+10px*/,
  },
}));
const StyledNestedItemListContainer = styled('div')(() => ({
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  alignItems: 'flex-start',
  gap: '20px',
  width: '100%',
  marginTop: '15px',
}));
export { StyledBar, StyledBarTitle, StyledH4, StyledLogoContainer, StyledItemListContainer, StyledNestedItemListContainer };
