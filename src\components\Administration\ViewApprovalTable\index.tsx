import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
// eslint-disable-next-line no-unused-vars
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

import RequestsGrid from 'components/RequestsGrid';
import ActionsColumn from '../ActionsColumn';

import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import withAdminPermissions from 'hoc/withAdminPermissions';
import AdministrationService from 'api/ApiService/AdministrationService';
import ViewApprovalPayload from 'types/Interfaces/ViewApprovalsPayload';
// eslint-disable-next-line no-unused-vars
import { AdminGridProps, AdministrationDialogData, AdminComponent } from 'types';
import { dateFormatter, getAdminColumnWidth } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'mantis/themes/type';
import { useMediaQuery, useTheme } from '@mui/material';

interface ViewApprovalTableProps extends AdminGridProps {}

const DIALOG_DATA: AdministrationDialogData = {
  initialState: { open: false, type: '', title: '', content: '', confirmationText: '', data: {} },
  deleteDialog: {
    title: 'Confirmation',
    content: 'Shall we proceed with this delete request?',
    confirmationText: 'Delete',
  },
};

const ViewApprovalTable: AdminComponent<ViewApprovalTableProps> = ({ permissions }: ViewApprovalTableProps) => {
  const apiAdministrationService = new AdministrationService();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [viewDetailsDialog, setViewDetailsDialog] = useState<{ open: boolean; id: string }>({ open: false, id: '' });
  const [confirmDialog, setConfirmDialog] = useState<AdministrationDialogData['initialState']>(DIALOG_DATA.initialState);
  const [approvalData, setApprovalData] = useState<ViewApprovalPayload[]>([]);
  const theme: NebulaTheme = useTheme();
  const isSmaller = useMediaQuery(theme.breakpoints.down('xl'));

  const fetchApprovalCatalogList = () => {
    setIsLoading(true);
    apiAdministrationService
      .getApprovals()
      .then((res) => {
        if (res.status) {
          setApprovalData(
            res.data.map((viewData) => ({
              ...viewData,
              approvalName: viewData?.name,
              id: viewData?._id,
              description: viewData?.description,
              serviceCatalogName: viewData?.catalogL4Name,
              approvalGroup: viewData?.approvalDetails?.map((approvalValue) => approvalValue.groupId),
              approvalLevel: viewData?.approvalDetails?.map((approvalValue) => approvalValue.level),
              createdBy: viewData?.createdBy,
              createdAt: viewData?.createdAt ? dateFormatter(viewData.createdAt) : '',
              updatedAt: viewData?.updatedAt ? dateFormatter(viewData.updatedAt) : '',
              updatedBy: viewData?.updatedBy,
            }))
          );
        } else {
          setApprovalData([]);
        }
        fetchApprovalCatalogList;
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  useEffect(() => {
    fetchApprovalCatalogList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onDeleteHandler = (deleteApiParam: string) => {
    setIsLoading(true);
    apiAdministrationService
      .deleteApprovals(deleteApiParam)
      .then((res) => {
        if (res.status) {
          toast.success('deleted successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
          fetchApprovalCatalogList();
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const columns: GridColDef[] = [
    {
      field: 'approvalName',
      headerName: 'Approval Name',
      flex: 1,
    },
    {
      field: 'serviceCatalogName',
      headerName: 'Service Catalog Name',
      flex: 1,
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
    },
    { field: 'createdAt', headerName: 'Created Date', flex: 1 },
    { field: 'createdBy', headerName: 'Created By', flex: 1 },
    { field: 'updatedAt', headerName: 'Updated Date', flex: 1 },
    { field: 'updatedBy', headerName: 'Updated By', flex: 1 },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      width: getAdminColumnWidth(isSmaller),
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        const {
          row: { shortName },
        } = params;
        return (
          <ActionsColumn
            permissions={permissions}
            disableDelete
            editUrl={`/administration/approvals/${shortName}`}
            onDeleteHandler={() => onDeleteHandler(shortName)}
            disableEdit
          />
        );
      },
    },
  ];

  const dialogConfirmHandler = () => {
    const {
      row: { id },
    } = confirmDialog.data;
    setApprovalData(approvalData.filter((obj) => obj.id !== id));
    setConfirmDialog(DIALOG_DATA.initialState);
  };

  const dialogCloseHandler = (): void => {
    setConfirmDialog(DIALOG_DATA.initialState);
  };

  return (
    <>
      <RequestsGrid
        isLoading={isLoading}
        rows={approvalData}
        columns={columns}
        viewDetailsDialog={viewDetailsDialog}
        setViewDetailsDialog={setViewDetailsDialog}
        confirmDialog={confirmDialog}
        dialogConfirmHandler={dialogConfirmHandler}
        dialogCloseHandler={dialogCloseHandler}
        fetchData={fetchApprovalCatalogList}
      />
    </>
  );
};

ViewApprovalTable.type = ADMIN_TILE_PERMISSION_TYPE.grid;

export default withAdminPermissions(ViewApprovalTable);
