import { createSlice } from '@reduxjs/toolkit';
import set from 'lodash/set';
import findIndex from 'lodash/findIndex';

import { Devices, Interfaces } from 'types';

export interface DNP {
  devices: Devices[];
  selectedDevices: Devices[];
  selectedHostname: string;
}

// initial state
const initialState: DNP = {
  devices: [],
  selectedDevices: [],
  selectedHostname: '',
};
// // ==============================|| SLICE - User ||============================== //

const dnp = createSlice({
  name: 'dnp',
  initialState,
  reducers: {
    setDevices(state, action) {
      const { devices } = action.payload;
      state.devices = Array.isArray(devices) ? devices.map((device) => ({ ...device, id: device.hostname })) : [];
    },
    setDeviceInterfaces(state, action) {
      const cloneDevices = [...state.devices];
      const { deviceId, interfaces } = action.payload as { deviceId: string; interfaces: Array<Interfaces> };
      const matchedDeviceIndex = findIndex(cloneDevices, { id: deviceId });
      if (matchedDeviceIndex !== -1) {
        set(
          cloneDevices,
          `[${matchedDeviceIndex}].interfaces`,
          interfaces.map((interfaceDetails, index) => ({ ...interfaceDetails, id: index + 1 }))
        );
        state.devices = cloneDevices;
      } else {
        console.log('Failed to set interfaces for this device::', deviceId);
      }
    },
    setSelectedDevice(state, action) {
      let clonedSelectedDevices = state.selectedDevices;
      const { deviceIds } = action.payload as { deviceIds: string[] };

      // remove the items from selectedDevices which are not unselected
      clonedSelectedDevices = clonedSelectedDevices.filter(({ id }) => deviceIds.includes(id));

      deviceIds.map((deviceId) => {
        const currentDevice = clonedSelectedDevices.find((device) => device.id === deviceId);
        if (!currentDevice) {
          const currentDeviceDetails = state.devices.find((device) => device.id === deviceId);
          if (currentDeviceDetails) {
            clonedSelectedDevices.push({ ...currentDeviceDetails, interfaces: [] });
          }
        }
      });

      state.selectedDevices = clonedSelectedDevices;
    },
    setSelectedDeviceInterfaces(state, action) {
      let clonedSelectedDevices = state.selectedDevices;
      const { deviceId, interfaceIds } = action.payload as { deviceId: string; interfaceIds: number[] };

      const selectedDeviceIndex = clonedSelectedDevices.findIndex((device) => device.id === deviceId);
      const selectedDevice = state.devices.find((device) => device.id === deviceId);

      if (selectedDeviceIndex !== -1 && selectedDevice?.interfaces?.length) {
        clonedSelectedDevices[selectedDeviceIndex]['interfaces'] = selectedDevice.interfaces.filter((interfaceDetails) =>
          interfaceIds.includes(interfaceDetails.id)
        );
      }
      state.selectedDevices = clonedSelectedDevices;
    },
    removeSelectedDevice(state, action) {
      const { deviceId } = action.payload;
      state.selectedDevices = state.selectedDevices.filter((device) => device.id !== deviceId);
    },
    clearSelectedDevices(state) {
      state.selectedDevices = [];
    },
    clearDevices(state) {
      state.devices = [];
    },
    removeSelectedDeviceInterface(state, action) {
      let clonedSelectedDevices = state.selectedDevices;
      const { deviceId, interfaceId } = action.payload;
      const selectedDeviceIndex = clonedSelectedDevices.findIndex((device) => device.id === deviceId);
      if (selectedDeviceIndex !== -1) {
        clonedSelectedDevices[selectedDeviceIndex]['interfaces'] = clonedSelectedDevices[selectedDeviceIndex]['interfaces'].filter(
          (interfaceDetails) => interfaceDetails.id !== interfaceId
        );
      }
      state.selectedDevices = clonedSelectedDevices;
    },
    setSelectedHostname(state, action) {
      state.selectedHostname = action.payload;
    },
  },
});

export default dnp.reducer;

export const {
  setDevices,
  setDeviceInterfaces,
  setSelectedDevice,
  setSelectedDeviceInterfaces,
  removeSelectedDevice,
  clearDevices,
  clearSelectedDevices,
  removeSelectedDeviceInterface,
  setSelectedHostname,
} = dnp.actions;
