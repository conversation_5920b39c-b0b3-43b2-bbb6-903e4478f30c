export default interface MultiEnvServiceCatalogListData {
  id: string;
  tags: [];
  domain: [];
  name: string;
  level03Id: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  approvalRequired: boolean;
  isDeleted: boolean;
  shortName: string;
  icon: string;
  component: string;
  enabled: boolean;
  level02Id: string;
  level01Id: string;
  level01Name: string;
  level02Name: string;
  level03Name: string;
}
