import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import AddRoleForm from 'components/Administration/AddRoleForm';
import useNblNavigate from 'hooks/useNblNavigate';

const AddRole = () => {
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const navigateToRoleDetails = () => {
    navigate('/administration/roles');
  };

  return <AddRoleForm title="Add Role" permissions={{}} onSuccess={navigateToRoleDetails} onClose={navigateToRoleDetails} />;
};

export default AddRole;
