import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import EditSecretDetails from '.';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { useSearchParams } from 'react-router-dom';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { SecretType } from 'types/Enums/SecretType';
import { useApiService } from 'api/ApiService/context';
import userEvent from '@testing-library/user-event';
import dayjs from 'dayjs';
import { MemoryRouter } from 'react-router-dom';
import ReduxProvider from 'mock/ReduxProvider';

// Mock form values
const mockFormValues = {
  vaultKey: 'testKey',
  vaultPassword: 'testValue',
  namespaceName: 'testNamespace',
  path: 'testPath',
  type: SecretType.NormalSecret,
  policyId: 'Test',
  active: true,
};

const rotatingFormValues = {
  ...mockFormValues,
  type: SecretType.RotatingSecret,
  userNameKey: 'usernameKey',
  userNamePassword: 'maskedUserPassword',
  secretTTLInHours: 24,
  nextRotationDate: dayjs(),
  rotationType: 'Time-based',
  notifyBeforeTokenExpiry: true,
};

// Mock form props
const mockNblFormProps = {
  values: mockFormValues,
  touched: {},
  errors: {},
  handleChange: jest.fn(),
  handleBlur: jest.fn(),
  handleSubmit: jest.fn(),
  resetForm: jest.fn(),
  setFieldValue: jest.fn(),
  initialValues: {
    ...rotatingFormValues,
  },
};

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

jest.mock('../CreateNewSecretForm/SecretCommonFields', () => () => [
  {
    title: 'Test Field',
    value: 'Test Value',
  },
]);

const mockGetPassowrdPolicies = jest.fn().mockResolvedValue({
  status: true,
  data: [
    {
      policyId: 'Test',
      policyName: 'Test',
      description: 'Test',
    },
  ],
});

// Mock useNblForms hook
jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  useNblForms: jest.fn(),
}));

// Mock react-router-dom's useSearchParams to return secretId param
jest.mock('react-router-dom', () => {
  const actual = jest.requireActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: jest.fn(),
  };
});

// MOCK DeleteSecret component to avoid useApiService error
jest.mock('componentsV2/SecretsManagement/DeleteSecret', () => () => {
  return <button data-testid="mock-delete-secret">Mock DeleteSecret</button>;
});

jest.mock('../CreateNewSecretForm/SecretValueField', () => () => {
  return <input data-testid="secret-value-field" />;
});

describe('EditSecretDetails Component', () => {
  beforeEach(() => {
    // Mock useNblForms to return mocked form props and values
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockNblFormProps,
      nblFormValues: mockFormValues,
    });
    (useApiService as jest.Mock).mockReturnValue({
      apiSecretsManagement: {
        getPasswordPolicies: mockGetPassowrdPolicies,
      },
    });
    (useSearchParams as jest.Mock).mockReturnValue([new URLSearchParams('secretId=123'), jest.fn()]);
  });

  it('renders noraml secret details in view mode initially', () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('Secret Details')).toBeInTheDocument();
    expect(screen.getByText('testKey')).toBeInTheDocument();
    expect(screen.getByText('testPath')).toBeInTheDocument();
    expect(screen.getByTestId('mock-delete-secret')).toBeInTheDocument();
  });

  it('renders secret details in view mode initially', () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('Secret Details')).toBeInTheDocument();
    expect(screen.getByText('testKey')).toBeInTheDocument();
    expect(screen.getByText('testPath')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Edit' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Mock DeleteSecret' })).toBeInTheDocument();

    const editButton = screen.getByRole('button', { name: /edit/i });
    const deleteButton = screen.getByRole('button', { name: /Mock DeleteSecret/i });

    expect(editButton).toBeEnabled();
    expect(deleteButton).toBeEnabled();

    fireEvent.click(editButton);
  });

  it('renders rotating secret fields', async () => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockNblFormProps,
      nblFormValues: rotatingFormValues,
    });

    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('Username Key')).toBeInTheDocument();
    expect(screen.getByText('Rotation Interval')).toBeInTheDocument();
    expect(screen.getByText('First Run')).toBeInTheDocument();
    expect(screen.getByText('Rotation Type')).toBeInTheDocument();
    expect(screen.getByText('Notify before expiry')).toBeInTheDocument();
  });

  it('should display no when notify before token expiry is false', async () => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockNblFormProps,
      nblFormValues: { ...rotatingFormValues, notifyBeforeTokenExpiry: false },
    });

    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('No')).toBeInTheDocument();
  });

  it('toggles to edit mode and back to view mode', async () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    const editButton = screen.getByRole('button', { name: 'Edit' });
    userEvent.click(editButton);

    expect(await screen.findByRole('button', { name: 'Cancel' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Update' })).toBeDisabled(); // update is disabled if no change

    // toggle back
    const cancelButton = screen.getByRole('button', { name: 'Cancel' });
    userEvent.click(cancelButton);
    expect(await screen.findByRole('button', { name: 'Edit' })).toBeInTheDocument();
  });

  it('not displays password policy label in view mode if not matched', () => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockNblFormProps,
      nblFormValues: { ...mockFormValues, policyId: 'Test' },
    });

    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('testPath')).toBeInTheDocument();
  });

  it('renders MaskedValueCell for value fields in view mode', () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('••••••••')).toBeInTheDocument();
  });

  it('handles no password policy match case gracefully', () => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockNblFormProps,
      nblFormValues: { ...mockFormValues, policyId: 'unknown' },
    });

    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    // No crash, no label
    expect(screen.queryByText('Test')).not.toBeInTheDocument();
  });

  it('renders rotating secret fields', async () => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockNblFormProps,
      nblFormValues: rotatingFormValues,
    });

    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('Username Key')).toBeInTheDocument();
    expect(screen.getByText('Rotation Interval')).toBeInTheDocument();
    expect(screen.getByText('First Run')).toBeInTheDocument();
    expect(screen.getByText('Rotation Type')).toBeInTheDocument();
    expect(screen.getByText('Notify before expiry')).toBeInTheDocument();
  });

  it('should display no when notify before token expiry is false', async () => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockNblFormProps,
      nblFormValues: { ...rotatingFormValues, notifyBeforeTokenExpiry: false },
    });

    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('No')).toBeInTheDocument();
  });

  it('toggles to edit mode and back to view mode', async () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    const editButton = screen.getByRole('button', { name: 'Edit' });
    userEvent.click(editButton);

    expect(await screen.findByRole('button', { name: 'Cancel' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Update' })).toBeDisabled(); // update is disabled if no change

    // toggle back
    const cancelButton = screen.getByRole('button', { name: 'Cancel' });
    userEvent.click(cancelButton);
    expect(await screen.findByRole('button', { name: 'Edit' })).toBeInTheDocument();
  });

  it('not displays password policy label in view mode if not matched', () => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockNblFormProps,
      nblFormValues: { ...mockFormValues, policyId: 'Test' },
    });

    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('testPath')).toBeInTheDocument();
  });

  it('renders MaskedValueCell for value fields in view mode', () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(screen.getByText('••••••••')).toBeInTheDocument();
  });

  it('handles no password policy match case gracefully', () => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormProps: mockNblFormProps,
      nblFormValues: { ...mockFormValues, policyId: 'unknown' },
    });

    render(
      <MemoryRouter>
        <ReduxProvider>
          <NebulaThemeProvider>
            <EditSecretDetails />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    // No crash, no label
    expect(screen.queryByText('Test')).not.toBeInTheDocument();
  });
});
