import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { NblGridContainer, NblGridItem } from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('Nbl Grid Container', () => {
  test('renders NblGridContainer with nested NblGridItem components', () => {
    render(
      <NebulaThemeProvider>
        <NblGridContainer columnMinWidth="150px" columns={3} spacing={2} justifyContent="center" alignContent="start">
          <NblGridItem width="100px" height="50px">
            Item 1
          </NblGridItem>
          <NblGridItem width="150px" height="75px">
            Item 2
          </NblGridItem>
          <NblGridItem>Item 3</NblGridItem>
        </NblGridContainer>
      </NebulaThemeProvider>
    );

    // Assertions for NblGridContainer
    const gridContainer = screen.getByRole('grid');

    expect(gridContainer).toBeInTheDocument();
    expect(gridContainer).toHaveStyle(`
    grid-template-columns: repeat(3, minmax(150px, 1fr));
    gap: 16px;
    justify-content: center;
    align-content: start;
  `);

    // Assertions for NblGridItem
    const item1 = screen.getByText('Item 1');
    expect(item1).toBeInTheDocument();
    expect(item1).toHaveStyle(`
    width: 100px;
    height: 50px;
  `);

    const item2 = screen.getByText('Item 2');
    expect(item2).toBeInTheDocument();
    expect(item2).toHaveStyle(`
    width: 150px;
    height: 75px;
  `);

    const item3 = screen.getByText('Item 3');
    expect(item3).toBeInTheDocument();
    expect(item3).toHaveStyle(`
    width: 100%;
    height: 100%;
  `);
  });

  test('renders NblGridContainer with dynamic grid columns', () => {
    render(
      <NebulaThemeProvider>
        <NblGridContainer columns={5} columnMinWidth="200px">
          <div>Item</div>
        </NblGridContainer>
      </NebulaThemeProvider>
    );

    const container = screen.getByText('Item').parentElement;
    expect(container).toHaveStyle(`
    grid-template-columns: repeat(5, minmax(200px, 1fr));
  `);
  });

  test('applies alignment and justification styles', () => {
    render(
      <NebulaThemeProvider>
        <NblGridContainer justifyContent="space-between" alignContent="center">
          <div>Item</div>
        </NblGridContainer>
      </NebulaThemeProvider>
    );

    const container = screen.getByText('Item').parentElement;
    expect(container).toHaveStyle(`
    justify-content: space-between;
    align-content: center;
  `);
  });

  test('renders nested grid containers correctly', () => {
    render(
      <NebulaThemeProvider>
        <NblGridContainer columns={2}>
          <NblGridContainer columns={3}>
            <NblGridItem>Nested Item 1</NblGridItem>
            <NblGridItem>Nested Item 2</NblGridItem>
          </NblGridContainer>
          <NblGridItem>Main Item</NblGridItem>
        </NblGridContainer>
      </NebulaThemeProvider>
    );

    expect(screen.getByText('Nested Item 1')).toBeInTheDocument();
    expect(screen.getByText('Nested Item 2')).toBeInTheDocument();
    expect(screen.getByText('Main Item')).toBeInTheDocument();
  });
});
