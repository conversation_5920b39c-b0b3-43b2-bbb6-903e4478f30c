import { render, act, fireEvent } from '@testing-library/react';
import { MemoryRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import EditGroup from './index';
import ThemeProvider from 'mock/ThemeProvider';
import { GetGroups, GetGroupsPermissions } from 'mock/Groups';
import * as api from 'api/static-data';
import PermissionService from 'api/ApiService/PermissionService';
import { GetAdminstrationCatalogItems, GetAdminTiles, GetAdminTileRoles } from 'mock/AdminTiles';

const mockStore = configureMockStore();

const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'groups', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/groups/IPAM-GROUP-ADMIN'];

describe('EditGroup component', () => {
  let getGroupDetailsSpy: jest.SpyInstance;
  let getGroupsPermissionsSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;
  let getAdminTilesSpy: jest.SpyInstance;
  let getAdminTileRolesSpy: jest.SpyInstance;

  beforeEach(async () => {
    getGroupDetailsSpy = jest.spyOn(PermissionService.prototype, 'getGroupDetails');
    getGroupDetailsSpy.mockResolvedValue(GetGroups);

    getGroupsPermissionsSpy = jest.spyOn(PermissionService.prototype, 'getGroupsPermissions');
    getGroupsPermissionsSpy.mockResolvedValue(GetGroupsPermissions);

    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);

    getAdminTilesSpy = jest.spyOn(PermissionService.prototype, 'getAdminTiles');
    getAdminTilesSpy.mockResolvedValue(GetAdminTiles);

    getAdminTileRolesSpy = jest.spyOn(PermissionService.prototype, 'getAdminTileRoles');
    getAdminTileRolesSpy.mockResolvedValue(GetAdminTileRoles);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <ThemeProvider>
            <Router initialEntries={BASE_ROUTE}>
              <ReduxProvider store={store}>
                <Routes>
                  <Route path="/administration/groups/:groupName" element={<EditGroup />}></Route>
                </Routes>
              </ReduxProvider>
            </Router>
          </ThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });

  test('Should render the Edit Registered AD group with pre populated values', async () => {
    const selectedGroup = GetGroups.data[0];
    const { getByText, getByLabelText } = await act(async () =>
      render(
        <ThemeProvider>
          <Router initialEntries={BASE_ROUTE}>
            <ReduxProvider store={store}>
              <Routes>
                <Route path="/administration/groups/:groupName" element={<EditGroup />}></Route>
              </Routes>
            </ReduxProvider>
          </Router>
        </ThemeProvider>
      )
    );

    expect(getByText('Edit Registered AD group')).toBeInTheDocument();
    expect(getByLabelText('Group Name *')).toHaveValue(selectedGroup.groupName);
    expect(getByLabelText('Description')).toHaveValue(selectedGroup.description);

    // expect(getByText(selectedGroup.projectPermissions[0].projectName)).toBeInTheDocument();
    // expect(getByText(selectedGroup.catalogPermissions[0].catalogName)).toBeInTheDocument();
  });

  test('Should navigate to view group details when user clicks on  Cancel button', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <Router initialEntries={BASE_ROUTE}>
            <ReduxProvider store={store}>
              <Routes>
                <Route path="/administration/groups/:groupName" element={<EditGroup />}></Route>
                <Route path="/administration/groups/manage-registered-ad-groups" element={<div>View Group details</div>} />
              </Routes>
            </ReduxProvider>
          </Router>
        </ThemeProvider>
      )
    );
    fireEvent.click(getByText('Cancel'));
    expect(getByText('View Group details')).toBeInTheDocument();
  });
});
