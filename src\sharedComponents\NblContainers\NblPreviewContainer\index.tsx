import React from 'react';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import NblBorderContainer from '../NblBorderContainer';
import NblFlexContainer from '../NblFlexContainer';
import NblDivider from 'sharedComponents/NblDivider';
import NblTypography from 'sharedComponents/NblTypography';

interface NblPreviewContainerProps {
  children: React.ReactNode;
  title: string;
  subTitle?: String;
}

const NblPreviewContainer: React.FC<NblPreviewContainerProps> = ({ children, title, subTitle }) => {
  const theme = useTheme<NebulaTheme>();
  return (
    <NblBorderContainer>
      <NblFlexContainer direction="column" alignItems="center" justifyContent="center" margin="20px">
        <NblTypography variant="h2" weight={'bold'} color={'shade1'}>{title}</NblTypography>
        <NblTypography variant="h5" weight={'light'} color={'shade1'}>{subTitle}</NblTypography>
      </NblFlexContainer>
      <NblDivider
        orientation="horizontal"
        length="100%"
        borderRadius={1}
        color={theme.palette.nbldivider.variant3}
        opacity={0.4}
        strokeWidth={0.3}
      />
      <NblFlexContainer>{children}</NblFlexContainer>
    </NblBorderContainer>
  );
};
export default NblPreviewContainer;
