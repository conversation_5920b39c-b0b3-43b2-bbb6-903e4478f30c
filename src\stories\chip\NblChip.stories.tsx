//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NblChip from 'sharedComponents/NblChip';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';

type StoryProps = ComponentProps<typeof NblChip>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Chip/NblChip',
  component: NblChip,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { type: 'string' },
    color: { control: 'radio', options: ['primary', 'secondary', 'info', 'success', 'warning', 'error', 'default'], type: 'string' },
    id: { type: 'string' },
    clickable: { type: 'boolean' },
    onClick: { type: 'function', action: 'onClick' },
    borderRadius: ['sm', 'lg'],
    extracolor: {
      control: 'radio',
      options: ['primary'],
    },
  },
};

export default meta;

export const Chip: Story = {
  args: {
    label: 'Network',
    id: 'chip',
    color: 'primary',
    clickable: false,
    borderRadius: 'sm',
    disabled: false
  },
  render: (args) => (
    <NebulaTheme>
      <NblChip {...args} />
    </NebulaTheme>
  ),
};
