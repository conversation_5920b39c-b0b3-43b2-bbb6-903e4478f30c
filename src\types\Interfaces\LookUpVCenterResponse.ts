export default interface LookUpVCenterResponse {
  submitted: {
    hostname: string;
    datacenterId: string;
    vCenterHost: string;
    vCenterName: string;
    cloudId: string;
    cloudName: string;
    domain: string;
    vCenterPassword: string;
    vCenterPort: number;
    vCenterProtocol: string;
    vCenterUser: string;
    cloudDatacenter: string;
  };
  apiResponse: {
    cloudDetails: CloudDetailsData[];
  };
}

export interface CloudData {
  cloudDetails: Array<{
    cloudId: string;
    cloudName: string;
  }>;
}
export interface CloudDetailsData {
  cloudId: string;
  cloudName: string;
  vcenterHost: string;
  clusters: Cluster[];
  hosts: HostDetails[];
  datastore: Datastore[];
  networks: Network[];
  osLayouts: OSLayout[];
}
export interface LookupPayload {
  cloudDatacenter: string;
  domain: string;
  vCenterHost: string;
  vCenterPassword: string;
  vCenterPort: string;
  vCenterProtocol: string;
  vCenterUser: string;
  vCenterName: string;
}

export interface Cluster {
  clusterMor: string;
  type?: string;
  clusterStatus: string | null;
  drsEnabled: boolean | null;
  haEnabled: boolean;
  hostCount: number;
  hosts: string[];
  name: string;
  restricted: boolean;
  vmCount: number;
  disabled: boolean;
}

export interface Datastore {
  datastoreMor: string;
  name: string;
  type: string;
  freeSpace: number;
  capacity: number;
  disabled: boolean;
}

export type HostDetails = {
  hostMor: string;
  name: string;
  connectionState: string;
  powerState: string;
  disabled: boolean;
  location: string;
  vendor: string;
  model: string;
  cpu: number;
  core: number;
  memory: number;
};

export type Network = {
  dhcp: boolean;
  disabled: boolean;
  dnsDomain: string;
  ipv4DhcpServer: string | null;
  ipv4DnsPrimary: string;
  ipv4DnsSecondary: string;
  ipv4Enabled: boolean;
  ipv4Gateway: string;
  ipv4Subnet: string;
  ipv6DhcpServer: string;
  ipv6DnsPrimary: string;
  ipv6DnsSecondary: string;
  ipv6Enabled: boolean;
  ipv6Gateway: string;
  ipv6Subnet: string;
  name: string;
  networkMor: string;
  type: string;
};

export type OSLayout = {
  disabled: boolean;
  imageName: string;
  imagePath: string | null;
  layoutMor: string;
  layoutName: string;
  osName: string;
  restricted: boolean;
  shortName: string;
};

export type VCenterDetails = {
  hostname: string;
  port: number;
  username: string;
  password: string;
};
