import { useEffect, useState } from 'react';
import { getProjectData } from 'api/static-data';
import Catalog from 'components/Catalog';
import { CatalogTilesData } from 'types';

export default function Project() {
  const [content, setContent] = useState<CatalogTilesData[]>([]);
  useEffect(() => {
    async function getCatalogItems() {
      try {
        const data = await getProjectData();
        console.log(data);
        setContent(data);
      } catch (error) {
        console.log(error);
      }
    }
    getCatalogItems();
  }, []);

  return content?.length ? <Catalog catalogItems={content} isAdminTiles /> : <div>No items to display</div>;
}
