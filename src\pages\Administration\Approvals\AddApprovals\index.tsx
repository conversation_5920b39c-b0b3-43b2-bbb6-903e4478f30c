import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import AddApprovalForm from 'components/Administration/AddApprovalForm';
import useNblNavigate from 'hooks/useNblNavigate';

const AddApprovals = () => {
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const navigateToApprovalDetails = () => {
    navigate('/administration/approvals');
  };

  return <AddApprovalForm title="Add Approval" permissions={{}} onClose={navigateToApprovalDetails} />;
};

export default AddApprovals;
