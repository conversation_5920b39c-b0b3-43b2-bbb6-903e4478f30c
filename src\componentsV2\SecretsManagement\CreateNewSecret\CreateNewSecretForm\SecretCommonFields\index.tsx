import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';

import { useApiService } from 'api/ApiService/context';
import { showSpinner } from 'store/reducers/spinner';

export type SecretCommonFieldsData = {
  project: string;
  application: string;
  environment: string;
  domain: string;
  namespaceName: string;
};

type Fields = Array<{
  title: string;
  value: string | React.ReactElement;
  span?: number;
}>;

const useSecretCommonFields = (updateNamespace?: (namespaceName: string, namespacePath: string) => void) => {
  //Hooks
  const { apiAssetService } = useApiService();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const { resourceId } = useParams();

  //States
  const [fields, setFields] = useState<Fields>([
    { title: 'Domain', value: '' },
    { title: 'Project', value: '' },
    { title: 'Application', value: '' },
    { title: 'Environment', value: '' },
    { title: 'Namespace Name', value: '' },
  ]);

  //Side effects
  useEffect(() => {
    dispatch(showSpinner({ id: 'Fetching resource details', status: true, message: 'Loading resource details...' }));
    const filter = JSON.stringify({
      resourceId: {
        contains: resourceId,
      },
    });
    apiAssetService
      .getMyResourcesv2(1, 1, '', filter)
      .then((res) => {
        if (res.status) {
          const asset = res.data.items[0];
          updateNamespace?.(asset.resourceName || '', asset.resourcesDetails?.data?.path || '');
          setFields([
            { title: 'Domain', value: asset.platformContext.domainName },
            { title: 'Project', value: asset.projectName },
            { title: 'Application', value: asset.platformContext.applicationName },
            { title: 'Environment', value: asset.platformContext.environmentName },
            { title: 'Namespace Name', value: asset.resourceName || '' },
          ]);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: 'Fetching resource details', status: false, message: '' }));
      });
  }, [apiAssetService, resourceId, searchParams.get('metadata')]);

  //JSX
  return fields;
};

export default useSecretCommonFields;
