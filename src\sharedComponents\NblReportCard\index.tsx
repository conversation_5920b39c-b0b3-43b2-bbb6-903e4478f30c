import React from 'react';
import { useTheme } from '@mui/material';

import NblTypography from 'sharedComponents/NblTypography';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { NebulaTheme } from 'NebulaTheme/type';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { ReportCardPalette } from 'NebulaTheme/type/reportCardPalette';
import { StyledArrowIcon } from './styled';
import { getHexColorWithOpacity } from 'utils/common';
import useMediaQuery from 'hooks/useMediaQuery';

export interface NblReportCardProps {
  value: string | number;
  prevValue: NblReportCardProps['value'];
  label: string;
  chip: {
    arrow: 'up' | 'down';
    label: string | number;
    color: keyof ReportCardPalette['reportCard']['chipVariant'];
  };
  width?: string;
}
const NblReportCard: React.FC<NblReportCardProps> = ({ value, prevValue, label, chip, width }) => {
  //Hooks
  const { reportCard } = useTheme<NebulaTheme>().palette;
  const styles = useMediaQuery({ width: '324px', height: '118px', padding: '16px' }, { width: '220px', height: '100px', padding: '8px' });

  //JSX
  return (
    <NblBorderContainer
      width={width || styles.width}
      height={styles.height}
      backgroundColor={reportCard.cardBg}
      border={`1px solid ${reportCard.cardBorder}`}
    >
      <NblFlexContainer direction="column" justifyContent="space-between" padding={styles.padding}>
        <NblFlexContainer height="auto" justifyContent="space-between" alignItems="center">
          <NblTypography variant="h2" weight="bold" color={reportCard.cardText}>
            {value}
          </NblTypography>
          <NblFlexContainer
            width="68px"
            height="26px"
            spacing={0.5}
            backgroundColor={getHexColorWithOpacity(reportCard.chipVariant[chip.color].bg, 50)}
            borderRadius="13px"
            border={`1ps solid ${reportCard.chipVariant[chip.color].bg}`}
            center
          >
            <StyledArrowIcon arrowColor={reportCard.chipVariant[chip.color].arrow} direction={chip.arrow} />
            <NblTypography variant="subtitle2" weight="medium" color={reportCard.chipVariant[chip.color].text}>
              {chip.label}
            </NblTypography>
          </NblFlexContainer>
        </NblFlexContainer>
        <NblFlexContainer height="auto" direction="column" spacing={0}>
          <NblTypography variant="subtitle1" weight="medium" color={reportCard.cardText}>
            {label}
          </NblTypography>
          <NblTypography variant="body1" weight="regular" color={reportCard.cardText}>
            vs previous= {prevValue}
          </NblTypography>
        </NblFlexContainer>
      </NblFlexContainer>
    </NblBorderContainer>
  );
};

export default NblReportCard;
