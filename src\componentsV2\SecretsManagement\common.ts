// eslint-disable-next-line
import SecretsManagementService from '../../api/ApiService/SecretsManagementService';

export async function decryptPassword(encryptedPassword: string, apiSecretsManagement: SecretsManagementService) {
  try {
    const response = await apiSecretsManagement.createDecryptedPassword({
      encryptedPassword,
    });
    return response.data.decryptedPassword;
  } catch (error) {
    return 'Decryption failed for secret';
  }
}

export async function encryptPassword(password: string, apiSecretsManagement: SecretsManagementService) {
  try {
    const response = await apiSecretsManagement.createEncryptedPassword({
      password,
    });
    return response.data.encryptedPassword;
  } catch (error) {
    return 'Encryption failed for secret';
  }
}
