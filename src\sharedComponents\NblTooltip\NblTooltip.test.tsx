import { act, render } from '@testing-library/react';

import NblTypography from 'sharedComponents/NblTypography';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblTooltip from '.';

describe('NblTooltip component', () => {
  const props = {
    tooltipMessage: 'Please connect nebula admin',
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblTooltip {...props}><NblTypography variant='h3'>Info</NblTypography></NblTooltip>
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
