// eslint-disable-next-line
import { ApexOptions } from 'apexcharts';
import { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';

interface DonutChartProps {
  data: { id: string; label: string; value: number }[];
  onSliceClick: (index: number) => void;
}

const DonutChart: React.FunctionComponent<DonutChartProps> = ({ data, onSliceClick }) => {
  const [version, setVersion] = useState(0);
  const series = data?.map((item) => item.value);
  const totalCount = data.reduce((acc, item) => {
    return acc + item.value;
  }, 0);
  const labels = data?.map((item) => item.label);

  useEffect(() => {
    setVersion((prev) => prev + 1);
  }, [data]);

  const options: ApexOptions = {
    labels,
    chart: {
      events: {
        dataPointSelection: (event: any, context: any, dataPoint: { dataPointIndex: number }) => {
          setTimeout(() => {
            onSliceClick(dataPoint.dataPointIndex);
          }, 1);
        },
      },
    },
    legend: {
      position: 'bottom',
      horizontalAlign: 'left',
    },

    fill: {
      type: 'solid',
    },
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      y: {
        formatter: function (val: number) {
          const requestCount = ((val / totalCount) * 100).toFixed(1);
          return ` ${requestCount}% | Request Count: ${val}`;
        },
      },
    },
    noData: {
      align: 'center',
      verticalAlign: 'middle',
      text: 'No Data Found',
    },
  };
  return <Chart key={version} options={options} series={series} type="donut" width={200} height={400} />;
};

export default DonutChart;
