import { Typography, styled } from '@mui/material';

import { NebulaTheme } from 'NebulaTheme/type';
import { ApprovalStatus } from 'types/Enums';

interface StyledRequestStatusProps {
  theme?: NebulaTheme;
  status: ApprovalStatus;
}

export const StyledRequestStatus = styled('div')<StyledRequestStatusProps>(({ status, theme }) => {
  const { palette } = theme;
  return {
    display: 'inline-block',
    width: '11px',
    height: '11px',
    borderRadius: '50%',
    flex: '0 0 auto',
    ...(status === ApprovalStatus.PENDING && {
      backgroundColor: palette.info.main,
    }),
    ...((status === ApprovalStatus.APPROVED || status === ApprovalStatus.AUTO_APPROVED) && {
      backgroundColor: palette.success.main,
    }),
    ...(status === ApprovalStatus.REJECTED && {
      backgroundColor: palette.error.main,
    }),
  };
});

export const StyledDateTypography = styled(Typography)<{
  theme?: NebulaTheme;
}>(({ theme }) => {
  const { palette } = theme;
  return {
    color: palette.typography.shade5,
  };
});
