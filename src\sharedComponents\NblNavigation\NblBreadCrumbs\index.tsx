import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import React from 'react';
import { StyledLink, StyledBreadCrumbs } from './styled';
import useNblNavigate from 'hooks/useNblNavigate';

export interface NblBreadcrumbItem {
  label: string;
  route: string;
}

export interface NblBreadCrumbsProps {
  seperator?: 'arrow';
  breadCrumbs: NblBreadcrumbItem[];
  onClick?: (items: NblBreadcrumbItem, index: number) => void;
}

const NblBreadCrumbs: React.FC<NblBreadCrumbsProps> = ({ seperator = 'arrow', breadCrumbs, onClick }) => {
  //Hooks
  const navigate = useNblNavigate();

  //Utils
  const handleClick = (route: string) => {
    navigate(route);
  };

  //Renders
  const renderSeperator = () => {
    switch (seperator) {
      case 'arrow':
        return <NavigateNextIcon fontSize="small" />;
    }
  };

  const renderBreadCrumbs = () => {
    const lastBreadCrumb = breadCrumbs.length - 1;
    return breadCrumbs.map((breadCrumb, index) => (
      <StyledLink
        variant={index === lastBreadCrumb ? 'body3' : 'button'}
        underline="none"
        key={breadCrumb.label}
        onClick={() => {
          if (onClick) {
            onClick(breadCrumb, index);
          } else {
            handleClick(breadCrumb.route);
          }
        }}
      >
        {breadCrumb.label}
      </StyledLink>
    ));
  };

  //JSX
  return (
    <StyledBreadCrumbs separator={renderSeperator()} aria-label="nblBreadcrumb">
      {renderBreadCrumbs()}
    </StyledBreadCrumbs>
  );
};

export default NblBreadCrumbs;
