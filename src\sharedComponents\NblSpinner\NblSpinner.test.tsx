import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblSpinner, { NblSpinnerProps } from '.';

describe('NblSpinner component', () => {
  const props: NblSpinnerProps = {
    spinnerData: [{ id: '123', message: 'Test Loading', status: true }],
  };
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblSpinner {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
