import { render, screen, fireEvent, act } from '@testing-library/react';
import CreatePasswordPolicyForm from '.';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { useApiService } from 'api/ApiService/context';
import { createMemoryHistory } from 'history';
import { Router } from 'react-router-dom';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { Provider } from 'react-redux';

jest.mock('sharedComponents/NblContainers/NblFormContainer');
jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

jest.mock(
  'componentsV2/SecretsManagement/CreateNewSecret/CreateNewSecretForm/SecretCommonFields',
  () => ({
    __esModule: true,
    default: () => [
      { title: 'Secret Type', value: 'Text' },
      { title: 'Owner', value: 'Admin' },
    ],
  })
);

const mockStore = configureMockStore([thunk]);
const store = mockStore();
const mockDispatch = jest.fn();
const mockGetMyResourcesv2 = jest.fn();

describe('CreatePasswordPolicyForm Component', () => {
  const mockNblFormValues = {
    policyname: '',
    totalchars: 0,
    smallAlphabets: 0,
    bigAlphabets: 0,
    splChars: '',
    noOfSplChars: 0,
    numbers: 0,
    description: '',
  };

  const mockNblFormProps = {
    values: mockNblFormValues,
    touched: {},
    errors: {},
    handleChange: jest.fn(),
    handleBlur: jest.fn(),
    handleSubmit: jest.fn(),
    setFieldValue: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    (useNblForms as jest.Mock).mockReturnValue({
      nblFormValues: mockNblFormValues,
      nblFormProps: mockNblFormProps,
    });

    (useApiService as jest.Mock).mockReturnValue({
      apiAssetService: {
        getMyResourcesv2: mockGetMyResourcesv2,
      },
    });
  });

  test('renders the password policy fields and common fields', async () => {
    const history = createMemoryHistory();
    store.dispatch = mockDispatch;

    await act(async () => {
      render(
        <Provider store={store}>
          <Router location={history.location} navigator={history}>
            <NebulaThemeProvider>
              <CreatePasswordPolicyForm setFormInitialValues={() => {}} />
            </NebulaThemeProvider>
          </Router>
        </Provider>
      );
    });

    // Check static fields
    expect(screen.getByText('Policy Name')).toBeInTheDocument();
    expect(screen.getByText('Password Length')).toBeInTheDocument();
    expect(screen.getByText('Minimum Lower Case')).toBeInTheDocument();
    expect(screen.getByText('Minimum Upper Case')).toBeInTheDocument();
    expect(screen.getByText('Allowed Special Characters')).toBeInTheDocument();
    expect(screen.getByText('Minimum Special Characters')).toBeInTheDocument();
    expect(screen.getByText('Minimum Numerals')).toBeInTheDocument();
    expect(screen.getByText('Policy Description')).toBeInTheDocument();

    // Check custom/common fields
    expect(screen.getByText('Secret Type')).toBeInTheDocument();
    expect(screen.getByText('Text')).toBeInTheDocument();
    expect(screen.getByText('Owner')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
  });

    test('displays validation errors when fields are touched and have errors', () => {
    const fieldErrors = {
        policyname: 'Policy Name is required',
        totalchars: 'Total characters required',
        smallAlphabets: 'Small alphabets required',
        bigAlphabets: 'Big alphabets required',
        splChars: 'Special characters are required',
        noOfSplChars: 'Special characters count is required',
        numbers: 'Numbers count is required',
        description: 'Description is required',
    };

    const fieldTouched = {
        policyname: true,
        totalchars: true,
        smallAlphabets: true,
        bigAlphabets: true,
        splChars: true,
        noOfSplChars: true,
        numbers: true,
        description: true,
    };

    (useNblForms as jest.Mock).mockReturnValue({
        nblFormValues: { ...mockNblFormValues },
        nblFormProps: {
        ...mockNblFormProps,
        errors: fieldErrors,
        touched: fieldTouched,
        },
    });

    const history = createMemoryHistory();

    render(
        <Provider store={store}>
        <Router location={history.location} navigator={history}>
            <NebulaThemeProvider>
            <CreatePasswordPolicyForm setFormInitialValues={() => {}} />
            </NebulaThemeProvider>
        </Router>
        </Provider>
    );

    // Assert all validation error messages
    Object.values(fieldErrors).forEach((msg) => {
        expect(screen.getByText(msg)).toBeInTheDocument();
    });
    });

      test('calls setFieldValue when counter fields are changed', async () => {
    const mockSetFieldValue = jest.fn();
    const history = createMemoryHistory();

    (useNblForms as jest.Mock).mockReturnValue({
      nblFormValues: mockNblFormValues,
      nblFormProps: {
        ...mockNblFormProps,
        setFieldValue: mockSetFieldValue,
      },
    });

    await act(async () => {
      render(
        <Provider store={store}>
          <Router location={history.location} navigator={history}>
            <NebulaThemeProvider>
              <CreatePasswordPolicyForm setFormInitialValues={() => {}} />
            </NebulaThemeProvider>
          </Router>
        </Provider>
      );
    });

    // Simulate changes for each field
    const changeValue = (labelText: string, name: keyof typeof mockNblFormValues, newValue: number) => {
      const input = screen.getByLabelText(labelText);
      fireEvent.change(input, { target: { value: newValue } });
      expect(mockSetFieldValue).toHaveBeenCalledWith(name, newValue);
    };

    changeValue('Password Length', 'totalchars', 10);
    changeValue('Minimum Lower Case', 'smallAlphabets', 5);
    changeValue('Minimum Upper Case', 'bigAlphabets', 4);
    changeValue('Minimum Special Characters', 'noOfSplChars', 3);
    changeValue('Minimum Numerals', 'numbers', 2);
  });


});