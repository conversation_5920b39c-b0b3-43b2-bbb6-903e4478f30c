import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

const catalogPermissionsSchema = yup.object().shape({
  catalogId: yup.string().required('Catalog Name is required'),
  domainId: yup
    .string()
    .nullable(true)
    .test('not-empty', 'Domain is required', (value) => value === null || Boolean(value)),
  roleId: yup.string().required('Role is required'),
});

export const catalogAccessRequestSchema = yup.object().shape({
  groupId: yup.string().required('Group Name is required'),
  emailDistribution: yup.array().min(1, 'Selected Group Name should have at least one Email DL'),
  description: yup
    .string()
    .trim()
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage)
    .required('Max 500 words'),
  catalogPermissions: yup
    .array()
    .of(catalogPermissionsSchema)
    .required('At least one catalog permission record is required')
    .min(1, 'At least one catalog permission record is required')
    .test('unique-permission-combo', 'Duplicate permissions are not allowed', (permissions) => {
      if (!permissions) {
        return true;
      } else {
        const permissionSet = new Set(permissions.map(({ catalogId, domainId, roleId }) => `${catalogId}-${domainId}-${roleId}`));
        return permissionSet.size === permissions.length;
      }
    }),
});
