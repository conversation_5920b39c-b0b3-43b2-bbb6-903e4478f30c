import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
//eslint-disable-next-line no-unused-vars
import UserService from 'api/ApiService/UserService';

export interface User {
  userDetails: {
    accessToken: string;
    email: string;
    firstName: string;
    lastName: string;
    isApprover: boolean;
    userId: string;
    isAdmin: boolean;
    favoriteTools: {
      _id: string;
      name: string;
      description: string;
      shortName: string;
      icon: string;
      component: string;
      enabled: boolean;
      formLink: string;
    }[];
  };
}

// initial state
const initialState: User = {
  userDetails: {
    accessToken: '',
    email: '',
    firstName: '',
    lastName: '',
    isApprover: false,
    userId: '',
    isAdmin: false,
    favoriteTools: [],
  },
};

// ==============================|| SLICE - User ||============================== //

const user = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserDetails(state, action) {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },
  },
  extraReducers: (builder) => {
    builder.addCase(initUserDataAsyncThunk.fulfilled, (state, action) => {
      state.userDetails = { ...state.userDetails, ...action.payload.data };
    });
  },
});

export const initUserDataAsyncThunk = createAsyncThunk('user/setInitUserData', async (apiUserService: UserService) => {
  return await apiUserService.getUserProfile();
});

export default user.reducer;

export const { setUserDetails } = user.actions;
