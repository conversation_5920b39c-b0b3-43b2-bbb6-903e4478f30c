import { act, render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import AdminPermission from '.';
import ThemeProvider from 'mock/ThemeProvider';
import { AdminTilePermissions } from 'mock/Groups';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});

describe('Render Admin tile  Permission form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  const handleData = jest.fn();

  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <AdminPermission
              open={true}
              onClose={handleClose}
              onSuccess={handleSuccess}
              adminTilePermissions={AdminTilePermissions}
              selectedAdminPermissions={[]}
              setAdminData={handleData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(screen.getByText('Admin Tile *')).toBeInTheDocument();
    expect(screen.getByText('Admin Roles *')).toBeInTheDocument();
    const saveButton = getByText('Save');
    const cancelButton = getByText('Cancel');
    expect(saveButton).toBeEnabled();
    expect(cancelButton).toBeEnabled();
  });

  test('displays validation error if no admin tile is selected', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <AdminPermission
              open={true}
              onClose={handleClose}
              onSuccess={handleSuccess}
              adminTilePermissions={AdminTilePermissions}
              selectedAdminPermissions={[]}
              setAdminData={handleData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    fireEvent.click(getByText('Save'));
    waitFor(() => {
      expect(getByText('Admin is required')).toBeInTheDocument();
      expect(getByText('Please select atleast one admin role')).toBeInTheDocument();
    });
  });
});
