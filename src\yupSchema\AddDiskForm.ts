import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

const AddDiskFormSchema = (isVmTypeWindows: boolean) => {
  const maxDiskValue = Number(process.env.REACT_APP_MAX_DISK_VALUE) || 1024;
  const minDiskValue = Number(process.env.REACT_APP_MIN_DISK_VALUE) || 10;
  return yup.object().shape({
    diskCount: yup
      .number()
      .transform((value) => (value === '' || value === 0 ? undefined : value))
      .required('Disk Count is required')
      .typeError('Disk Count must be a number'),
    diskFileSystem: yup.string().when({
      is: () => !isVmTypeWindows,
      then: yup.string().required('Disk File System is required'),
      otherwise: yup.string().nullable(),
    }),
    addDisks: yup
      .array()
      .of(
        yup.object().shape({
          diskValue: yup
            .number()
            .required('Disk Value is required')
            .min(minDiskValue, `Minimum disk value should be be ${minDiskValue} GB`)
            .max(maxDiskValue, `maximum value must be ${maxDiskValue} GB`)
            .typeError('DiskValue must be a number')
            .transform((value, originalValue) => {
              if (originalValue === '' || isNaN(originalValue)) return null;
              return Number(value);
            })
            .nullable(),
          diskName: yup
            .string()
            .trim()
            .max(255, 'string must be at max 255 character')
            .required('Disk Name is required')
            .matches(yupMatchesParams.alphaNumericWithHyphenAndSlash.pattern, yupMatchesParams.alphaNumericWithHyphenAndSlash.errorMessage),
        })
      )
      .test('unique-disk-name', 'Disk Mount Points must be unique', function (addDisks) {
        if (addDisks && addDisks.length > 1) {
          const diskNames = addDisks?.map((disk) => disk.diskName);
          const uniqueDiskNames = new Set(diskNames);
          if (uniqueDiskNames.size !== diskNames?.length) {
            return this.createError({
              message: 'Disk Mount Point must be unique',
              path: `${this.path}[${addDisks.findIndex(
                (disk) => diskNames.indexOf(disk.diskName) !== diskNames.lastIndexOf(disk.diskName)
              )}].diskName`,
            });
          }
        }
        return true;
      }),
  });
};

export default AddDiskFormSchema;
