import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblStepper from '.';

describe('NblStepper component', () => {
  const props = {
    steps: [
      { icon: 'CloudOutlined', title: 'VM Details', caption: 'Enter the VM details', status: 'current', errorFields: [] },
      { icon: 'CloudOutlined', title: 'VM Details', caption: 'Enter the VM details', status: 'pending', errorFields: [] },
      { icon: 'CloudOutlined', title: 'VM Details', caption: 'Enter the VM details', status: 'pending', errorFields: [] },
      { icon: 'CloudOutlined', title: 'VM Details', caption: 'Enter the VM details', status: 'error', errorFields: [] },
    ],
    wrap: true,
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            {/* @ts-ignore */}
            <NblStepper {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
