import { useTheme } from '@mui/material';
import icons from 'assets/images/icons';
import { NebulaTheme } from 'NebulaTheme/type';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblDivider from 'sharedComponents/NblDivider';
import { StyledTypography } from './styled';

export interface NblStepperProps {
  steps: {
    icon: string;
    title: string;
    caption: string;
    status: 'completed' | 'pending' | 'error' | 'current';
    errorFields: string[];
  }[];
  wrap?: boolean;
  onStepClick?: (index: number) => void;
}

const NblStepper: React.FC<NblStepperProps> = ({ steps, wrap = true, onStepClick }) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();

  //Local
  const PALETTE = theme.palette.nblStepper;

  //Renders
  const renderIcon = (icon: string, color: string) => {
    /* @ts-ignore */
    const Icon = icons[icon];
    if (!Icon) return null;
    //JSX
    return (
      <Icon
        data-testid="mui-icon"
        style={{
          fontSize: '1.5rem',
          color,
        }}
      />
    );
  };

  const renderDivider = (index: number) => {
    if (steps.length <= index + 1) return null;
    //Jsx
    return <NblDivider length="130px" color={theme.palette.nbldivider.variant3} borderRadius={2} opacity={0.4} strokeWidth={0.3} />;
  };

  //Jsx
  return (
    <NblFlexContainer spacing={3} {...(wrap && { wrap: 'wrap' })}>
      {steps.map((step, index) => (
        <NblFlexContainer alignItems="center" width="auto" key={index} cursor={'pointer'} onClick={() => onStepClick?.(index)}>
          <NblFlexContainer center borderRadius="50%" backgroundColor={PALETTE[step.status].iconBg} width="52px" height="52px">
            {renderIcon(step.icon, PALETTE[step.status].icon)}
          </NblFlexContainer>
          <NblFlexContainer direction="column" justifyContent="center" width="calc(100% - 57px)">
            <NblFlexContainer alignItems="center" spacing={3}>
              <StyledTypography variant="subtitle2" color={PALETTE[step.status].title}>
                {step.title}
              </StyledTypography>
              {renderDivider(index)}
            </NblFlexContainer>
            <StyledTypography variant="body3" color={PALETTE[step.status].caption}>
              {step.caption}
            </StyledTypography>
          </NblFlexContainer>
        </NblFlexContainer>
      ))}
    </NblFlexContainer>
  );
};

export default NblStepper;
