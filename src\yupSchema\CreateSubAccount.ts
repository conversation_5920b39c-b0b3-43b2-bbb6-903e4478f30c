import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';
import { Address4 } from 'ip-address';
import { matchIsValidTel } from 'mui-tel-input';

const isValidIpv4NetworkAddress = (value: any) => {
  if (!Address4.isValid(value)) {
    return 'Enter a valid IP';
  }
  const ipObj = new Address4(value);
  if (!ipObj.address.includes('/')) {
    return 'Mask must be added';
  }
  const startIp = ipObj.startAddress();
  if (startIp.addressMinusSuffix !== ipObj.addressMinusSuffix) {
    return 'Invalid network address';
  }

  return true;
};

const isValidAwsAccountName = (value: string) => {
  const minLength = 1;
  const maxLength = 12;
  const allowedChars = /^[a-zA-Z0-9.+,=@-]+$/;

  if (value?.length < minLength || value?.length > maxLength) {
    return `Account Name must be between ${minLength} and ${maxLength} characters`;
  }
  if (value.trim() !== value) {
    return 'No Leading or Trailing Spaces allowed';
  }
  if (!allowedChars.test(value)) {
    return 'Allowed special characters are + = . , @ -';
  }
  return true;
};
const accessRecordschema = yup.object().shape({
  userEmail: yup.string().required('Email is required').email('Please enter valid email'),
  pid: yup
    .string()
    .required('PID is required')
    .matches(yupMatchesParams.spaceNotAllowed.pattern, yupMatchesParams.spaceNotAllowed.errorMessage),
  name: yup.string().required('Name is required'),
  role: yup
    .string()
    .oneOf(['admin', 'power-user', 'read-user'], 'Allowed values are admin, power-user, read-user')
    .required('Role is required'),
});
export const CreateSubAccountSchema = (isReadOnlyMode: boolean | undefined) =>
  yup.object().shape({
    organization: yup.string().required('Organization is required'),
    accountName: yup
      .string()
      .required('Account name is required')
      .test('is-valid-aws-name', 'Please enter valid account name', function (value) {
        if (!value) {
          return true;
        }
        const validationResult = isValidAwsAccountName(value);
        if (validationResult !== true) {
          return this.createError({ message: validationResult });
        }
        return true;
      }),
    accountDescription: yup
      .string()
      .required('Account description is required')
      .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),

    accountOwner: yup
      .string()
      .required('Account Owner is required')
      .matches(yupMatchesParams.firstandLastName.pattern, yupMatchesParams.firstandLastName.errorMessage),
    accountOwnerEmail: yup.string().required('Account Owner Email is required').email('Please enter valid email'),
    environment: yup.string().required('Environment is required'),
    costCenterID: yup
      .string()
      .required('Cost Center ID is required')
      .matches(yupMatchesParams.costCenter.pattern, yupMatchesParams.costCenter.errorMessage),
    evName: yup
      .string()
      .required('EVP Name is required')
      .matches(yupMatchesParams.firstandLastName.pattern, yupMatchesParams.firstandLastName.errorMessage),
    vpName: yup
      .string()
      .required('VP Name is required')
      .matches(yupMatchesParams.firstandLastName.pattern, yupMatchesParams.firstandLastName.errorMessage),
    vpEmail: yup.string().required('VP Email is required').email('Please enter valid email'),

    operationsName: yup.string().required('Name is required'),
    operationsEmail: yup.string().required('Email is required').email('Please enter valid email'),
    operationsTitle: yup.string().required('Title is required'),
    operationsPhone: yup
      .string()
      .required('Phone no is required')
      .test('is-valid-phone', 'Please enter valid phone number', (value) => {
        return typeof value === 'string' && matchIsValidTel(value);
      }),

    billingName: yup.string().required('Name is required'),
    billingEmail: yup.string().required('Email is required').email('Please enter valid email'),
    billingTitle: yup.string().required('Title is required'),
    billingPhone: yup
      .string()
      .required('Phone no is required')
      .test('is-valid-phone', 'Please enter valid phone number', (value) => {
        return typeof value === 'string' && matchIsValidTel(value);
      }),
    securityName: yup.string().required('Name is required'),
    securityEmail: yup.string().required('Email is required').email('Please enter valid email'),
    securityTitle: yup.string().required('Title is required'),
    securityPhone: yup
      .string()
      .required('Phone no is required')
      .test('is-valid-phone', 'Please enter valid phone number', (value) => {
        return typeof value === 'string' && matchIsValidTel(value);
      }),

    vpcDescription: yup
      .string()
      .required('Network Description is required')
      .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
    vpcRequirement: yup.string().required('VPC is required'),
    onPremCon: yup.string().required('Onprem connectivity required'),
    eksSubnet: yup.string().required('EKS subnet is required'),
    vpcBASE: yup
      .string()
      .test('valid-ipv4-address', 'Please enter valid IPv4 address', function (value) {
        if (!value) {
          return true;
        }
        const validationResult = isValidIpv4NetworkAddress(value);
        if (validationResult !== true) {
          return this.createError({ message: validationResult });
        }
        return true;
      })
      .when([], {
        is: () => isReadOnlyMode,
        then: (schema) => schema.required('VPC CIDR BASE is required'),
        otherwise: (schema) => schema,
      }),
    vpcGUA: yup.string().test('valid-ipv4-address', 'Please enter valid IPv4 address', function (value) {
      if (!value) {
        return true;
      }
      const validationResult = isValidIpv4NetworkAddress(value);
      if (validationResult !== true) {
        return this.createError({ message: validationResult });
      }
      return true;
    }),
    accessRecords: yup
      .array()
      .of(accessRecordschema)
      .min(1, 'At least one access record is required')
      .test('at-least-one-admin', 'At least one user must have the admin role', function (value) {
        const hasAdmin = value?.some((record: any) => record.role === 'admin');
        if (!hasAdmin) {
          //@ts-ignore
          return this.createError({ message: 'At least one user must have the admin role' });
        }
        return true;
      }),
  });
