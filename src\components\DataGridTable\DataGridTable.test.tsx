import { act, render, fireEvent } from '@testing-library/react';

import DataGridTable from '.';
import { PendingRequestColumns, PendingRequestRows } from 'mock/Requests';
import ThemeProvider from 'mock/ThemeProvider';

const tableTitle = 'Data grid table';

describe('Data Grid Table', () => {
  test('Should load table with title, refresh and reset filters button', async () => {
    const mockRefreshHandler = jest.fn();

    const { getByText, getByRole } = await act(async () =>
      render(
        <ThemeProvider>
          <DataGridTable
            loading={false}
            rows={PendingRequestRows}
            columns={PendingRequestColumns}
            title={tableTitle}
            refreshHandler={mockRefreshHandler}
          />
        </ThemeProvider>
      )
    );

    expect(getByText(tableTitle)).toBeInTheDocument();
    const refreshList = getByRole('button', { name: /Refresh List/i });
    fireEvent.click(refreshList);
    expect(mockRefreshHandler).toHaveBeenCalled();
  });

  test('Should display skeleton loader when data is loading', async () => {
    const mockRefreshHandler = jest.fn();

    const { queryByText } = await act(async () =>
      render(
        <ThemeProvider>
          <DataGridTable loading={true} rows={[]} columns={PendingRequestColumns} title={tableTitle} refreshHandler={mockRefreshHandler} />
        </ThemeProvider>
      )
    );

    expect(queryByText('No Record Found')).toBeNull();
  });

  test('Should display No Record Found when data is not available', async () => {
    const mockRefreshHandler = jest.fn();

    const { getByText, queryByText } = await act(async () =>
      render(
        <ThemeProvider>
          <DataGridTable
            loading={false}
            rows={[]}
            columns={PendingRequestColumns}
            title={'Table Heading'}
            refreshHandler={mockRefreshHandler}
          />
        </ThemeProvider>
      )
    );

    expect(getByText('No Record Found')).toBeInTheDocument();
    expect(queryByText('Filter returns no result')).toBeNull();
  });
});
