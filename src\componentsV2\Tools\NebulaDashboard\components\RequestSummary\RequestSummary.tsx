import { useEffect, useMemo, useState } from 'react';
import { StyledUMCard } from '../../styled';
import RequestSummaryRowData from '../RequestSummaryRowData/RequestSummaryRowData';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line
import { State } from 'store/reducers/type';
// eslint-disable-next-line
import { RequestSummaryByMonthResponse, RequestSummaryMonthlyData } from 'api/ApiService/type';
import { GENERATE_QUERY_LATEST_REQUEST, GENERATE_REQUEST_BY_MONTH_QUERY } from '../../queries';
import NblTypography from 'sharedComponents/NblTypography';
// eslint-disable-next-line no-unused-vars
import { ColumnData, NblTable } from 'sharedComponents/NblTable';
// eslint-disable-next-line no-unused-vars
import { GridRenderCellParams } from '@mui/x-data-grid';
import LastTenRequests from '../LastTenRequests/LastTenRequests';
import { updateRequestOverviewType } from 'store/reducers/requestOverview';
import NblBox from 'sharedComponents/NblContainers/NblBox';
import useNblNavigate from 'hooks/useNblNavigate';
import { addSummaryToCatalog } from '../../utils/utility';
// eslint-disable-next-line no-unused-vars
import { CatalogWithSummary } from '../../utils/types';
import { getTableData } from './utility';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { useApiService } from '../../../../../api/ApiService/context';
import NblBreadCrumbs, { NblBreadcrumbItem } from 'sharedComponents/NblNavigation/NblBreadCrumbs';
import CustomIconButton from 'components/CustomIconButton';
import { VisibilityOutlined } from '@mui/icons-material';
interface RequestSummaryProps {}

const RequestSummary: React.FunctionComponent<RequestSummaryProps> = () => {
  const [requestCountSummary, setRequestCountSummary] = useState([] as RequestSummaryMonthlyData[]);
  const {
    allCatalog1,
    allCatalog3,
    allCatalog4,
    allCatalog2,
    selectedCatalog1,
    selectedCatalog2,
    selectedCatalog3,
    selectedCatalog4,
    year,
    quarters,
    levelSelected,
    isFilterApplied,
  } = useSelector((state: State) => state.RequestFilterConfig);

  const [allRequestList, setAllRequestList] = useState<any>(null);
  // const [isLoading, setisLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any>([]);
  const [drillLevel, setDrillLevel] = useState(1);
  const [breadcrumbItems, setBreadcrumbItems] = useState<NblBreadcrumbItem[]>([{ label: 'Catalog-1', route: '' }]);
  const dispatch = useDispatch();
  const navigate = useNblNavigate();
  const { apiUsageMetricsService } = useApiService();

  const fetchRequestSummaryByMonth = async () => {
    const allCatalog1Names = selectedCatalogs[1].map(({ value }) => value);
    const allCatalog3Names = selectedCatalogs[3].map(({ value }) => value);
    const allCatalog4Names = selectedCatalogs[4].map(({ value }) => value);

    const payload = {
      query: GENERATE_REQUEST_BY_MONTH_QUERY(year, quarters, allCatalog1Names, allCatalog3Names, allCatalog4Names),
    };

    const response = await apiUsageMetricsService.customGraphQL<RequestSummaryByMonthResponse>(payload);
    const requestSummaryData: RequestSummaryMonthlyData[] = response.data.data.getRequestSummaryByMonth;
    setRequestCountSummary(requestSummaryData);
  };

  // UseEffect for API call
  useEffect(() => {
    if (allCatalog1.length) {
      fetchRequestSummaryByMonth();
      getLast10RequestData();
    }
  }, [year, quarters, selectedCatalog1, selectedCatalog2, selectedCatalog3, selectedCatalog4, allCatalog1]);

  const getLast10RequestData = async () => {
    const allCatalog1Names = selectedCatalogs[1].map(({ value }) => value);
    const allCatalog3Names = selectedCatalogs[3].map(({ value }) => value);
    const allCatalog4Names = selectedCatalogs[4].map(({ value }) => value);
    let requestTypeData: any = {};
    const payloadforPresentQuarter = {
      query: GENERATE_QUERY_LATEST_REQUEST(allCatalog1Names, allCatalog3Names, allCatalog4Names),
    };
    const request = await apiUsageMetricsService.getAllRequestsStatus(payloadforPresentQuarter);
    const allData = request?.data?.data?.getLastestRequestStatus;
    allData?.forEach((req) => {
      requestTypeData[req._id] = req.requests;
    });

    setAllRequestList(requestTypeData);
  };

  const selectedCatalogs: CatalogWithSummary[][] = useMemo(() => {
    const filteredCatalog1 = allCatalog1.filter((item) => selectedCatalog1.includes(item.id));
    const filteredCatalog2 = allCatalog2.filter((item) => selectedCatalog2.includes(item.id));
    const filteredCatalog3 = allCatalog3.filter((item) => selectedCatalog3.includes(item.id));
    const filteredCatalog4 = allCatalog4.filter((item) => selectedCatalog4.includes(item.id));
    return [[], filteredCatalog1, filteredCatalog2, filteredCatalog3, filteredCatalog4];
  }, [selectedCatalog1, selectedCatalog2, selectedCatalog3, selectedCatalog4, requestCountSummary, allRequestList]);

  const catalogWithSummary: CatalogWithSummary[][] = useMemo(() => {
    const Catalog4WithSummary = selectedCatalogs[4].map((item) => {
      const summary = requestCountSummary.find((res) => res.requestType === item.requestType)?.summary;
      const requestListByRequestType = allRequestList && item.requestType && allRequestList[item.requestType];
      const newItem = { ...item, summary, requestListByRequestType };
      return newItem;
    });
    const Catalog3WithSummary = addSummaryToCatalog(selectedCatalogs[3], Catalog4WithSummary);
    const Catalog2WithSummary = addSummaryToCatalog(selectedCatalogs[2], Catalog3WithSummary);
    const Catalog1WithSummary = addSummaryToCatalog(selectedCatalogs[1], Catalog2WithSummary);
    return [[], Catalog1WithSummary, Catalog2WithSummary, Catalog3WithSummary, Catalog4WithSummary];
  }, [selectedCatalogs]);

  useEffect(() => {
    if (levelSelected === 5) {
      setTableData(getTableData(catalogWithSummary, levelSelected - 1));
    } else {
      setTableData(getTableData(catalogWithSummary, levelSelected));
    }
    let newItems = [] as NblBreadcrumbItem[];
    for (let i = 1; i <= levelSelected; i++) {
      newItems.push({ label: 'Catalog-' + i, route: '' });
    }
    setBreadcrumbItems(newItems);
    setDrillLevel(levelSelected);
  }, [catalogWithSummary]);

  const navigateToDetails = (catalogData: any) => {
    dispatch(updateRequestOverviewType({ ...catalogData, year, quarters: quarters }));
    navigate('requestoverview');
  };

  const handleRowClick = (row: any) => {
    if (drillLevel < 4) {
      setDrillLevel((prev) => prev + 1);
      setTableData(getTableData(catalogWithSummary, drillLevel + 1).filter((item) => item.parentId === row.id));
      setBreadcrumbItems((prevItems) => [...prevItems, { label: 'Catalog-' + (drillLevel + 1), route: row.id }]);
    }
  };
  const handleBreadcrumbItemClick = (item: NblBreadcrumbItem, index: number) => {
    setDrillLevel(index + 1);
    if (index === 0 || isFilterApplied) {
      setTableData(getTableData(catalogWithSummary, index + 1));
    }
    if (item.route) {
      setTableData(getTableData(catalogWithSummary, index + 1).filter((it) => it.parentId === item.route));
    }
    setBreadcrumbItems((prevItems) => prevItems.slice(0, index + 1));
  };

  const columns: ColumnData[] = [
    {
      field: 'catalogName',
      headerName: 'Requests',
      flex: 1,
      renderCell(params: GridRenderCellParams) {
        const {
          row: { catalogName, catalogOneName },
        } = params;
        return (
          <NblBox>
            {catalogName}
            <NblTypography variant="subtitle1" weight="bold" color={'shade1'}>
              {catalogOneName}
            </NblTypography>
          </NblBox>
        );
      },
    },
    {
      field: 'allRequestMonthlyData',
      headerName: 'Request Trends',
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell(params: GridRenderCellParams) {
        const { row } = params;
        return <RequestSummaryRowData year={year} quarters={quarters} catalogData={row} />;
      },
    },
    {
      field: 'allRequests',
      headerName: 'Last 10 Requests',
      flex: 1,
      disableColumnMenu: true,
      sortable: false,
      renderCell(params: GridRenderCellParams) {
        const { row } = params;
        return (
          <NblFlexContainer>
            <NblBox margin={'7px 15px 0 0'}>
              <LastTenRequests catalogData={row} />
            </NblBox>
            <NblBox>
              <CustomIconButton
                tooltipMessage={''}
                iconButtonId={'view-icon'}
                icon={VisibilityOutlined}
                onClickHandler={() => navigateToDetails(row)}
              />
            </NblBox>
          </NblFlexContainer>
        );
      },
    },
  ];

  return (
    <StyledUMCard>
      <NblTypography variant="subtitle1" weight="bold" color={'shade1'}>
        Request Summary
      </NblTypography>
      <NblBreadCrumbs breadCrumbs={breadcrumbItems} onClick={handleBreadcrumbItemClick} />
      <NblFlexContainer>
        {tableData ? (
          <NblTable
            rows={tableData}
            columns={columns}
            serverPagination={false}
            rowSize={'5'}
            pageSizeOptions={['5', '10']}
            handleRequestRowClick={(row) => handleRowClick(row)}
            autoHeight={true}
          />
        ) : (
          'No Records Found'
        )}
      </NblFlexContainer>
    </StyledUMCard>
  );
};

export default RequestSummary;
