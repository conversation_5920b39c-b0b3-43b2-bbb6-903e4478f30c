import { render, act } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import CatalogTiles from '.';

const CatalogTilesItems = [
  {
    name: 'IPAM',
    description: 'IPAM is a suite of tools to plan, deploy, manage and monitor your IP address',
    path: '/IaaS/network/IPAM',
    icon: 'LanOutlined',
  },
];

describe('CatalogTiles component', () => {
  const mockStore = configureMockStore();
  const store = mockStore({ common: { exposureParams: [] } });

  test('Should render IPAM card', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <Router>
              <CatalogTiles name="network" items={CatalogTilesItems} />
            </Router>
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(getByText(CatalogTilesItems[0]['name'])).toBeInTheDocument();
    expect(getByText(CatalogTilesItems[0]['description'])).toBeInTheDocument();
  });
});
