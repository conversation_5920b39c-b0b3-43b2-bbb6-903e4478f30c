import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
// eslint-disable-next-line
import { Box, useMediaQuery, useTheme } from '@mui/material';
// eslint-disable-next-line no-unused-vars
import { GridColDef } from '@mui/x-data-grid';

import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import withAdminPermissions from 'hoc/withAdminPermissions';
import AdministrationService from 'api/ApiService/AdministrationService';
import AdminstrationAuthService from 'api/ApiService/AdminstrationAuthService';
import { ViewProjectPayload } from 'types';
import ActionsColumn from 'components/Administration/ActionsColumn';
import DataGridTable from 'components/DataGridTable';
// eslint-disable-next-line no-unused-vars
import { ComponentType, AdminGridProps } from 'types';
import { dateFormatter, getAdminColumnWidth } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'mantis/themes/type';

interface ViewProjectProps extends AdminGridProps {}

const ViewProject: React.FunctionComponent<ViewProjectProps> & ComponentType = ({ permissions }) => {
  const apiAdministrationService = new AdministrationService();
  const apiAdministrationAuthService = new AdminstrationAuthService();
  const [viewProjectData, setViewProjectData] = useState<ViewProjectPayload[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const theme: NebulaTheme = useTheme();
  const isSmaller = useMediaQuery(theme.breakpoints.down('xl'));
  const fetchProjectList = () => {
    setIsLoading(true);
    apiAdministrationService
      .getProject()
      .then((res) => {
        if (res.status) {
          setViewProjectData(
            res.data.map((viewData) => ({
              ...viewData,
              projectShortName: viewData.projectShortName,
              projectSetting: Object.keys(viewData?.projectSettings),
              id: viewData?.id,
              createdBy: viewData?.createdBy,
              createdAt: viewData?.createdAt ? dateFormatter(viewData.createdAt) : '',
              updatedAt: viewData?.updatedAt ? dateFormatter(viewData.updatedAt) : '',
              updatedBy: viewData?.updatedBy,
            }))
          );
        } else {
          setViewProjectData([]);
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    fetchProjectList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onDeleteHandler = (deleteApiParam: string) => {
    setIsLoading(true);
    apiAdministrationAuthService
      .deleteProject(deleteApiParam)
      .then((res) => {
        if (res.status) {
          toast.success('Project Deleted successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
          fetchProjectList();
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const columns: GridColDef[] = [
    {
      field: 'projectName',
      headerName: 'Project Name',
      flex: 1,
    },
    {
      field: 'projectSetting',
      headerName: 'Project Setting',
      flex: 1,
    },
    { field: 'description', headerName: 'Description', flex: 1 },
    { field: 'createdAt', headerName: 'Created Date', flex: 1 },
    { field: 'createdBy', headerName: 'Created By', flex: 1 },
    { field: 'updatedAt', headerName: 'Updated Date', flex: 1 },
    { field: 'updatedBy', headerName: 'Updated By', flex: 1 },
    {
      field: 'actionPending',
      headerName: 'Action',
      width: getAdminColumnWidth(isSmaller),
      filterable: false,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: any) => {
        const {
          row: { id, projectShortName },
        } = params;
        return (
          <ActionsColumn
            permissions={permissions}
            editUrl={`/administration/project/${projectShortName}`}
            onDeleteHandler={() => onDeleteHandler(id)}
          />
        );
      },
    },
  ];

  return (
    <Box sx={{ mx: 'auto', mb: 3 }}>
      <DataGridTable loading={isLoading} rows={viewProjectData} columns={columns} refreshHandler={fetchProjectList} />
    </Box>
  );
};

ViewProject.type = ADMIN_TILE_PERMISSION_TYPE.grid;

export default withAdminPermissions(ViewProject);
