import React from 'react';

import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import { generateEnum } from 'utils/common';
import { FormValues, initialValues } from '../..';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import SecretValueField from '../SecretValueField';

interface NormalSecretFieldsProps {
  policyDescription: string;
}

const NormalSecretFields: React.FC<NormalSecretFieldsProps> = ({ policyDescription }: NormalSecretFieldsProps) => {
  //Hooks
  const { nblFormValues, nblFormProps } = useNblForms<FormValues>();
  const FIELD_NAMES = generateEnum(initialValues);

  //JSX
  return (
    <React.Fragment>
      <NblGridItem>
        <NblTextField
          mandatory
          type="text"
          label="Key"
          placeholder="Enter"
          name={FIELD_NAMES.vaultKey}
          value={nblFormValues.vaultKey}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          helperText={nblFormProps.touched.vaultKey ? nblFormProps.errors.vaultKey : ' '}
          error={Boolean(nblFormProps.errors.vaultKey && nblFormProps.touched.vaultKey)}
        />
      </NblGridItem>
      <NblGridItem>
        <SecretValueField
          isPasswordPolicyRequired={Boolean(nblFormValues.policyId)}
          policyId={nblFormValues.policyId}
          label={'Value'}
          name={FIELD_NAMES.vaultPassword}
          value={nblFormValues.vaultPassword}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          setPasswordValidStatus={(isValid: boolean) => nblFormProps.setFieldValue('isPasswordValid', isValid)}
          helperText={(nblFormProps.touched.vaultPassword ? nblFormProps.errors.vaultPassword : ' ') || ''}
          error={Boolean(nblFormProps.errors.vaultPassword && nblFormProps.touched.vaultPassword)}
          policyDescription={policyDescription}
        />
      </NblGridItem>
    </React.Fragment>
  );
};

export default NormalSecretFields;
