export default interface VPCDetails {
  serviceRequestId: string;
  status: string;
  project: string;
  organization: string;
  accountId: string;
  accountName: string;
  vpc_name: string;
  region: string;
  description: string;
  baseSubnetCount: number;
  platformContext: {
    catalogId: string;
    envId: string;
    projectName: string;
    applicationId: string;
    applicationName: string;
    environmentName: string;
  };
  vpcSettings: {
    cidrsGua: string;
    cidrsBase: string;
  };
}
