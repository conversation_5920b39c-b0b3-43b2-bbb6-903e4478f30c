import { Box } from '@mui/material';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

interface StyledSwitchWrapperProps {
  theme?: NebulaTheme;
}

interface StyledSwitchTabProps {
  selected: boolean;
  disabled?: boolean;
  theme?: NebulaTheme;
}
// eslint-disable-next-line no-unused-vars
export const SwitchWrapper = styled(Box)<StyledSwitchWrapperProps>(({ theme }) => ({
  display: 'inline',
  borderRadius: 6,
  overflow: 'hidden',
  width: 'fit-content',
}));

export const StyledSwitchTab = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'selected' && prop !== 'disabled',
})<StyledSwitchTabProps>(({ theme, selected, disabled }) => ({
  flex: 1,
  padding: '8px 16px',
  backgroundColor: selected ? theme.palette.primary.shade11 : theme.palette.secondary.shade2,
  color: selected ? theme.palette.secondary.shade2 : theme.palette.secondary.shade5,
  textAlign: 'center',
  fontSize: theme?.typography.body2.fontSize,
  cursor: disabled ? 'not-allowed' : 'pointer',
  pointerEvents: disabled ? 'none' : 'auto',
  borderRight: `1px solid ${theme?.palette.grey[300]}`,
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  userSelect: 'none',
  '&:last-child': {
    borderRight: 'none',
  },
  '&:hover': {
    backgroundColor: !selected && !disabled ? theme?.palette.grey[100] : undefined,
  },
}));
