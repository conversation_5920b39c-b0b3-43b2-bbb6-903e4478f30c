// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';

export const getOutlinedBaseStyles = (theme: NebulaTheme) => {
  const { palette, typography } = theme;
  const { textfield } = palette;
  return {
    ...typography.subtitle1,
    padding: '8px 12px',
    color: textfield.color,
  };
};

export const getOutlinedInputStyles = (theme: NebulaTheme) => {
  const { palette } = theme;
  const { textfield, select } = palette;
  return {
    '& input, & textarea': {
      color: `${textfield.color} !important`,
      padding: 0,
      '&::placeholder': {
        color: textfield.placeholderColor,
      },
    },
    '&.MuiOutlinedInput-root': {
      backgroundColor: textfield.backgroundColor,
    },
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: textfield.borderColor,
    },
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: textfield.hoveredBorderColor,
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: textfield.toggledBorderColor,
      boxShadow: `0px 0px 6px ${select.boxShadowColor}`,
    },
    '&.Mui-error .MuiOutlinedInput-notchedOutline': {
      border: `2px solid ${textfield.errorColor}`,
      boxShadow: `0px 0px 6px ${select.errorBoxShadowColor}`,
    },
    '&.Mui-disabled': {
      '& input, & textarea': {
        opacity: 0.3,
        cursor: 'not-allowed',
        '&::placeholder': {
          opacity: 0.3,
        },
      },
    },
    '&.Mui-readOnly': {
      '& fieldset': {
        border: 'none',
      },
      '& input, & textarea': {
        padding: '0',
      },
    },
  };
};

export const getScrollbarStyles = (theme: NebulaTheme) => {
  const { palette } = theme;
  const { textfield } = palette;
  return {
    overflow: 'auto',
    scrollBehavior: 'smooth',
    '& *': {
      boxSizing: 'border-box',
    },
    '&::-webkit-scrollbar': {
      width: '8px',
    },
    '&::-webkit-scrollbar-track': {
      background: textfield.scrollBarTrackColor,
    },
    '&::-webkit-scrollbar-thumb': {
      background: textfield.scrollBarThumbDefault,
      borderRadius: '4px',
      cursor: 'pointer',
    },
    '&::-webkit-scrollbar-thumb:hover': {
      background: textfield.scrollBarThumbHover,
    },
    '&::-webkit-scrollbar-thumb:active': {
      background: textfield.scrollBarThumbActive,
    },
  };
};

export const getPaperPropStyles = (theme: NebulaTheme) => {
  const { palette } = theme;
  const { secondary } = palette;
  return {
    boxShadow: 'none',
    margin: '0',
    padding: '0',
    marginTop: '6px',
    border: `1px solid ${secondary.shade3}`,
    borderRadius: '4px',
    maxHeight: '150px',
  };
};

export const getMenulistPropsStyles = {
  padding: '0px 4px',
};

export const truncateLabel = (str: String, maxLength: number) => {
  return str.length > maxLength ? str.substring(0, maxLength) + '...' : str;
};

export const getMuiSelectPlaceholderStyles = () => {
  return {
    '& .MuiSelect-placeholder': {
      opacity: 0.3,
    },
  };
};

export const getDisabledInputStylesForMuiSelect = () => {
  return {
    '& .MuiSelect-select.MuiSelect-outlined.Mui-disabled.MuiInputBase-input.MuiOutlinedInput-input': {
      opacity: 0.3,
      cursor: 'not-allowed',
    },
    '&  input': {
      opacity: '0 !important',
    },
  };
};

export const getContainedVariantStylesForMuiSelect = (theme: NebulaTheme) => {
  const { palette } = theme;
  const { select } = palette;
  return {
    backgroundColor: select.containedVariantBgColor,
    color: select.containedVariantTextColor,
    '& .MuiOutlinedInput-notchedOutline': {
      border: 'none !important',
      borderRadius: '10px !important',
    },
  };
};
