import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

const StyledIconButton = styled('button')<{ theme?: NebulaTheme }>(({ theme }) => {
  return {
    all: 'unset',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '100%',
    boxSizing: 'border-box',
    color: theme.palette.typography.shade7,
    ...theme.typography.h1,
    wordBreak: 'break-word',
    cursor: 'pointer',
  };
});

export { StyledIconButton };
