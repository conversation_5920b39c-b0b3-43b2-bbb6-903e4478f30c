import { useEffect, useRef, useState } from 'react';
import { TransformWrapper, TransformComponent, ReactZoomPanPinchRef } from 'react-zoom-pan-pinch';
import { useSelector } from 'react-redux';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import useContainerDimensions from 'hooks/useContainerDimensions';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblTypography from 'sharedComponents/NblTypography';
import NblSpinner from 'sharedComponents/NblSpinner';

interface ImageZoomerProps {
  imageUrl: string;
  imageAltText: string;
  downloadHandler: () => void;
}

const NblImageViewer: React.FunctionComponent<ImageZoomerProps> = ({ imageUrl, imageAltText, downloadHandler }: ImageZoomerProps) => {
  //HOOKS
  const componentRef = useRef(null);
  const { width, height } = useContainerDimensions(componentRef);
  const { isDialogMaximized } = useSelector((state: State) => state.common);
  const transformRef = useRef<ReactZoomPanPinchRef>(null);

  //States
  const [defaultScale, setDefaultScale] = useState(0);

  //Side effects
  useEffect(() => {
    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      const scale = Math.min(width / img.width, height / img.height);
      setDefaultScale(scale <= 0.3 ? 1 : scale * 3);
    };
  }, [imageUrl, width, height]);

  //JSX
  return (
    <NblFlexContainer ref={componentRef} position="relative" direction="column" overflowY="hidden">
      {defaultScale ? (
        <TransformWrapper initialScale={defaultScale} minScale={0.1} limitToBounds={false} centerOnInit ref={transformRef}>
          {({ zoomIn, zoomOut }) => (
            <>
              <NblFlexContainer width="auto" height="40px" alignSelf="flex-end">
                <NblButton buttonID="zoom-in" tooltip="Zoom In" onClick={() => zoomIn()}>
                  <NblTypography variant="h1" display="flex">
                    <ZoomInIcon />
                  </NblTypography>
                </NblButton>
                <NblButton buttonID="zoom-out" tooltip="Zoom Out" onClick={() => zoomOut()}>
                  <NblTypography variant="h1" display="flex">
                    <ZoomOutIcon />
                  </NblTypography>
                </NblButton>
                <NblButton buttonID="zoom-reset" tooltip="Reset Zoom" onClick={() => transformRef.current?.centerView(defaultScale)}>
                  <NblTypography variant="h1" display="flex">
                    <RestartAltIcon />
                  </NblTypography>
                </NblButton>
                <NblButton buttonID="download" tooltip="Download" onClick={downloadHandler}>
                  <NblTypography variant="h1" display="flex">
                    <FileDownloadIcon />
                  </NblTypography>
                </NblButton>
              </NblFlexContainer>
              <TransformComponent contentStyle={{ width, height: isDialogMaximized ? 460 : 410 }}>
                <img src={imageUrl} alt={imageAltText} style={{ width, position: 'relative', top: '-300px' }} />
              </TransformComponent>
            </>
          )}
        </TransformWrapper>
      ) : (
        <NblSpinner display="inline" spinnerData={[{ id: 'image', message: 'Loading...', status: true }]} />
      )}
    </NblFlexContainer>
  );
};

export default NblImageViewer;
