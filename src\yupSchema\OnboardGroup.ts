import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';

const accessRequestSchema = yup.object().shape({
  catalogId: yup.string().required('Catalog Name is required'),
  domainId: yup
    .string()
    .nullable(true)
    .test('not-empty', 'Domain is required', (value) => value === null || Bo<PERSON>an(value)),
  roleId: yup.string().required('Role is required'),
});

export const validationSchema = yup.object().shape({
  groupName: yup
    .string()
    .trim()
    .matches(yupMatchesParams.groupName.pattern, yupMatchesParams.groupName.errorMessage)
    .required('AD Group Name is required'),
  emaildl: yup
    .array()
    .min(1, 'At least one Email distribution is required')
    .of(
      yup
        .string()
        .email('Invalid email format')
        .matches(/@charter.com/, 'Please provide charter email id')
    )
    .test('Unique', 'Duplicate emails are not allowed', (emails) => {
      return new Set(emails).size === emails?.length;
    }),
  description: yup
    .string()
    .trim()
    .required('Description is required')
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
  clientId: yup.string().trim(),
  organization: yup.string().required('Organization is required'),
  vertical: yup.string().required('Verticals is required'),
  department: yup.string().required('Departments is required'),
  accessRequest: yup.array().of(accessRequestSchema).min(1, 'At least one catalog permission record is required'),
});
