import React, { Dispatch, ReactElement, ReactNode, SetStateAction, useCallback, useEffect, useRef, useState } from 'react';
/* eslint-disable no-unused-vars */
import { Pagination, useTheme } from '@mui/material';
/* eslint-disable no-unused-vars */
import {
  GridCellModes,
  GridCellModesModel,
  GridCellParams,
  GridColDef,
  GridColumnVisibilityModel,
  GridFilterModel,
  gridPageCountSelector,
  gridPageSelector,
  GridPaginationModel,
  GridRenderCellParams,
  GridRowParams,
  GridRowSelectionModel,
  GridSortModel,
  GridToolbarColumnsButton,
  GridToolbarContainer,
  useGridApiContext,
  useGridSelector,
} from '@mui/x-data-grid';

import { PageInfoData, PaginationConfiguration } from 'types/Interfaces/PaginationResponse';
import { StyledNblDataGrid, StyledNblPaginationItem } from './styled';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { DeleteIcon, EditIcon, ColumnIcon, ResetFilterIcon } from 'assets/images/icons/custom-icons';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblTypography from 'sharedComponents/NblTypography';
import NblSelect from 'sharedComponents/NblFormInputs/NblSelect';
import useDataGridUtils from 'hooks/useDataGridUtils';
import useMediaQuery from 'hooks/useMediaQuery';
import { NebulaTheme } from 'NebulaTheme/type';
import NblChip from 'sharedComponents/NblChip';
import useContainerDimensions from 'hooks/useContainerDimensions';
import { TypographyVariant } from 'sharedComponents/NblTypography/styled';

export interface TableRowData {
  [key: string | symbol]: any;
}

// Column data passed to the custom component (only field and headerName)
export type ColumnData = GridColDef & {
  field: string;
  headerName: string;
  renderHeader?: () => JSX.Element;
  renderCell?: (params: GridRenderCellParams) => JSX.Element;
  width?: number;
  flex?: number;
  editable?: boolean;
};

// Custom DataGrid column definition with defaults
type GridColDefWithDefaults = {
  field: string;
  headerName: string;
  type: 'string' | 'number' | 'boolean';
};

// Default column properties
const defaultColumnProps: Omit<GridColDefWithDefaults, 'field' | 'headerName'> = {
  type: 'string',
};

interface TableProps extends PaginationConfiguration {
  columns: ColumnData[];
  rows: TableRowData[];
  checkboxSelection?: boolean;
  rowsOverlayMessage?: string;
  rowSize?: string;
  pageSizeOptions?: string[];
  loading?: boolean;
  pageInfo?: PageInfoData;
  onFilterModelChange?: (filter: GridFilterModel) => void;
  renderElement?: ReactElement[] | ReactNode;
  showResetFilter?: boolean;
  showEditButton?: boolean;
  showRefreshButton?: boolean;
  onRefreshHandler?: () => void;
  onResetHandler?: () => void;
  onRowSelectionModelChange?: (selected: GridRowSelectionModel) => void;
  isRowsSelectable?: (params: GridRowParams) => boolean;
  setEditMode?: Dispatch<SetStateAction<boolean>>;
  deleteHandler?: () => void;
  onPageChange?: (params: GridPaginationModel) => void;
  allSelectedRows?: number;
  saveDataHandler?: () => void;
  onMouseEnter?: (event: React.MouseEvent<HTMLDivElement>) => void;
  onSortModelChange?: (sortModel: GridSortModel) => void;
  handleRowEdit?: any;
  autoHeight?: boolean;
  selectedRows?: GridRowSelectionModel;
  setSelectedRows?: Dispatch<SetStateAction<GridRowSelectionModel>>;
  handleRequestRowClick?: (row: TableRowData) => void;
  genericButtonHandler?: () => void;
  genericButtonLabel?: string;
  showGridBorder?: boolean;
  disableRowSelectionOnClick?: boolean;
  genericButtonDisable?: boolean;
  hideFooterAndPagination?: boolean;
  isToolBarVisible?: boolean;
  columnVisibilityModel?: GridColumnVisibilityModel;
  onColumnVisibilityModelChange?: (newModel: GridColumnVisibilityModel) => void;
  showRecordsBottom?: boolean;
}

export const NblTable: React.FunctionComponent<TableProps> = ({
  rows,
  columns,
  checkboxSelection,
  isRowsSelectable,
  rowsOverlayMessage = 'No Record Found',
  serverPagination,
  serverPaginationFn,
  componentApiMappings,
  onRowSelectionModelChange,
  showRecordsBottom = true,
  pageInfo,
  rowSize = '5', // Default value if not provided
  pageSizeOptions = ['5', '20', '40', '60'], // Default value if not provided
  loading,
  onFilterModelChange,
  renderElement,
  showResetFilter = true,
  showEditButton,
  onResetHandler,
  setEditMode,
  deleteHandler,
  saveDataHandler,
  onMouseEnter,
  onSortModelChange,
  handleRowEdit,
  allSelectedRows,
  onPageChange,
  autoHeight = false,
  setSelectedRows,
  selectedRows,
  handleRequestRowClick,
  onRefreshHandler,
  showRefreshButton,
  showGridBorder = true,
  genericButtonHandler,
  genericButtonLabel,
  genericButtonDisable,
  disableRowSelectionOnClick = true,
  hideFooterAndPagination = false,
  isToolBarVisible,
  columnVisibilityModel,
  onColumnVisibilityModelChange,
}: TableProps) => {
  //Hooks
  const componentRef = useRef(null);
  const { width } = useContainerDimensions(componentRef);
  const styles = useMediaQuery(
    { header: { height: '42px' }, footer: { fontSize: 'h6' as TypographyVariant }, pagination: { minWidth: '32px', height: '32px' } },
    {
      header: { height: '32px' },
      footer: { fontSize: width < 900 ? 'body4' : ('h6' as TypographyVariant) },
      pagination: { minWidth: width < 900 ? '25px' : '32px', height: width < 900 ? '25px' : '32px' },
    }
  );

  const {
    palette: { secondary },
  } = useTheme<NebulaTheme>();
  //States
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: Number(rowSize),
  });
  const [filteringModel, setFilteringModel] = useState({ items: [] } as GridFilterModel);
  const [sortingModel, setSortingModel] = useState([] as GridSortModel);
  const [currentRowSize, setRowSize] = useState(rowSize);
  const [muiTableKey, setMuiTableKey] = useState(1);
  const [showSaveButton, setShowSaveButton] = useState(false);
  const [isDeleteVisible, setIsDeleteVisible] = useState(false);
  const [cellModesModel, setCellModesModel] = useState<GridCellModesModel>({});
  const { isEditMode, selectedRows: selectedRowsState, canEditRow } = useDataGridUtils();

  //Local Variables
  const shouldRenderToolBar = Boolean(
    renderElement ||
      genericButtonLabel ||
      showResetFilter ||
      (showEditButton && isDeleteVisible && selectedRows && selectedRows?.length > 0) ||
      (showEditButton && !showSaveButton && checkboxSelection) ||
      showSaveButton ||
      showRefreshButton
  );
  const ToolbarHeight = shouldRenderToolBar ? '48px' : '0px';

  //Memoization
  const handleCellClick = useCallback(
    (params: GridCellParams, event: React.MouseEvent) => {
      // Check if the row is selected
      if (!selectedRows?.includes(params.id)) {
        return;
      }

      if (!params.isEditable || !canEditRow(selectedRowsState, params.id, isEditMode)) {
        return;
      }

      // Ignore portal
      if ((event.target as any).nodeType === 1 && !event.currentTarget.contains(event.target as Element)) {
        return;
      }

      setCellModesModel((prevModel) => {
        return {
          // Revert the mode of the other cells from other rows
          ...Object.keys(prevModel).reduce(
            (acc, id) => ({
              ...acc,
              [id]: Object.keys(prevModel[id]).reduce(
                (acc2, field) => ({
                  ...acc2,
                  [field]: { mode: GridCellModes.View },
                }),
                {}
              ),
            }),
            {}
          ),
          [params.id]: {
            // Revert the mode of other cells in the same row
            ...Object.keys(prevModel[params.id] || {}).reduce((acc, field) => ({ ...acc, [field]: { mode: GridCellModes.View } }), {}),
            [params.field]: { mode: GridCellModes.Edit },
          },
        };
      });
    },
    [selectedRows, showSaveButton, selectedRowsState, isEditMode]
  );

  const handleCellModesModelChange = useCallback((newModel: GridCellModesModel) => {
    setCellModesModel(newModel);
  }, []);

  // Update cell modes based on row selection changes
  const onRowSelectionModelChangeHandler = (newRowSelectionModel: GridRowSelectionModel) => {
    setSelectedRows?.(newRowSelectionModel);
    onRowSelectionModelChange?.(newRowSelectionModel);

    setCellModesModel((prevModel) => {
      return {
        // Set all cells to view mode
        ...Object.keys(prevModel).reduce(
          (acc, id) => ({
            ...acc,
            [id]: Object.keys(prevModel[id]).reduce(
              (acc2, field) => ({
                ...acc2,
                [field]: { mode: GridCellModes.View },
              }),
              {}
            ),
          }),
          {}
        ),
        // Set cells of selected rows to edit mode
        ...newRowSelectionModel.reduce(
          (acc, id) => ({
            ...acc,
            [id]: Object.keys(prevModel[id] || {}).reduce(
              (acc2, field) => ({
                ...acc2,
                [field]: { mode: GridCellModes.Edit },
              }),
              {}
            ),
          }),
          {}
        ),
      };
    });
  };

  //Renders
  const paginationModelChange = (pageModel: { page: number; pageSize: number }) => {
    setPaginationModel({
      ...pageModel,
      page: pageModel.page,
    });
  };

  const CustomPagination = () => {
    const apiRef = useGridApiContext();
    const page = useGridSelector(apiRef, gridPageSelector) + 1;
    const pageCount = useGridSelector(apiRef, gridPageCountSelector);

    return (
      <NblFlexContainer width="100%" alignItems="center" justifyContent="space-between" padding="0 15px" direction="row">
        <NblFlexContainer direction="row" height="auto" spacing={1} width="auto" center>
          {!!allSelectedRows && (
            <NblFlexContainer width="auto">
              <NblTypography color="shade7" variant={styles.footer.fontSize} weight="medium">
                {allSelectedRows > 1 ? `${allSelectedRows} Rows Selected` : `${allSelectedRows} Row Selected`}
              </NblTypography>
            </NblFlexContainer>
          )}
          <NblFlexContainer width="auto">
            <NblTypography color="shade1" variant={styles.footer.fontSize}>
              Items per page
            </NblTypography>
          </NblFlexContainer>
          <NblFlexContainer width="15%" minWidth="60px">
            <NblSelect
              value={currentRowSize}
              handleChange={(event: any) => {
                setRowSize(event.target.value);

                apiRef.current.setPageSize(Number(event.target.value));

                if (serverPagination) {
                  setPaginationModel({
                    page: 0,
                    pageSize: Number(event.target.value),
                  });
                }
              }}
              name={''}
              options={pageSizeOptions.map((size) => ({
                label: size,
                value: size,
              }))}
              placeholder={''}
            />
          </NblFlexContainer>
        </NblFlexContainer>

        <NblFlexContainer spacing={1} justifyContent="end" height="auto" width="auto">
          {(pageCount > 1 || (pageInfo?.totalPages && pageInfo?.totalPages > 1)) && (
            <Pagination
              color="primary"
              variant="outlined"
              shape="rounded"
              page={serverPagination ? pageInfo?.page : page}
              count={serverPagination ? pageInfo?.totalPages : pageCount}
              renderItem={(props) => <StyledNblPaginationItem {...props} styles={styles} component="div" />}
              onChange={(event: React.ChangeEvent<unknown>, value: number) => {
                serverPagination
                  ? setPaginationModel({
                      page: value - 1,
                      pageSize: Number(currentRowSize),
                    })
                  : apiRef.current.setPage(value - 1);
              }}
            />
          )}
          {Boolean(pageInfo?.totalDocs || rows?.length) && showRecordsBottom && (
            <NblFlexContainer width="auto">
              <NblChip
                borderRadius="sm"
                color="default"
                id="totla-records-found"
                label={`${serverPagination ? pageInfo?.totalDocs : rows?.length} Record's Found`}
                onClick={() => {}}
              />
            </NblFlexContainer>
          )}
        </NblFlexContainer>
      </NblFlexContainer>
    );
  };

  const gridColumns = columns.map((col) => ({
    ...col,
    ...defaultColumnProps,
  }));

  const fetchRows = async () => {
    if (!serverPagination) {
      return;
    }
    let sortString = '';
    let filterString = '';
    if (sortingModel?.length > 0) {
      const sortObject = sortingModel.reduce((acc: Record<string, number>, curr) => {
        const field = componentApiMappings?.[curr.field] || curr.field;
        acc[field] = curr.sort === 'desc' ? -1 : 1;
        return acc;
      }, {});
      sortString = JSON.stringify(sortObject);
    }
    if (Object.keys(filteringModel).length != 0) {
      const filterObject: Record<string, Record<string, string>> = filteringModel.items.reduce(
        (acc: Record<string, Record<string, string>>, curr) => {
          const field = componentApiMappings?.[curr.field] || curr.field;
          acc[field] = { [curr.operator]: curr.value };
          return acc;
        },
        {}
      );
      filterString = JSON.stringify(filterObject);
    }

    serverPaginationFn?.(paginationModel.page + 1, paginationModel.pageSize, sortString, filterString);
  };

  const resetFilterHandler = () => {
    setMuiTableKey(muiTableKey + 1);
    setFilteringModel({ items: [] }); // Clear the filtering model
    if (onRowSelectionModelChange) {
      onRowSelectionModelChange([]);
    }
    onResetHandler?.();
  };

  const editDataHandler = () => {
    setEditMode?.(true);
    if (selectedRows && selectedRows?.length > 0) {
      // Row edit mode

      setShowSaveButton(true);
      setIsDeleteVisible(true);
    } else {
      // Main edit mode
      setShowSaveButton(true);
      const allRowIds = rows.map((row) => row.id); // Get all row IDs
      setSelectedRows?.(allRowIds); // Select all rows
      if (allRowIds.length > 0) {
        setIsDeleteVisible(true);
      }
    }
  };
  const saveHandler = () => {
    setEditMode?.(false);
    setShowSaveButton(false);
    setIsDeleteVisible(false);
    setSelectedRows?.([]);
    saveDataHandler?.();
  };

  /**Data grid will trigger onFilterModelChange even if there is no changes on filter
   * Due to this unnecessary fetches happening.
   * So this function will compare the filter really changed or not, if its changed then only it will push this filter for api call
   */
  const isFilterReallyChanged = (newFilterModel: GridFilterModel) => {
    const oldItems = filteringModel.items;
    const newItems = newFilterModel.items;
    //If no previous filters and if there are new filters which does not have any filter value, no need to consider this as filter changed
    if (oldItems.length === 0 && newItems.every((item) => !item.value)) {
      return false;
      //If previous filters length and current filter length are same  and also if all the values for each filter field is same as previous then no need to consider this as filter changed
    } else if (
      oldItems.length === newItems.length &&
      oldItems.every((item, index) => newItems[index].field === item.field && newItems[index].value === item.value)
    ) {
      return false;
      //If current filter length is 0(Means resetting the filter) and if already filter fields are there but those values are empty then this also no need to consider as filter changed
    } else if (newItems.length === 0 && oldItems.every((item) => !item.value)) {
      return false;

      //If none of the above satisfies then considering the filter as changed
    } else {
      return true;
    }
  };

  const handleFilterModelChange = (newFilterModel: GridFilterModel) => {
    const trimmedFilterModel = {
      ...newFilterModel,
      items: newFilterModel.items.map((item) => ({
        ...item,
        value: Array.isArray(item.value) ? item.value.map((value) => value.trim()) : item.value?.trim() || '',
      })),
    };
    if (isFilterReallyChanged(trimmedFilterModel)) {
      paginationModelChange({ page: 0, pageSize: paginationModel.pageSize });
      setFilteringModel(trimmedFilterModel);
      onFilterModelChange?.(trimmedFilterModel);
    }
  };

  useEffect(() => {
    if (selectedRows && selectedRows?.length > 0) {
      setIsDeleteVisible(true);
    }
  }, [selectedRows]);

  //Side Effects
  useEffect(() => {
    if (serverPagination) {
      fetchRows();
    }
  }, [paginationModel, sortingModel, filteringModel]);

  const ShowColumnToolBar = () => {
    return (
      <GridToolbarContainer>
        <GridToolbarColumnsButton />
      </GridToolbarContainer>
    );
  };

  return (
    <NblFlexContainer width="100%" height="100%" direction="column" ref={componentRef} spacing={ToolbarHeight === '0px' ? 0 : undefined}>
      <NblFlexContainer height={ToolbarHeight} alignItems="center" justifyContent="space-between">
        <NblFlexContainer width="auto" flex={'1 0'} height="auto">
          {renderElement}
        </NblFlexContainer>
        <NblFlexContainer spacing={2} width="auto" height={styles.header.height} alignItems="center">
          {genericButtonLabel && (
            <NblButton buttonID={`table-${genericButtonLabel}-btn`} variant="outlined" onClick={genericButtonHandler} color="primary">
              {genericButtonLabel}
            </NblButton>
          )}
          {showResetFilter && (
            <NblButton
              buttonID={'table-reset-btn'}
              color="primary"
              startIcon={<ResetFilterIcon />}
              onClick={resetFilterHandler}
              variant="outlined"
            >
              Reset Filter
            </NblButton>
          )}
          {showEditButton && isDeleteVisible && selectedRows && selectedRows?.length > 0 && (
            <NblButton buttonID={'table-delete-btn'} color="error" startIcon={<DeleteIcon />} onClick={deleteHandler} variant="outlined">
              Delete
            </NblButton>
          )}
          {showEditButton && !showSaveButton && checkboxSelection && (
            <NblButton buttonID={'table-edit-btn'} color="primary" startIcon={<EditIcon />} onClick={editDataHandler} variant="outlined">
              Edit
            </NblButton>
          )}
          {showSaveButton && (
            <NblButton buttonID={'table-save-btn'} color="info" onClick={saveHandler} variant="contained">
              Save
            </NblButton>
          )}
          {showRefreshButton && (
            <NblButton buttonID={'table-refresh-btn'} color="info" onClick={onRefreshHandler} variant="contained">
              Refresh
            </NblButton>
          )}
        </NblFlexContainer>
      </NblFlexContainer>
      <NblFlexContainer
        height={`calc(100% - ${ToolbarHeight})`}
        justifyContent="center"
        {...(showGridBorder && { border: `1px solid ${secondary.shade3}`, borderRadius: '10px', overflowX: 'hidden', overflowY: 'hidden' })}
      >
        <StyledNblDataGrid
          styles={styles}
          autoHeight={autoHeight}
          getRowHeight={() => 'auto'}
          key={muiTableKey}
          loading={loading}
          checkboxSelection={checkboxSelection}
          rowSelectionModel={selectedRows}
          onRowSelectionModelChange={onRowSelectionModelChangeHandler}
          isRowSelectable={isRowsSelectable}
          slotProps={{
            row: {
              onMouseEnter: onMouseEnter,
            },
          }}
          rows={rows}
          columns={gridColumns}
          hideFooterPagination={hideFooterAndPagination}
          hideFooter={hideFooterAndPagination}
          pagination
          disableRowSelectionOnClick={disableRowSelectionOnClick}
          initialState={{
            pagination: {
              paginationModel: {
                pageSize: paginationModel?.pageSize || Number(rowSize),
              },
            },
          }}
          rowCount={serverPagination ? pageInfo?.totalDocs || paginationModel.pageSize : undefined}
          paginationMode={serverPagination ? 'server' : 'client'}
          paginationModel={paginationModel}
          onPaginationModelChange={
            serverPagination
              ? (newPaginationModel: GridPaginationModel) => {
                  paginationModelChange(newPaginationModel);
                }
              : (newPaginationModel: GridPaginationModel) => {
                  setPaginationModel(newPaginationModel);
                  onPageChange?.(newPaginationModel);
                }
          }
          onFilterModelChange={handleFilterModelChange}
          onSortModelChange={(newSortModel) => {
            if (serverPagination) {
              setSortingModel(newSortModel || null);
            }
            onSortModelChange?.(newSortModel);
          }}
          onRowClick={(params) => {
            handleRequestRowClick?.(params.row); // Call the function when a row is clicked
          }}
          slots={{
            toolbar: isToolBarVisible ? ShowColumnToolBar : undefined,
            pagination: CustomPagination,
            noRowsOverlay: () => (
              <NblTypography variant={'subtitle1'} color="shade1" textAlign="center" padding="20px">
                {rowsOverlayMessage}
              </NblTypography>
            ),
            noResultsOverlay: () => (
              <NblTypography variant={'subtitle1'} color="shade1" textAlign="center" padding="20px">
                {rowsOverlayMessage}
              </NblTypography>
            ),
          }}
          getRowClassName={(params: any) => {
            return params.isSelected ? 'selectedRow' : '';
          }}
          columnBuffer={3}
          editMode="row"
          processRowUpdate={handleRowEdit}
          cellModesModel={cellModesModel}
          onCellModesModelChange={handleCellModesModelChange}
          onCellClick={handleCellClick}
          hideFooterSelectedRowCount={Boolean(allSelectedRows)}
          columnVisibilityModel={columnVisibilityModel}
          onColumnVisibilityModelChange={onColumnVisibilityModelChange}
        />
      </NblFlexContainer>
    </NblFlexContainer>
  );
};
