import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import { Button, Grid, Typography } from '@mui/material';
import { getPermissionKeyItems } from 'api/static-data';
import NetworkConfigDialog from './NetworkConfigDialog';
import TagConfigDialog from './TagConfigDialog';
// eslint-disable-next-line
import { AddProjectData, EditProjectTags, ProjectTagData } from 'types';
import { useSelector } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
// eslint-disable-next-line no-unused-vars
import { ProjectNetworkSettings } from 'types';
import DialogBox from 'components/DialogBox/Dialog';

interface ProjectPermissionProps {
  onClose: () => void;
  openDialog: boolean;
  onSuccess?: () => void;
  projectSettingsData: <T extends ProjectNetworkSettings | ProjectTagData>(data: T) => void;
  editTagValue?: EditProjectTags;
  dataCenter: { id: string; name: string }[];
  projectData?: AddProjectData[];
}

const ProjectSettingComponent: React.FunctionComponent<ProjectPermissionProps> = ({
  onClose,
  openDialog,
  projectSettingsData,
  editTagValue,
  dataCenter,
  projectData,
}: ProjectPermissionProps) => {
  const [openNetworkConfigDialog, setOpenNetworkConfigDialog] = useState<boolean>(false);
  const [openTagConfigDialog, setOpenTagConfigDialog] = useState<boolean>(false);
  const [editProjectTag, setEditProjectTag] = useState<EditProjectTags>();
  const [tagData, setTagData] = useState<ProjectTagData[]>([]);
  const { projectValues } = useSelector((state: State) => state.projectNetworks);
  useEffect(() => {
    async function getPermissionItems() {
      try {
        await getPermissionKeyItems();
      } catch (error) {
        console.log(error);
      }
    }
    getPermissionItems();
  }, []);

  useEffect(() => {
    if (editTagValue?.settingName === 'Tags' || editTagValue?.settingName === 'Label') {
      setEditProjectTag(editTagValue);
      setOpenTagConfigDialog(true);
    }
  }, [editTagValue]);

  useEffect(() => {
    if (projectValues?.settingName === 'Network') {
      setOpenNetworkConfigDialog(true);
    }
  }, [projectValues]);

  const formik = useFormik({
    initialValues: {
      projectNames: '',
      projectRoles: '',
    },
    onSubmit: (values) => {
      console.log(values);
    },
  });

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const isProjectTagData = (value: any): value is ProjectTagData => {
    return (value as ProjectTagData).settingName !== undefined;
  };

  const projectSettingConfigData = <T extends ProjectNetworkSettings | ProjectTagData>(values: T) => {
    if (isProjectTagData(values) && values.settingName && values.settingName.length !== 0) setTagValue(values);
    projectSettingsData(values);
    setOpenNetworkConfigDialog(false);
    setOpenTagConfigDialog(false);
  };

  const handleNetworkConfigDialog = () => {
    setOpenNetworkConfigDialog(true);
  };

  const handleTagConfigDialog = () => {
    setOpenTagConfigDialog(true);
  };

  const networkDialogCloseHandler = () => {
    setOpenNetworkConfigDialog(false);
  };

  const tagDialogCloseHandler = () => {
    setOpenTagConfigDialog(false);
  };

  const setTagValue = (value: ProjectTagData) => {
    setTagData([...tagData, value]);
  };

  return (
    <>
      <DialogBox open={openDialog} onClose={onCancel} canMaximize={false}>
        <Grid container spacing={1} mb={-2} sx={{ p: 5 }}>
          <Grid container item justifyContent={'center'} textAlign={'center'} mb={6} spacing={3}>
            <Typography variant="h2">Project Settings</Typography>
          </Grid>
          <Grid container flexDirection={'column'} rowGap={3} alignItems={'center'}>
            <Button data-testid={'add-network-config-btn'} fullWidth variant="contained" onClick={handleNetworkConfigDialog}>
              Add Network Config
            </Button>
            <Button data-testid={'add-tag-btn'} fullWidth variant="contained" onClick={handleTagConfigDialog}>
              Add Tag
            </Button>
          </Grid>
        </Grid>
      </DialogBox>
      <NetworkConfigDialog
        onClose={networkDialogCloseHandler}
        setNetworkData={projectSettingConfigData}
        openNetworkDialog={openNetworkConfigDialog}
        dataCenter={dataCenter}
        projectData={projectData}
      />
      <TagConfigDialog
        onClose={tagDialogCloseHandler}
        setTagData={projectSettingConfigData}
        openTagDialog={openTagConfigDialog}
        editProjectTags={editProjectTag}
        tagData={tagData}
      />
    </>
  );
};

export default ProjectSettingComponent;
