// eslint-disable-next-line no-unused-vars
import { NavigateOptions, useLocation, useNavigate } from 'react-router';
import { nebulaBasePath } from 'utils/constant';

const basePath = nebulaBasePath;

const useNblNavigate = () => {
  //Hooks
  const routerNavigate = useNavigate();
  const location = useLocation();

  //Local
  const routes = location.pathname.split('/');
  const lastIndex = routes.length;

  //Methods
  function navigate(value: string | number = '', options?: NavigateOptions) {
    if (typeof value === 'string') {
      let routeString = value;
      if (value.startsWith('/')) routeString = '/' + basePath + value.replace(/\/$/, '');
      routerNavigate(routeString.replaceAll(`/${nebulaBasePath}/${nebulaBasePath}`, `/${nebulaBasePath}`).replaceAll('//', '/'), options);
    } else {
      back(value * -1);
    }
  }

  function back(n: number = 1, options?: NavigateOptions) {
    routerNavigate(
      routes
        .slice(0, lastIndex - n)
        .join('/')
        .replaceAll('//', '/'),
      options
    );
  }

  return navigate;
};

export default useNblNavigate;
