import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import ThemeProvider from 'mock/ThemeProvider';
import TagMappingForm from '.';
import { GetTagKeysData } from 'mock/GetTagKeysData';
import { GetTagValuesData } from 'mock/GetTagValuesData';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});

jest.mock('react-toastify');

describe('Render Project Permission form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  const handleData = jest.fn();
  const tagKeys = GetTagKeysData.data.tagKeys;
  const tagValues = GetTagValuesData.data.tagValues;
  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <TagMappingForm
              onClose={handleClose}
              onSuccess={handleSuccess}
              setTagData={handleData}
              open={true}
              tagKeys={tagKeys}
              tagValues={tagValues}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(screen.getByText('Tag Key *')).toBeInTheDocument();
    expect(screen.getByText('Tag Value *')).toBeInTheDocument();

    expect(screen.getByText('Description *')).toBeInTheDocument();
    const saveButton = getByText('Save');
    const cancelButton = getByText('Cancel');
    expect(saveButton).toBeEnabled();
    expect(cancelButton).toBeEnabled();
  });
  test('displays validation error if no options are selected', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <TagMappingForm
              onClose={handleClose}
              onSuccess={handleSuccess}
              setTagData={handleData}
              open={true}
              tagKeys={tagKeys}
              tagValues={tagValues}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    fireEvent.click(getByText('Save'));
    waitFor(() => {
      expect(getByText('Tag Key is required')).toBeInTheDocument();
      expect(getByText('Tag Value is required')).toBeInTheDocument();
    });
  });
});
