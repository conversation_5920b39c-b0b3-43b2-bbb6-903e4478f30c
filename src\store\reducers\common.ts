// types
import { createSlice } from '@reduxjs/toolkit';

export enum DefaultAccordions {
  ALL = 'all',
  NONE = 'none',
}

export type Common = {
  apiToastErrorMessage: string;
  exposureParams: Array<string>;
  isDialogMaximized: boolean;
  disableDialogContentScroll: boolean;
  refreshMyApprovalsGrid: boolean;
  expandedAccordions: Array<string>;
};

// initial state
const initialState: Common = {
  apiToastErrorMessage: '',
  exposureParams: [],
  isDialogMaximized: false,
  disableDialogContentScroll: false,
  refreshMyApprovalsGrid: false,
  expandedAccordions: [DefaultAccordions.NONE],
};

// ==============================|| SLICE - Common ||============================== //

const common = createSlice({
  name: 'common',
  initialState,
  reducers: {
    setApiToastErrorMessage(state, action) {
      state.apiToastErrorMessage = action.payload;
    },
    setExposureParams(state, action) {
      state.exposureParams = action.payload;
    },
    setDialogMaximized(state, action) {
      state.isDialogMaximized = action.payload;
    },
    setDialogContentScrollOff(state, action) {
      state.disableDialogContentScroll = action.payload;
    },
    setRefreshMyApprovalsGrid(state, action) {
      state.refreshMyApprovalsGrid = action.payload;
    },
    setAccordions(state, action) {
      const accordionName = action.payload;
      if (DefaultAccordions.ALL === accordionName || DefaultAccordions.NONE === accordionName) {
        state.expandedAccordions = [accordionName];
      } else {
        const index = state.expandedAccordions.findIndex((v) => accordionName === v);
        const newExpandedAccordions = [...state.expandedAccordions];
        if (index >= 0) newExpandedAccordions.splice(index, 1);
        else newExpandedAccordions.push(accordionName);
        if (newExpandedAccordions.length === 0) {
          newExpandedAccordions.push(DefaultAccordions.NONE);
        }
        state.expandedAccordions = newExpandedAccordions;
      }
    },
  },
});

export default common.reducer;
export const {
  setApiToastErrorMessage,
  setExposureParams,
  setDialogMaximized,
  setDialogContentScrollOff,
  setRefreshMyApprovalsGrid,
  setAccordions,
} = common.actions;
