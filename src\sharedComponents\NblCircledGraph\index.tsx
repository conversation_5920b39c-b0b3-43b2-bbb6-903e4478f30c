import { Box, useTheme } from '@mui/material';
/* eslint-disable no-unused-vars */
import { NebulaTheme } from 'NebulaTheme/type';
import React from 'react';
import {
  Container,
  Title,
  LegendContainer,
  LegendItem,
  LegendCircle,
  LegendLabel,
  CircleContainer,
  Circle,
  CircleContent,
  CircleValue,
  CircleLabel,
  Circles,
} from './styled';

// Define the type for the input props
interface CircledGraphProps {
  data: { width: number; value: number; label: string }[]; // Array of {width, value, label}
  loading: boolean;
}

export const NblCircledGraph: React.FC<CircledGraphProps> = ({ data, loading }) => {
  const theme: NebulaTheme = useTheme();
  const { colorVariants, title, value } = theme.palette.circle;
  const colors = [colorVariants.color1, colorVariants.color2, colorVariants.color3]; // Define the colors to be applied to the circles

  return (
    <Container>
      {/* Title and Legend */}
      <Box display="flex" justifyContent="space-between" alignItems="center" width="100%" mb={2}>
        <Title color={title.color} theme={theme}>
          Service Chart
        </Title>
        <LegendContainer>
          {data.map((item, index) => (
            <LegendItem key={index}>
              <LegendCircle color={colors[index % colors.length]} />
              <LegendLabel theme={theme}>{item.label}</LegendLabel>
            </LegendItem>
          ))}
        </LegendContainer>
      </Box>

      {/* Circles */}
      <CircleContainer>
        {!loading && (
          <Circles>
            {data.map((item, index) => (
              <Circle key={index} color={colors[index % colors.length]} size={item.width} theme={undefined}>
                <CircleContent>
                  <CircleValue value={value.color} theme={theme}>
                    {item.value}
                  </CircleValue>
                  <CircleLabel value={value.color} theme={theme}>
                    {item.label}
                  </CircleLabel>
                </CircleContent>
              </Circle>
            ))}
          </Circles>
        )}
      </CircleContainer>
    </Container>
  );
};
