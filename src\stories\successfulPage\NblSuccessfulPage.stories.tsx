//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblSuccessfulPage from 'sharedComponents/NblSuccessfulPage';

type StoryProps = ComponentProps<typeof NblSuccessfulPage>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'SuccessPage/NblSuccessfulPage',
  component: NblSuccessfulPage,
  parameters: {
    layout: 'left',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

export const SuccessPage: Story = {
  args: {
    title: 'Windows VM',
    requestId: 'NEB-IAAS-VM-20420',
    buttonTitle: 'Track Request',
    showTrackbtn: true,
  },
  render: (args) => (
    <NebulaTheme>
      <NblSuccessfulPage {...args} />
    </NebulaTheme>
  ),
};
