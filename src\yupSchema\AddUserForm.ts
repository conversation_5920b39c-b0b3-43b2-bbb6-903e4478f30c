import * as yup from 'yup';

export const addUserValidationSchema = () =>
  yup.object().shape({
    userData: yup.array().of(
      yup.object().shape({
        username: yup
          .string()
          .required('Username is required')
          .test('unique', 'Username should be unqiue', function (value: any) {
            const { userData } = this.options.context as { userData: Array<{ username: string }> };
            return userData.filter(({ username }) => username === value).length === 1;
          }),
      })
    ),
  });