import React, { useEffect, useState } from 'react';
import { AdministrationTabsData } from 'types';
import { getApprovalItems } from 'api/static-data';
import Catalog from 'components/Catalog';

interface ApprovalProps {}

const Approvals: React.FunctionComponent<ApprovalProps> = () => {
  const [content, setContent] = useState<AdministrationTabsData[]>([]);
  useEffect(() => {
    async function getCatalogItems() {
      try {
        const data = await getApprovalItems();
        console.log(data);
        setContent(data);
      } catch (error) {
        console.log(error);
      }
    }
    getCatalogItems();
  }, []);
  return content?.length ? <Catalog catalogItems={content} isAdminTiles /> : <div>No items to display</div>;
};

export default Approvals;
