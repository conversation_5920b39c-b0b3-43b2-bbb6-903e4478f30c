export type MultiENVEnvironment = {
  name: string;
  settings: {
    type: string;
    configurations: {
      domain: string;
      dataCenter: string;
      dataCenterName: string;
      value: {
        description: string | null;
        displayName: string | null;
        id: number;
        name: string;
        subnetIpv4: string | null;
        subnetIpv6: string | null;
        zoneId: string;
        zoneName: string;
        morpheusNetworkName?: string;
      };
      active: boolean;
      description?: string;
    }[];
  }[];
  id: string;
};
