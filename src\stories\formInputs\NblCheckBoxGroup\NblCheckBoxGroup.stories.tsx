//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NblCheckBoxGroup from 'sharedComponents/NblFormInputs/NblCheckBoxGroup';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

type StoryProps = ComponentProps<typeof NblCheckBoxGroup>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblCheckboxGroup',
  component: NblCheckBoxGroup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

const Template = (args: StoryProps) => {
  //State
  const [options, setOptions] = useState(args.options);

  function handleChange(_: any, index: number, checked: boolean) {
    setOptions(
      options.map((option, i) => {
        if (i === index) {
          return { ...option, checked };
        } else {
          return option;
        }
      })
    );
  }

  //JSX
  return <NblCheckBoxGroup {...args} options={options} onChange={handleChange} />;
};

export const NblCheckbox: Story = {
  args: {
    label: 'Nbl Checkbox',
    disabled: false,
    error: false,
    helperText: 'Helper text will display here',

    options: [
      { name: 'option1', label: 'Option 1', checked: false, disabled: false },
      { name: 'option2', label: 'Option 2', checked: true, disabled: false },
      { name: 'option3', label: 'Option 3', checked: false, disabled: true },
    ],

    mandatory: true
  },
  render: (args) => (
    <NebulaTheme>
      <NblFlexContainer>
        <Template {...args} />
      </NblFlexContainer>
    </NebulaTheme>
  ),
};
