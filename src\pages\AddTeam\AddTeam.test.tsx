import { render, act } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import { Provider as ReduxProvider } from 'react-redux';
import { MemoryRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';

import AddTeam from '.';

describe('AddTeam component', () => {
  const mockStore = configureMockStore();
  const store = mockStore({
    common: {
      exposureParams: [],
    },
  });
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <Router>
            <ReduxProvider store={store}>
              <ThemeProvider>
                <AddTeam />
              </ThemeProvider>
            </ReduxProvider>
          </Router>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });

  test('Should render the Add Team Form', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <Router initialEntries={['/administration/teams/add-team']}>
              <AddTeam />
            </Router>
          </ThemeProvider>
        </ReduxProvider>
      )
    );
  });
});
