import React, { useEffect } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid } from '@mui/material';
import { filterSelectedOptions, formWrapperError, yupMatchesParams } from 'utils/common';
import Select from 'components/Select';
import FormWrapper from 'components/FormWrapper';
import TextField from 'components/TextField';
import { TagMappingData } from 'types';
import DialogBox from 'components/DialogBox/Dialog';
import Checkbox from 'components/Checkbox';

interface TagMappingFormProps {
  onClose: () => void;
  onSuccess?: () => void;
  open: boolean;
  setTagData: (values: any) => void;
  tagDetails?: {
    tagKey: string;
    tagKeyId: string;
    tagValue: string;
    tagValueName: string;
    description: string;
    dynamic: boolean;
    staticTagValue: string;
  };
  tagData?: TagMappingData[];
  tagKeys: { id: string; name: string }[];
  tagValues: { id: string; name: string }[];
}

const validationSchema = yup.object().shape({
  tagKey: yup.string().required('Tag Key is required'),
  tagValue: yup.string().test('is-required', 'Dynamic Tag Value is required', function (value) {
    const { dynamic } = this.parent;
    if (dynamic) {
      return !!value;
    }
    return true;
  }),
  staticTagValue: yup
    .string()
    .trim()
    .test('is-required', 'Tag Value is required', function (value) {
      const { dynamic } = this.parent;
      if (!dynamic) {
        return !!value;
      }
      return true;
    })
    .matches(yupMatchesParams.tagValue.pattern, yupMatchesParams.tagValue.errorMessage),
  description: yup
    .string()
    .trim()
    .required('Description is required')
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});

const TagMappingForm: React.FunctionComponent<TagMappingFormProps> = ({
  onClose,
  setTagData,
  tagDetails,
  open,
  tagData,
  tagKeys,
  tagValues,
}: TagMappingFormProps) => {
  const formik = useFormik({
    initialValues: {
      tagKey: '',
      tagValue: '',
      description: '',
      staticTagValue: '',
      dynamic: false,
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const selectedTagKey = tagKeys?.find((tagKey) => tagKey.id === values.tagKey);
      const selectedTagValue = tagValues?.find((tagValue) => tagValue.id === values.tagValue);
      if (selectedTagKey) {
        setTagData({
          tagKey: selectedTagKey?.name,
          tagKeyId: values?.tagKey,
          tagValue: values?.tagValue,
          tagValueName: selectedTagValue?.name,
          description: values?.description,
          dynamic: values?.dynamic,
          staticTagValue: values?.staticTagValue,
        });
      }
      formik.resetForm();
    },
  });

  useEffect(() => {
    if (tagDetails) {
      formik.setValues({
        tagKey: tagDetails.tagKeyId,
        tagValue: tagDetails.tagValue,
        description: tagDetails.description,
        dynamic: tagDetails.dynamic,
        staticTagValue: tagDetails.staticTagValue,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tagDetails]);

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const getTagKeyOptions = () => {
    const tagKeyOptions = tagKeys?.map((data) => ({ value: data.id, label: data.name }));
    const selectedTagKeys = tagData?.map((tags) => tags.tagKeyId) || [];
    return filterSelectedOptions(tagKeyOptions, selectedTagKeys, tagDetails?.tagKeyId);
  };

  return (
    <>
      <DialogBox open={open} fullWidth maxWidth={'sm'} onClose={onCancel}>
        <FormWrapper
          title={'Add Tag Details'}
          isSubmitting={formik.isSubmitting}
          errors={formWrapperError(formik)}
          submitText={'Save'}
          onCancel={onCancel}
          onSubmit={formik.handleSubmit}
          isPopUpView
        >
          <Grid container spacing={1}>
            <Grid container item justifyContent={'center'} spacing={3}>
              <Grid item xs={12}>
                <Select
                  value={formik.values.tagKey}
                  label="Tag Key *"
                  placeholder="Select"
                  name="tagKey"
                  handleChange={formik.handleChange}
                  error={formik.touched.tagKey && formik.errors.tagKey}
                  options={getTagKeyOptions()}
                />
              </Grid>
              <Grid item xs={12}>
                <Checkbox
                  label="Dynamic"
                  labelPlacement="end"
                  name="dynamic"
                  checked={formik.values.dynamic}
                  handleChange={formik.handleChange}
                  handleBlur={formik.handleBlur}
                  error={formik.touched.dynamic && formik.errors.dynamic}
                />
              </Grid>
              {formik.values.dynamic === false ? (
                <Grid item xs={12}>
                  <TextField
                    type="text"
                    value={formik.values.staticTagValue}
                    label="Tag Value *"
                    name="staticTagValue"
                    handleChange={formik.handleChange}
                    handleBlur={formik.handleBlur}
                    error={formik.touched.staticTagValue && formik.errors.staticTagValue}
                  />
                </Grid>
              ) : (
                <Grid item xs={12}>
                  <Select
                    value={formik.values.tagValue}
                    label="Dynamic Tag Value *"
                    placeholder="Select"
                    name="tagValue"
                    handleChange={formik.handleChange}
                    error={formik.touched.tagValue && formik.errors.tagValue}
                    options={tagValues?.map((data) => ({ value: data.id, label: data.name }))}
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  type="text"
                  value={formik.values.description}
                  label="Description *"
                  name="description"
                  handleChange={formik.handleChange}
                  handleBlur={formik.handleBlur}
                  error={formik.touched.description && formik.errors.description}
                />
              </Grid>
            </Grid>
          </Grid>
        </FormWrapper>
      </DialogBox>
    </>
  );
};

export default TagMappingForm;
