import React from 'react';
import { render } from '@testing-library/react';
import AddPermisssion from 'componentsV2/Administration/Permissions/AddPermission';

const mockNavigate = jest.fn();
jest.mock('hooks/useNblNavigate', () => ({
  __esModule: true,
  default: () => mockNavigate,
}));

jest.mock('hooks/useShowNavigationWarning', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('componentsV2/Administration/Permissions/AddPermission', () => {
  return {
    __esModule: true,
    default: ({ onSuccess, onClose }: any) => (
      <div>
        Mocked PermissionForm
        <button onClick={onSuccess}>Trigger Success</button>
        <button onClick={onClose}>Trigger Close</button>
      </div>
    ),
  };
});

describe('PermissionForm', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders PermissionForm component', () => {
    const { getByText } = render(<AddPermisssion onClose={() => {}} permissions={{}} />);
    expect(getByText('Mocked PermissionForm')).toBeInTheDocument();
  });
});
