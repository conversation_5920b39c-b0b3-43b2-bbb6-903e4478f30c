// eslint-disable-next-line
import { CatalogWithSummary, MonthlyCount } from './types';

export const addSummaryToCatalog = (allCatalogs: CatalogWithSummary[], allChildCatalogsWithSummary: CatalogWithSummary[]) => {
  return allCatalogs.map((catalog) => {
    const childSummaries = allChildCatalogsWithSummary
      .filter((child) => child.parentId === catalog.id)
      .map((child) => child.summary)
      .flat();
    const requestListByRequestType = allChildCatalogsWithSummary
      .filter((child) => child.parentId === catalog.id)
      .flatMap((child) => child.requestListByRequestType);

    const summary = childSummaries.reduce<MonthlyCount[]>((acc, item) => {
      if (!item) return acc;
      const { month, count } = item;
      const existing = acc.find((i) => i.month === month);
      if (existing) {
        existing.count += count;
      } else {
        acc.push({ month, count });
      }
      return acc;
    }, []);

    return { ...catalog, summary, requestListByRequestType };
  });
};
