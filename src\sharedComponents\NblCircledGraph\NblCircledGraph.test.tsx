import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { NblCircledGraph } from '.';

describe('NblCircledGraph component', () => {
  const props = {
    loading: false,
    data: [
      { width: 100, value: 70, label: 'IaaS' },
      { width: 88, value: 30, label: 'PaaS' },
      { width: 59, value: 23, label: 'Tools' },
    ],
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblCircledGraph {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
