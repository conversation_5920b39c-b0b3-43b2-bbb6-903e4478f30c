import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import RotatingSecretFields from '.';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import useNblNavigate from 'hooks/useNblNavigate';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

jest.mock('sharedComponents/NblContainers/NblFormContainer');
jest.mock('hooks/useNblNavigate');

jest.mock('../SecretValueField', () => () => {
  return <input data-testid="secret-value-field" />
});

describe('RotatingSecretFields Component', () => {
  const mockNblFormValues = {
    secretPolicy: '',
    devicePasswordKey: '',
    deviceUsernameKey: '',
    username: '',
    password: '',
    rotationInterval: 1,
    firstRun: null,
    notifyBeforeSecretExpiry: true,
  };

  const mockNblFormProps = {
    values: mockNblFormValues,
    touched: {},
    errors: {},
    handleChange: jest.fn(),
    handleBlur: jest.fn(),
    handleSubmit: jest.fn(),
    resetForm: jest.fn(),
    setFieldValue: jest.fn(),
  };

  const mockNavigate = jest.fn();

  beforeEach(() => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormValues: mockNblFormValues,
      nblFormProps: mockNblFormProps,
    });
    (useNblNavigate as jest.Mock).mockReturnValue(mockNavigate);
    mockNblFormProps.setFieldValue.mockClear();
    mockNavigate.mockClear();
  });

  it('renders the rotating secret fields', () => {
    render(
      <NebulaThemeProvider>
        <RotatingSecretFields policyDescription={''} />
      </NebulaThemeProvider>
    );

    expect(screen.getByText('Password Key')).toBeInTheDocument();
    expect(screen.getByText('Username Key')).toBeInTheDocument();
    expect(screen.getByText('Username')).toBeInTheDocument();
    expect(screen.getByTestId('secret-value-field')).toBeInTheDocument();
    expect(screen.getByText('Rotation Interval')).toBeInTheDocument();
    expect(screen.getByText('First Run')).toBeInTheDocument();
    expect(screen.getByText('Notify before secret expiry/rotation')).toBeInTheDocument();
  });

  it('updates notifyBeforeSecretExpiry when the checkbox is clicked', () => {
    render(
      <NebulaThemeProvider>
        <RotatingSecretFields policyDescription={''} />
      </NebulaThemeProvider>
    );
    const notifyCheckbox = screen.getByText('Notify before secret expiry/rotation');
    fireEvent.click(notifyCheckbox);
    expect(mockNblFormProps.setFieldValue).toHaveBeenCalled();
  });
});
