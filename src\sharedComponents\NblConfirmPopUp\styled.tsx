import { Dialog, DialogTitle, IconButton, styled } from '@mui/material';

interface PopUpComponentProps {
  maxWidth?: any;
}

export const StyledPopUpComponent = styled(Dialog)<PopUpComponentProps>(({ maxWidth }) => ({
  '& .MuiDialog-container': {
    backdropFilter: 'blur(14px)',
    '& .MuiPaper-root': {
      maxWidth: maxWidth,
    },
    '& .MuiSvgIcon-root': {
      height: '30px',
    },
  },
}));

export const StyledPopUpTitle = styled(DialogTitle)({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
});

export const StyledPopUpIconButton = styled(IconButton)({
  '& .MuiSvgIcon-root ': {
    color: 'inherit !important',
  },
});
