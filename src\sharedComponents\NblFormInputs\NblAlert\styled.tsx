import { styled } from '@mui/material/styles';
import { Alert, AlertTitle } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledAlert = styled(Alert)<{ theme?: NebulaTheme; fullWidth?: boolean }>(({ theme, fullWidth }) => {
  const { typography, palette } = theme;
  const { alert } = palette;
  return {
    ...typography.h6,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    ...(fullWidth && { width: '100%' }),
    color: alert.color,
    paddingTop: '0',
    paddingBottom: '0',
    '&.MuiAlert-colorWarning': {
      backgroundColor: alert.warningBgColor,
    },
    '&.MuiAlert-colorSuccess': {
      backgroundColor: alert.successBgColor,
    },
    '&.MuiAlert-colorInfo': {
      backgroundColor: alert.infoBgColor,
    },
    '&.MuiAlert-colorError': {
      backgroundColor: alert.errorBgColor,
    },
  };
});

const getTitleColor = (titleVariant: 'success' | 'error' | 'info' | 'warning', alert: any) => {
  // const {alert} = palette;
  switch (titleVariant) {
    case 'success':
      return alert.successColor;
    case 'error':
      return alert.errorColor;
    case 'info':
      return alert.infoColor;
    case 'warning':
      return alert.warningColor;
    default:
      return alert.color; // Default color
  }
};

export const StyledAlertTitle = styled(AlertTitle, {
  shouldForwardProp: (prop) => prop !== 'titleVariant',
})<{ theme?: NebulaTheme; titleVariant: 'success' | 'error' | 'info' | 'warning' }>(({ theme, titleVariant }) => {
  const { typography, palette } = theme;
  const { alert } = palette;
  return {
    ...typography.subtitle1,
    display: 'inline',
    marginRight: '4px',
    '&.MuiAlertTitle-root': {
      color: getTitleColor(titleVariant, alert),
    },
  };
});
