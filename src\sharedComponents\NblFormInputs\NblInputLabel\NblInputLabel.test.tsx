import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblInputLabel from '.';

describe('NblInputLabel component', () => {
  const props = {
    label: 'VM Size', 
    name: 'vmSize', 
    disabled: false, 
    mandatory: true
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblInputLabel {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
