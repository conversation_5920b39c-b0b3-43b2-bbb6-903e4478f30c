import React, { useEffect } from 'react';

import { Input<PERSON>abel, FormHelperText, Stack, MenuItem, TextField, Typography, useTheme, ListItemIcon } from '@mui/material';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import { truncateLabel } from 'sharedComponents/NblFormInputs/common';

interface SelectProps {
  disabled?: boolean;
  error?: boolean | string;
  label?: string;
  name?: string;
  options: any;
  placeholder?: string;
  type?: string;
  value: string | number;
  truncateSelected?: number;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  handleChange: (event: any) => void;
  datatestid?: any;
  showDefaultValue?: (value: string) => void;
}

const Select: React.FunctionComponent<SelectProps> = ({
  disabled,
  error,
  label,
  name,
  placeholder,
  value,
  options = [],
  truncateSelected,
  onMouseEnter,
  onMouseLeave,
  handleChange,
  datatestid,
  showDefaultValue,
}: SelectProps) => {
  const {
    palette: {
      forms: { fieldPlaceHolderColor },
    },
  }: NebulaTheme = useTheme();

  useEffect(() => {
    if (showDefaultValue && options?.length === 1 && !value) {
      showDefaultValue(options[0]['value']);
    }
  }, [options]);

  return (
    <Stack spacing={1}>
      <InputLabel htmlFor={name}>{label}</InputLabel>
      <TextField
        disabled={disabled}
        select
        id={name}
        value={value}
        name={name}
        onChange={handleChange}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        placeholder={placeholder}
        error={Boolean(error)}
        inputProps={{ 'data-testid': `${datatestid}` }}
        SelectProps={{
          displayEmpty: true,
          renderValue: (selected: unknown) => {
            if (!selected) {
              return <Typography sx={{ color: fieldPlaceHolderColor, opacity: 0.5 }}>{placeholder || 'Select'}</Typography>;
            }
            const selectedOption = options.find((option: { value: string | number }) => option.value === selected);
            if (!selectedOption) return '';
            return (
              <div style={{ display: 'flex', alignItems: 'center', overflow: 'hidden' }}>
                {selectedOption.icon && (
                  <ListItemIcon sx={{ svg: { fontSize: '1.2rem', mr: '10px' } }}>
                    <selectedOption.icon />
                  </ListItemIcon>
                )}
                {selectedOption?.label}
              </div>
            );
          },
        }}
      >
        {options.map((option: any, index: any) => (
          <MenuItem key={`${name}-option-${index}`} value={option.value} disabled={Boolean(option.disabled)}>
            {option.icon && (
              <ListItemIcon sx={{ svg: { fontSize: '1.2rem' } }}>
                <option.icon />
              </ListItemIcon>
            )}
            {truncateSelected ? truncateLabel(option.label, truncateSelected) : option.label}
          </MenuItem>
        ))}
      </TextField>
      <FormHelperText error>{error}</FormHelperText>
    </Stack>
  );
};

export default Select;
