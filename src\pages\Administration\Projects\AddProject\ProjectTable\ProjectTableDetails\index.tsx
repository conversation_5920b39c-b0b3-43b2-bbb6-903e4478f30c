/* eslint-disable no-unused-vars */
import { Grid, Typography, useTheme, Stack } from '@mui/material';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import { AddProjectData, ProjectTagData } from 'types';
interface ProjectTableProps {
  name: string;
  dataCenter: string;
  vlan: string;
  setTagDetails?: AddProjectData;
}

const ProjectTableDetails: React.FunctionComponent<ProjectTableProps> = ({
  name = '',
  dataCenter = '',
  vlan = '',
  setTagDetails,
}: ProjectTableProps) => {
  const theme: NebulaTheme = useTheme();
  const {
    palette: { dialog },
    typography,
  } = theme;
  const renderDataCenterDetails = () => {
    return (
      <>
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="body2" color={dialog.textColor} sx={{ fontWeight: typography.medium }} textAlign={'right'}>
              Setting Name:
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="inherit" color={dialog.textColor}>
              {name}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color={dialog.textColor} sx={{ fontWeight: typography.medium }} textAlign={'right'}>
              Data Center:
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="inherit" color={dialog.textColor}>
              {dataCenter}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color={dialog.textColor} sx={{ fontWeight: typography.medium }} textAlign={'right'}>
              {name === 'Network' ? 'VLAN(s):' : 'Label:'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="inherit" color={dialog.textColor}>
              {vlan}
            </Typography>
          </Grid>
        </Grid>
      </>
    );
  };

  const renderTagDetails = () => {
    return (
      <>
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="body2" color={dialog.textColor} sx={{ fontWeight: typography.medium }} textAlign={'right'}>
              Setting Name:
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="inherit" color={dialog.textColor}>
              {setTagDetails?.settingName}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color={dialog.textColor} sx={{ fontWeight: typography.medium }} textAlign={'right'}>
              Tag Key:
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="inherit" color={dialog.textColor}>
              {setTagDetails?.tagKey}
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="body2" color={dialog.textColor} sx={{ fontWeight: typography.medium }} textAlign={'right'}>
              Tag Value:
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography variant="inherit" color={dialog.textColor}>
              {setTagDetails?.tagValue}
            </Typography>
          </Grid>
        </Grid>
      </>
    );
  };
  return (
    <>
      <Stack sx={{ maxWidth: '65%', mx: 'auto', alignItems: 'center' }}>
        {setTagDetails?.settingName !== 'Tags' && renderDataCenterDetails()}
        {setTagDetails?.settingName === 'Tags' && renderTagDetails()}
      </Stack>
    </>
  );
};

export default ProjectTableDetails;
