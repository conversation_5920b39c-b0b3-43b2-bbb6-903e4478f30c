export const VmSizeMockValues = {
  status: true,
  data: [
    {
      _id: '663e2bbca6b6925c01599671',
      name: 'MongoDB 6',
      config: {
        size: {
          small: {
            customMemory: 16,
            root: '40',
            home: '8',
            opt: '8',
            var: '4',
            var_log: '4',
            var_log_audit: '4',
            var_tmp: '4',
            tmp: '4',
            customCores: 8,
            total: 500,
            addDisks: '4',
            addDisk2: 500,
            disk2name: 'mongodb',
            addDisk3: 500,
            disk3name: 'mongobackup',
            addDisk4: 40,
            disk4name: 'mongolog',
            addDisk5: 50,
            disk5name: 'mongohome',
            diskFilesystem: 'xfs',
          },
          medium: {
            customMemory: 32,
            root: '40',
            home: '8',
            opt: '8',
            var: '4',
            var_log: '4',
            var_log_audit: '4',
            var_tmp: '4',
            tmp: '4',
            customCores: 16,
            total: 750,
            addDisks: '4',
            addDisk2: 750,
            disk2name: 'mongodb',
            addDisk3: 750,
            disk3name: 'mongobackup',
            addDisk4: 40,
            disk4name: 'mongolog',
            addDisk5: 50,
            disk5name: 'mongohome',
            diskFilesystem: 'xfs',
          },
          large: {
            customMemory: 64,
            root: '40',
            home: '8',
            opt: '8',
            var: '4',
            var_log: '4',
            var_log_audit: '4',
            var_tmp: '4',
            tmp: '4',
            customCores: 24,
            total: 1000,
            addDisks: '4',
            addDisk2: 1000,
            disk2name: 'mongodb',
            addDisk3: 1000,
            disk3name: 'mongobackup',
            addDisk4: 40,
            disk4name: 'mongolog',
            addDisk5: 50,
            disk5name: 'mongohome',
            diskFilesystem: 'xfs',
          },
        },
      },
      shortName: 'mongodb6',
    },
    {
      _id: '6641e1f6a6b6925c01599683',
      name: 'MongoDB 7',
      config: {
        size: {
          small: {
            customMemory: 16,
            root: '40',
            home: '8',
            opt: '8',
            var: '4',
            var_log: '4',
            var_log_audit: '4',
            var_tmp: '4',
            tmp: '4',
            customCores: 8,
            total: 500,
            addDisks: '4',
            addDisk2: 500,
            disk2name: 'mongodb',
            addDisk3: 500,
            disk3name: 'mongobackup',
            addDisk4: 40,
            disk4name: 'mongolog',
            addDisk5: 50,
            disk5name: 'mongohome',
            diskFilesystem: 'xfs',
          },
          medium: {
            customMemory: 32,
            root: '40',
            home: '8',
            opt: '8',
            var: '4',
            var_log: '4',
            var_log_audit: '4',
            var_tmp: '4',
            tmp: '4',
            customCores: 16,
            total: 750,
            addDisks: '4',
            addDisk2: 750,
            disk2name: 'mongodb',
            addDisk3: 750,
            disk3name: 'mongobackup',
            addDisk4: 40,
            disk4name: 'mongolog',
            addDisk5: 50,
            disk5name: 'mongohome',
            diskFilesystem: 'xfs',
          },
          large: {
            customMemory: 64,
            root: '40',
            home: '8',
            opt: '8',
            var: '4',
            var_log: '4',
            var_log_audit: '4',
            var_tmp: '4',
            tmp: '4',
            customCores: 24,
            total: 1000,
            addDisks: '4',
            addDisk2: 1000,
            disk2name: 'mongodb',
            addDisk3: 1000,
            disk3name: 'mongobackup',
            addDisk4: 40,
            disk4name: 'mongolog',
            addDisk5: 50,
            disk5name: 'mongohome',
            diskFilesystem: 'xfs',
          },
        },
      },
      shortName: 'mongodb7',
    },
    {
      _id: '666c143daee60c1b679ff546',
      name: 'Linux 7',
      shortName: 'linux7',
      config: {
        size: {
          tiny: {
            customMemory: 4,
            root: '20',
            home: '2',
            opt: '4',
            var: '6',
            var_log: '2',
            var_log_audit: '2',
            var_tmp: '2',
            tmp: '2',
            customCores: 2,
          },
          small: {
            customMemory: 7,
            root: 40,
            home: 2,
            opt: 6,
            var: 4,
            var_log: 9,
            var_log_audit: 2,
            var_tmp: 3,
            tmp: 4,
            customCores: 2,
            total: 70,
          },
          medium: {
            customMemory: 16,
            root: '20',
            home: '8',
            opt: '16',
            var: '24',
            var_log: '8',
            var_log_audit: '8',
            var_tmp: '8',
            tmp: '8',
            customCores: 8,
          },
          large: {
            customMemory: 32,
            root: '20',
            home: '16',
            opt: '32',
            var: '48',
            var_log: '16',
            var_log_audit: '16',
            var_tmp: '16',
            tmp: '16',
            customCores: 16,
          },
          'x-large': {
            customMemory: 64,
            root: '20',
            home: '32',
            opt: '64',
            var: '96',
            var_log: '32',
            var_log_audit: '32',
            var_tmp: '32',
            tmp: '32',
            customCores: 32,
          },
        },
      },
    },
    {
      _id: '666febe8e9fbb02de411586d',
      name: 'Windows',
      shortName: 'windows',
      config: {
        size: {
          tiny: {
            customMemory: 4,
            root: '60',
            customCores: 8,
            customVolume: 60,
            total: 60,
          },
          small: {
            customMemory: 8,
            root: '60',
            customCores: 8,
            customVolume: 60,
            total: 60,
          },
          medium: {
            customMemory: 16,
            root: '80',
            customCores: 8,
            customVolume: 80,
            total: 80,
          },
          large: {
            customMemory: 32,
            root: '120',
            customCores: 16,
            customVolume: 120,
            total: 120,
          },
          'x-large': {
            customMemory: 64,
            root: '160',
            customCores: 16,
            customVolume: 160,
            total: 160,
          },
        },
      },
    },
    {
      _id: '666febe8e9fbb02de411586e',
      name: 'Ubuntu',
      shortName: 'ubuntu',
      config: {
        size: {
          tiny: {
            customMemory: 4,
            root: '20',
            home: '2',
            opt: '8',
            var: '6',
            var_log: '4',
            var_log_audit: '4',
            var_tmp: '2',
            tmp: '2',
            customCores: 4,
            total: 46,
          },
          small: {
            customMemory: 8,
            root: '20',
            home: '4',
            opt: '8',
            var: '12',
            var_log: '4',
            var_log_audit: '4',
            var_tmp: '4',
            tmp: '4',
            customCores: 4,
            total: 60,
          },
          medium: {
            customMemory: 16,
            root: 40,
            home: 2,
            opt: 6,
            var: 4,
            var_log: 9,
            var_log_audit: 2,
            var_tmp: 3,
            tmp: 4,
            customCores: 2,
            total: 70,
          },
          large: {
            customMemory: 32,
            root: '20',
            home: '16',
            opt: '32',
            var: '48',
            var_log: '16',
            var_log_audit: '16',
            var_tmp: '16',
            tmp: '16',
            customCores: 16,
            total: 180,
          },
          'x-large': {
            customMemory: 64,
            root: '20',
            home: '32',
            opt: '64',
            var: '96',
            var_log: '32',
            var_log_audit: '32',
            var_tmp: '32',
            tmp: '32',
            customCores: 16,
            total: 340,
          },
        },
      },
    },
    {
      _id: '666fec5ce9fbb02de411586f',
      name: 'Linux 8 & 9',
      shortName: 'linux8and9',
      config: {
        size: {
          tiny: {
            customMemory: 4,
            root: '20',
            home: '2',
            opt: '4',
            var: '6',
            var_log: '2',
            var_log_audit: '2',
            var_tmp: '2',
            tmp: '2',
            customCores: 2,
            total: 46,
          },
          small: {
            customMemory: 8,
            root: '20',
            home: '4',
            opt: '8',
            var: '12',
            var_log: '4',
            var_log_audit: '4',
            var_tmp: '4',
            tmp: '4',
            customCores: 4,
            total: 60,
          },
          medium: {
            customMemory: 16,
            root: '20',
            home: '8',
            opt: '16',
            var: '24',
            var_log: '8',
            var_log_audit: '8',
            var_tmp: '8',
            tmp: '8',
            customCores: 8,
            total: 100,
          },
          large: {
            customMemory: 32,
            root: '20',
            home: '16',
            opt: '32',
            var: '48',
            var_log: '16',
            var_log_audit: '16',
            var_tmp: '16',
            tmp: '16',
            customCores: 16,
            total: 180,
          },
          'x-large': {
            customMemory: 64,
            root: '20',
            home: '32',
            opt: '64',
            var: '96',
            var_log: '32',
            var_log_audit: '32',
            var_tmp: '32',
            tmp: '32',
            customCores: 32,
            total: 340,
          },
        },
      },
    },
    {
      _id: '667afbc7dd491511bd70184c',
      name: 'Linux 11',
      shortName: 'linux11',
      config: {
        size: {
          small: {
            customMemory: 7,
            root: 40,
            home: 2,
            opt: 6,
            var: 4,
            var_log: 9,
            var_log_audit: 2,
            var_tmp: 3,
            tmp: 4,
            customCores: 2,
            total: 70,
          },
        },
      },
    },
  ],
};

export const GetVMType = {
  data: [
    {
      id: '65fd4594fada3fc9c74bfba0',
      name: 'Ubuntu',
      type: 'VIRTUALMACHINE',
      displayName:'NCE'
    },
    {
      id: '65fd4594fada3fc9c74bfb9e',
      name: 'Linux 8 & 9',
      type: 'VIRTUALMACHINE',
       displayName:'STAMP'
    },
    {
      id: '65fd4594fada3fc9c74bfba2',
      name: 'Windows',
      type: 'VIRTUALMACHINE',
       displayName:'NCE'
    },
  ],
  status: true,
};

export const GetVMSizeCores = {
  content: [
    {
      option: 2,
    },
    {
      option: 3,
    },
    {
      option: 4,
    },
    {
      option: 8,
    },
    {
      option: 16,
    },
    {
      option: 32,
    },
    {
      option: 64,
    },
  ],
};
