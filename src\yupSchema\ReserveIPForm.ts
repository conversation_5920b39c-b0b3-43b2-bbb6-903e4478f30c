import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';

export const validationSchema = yup.object().shape({
  datacenter: yup.string().required('Datacenter is required'),
  description: yup
    .string()
    .trim()
    .required('Description is required')
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),

  company: yup.string().required('Company is required'),
  ipAddressType: yup.string().required('Address Type is required'),
  environment: yup.string().required('Environment is required'),
  cidrBlock: yup.string().required('CIDR Block is required'),
  addressReachability: yup.string().required('Public/Private is required'),
  comment: yup
    .string()
    .trim()
    .required('Comment is required')
    .matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),

  ipAllocationType: yup.string().required('IP Allocation Type is required'),
});