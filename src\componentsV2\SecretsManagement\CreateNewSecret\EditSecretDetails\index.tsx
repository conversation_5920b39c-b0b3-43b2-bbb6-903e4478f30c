import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { useTheme } from '@mui/material';

import { FormValues, initialValues } from 'componentsV2/SecretsManagement/CreateNewSecret';
import {
  rotatingSecreInitialValues,
  RotatingSecretFormValues,
} from 'componentsV2/SecretsManagement/CreateNewSecret/CreateNewSecretForm/RotatingSecretFields';
import { NebulaTheme } from 'NebulaTheme/type';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { NblFormErrors, NblFormTouched, useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import NblViewDetailsOverview, {
  // eslint-disable-next-line no-unused-vars
  NblViewDetailsOverviewProps,
} from 'sharedComponents/NblContainers/NblViewDetailsContainer/NblViewDetailsOverview';
import NblTypography from 'sharedComponents/NblTypography';
import { generateEnum } from 'utils/common';
import NblAutoComplete from 'sharedComponents/NblFormInputs/NblAutoComplete';
import NblCounterField from 'sharedComponents/NblFormInputs/NblCounterField';
import NblDatePicker from 'sharedComponents/NblFormInputs/NblDatePicker';
import useSecretCommonFields from '../CreateNewSecretForm/SecretCommonFields';
import { SecretType } from 'types/Enums/SecretType';
import MaskedValueCell from 'componentsV2/MaskedValueCell';
import { useApiService } from 'api/ApiService/context';
import { EditIcon } from 'assets/images/icons/custom-icons';
import DeleteSecret from 'componentsV2/SecretsManagement/DeleteSecret';
import { useParams, useSearchParams } from 'react-router-dom';
import useNblNavigate from 'hooks/useNblNavigate';
import NblInputAdornment from 'sharedComponents/NblFormInputs/NblInputAdorment';
import NblTooltip from 'sharedComponents/NblTooltip';
import { InfoOutlined, InfoRounded } from '@mui/icons-material';
import { GetPasswordPolicies } from 'api/ApiService/SecretsManagementService/type';
import SecretValueField from '../CreateNewSecretForm/SecretValueField';
import NblConfirmPopUp from 'sharedComponents/NblConfirmPopUp';

export const fieldDisplayNames: Record<string, string> = {
  secretTTLInHours: 'Rotation Interval in Hrs.',
  version: 'Version',
  password: 'Password',
  policyId: 'Policy',
  nextRotationDate: 'Next Rotation',
  type: 'Type',
  rotationType: 'Rotation Type',
  vaultNamespace: 'Namespace',
  vaultPath: 'Path',
  devicePasswordKey: 'Password Key',
  status: 'Vault Status',
  active: 'Active',
  lastDeviceSyncStatus: 'Last Secret Rotation Status',
  error: 'Error',
  secret: 'Password',
};

const EditSecretDetails = ({ initialUpdateMode = false }) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();
  const { nblFormProps, nblFormValues } = useNblForms<FormValues>();
  const { apiSecretsManagement } = useApiService();
  const commonFields = useSecretCommonFields();
  const navigate = useNblNavigate();
  const [searchParams] = useSearchParams();
  const secretId = searchParams.get('secretId');
  const { resourceId } = useParams();
  const submitRef = useRef<HTMLButtonElement>(null);

  //States
  const [updateMode, setUpdateMode] = useState(initialUpdateMode);
  const [passwordPolicies, setPaswordPolicies] = useState<
    Array<{ label: string; value: string; rules: GetPasswordPolicies['data'][0]['policyRules'] }>
  >([]);
  const [showDialog, setShowDialog] = useState(false);

  //Local
  const FIELD_NAMES = generateEnum({ ...initialValues, ...rotatingSecreInitialValues });
  const selectedRule = passwordPolicies.find((policy) => policy.value === nblFormValues.policyId)?.rules;
  const isSecretChanged =
    nblFormProps.initialValues.vaultPassword !== nblFormValues.vaultPassword ||
    nblFormProps.initialValues.userNamePassword !== nblFormValues.userNamePassword;

  //Column Definitions
  const fields: NblViewDetailsOverviewProps['viewDetailsFields'] = [
    ...commonFields,
    {
      title: 'Path',
      value: nblFormValues.path,
    },
    {
      title: 'Secret Policy',
      value: (
        <NblFlexContainer>
          {updateMode ? (
            <NblAutoComplete
              mandatory
              label=""
              placeholder="Select"
              name={FIELD_NAMES.policyId}
              value={nblFormValues.policyId}
              options={
                nblFormValues.type === SecretType.NormalSecret
                  ? [{ label: 'Not Required', value: '' }, ...passwordPolicies]
                  : passwordPolicies
              }
              onChange={(v) => {
                nblFormProps.setFieldValue(FIELD_NAMES.policyId, v);
              }}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.policyId ? nblFormProps.errors.policyId : ' '}
              error={Boolean(nblFormProps.errors.policyId && nblFormProps.touched.policyId)}
            />
          ) : (
            passwordPolicies.find((policy) => policy.value === nblFormValues.policyId)?.label || 'NA'
          )}
          {selectedRule ? (
            <NblTooltip
              tooltipMessage={`Max Characters: ${selectedRule.totalCharactersLength}\n
                        Min Upper Characters: ${selectedRule.upperCaseLettersCount}\n
                        Min Lower Characters: ${selectedRule.lowerCaseLettersCount}\n
                        Accepted Special Characters: ${selectedRule.acceptedSpecialCharacters}\n
                        Min Numerical Characters Count: ${selectedRule.numericalCharactersCount}\n
                        Min Special Characters Count: ${selectedRule.specialCharactersCount}`}
            >
              <InfoRounded sx={{ cursor: 'pointer', fontSize: '1.3rem', marginTop: updateMode ? '5px' : '-2px' }} />
            </NblTooltip>
          ) : (
            <NblTooltip tooltipMessage="Select a policy for secret">
              <InfoOutlined sx={{ cursor: 'pointer', fontSize: '1.3rem', marginTop: updateMode ? '5px' : '-2px' }} />
            </NblTooltip>
          )}
        </NblFlexContainer>
      ),
    },
    { title: nblFormValues.type === SecretType.RotatingSecret ? 'Password Key' : 'Key', value: nblFormValues.vaultKey },
    {
      title: nblFormValues.type === SecretType.RotatingSecret ? 'Password' : 'Value',
      value: updateMode ? (
        <SecretValueField
          label=""
          isPasswordPolicyRequired={Boolean(
            nblFormValues.type === SecretType.RotatingSecret || (nblFormValues.type === SecretType.NormalSecret && nblFormValues.policyId)
          )}
          policyId={nblFormValues.policyId}
          name={FIELD_NAMES.vaultPassword}
          value={nblFormValues.vaultPassword}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          setPasswordValidStatus={(isValid: boolean) => nblFormProps.setFieldValue('isPasswordValid', isValid)}
          helperText={(nblFormProps.touched.vaultPassword ? nblFormProps.errors.vaultPassword : ' ') || ''}
          error={Boolean(nblFormProps.errors.vaultPassword && nblFormProps.touched.vaultPassword)}
          policyDescription={selectedRule?.passwordDescription || ''}
        />
      ) : (
        <NblFlexContainer width="75%" margin="-5px 0 0 0">
          <MaskedValueCell value={nblFormValues.vaultPassword} />
        </NblFlexContainer>
      ),
    },
  ];

  //Instance of RotatingSecret
  if (nblFormValues.type === SecretType.RotatingSecret) {
    const rotatingTouched = nblFormProps.touched as NblFormTouched<RotatingSecretFormValues>;
    const rotatingErrors = nblFormProps.errors as NblFormErrors<RotatingSecretFormValues>;
    fields.push(
      { title: 'Username Key', value: nblFormValues.userNameKey },
      {
        title: 'Username',
        value: (
          <NblFlexContainer width="75%" margin="-5px 0 0 0">
            <MaskedValueCell value={String(nblFormValues.userNamePassword)} />
          </NblFlexContainer>
        ),
      },
      {
        title: 'Rotation Interval',
        value: updateMode ? (
          <NblCounterField
            width="100%"
            type="number"
            label=""
            name={FIELD_NAMES.secretTTLInHours}
            initialValue={nblFormValues.secretTTLInHours || 0}
            maxValue={90}
            handleChange={(v) => {
              nblFormProps.setFieldValue(FIELD_NAMES.secretTTLInHours, v).then(() => {
                nblFormProps.setFieldTouched(FIELD_NAMES.secretTTLInHours, true);
              });
            }}
            handleBlur={nblFormProps.handleBlur}
            helperText={rotatingTouched.secretTTLInHours ? rotatingErrors.secretTTLInHours : ' '}
            error={Boolean(rotatingErrors.secretTTLInHours && rotatingTouched.secretTTLInHours)}
            endAdornment={
              <NblInputAdornment
                position="end"
                label={nblFormValues.secretTTLInHours && nblFormValues.secretTTLInHours > 1 ? 'Days' : 'Day'}
              />
            }
          />
        ) : (
          nblFormValues.secretTTLInHours
        ),
      },
      {
        title: 'First Run',
        value: updateMode ? (
          <NblDatePicker
            label=""
            name={FIELD_NAMES.nextRotationDate}
            value={nblFormValues.nextRotationDate}
            handleChange={(newValue) => {
              nblFormProps.setFieldValue(FIELD_NAMES.nextRotationDate, newValue).then(() => {
                nblFormProps.setFieldTouched(FIELD_NAMES.nextRotationDate, true);
              });
            }}
            helperText={typeof rotatingErrors.nextRotationDate === 'string' ? rotatingErrors.nextRotationDate || ' ' : ''}
            error={Boolean(rotatingErrors.nextRotationDate)}
            minDate={dayjs()}
            withTimePicker={true}
          />
        ) : (
          dayjs(nblFormValues.nextRotationDate).format('YYYY-MM-DD HH:mm')
        ),
      },
      { title: 'Rotation Type', value: nblFormValues.rotationType },
      { title: 'Notify before expiry', value: nblFormValues.notifyBeforeTokenExpiry ? 'Yes' : 'No' }
    );
  }

  //Side effects
  useEffect(() => {
    if (resourceId) {
      apiSecretsManagement.getPasswordPolicies(resourceId).then((res) => {
        if (res.status && res.data) {
          setPaswordPolicies(res.data.map((policy) => ({ label: policy.policyName, value: policy.policyId, rules: policy.policyRules })));
        }
      });
    }
  }, [apiSecretsManagement, resourceId]);

  //JSX
  return (
    <NblBorderContainer height="auto" backgroundColor={theme.palette.secondary.main} padding="16px">
      <NblFlexContainer direction="column" spacing={2}>
        <NblFlexContainer height="auto" justifyContent="space-between">
          <NblTypography variant="h2" color="shade1" weight="bold">
            Secret Details
          </NblTypography>
          <NblFlexContainer width="auto">
            <NblButton
              buttonID={`btn${updateMode ? 'Cancel' : 'Edit'}`}
              color="primary"
              disabled={!nblFormValues.active}
              tooltip={!nblFormValues.active ? 'Secret is inactive' : undefined}
              onClick={() => {
                setUpdateMode((prev) => !prev);
                nblFormProps.resetForm();
              }}
              {...(!updateMode && { startIcon: <EditIcon /> })}
            >
              {updateMode ? 'Cancel' : 'Edit'}
            </NblButton>
            {updateMode && (
              <NblButton
                type={isSecretChanged ? 'button' : 'submit'}
                buttonID={`btnUpdate`}
                color={'primary'}
                variant={'contained'}
                onClick={isSecretChanged ? () => setShowDialog(true) : undefined}
                disabled={
                  updateMode &&
                  (!Object.keys(nblFormProps.touched).length ||
                    Object.values(nblFormProps.errors).length > 0 ||
                    JSON.stringify(nblFormProps.initialValues) === JSON.stringify(nblFormValues))
                }
              >
                {'Update'}
              </NblButton>
            )}
            {secretId && !updateMode && (
              <DeleteSecret secretId={[secretId]} type="button" disabled={!nblFormValues.active} onSuccess={() => navigate(-1)} />
            )}
          </NblFlexContainer>
        </NblFlexContainer>
        <NblViewDetailsOverview showBorder columns={5} spacingX={4} viewDetailsFields={fields} />
      </NblFlexContainer>
      <NblConfirmPopUp
        open={showDialog}
        title="Confirmation"
        content="Password will be updated only in Vault. Do you want to proceed?"
        submitText="Yes"
        cancelText="No"
        onSubmit={() => {
          submitRef.current?.click();
          setShowDialog(false);
        }}
        onClose={() => setShowDialog(false)}
      />
      <button type="submit" ref={submitRef} style={{ visibility: 'hidden' }}></button>
    </NblBorderContainer>
  );
};

export default EditSecretDetails;
