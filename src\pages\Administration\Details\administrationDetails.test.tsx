import { render, act } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import { BrowserRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';
import AdministrationDetails from '.';

describe('AdministrationDetails component', () => {
  const mockStore = configureMockStore();
  const store = mockStore({ common: { exposureParams: ['ec_AddTag'] } });

  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <Router>
            <ReduxProvider store={store}>
              <ThemeProvider>
                <AdministrationDetails></AdministrationDetails>
              </ThemeProvider>
            </ReduxProvider>
          </Router>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
