import { useTheme } from '@mui/material';

import Projects from 'componentsV2/Projects';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { NebulaTheme } from 'NebulaTheme/type';

export interface MyProjectsProps {}

const MyProjects: React.FunctionComponent<MyProjectsProps> = () => {
  //Hooks
  const theme = useTheme<NebulaTheme>();
  const { palette } = theme;

  //JSX
  return (
    <NblBorderContainer padding="10px" backgroundColor={palette.background.paper} height="100%">
      <Projects />
    </NblBorderContainer>
  );
};

export default MyProjects;
