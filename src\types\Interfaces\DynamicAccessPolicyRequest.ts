type ServiceCatalog = {
  catalogName: string;
  catalogType: string;
};

type Device = {
  deviceHostname: string;
  deviceCommonFwDataUrl: string;
};

type ParentServiceRequestDetail = {
  id: string;
  requestType: string;
  status: string;
  createdBy: string;
  requesterEmail: string;
  serviceRequestId: string;
  metadata: {
    serviceCatalog: ServiceCatalog;
  };
  payload: {
    project: string;
    requestCreator: string;
    requestName: string;
    appId: string;
    commonApplication: string;
    applicationIpAddresses: string;
    inboundPortAndProtocol: string;
    outboundPortAndProtocol: string;
    reason: string;
    deeplinkUrl: string;
    gitLabBranchName: string;
  };
  startedAt: string;
  completedAt: string | null;
  editedAt: string | null;
  approvalStatus: string;
  catalogStepsId: string;
  applicableSubTypes: any[];
  schemaVersion: number;
  approvalDetails: {
    approvalGroup: string;
    approvalStatus: string;
    approvedOrRejectedAt: string;
    approvedOrRejectedBy: string;
    level: number;
    rejectedReason: string | null;
  }[];
  downstreamError: any[];
  executionHistory: any[];
  createdAt: string;
  updatedAt: string;
  downstreamResponseData: {
    gitlabDetails: {
      mergeRequestId: string;
      webUrl: string;
    };
  };
  systemUpdate: {
    downStreamResponse: {
      jobName: string;
      timestamp: string;
      response: {
        nebulaRequestId: string;
        gitlabRequestId: string;
        result: string;
      };
    }[];
    errors: any[];
  };
  reviewedBy: string;
  childServiceRequestIds: string[];
};

type RequestPayload = {
  dapTaskID: string;
  nebulaRequestId: string;
  date: string;
  result: string;
  devices: Device[];
  parentServiceRequestDetails: ParentServiceRequestDetail;
};

export interface DynamicAccessPolicyRequest {
  id: string;
  requestType: string;
  status: string;
  createdBy: string;
  requesterEmail: string;
  serviceRequestId: string;
  parentServiceRequestId: string;
  metadata: {
    serviceCatalog: ServiceCatalog;
  };
  payload: RequestPayload;
  startedAt: string;
  completedAt: string | null;
  editedAt: string | null;
  approvalStatus: string;
  catalogStepsId: string;
  applicableSubTypes: any[];
  schemaVersion: number;
  approvalDetails: any[];
  downstreamError: any[];
  executionHistory: any[];
  createdAt: string;
  updatedAt: string;
}
