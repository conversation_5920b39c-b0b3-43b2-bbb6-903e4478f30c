// types
// eslint-disable-next-line no-unused-vars
import { CatalogLevel, CatalogsItem } from 'store/reducers/catalogs';
//eslint-disable-next-line no-unused-vars
import { NavigationBartStaticItem } from 'types/Interfaces/NavigationBarStaticResponse';

export class BarItem {
  id: string;
  label: string;
  name: string;
  icon: string;
  enabled: boolean;

  constructor(levelItem?: Omit<CatalogsItem, 'sequenceNumber'>) {
    this.id = levelItem?.id ?? '';
    this.icon = levelItem?.icon ?? '';
    this.label = levelItem?.name ?? '';
    this.name = levelItem?.shortName ?? '';
    this.enabled = levelItem?.enabled ?? true;
  }
}

export class Bar3Item {
  level3Item: BarItem;
  level4Items: BarItem[];

  constructor(level3Item?: CatalogsItem, level4Items?: CatalogLevel) {
    this.level3Item = new BarItem(level3Item);
    this.level4Items = level4Items?.[level3Item?.shortName || '']?.map((obj) => new BarItem(obj)) || [new BarItem()];
  }
}
export class BarMenuItem extends BarItem {
  constructor(menuItem: NavigationBartStaticItem) {
    super({
      id: menuItem.id || '',
      icon: menuItem?.icon || '',
      name: menuItem?.title || '',
      shortName: menuItem?.url || '',
      description: '',
      enabled: !menuItem.disabled,
    });
  }
}
