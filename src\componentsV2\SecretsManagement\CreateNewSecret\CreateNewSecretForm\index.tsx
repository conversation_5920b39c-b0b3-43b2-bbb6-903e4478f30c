import { IncreaseVMCountIcon } from 'assets/images/icons/custom-icons';
import { InfoOutlined, InfoRounded } from '@mui/icons-material';

import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblAutoComplete from 'sharedComponents/NblFormInputs/NblAutoComplete';
import { FormValues, initialValues } from '..';
import { generateEnum } from 'utils/common';
import { SecretType } from 'types/Enums/SecretType';
import NormalSecretFields from './NormalSecretFields';
import RotatingSecretFields, { rotatingSecreInitialValues, RotatingSecretFormValues } from './RotatingSecretFields';
// eslint-disable-next-line no-unused-vars
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import React, { useEffect, useState } from 'react';
import useSecretCommonFields from './SecretCommonFields';
import NblFieldWrapper from 'sharedComponents/NblFormInputs/NblFieldWrapper';
import NblTypography from 'sharedComponents/NblTypography';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import useNblNavigate from 'hooks/useNblNavigate';
import { useApiService } from 'api/ApiService/context';
import { GetPasswordPolicies } from 'api/ApiService/SecretsManagementService/type';
import NblTooltip from 'sharedComponents/NblTooltip';
import { useParams } from 'react-router';

interface CreateNewSecretFormProps {
  setFormInitialValues: React.Dispatch<React.SetStateAction<FormValues>>;
}

const CreateNewSecretForm: React.FC<CreateNewSecretFormProps> = () => {
  //Hooks
  const { nblFormValues, nblFormProps } = useNblForms<FormValues>();
  const FIELD_NAMES = generateEnum(initialValues as Required<FormValues>);
  const secretTypeOptions = Object.values(SecretType).map((secretType) => ({ label: secretType, value: secretType }));
  const commonFields = useSecretCommonFields((namespaceName, namespacePath) => {
    nblFormProps.setFieldValue(FIELD_NAMES.namespaceName, namespaceName);
    nblFormProps.setFieldValue(FIELD_NAMES.namespacePath, namespacePath);
  });
  const navigate = useNblNavigate();
  const { apiSecretsManagement } = useApiService();
  const { resourceId } = useParams();

  //States
  const [passwordPolicies, setPaswordPolicies] = useState<
    Array<{ label: string; value: string; rules: GetPasswordPolicies['data'][0]['policyRules'] }>
  >([]);

  //Local
  const selectedRule = passwordPolicies.find((policy) => policy.value === nblFormValues.policyId)?.rules;

  //Side effects
  useEffect(() => {
    if (nblFormValues.type === SecretType.RotatingSecret) {
      nblFormProps.setValues({ ...rotatingSecreInitialValues, ...nblFormValues });
    } else {
      const newFormValues = { ...nblFormValues };
      Object.keys(rotatingSecreInitialValues).forEach((field) => {
        if (!Object.keys(initialValues).includes(field)) {
          delete newFormValues[field as keyof RotatingSecretFormValues];
        }
      });

      nblFormProps.setValues(newFormValues);
    }
  }, [nblFormValues.type]);

  useEffect(() => {
    if (resourceId) {
      apiSecretsManagement.getPasswordPolicies(resourceId).then((res) => {
        if (res.status && res.data) {
          setPaswordPolicies(res.data.map((policy) => ({ label: policy.policyName, value: policy.policyId, rules: policy.policyRules })));
        }
      });
    }
  }, [apiSecretsManagement, resourceId]);

  //JSX
  return (
    <NblGridContainer columns={5} spacingX={5} spacingY={2}>
      {
        <>
          {commonFields.map((field) => (
            <NblGridItem key={field.title} colspan={field.span} margin={'0 0 16px 0'}>
              <NblFieldWrapper mandatory name={field.title} disabled={false} label={field.title} error={false} helperText={''}>
                <NblTypography
                  variant="subtitle1"
                  padding={field.value ? '10px 0 5px 0' : '0'}
                  weight={'medium'}
                  color={'shade1'}
                  whiteSpace={'pre-line'}
                  wordBreak={'break-word'}
                >
                  {field.value}
                </NblTypography>
              </NblFieldWrapper>
            </NblGridItem>
          ))}
          <NblGridItem>
            <NblAutoComplete
              mandatory
              label="Secret Type"
              placeholder="Select"
              name={FIELD_NAMES.type}
              value={nblFormValues.type}
              options={secretTypeOptions}
              onChange={(v) => {
                nblFormProps.setFieldValue(FIELD_NAMES.type, v);
              }}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.touched.type ? nblFormProps.errors.type || ' ' : ' '}
              error={Boolean(nblFormProps.errors.type && nblFormProps.touched.type)}
            />
          </NblGridItem>
          {nblFormValues.type && (
            <>
              <NblGridItem>
                <NblTextField
                  mandatory
                  type="text"
                  label="Path"
                  placeholder="Enter"
                  name={FIELD_NAMES.path}
                  value={nblFormValues.path}
                  handleChange={nblFormProps.handleChange}
                  handleBlur={nblFormProps.handleBlur}
                  helperText={nblFormProps.touched.path ? nblFormProps.errors.path || ' ' : ' '}
                  error={Boolean(nblFormProps.errors.path && nblFormProps.touched.path)}
                />
              </NblGridItem>
              <NblGridItem>
                <NblFlexContainer alignItems="center">
                  <NblAutoComplete
                    mandatory={nblFormValues.type === SecretType.RotatingSecret}
                    label="Secret Policy"
                    placeholder="Select"
                    name={FIELD_NAMES.policyId}
                    value={nblFormValues.policyId}
                    options={
                      nblFormValues.type === SecretType.NormalSecret
                        ? [{ label: 'Not Required', value: '' }, ...passwordPolicies]
                        : passwordPolicies
                    }
                    onChange={(v) => {
                      nblFormProps.setFieldValue(FIELD_NAMES.policyId, v);
                    }}
                    handleBlur={nblFormProps.handleBlur}
                    helperText={nblFormProps.touched.policyId ? nblFormProps.errors.policyId || ' ' : ' '}
                    error={Boolean(nblFormProps.errors.policyId && nblFormProps.touched.policyId)}
                  />
                  {selectedRule ? (
                    <NblTooltip
                      tooltipMessage={`Max Characters: ${selectedRule.totalCharactersLength}\n
                        Min Upper Characters: ${selectedRule.upperCaseLettersCount}\n
                        Min Lower Characters: ${selectedRule.lowerCaseLettersCount}\n
                        Accepted Special Characters: ${selectedRule.acceptedSpecialCharacters}\n
                        Min Numerical Characters Count: ${selectedRule.numericalCharactersCount}\n
                        Min Special Characters Count: ${selectedRule.specialCharactersCount}`}
                    >
                      <InfoRounded sx={{ cursor: 'pointer', fontSize: '1.3rem', marginTop: '2px' }} />
                    </NblTooltip>
                  ) : (
                    <NblTooltip tooltipMessage="Select a policy for secret">
                      <InfoOutlined sx={{ cursor: 'pointer', fontSize: '1.3rem', marginTop: '2px' }} />
                    </NblTooltip>
                  )}
                </NblFlexContainer>
              </NblGridItem>
              <NblGridItem colspan={2}>
                <NblFlexContainer
                  height="auto"
                  margin="30px 0 0 0"
                  alignItems="center"
                  cursor="pointer"
                  onClick={() => navigate('create-password-policy')}
                >
                  <IncreaseVMCountIcon sx={{ fontSize: '1.3rem' }} />
                  <NblTypography variant="subtitle2" weight="medium" textDecoration="underline">
                    Create Password Policy
                  </NblTypography>
                </NblFlexContainer>
              </NblGridItem>
            </>
          )}
          {nblFormValues.type === SecretType.NormalSecret && (
            <NormalSecretFields policyDescription={selectedRule?.passwordDescription || ''} />
          )}
          {nblFormValues.type === SecretType.RotatingSecret && (
            <RotatingSecretFields policyDescription={selectedRule?.passwordDescription || ''} />
          )}
        </>
      }
    </NblGridContainer>
  );
};

export default CreateNewSecretForm;
