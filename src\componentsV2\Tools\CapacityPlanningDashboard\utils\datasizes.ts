export interface DataSizeInterface {
  name: string;
  multiplier: number;
  scale?: number;
  prefix?: string;
}
export class DataSize implements DataSizeInterface {
  constructor(name: string, multiplier: number) {
    this.name = name;
    this.multiplier = multiplier;
  }

  name: string;
  multiplier: number;
  get prefix(): string {
    return this.name.charAt(0);
  }

  // Static calculation functions
  static calculateSize = (
    amount: number,
    inputRate: DataSize | DataSizeInterface,
    outputRate: DataSize | DataSizeInterface,
    scale = 1000
  ): number => {
    // Given a number an input rate and output rate, return the calculated number
    // Ex: calculateSize(1000, Mega, Giga) returns 1
    const rate = inputRate.multiplier - outputRate.multiplier;

    return amount * Math.pow(scale, rate);
  };

  formatScale(i: number, suffix = '', scale = 1000): string {
    for (const st of SizeTypesArray) {
      const scaledVal = DataSize.calculateSize(i, this, st, scale);
      if (scaledVal < 1000) {
        return st.prefix === 'P' ? `${(scaledVal * 1000).toPrecision(6)}TB` : `${scaledVal.toPrecision(3)}${st.prefix}${suffix}`;
      }
    }

    return `${i}${suffix}`;
  }
}

export const SizeTypes = {
  Default: new DataSize('', 0),
  Kilo: new DataSize('Kilo', 1),
  Mega: new DataSize('Mega', 2),
  Giga: new DataSize('Giga', 3),
  Tera: new DataSize('Tera', 4),
  Peta: new DataSize('Peta', 5),
};

export const SizeTypesArray: DataSize[] = Object.values(SizeTypes);
//SizeTypesArray = [{name:'Kilo',multiplier:2}]
