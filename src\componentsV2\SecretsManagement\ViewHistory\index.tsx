import React, { useEffect, useState } from 'react';
import NblViewDetailsAccordion from 'componentsV2/NblViewDetailsAccordion';
import { useApiService } from 'api/ApiService/context';
import SecretVersionData from 'types/Interfaces/SecretVersionData';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { dateFormatter } from 'utils/common';
import GenericViewHistoryTable from 'componentsV2/GenericViewHistoryTable';
import { useSearchParams } from 'react-router-dom';
import { showSpinner } from '../../../store/reducers/spinner';
import { useDispatch } from 'react-redux';

interface ViewHistoryProps {}

const ViewHistory: React.FunctionComponent<ViewHistoryProps> = () => {
  //Hooks
  const theme = useTheme<NebulaTheme>();
  const { apiSecretsManagement } = useApiService();
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();

  //State
  const [rows, setRows] = useState<SecretVersionData[]>([]);

  //Handler
  useEffect(() => {
    const secretId = searchParams.get('secretId');
    if (!secretId) return;
    dispatch(showSpinner({ message: 'Getting secret history....', status: true }));
    apiSecretsManagement
      .getSecretData(secretId)
      .then(async (res) => {
        if (res.status) {
          const filteredData = res.data.filter((item) => item !== null);

          const decryptedRows = await Promise.all(
            filteredData.map(async (data, index) => {
              const formattedOriginalValue = data.originalValue
                ? {
                    ...data.originalValue,
                    ...(data.originalValue.nextRotationDate && {
                      nextRotationDate: dateFormatter(data.originalValue.nextRotationDate),
                    }),
                  }
                : null;

              const formattedNewValue = {
                ...data.newValue,
                ...(data.newValue.nextRotationDate && {
                  nextRotationDate: dateFormatter(data.newValue.nextRotationDate),
                }),
              };

              return {
                ...data,
                id: index,
                lastUpdatedAt: data.lastUpdatedAt ? dateFormatter(data.lastUpdatedAt) : '',
                originalValue: formattedOriginalValue,
                newValue: formattedNewValue,
              };
            })
          );

          setRows(decryptedRows);
        } else {
          setRows([]);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ message: '', status: false }));
      });
  }, [searchParams]);

  //JSX
  return (
    <NblBorderContainer height="auto" backgroundColor={theme.palette.secondary.main} padding="16px">
      <NblViewDetailsAccordion hasDivider summary={'View History'} margin="0">
        <GenericViewHistoryTable rows={rows} />
      </NblViewDetailsAccordion>
    </NblBorderContainer>
  );
};

export default ViewHistory;
