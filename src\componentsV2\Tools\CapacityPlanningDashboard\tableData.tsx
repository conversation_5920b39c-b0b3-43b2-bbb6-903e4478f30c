// eslint-disable-next-line no-unused-vars
import { VropResource } from './utils/types';
import { CalculateVropsResource } from './utils/utilization';
import GetIconAndUtilization from './components/GetIconAndUtilization';

export const createTableData = (paginatedResources: VropResource[]) => {
  const data: any =
    paginatedResources &&
    paginatedResources.map((resource: VropResource) => {
      const usageStatistics = CalculateVropsResource(resource);
      const { cpuUtilized, memoryUtilized, storageUtilized } = usageStatistics;
      const maxUtilization = Math.max(cpuUtilized, memoryUtilized, storageUtilized);
      return {
        id: resource.resourceid,
        site: resource.resourcename,
        label: resource.label,
        statusAndUtilizationIcon: <GetIconAndUtilization status={maxUtilization} />,
        cpuAndUtilizationIcon: <GetIconAndUtilization status={cpuUtilized} isIndividualUtilization />,
        memoryAndUtilizationIcon: <GetIconAndUtilization status={memoryUtilized} isIndividualUtilization />,
        storageAndUtilizationIcon: <GetIconAndUtilization status={storageUtilized} isIndividualUtilization />,
        cpu: cpuUtilized,
        memory: memoryUtilized,
        storage: storageUtilized,
        resource: resource,
        maxutilized: maxUtilization,
      };
    });
  return data;
};
