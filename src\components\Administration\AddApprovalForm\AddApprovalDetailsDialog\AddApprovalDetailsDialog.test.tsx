import { act, render, screen } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import AddApprovalDetails from '.';
import ThemeProvider from 'mock/ThemeProvider';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});

jest.mock('react-toastify');

describe('Render ApprovalDetailsDialog  form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  const handleData = jest.fn();

  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <AddApprovalDetails
              onClose={handleClose}
              onSuccess={handleSuccess}
              setApprovalData={handleData}
              selectedGroupData={[]}
              open={true}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(screen.getByText('Approval Group *')).toBeInTheDocument();
    expect(screen.getByText('Number of Approvers *')).toBeInTheDocument();
    expect(screen.getByText('Approval Level *')).toBeInTheDocument();
    const saveButton = getByText('Save');
    const cancelButton = getByText('Cancel');
    expect(saveButton).toBeEnabled();
    expect(cancelButton).toBeEnabled();
  });
});
