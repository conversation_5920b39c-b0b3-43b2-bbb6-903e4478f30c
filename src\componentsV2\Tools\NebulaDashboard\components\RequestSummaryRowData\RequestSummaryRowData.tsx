import { StyledArea<PERSON>hart, StyledAreaChartWrapper } from '../../styled';
import RequestSummary<PERSON>hart from '../RequestSummaryChart/RequestSummaryChart';
import { getMonthsInQuarter } from '../../utility';
import { CatalogSummaryData } from 'types/Interfaces/NebulaDashboard';

interface requestSummaryPropsData {
  catalogData: CatalogSummaryData;
  quarters: number[];
  year: number;
}

const RequestSummaryRowData = ({ catalogData, quarters, year }: requestSummaryPropsData) => {
  const catalogName = catalogData.catalogName;
  const monthMap: Record<string, number> = {
    Jan: 0,
    Feb: 1,
    Mar: 3,
    Apr: 4,
    May: 4,
    Jun: 5,
    Jul: 6,
    Aug: 7,
    Sep: 8,
    Oct: 9,
    Nov: 10,
    Dec: 11,
  };
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  const getRequiredMonths = () => {
    const quartersAndMonth = getMonthsInQuarter(quarters);
    let allMonths: string[] = [];
    let seriesSet: number[] = [];
    quartersAndMonth.forEach((mn) => {
      const isDataPresent = catalogData?.allRequestMonthlyData?.[Number(mn.index)];
      const monthIndex = monthMap[mn.month];
      if (isDataPresent) {
        const monthCount = catalogData?.allRequestMonthlyData[Number(mn.index)];
        seriesSet.push(monthCount);
        allMonths.push(mn.month);
      } else if (!(year === currentYear && monthIndex > currentMonth)) {
        seriesSet.push(0);
        allMonths.push(mn.month);
      }
      // For else condition nothing should happen
    });
    seriesSet = seriesSet.reduce((prev: number, current: number) => prev + current, 0) ? seriesSet : [];
    return { allMonths, seriesSet };
  };
  const { allMonths, seriesSet } = getRequiredMonths();

  return (
    <StyledAreaChartWrapper>
      <StyledAreaChart>
        <RequestSummaryChart resourceName={catalogName} monthName={allMonths} dataSet={seriesSet} />
      </StyledAreaChart>
    </StyledAreaChartWrapper>
  );
};

export default RequestSummaryRowData;
