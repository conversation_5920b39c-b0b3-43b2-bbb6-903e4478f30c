//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

type StoryProps = ComponentProps<typeof NblTextField>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblTextField',
  component: NblTextField,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { type: 'string' },
    name: { type: 'string' },
    type: { control: 'select', type: 'string', options: ['text', ' number', 'password'] },
    helperText: { control: 'text' },
  },
};

export default meta;

export const TextField: Story = {
  args: {
    label: 'Label',
    name: 'Name',
    type: 'text',
    disabled: false,
    helperText: 'helper text',
    value: '',
    placeholder: '',
    error: false,
    minRows: 0,
    rows: 0,
    maxRows: 0,
    readOnly: false,
    multiline: false,
    mandatory: false,
  },
  render: (args) => (
    <NebulaTheme>
      <NblFlexContainer width="300px">
        <NblTextField {...args} />
      </NblFlexContainer>
    </NebulaTheme>
  ),
};
