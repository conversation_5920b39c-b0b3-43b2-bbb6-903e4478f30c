//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NblReportCard from 'sharedComponents/NblReportCard';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';

type StoryProps = ComponentProps<typeof NblReportCard>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Cards/NblReportCard',
  component: NblReportCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { type: 'string' },
    prevValue: { type: 'string' },
    value: { type: 'string' },
  },
};

export default meta;

const Template = (props: StoryProps) => {
  return <NblReportCard {...props} />;
};

export const Default: Story = {
  args: {
    value: '800',
    prevValue: '200',
    label: 'Findings',
    chip: {
      label: '40%',
      arrow: 'up',
      color: 'success',
    },
  },
  render: (args) => (
    <NebulaTheme>
      <Template {...args} />
    </NebulaTheme>
  ),
};
