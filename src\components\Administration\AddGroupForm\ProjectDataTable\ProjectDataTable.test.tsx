import { act, render, fireEvent, waitFor } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import ProjectDataTable from '.';
import ThemeProvider from 'mock/ThemeProvider';
import { GroupTableData } from 'mock/GroupsProjectData';
import { ProjectPermissions } from 'mock/Groups';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});

describe('Render Project permission table', () => {
  const setProjectData = jest.fn();

  test('Should render project permissions table ', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <ProjectDataTable
              projectPermissions={ProjectPermissions}
              setSelectedProjectPermissions={setProjectData}
              data={GroupTableData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    expect(getByText('Actions')).toBeInTheDocument();
    expect(getByText('Projects')).toBeInTheDocument();
    expect(getByText('Project Roles')).toBeInTheDocument();
    expect(getByText(GroupTableData[0]['projectName'])).toBeInTheDocument();
    expect(getByText(`${GroupTableData[0]['roles'][0]['roleName']}, ${GroupTableData[0]['roles'][1]['roleName']}`)).toBeInTheDocument();
  });

  test('Should close the Edit Project Permissions Dialog on clicking Cancel button', async () => {
    const { getByText, getByTestId, queryByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <ProjectDataTable
              projectPermissions={ProjectPermissions}
              setSelectedProjectPermissions={setProjectData}
              data={GroupTableData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    const editIcon = getByTestId(`edit-icon-${GroupTableData[0]['id']}`);
    act(() => {
      fireEvent.click(editIcon);
    });
    await waitFor(() => {
      expect(getByText('Edit Project Permissions')).toBeInTheDocument();
    });

    act(() => {
      fireEvent.click(getByText('Cancel'));
    });
    await waitFor(() => {
      expect(queryByText('Edit Project Permissions')).toBeNull();
    });
  });

  test('Should remove the deleted row from the table', async () => {
    const { getByText, getByTestId, queryByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <ProjectDataTable
              projectPermissions={ProjectPermissions}
              setSelectedProjectPermissions={setProjectData}
              data={GroupTableData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    expect(getByText(GroupTableData[0]['projectName'])).toBeInTheDocument();
    const deleteIcon = getByTestId(`delete-icon-${GroupTableData[0]['id']}`);
    await act(() => {
      fireEvent.click(deleteIcon);
    });
    waitFor(() => {
      expect(queryByText(GroupTableData[0]['projectName'])).toBeNull();
    });
  });
});
