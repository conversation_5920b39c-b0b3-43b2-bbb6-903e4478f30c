import React, { useEffect, useState } from 'react';
import { AdministrationTabsData } from 'types';
import { getRolesCatalogItems } from 'api/static-data';
import Catalog from 'components/Catalog';

interface RoleProps {}

const Roles: React.FunctionComponent<RoleProps> = () => {
  const [content, setContent] = useState<AdministrationTabsData[]>([]);
  useEffect(() => {
    async function getCatalogItems() {
      try {
        const data = await getRolesCatalogItems();
        console.log(data);
        setContent(data);
      } catch (error) {
        console.log(error);
      }
    }
    getCatalogItems();
  }, []);
  return content?.length ? <Catalog catalogItems={content} isAdminTiles /> : <div>No items to display</div>;
};

export default Roles;
