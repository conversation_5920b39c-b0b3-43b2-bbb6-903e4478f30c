// eslint-disable-next-line
import { CatalogWithSummary } from '../../utils/types';

export const CalendarMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

export function getStackedSeries(filteredCatalogs: CatalogWithSummary[]) {
  const seriesData = filteredCatalogs.map((item) => {
    let dataArray = new Array(12).fill(0);
    item.summary?.forEach((i) => {
      dataArray[i.month - 1] = i.count;
    });
    let newItem = { id: item.id, name: item.value, data: dataArray };
    return newItem;
  });
  const allZero = seriesData.every((series) => series.data.every((v) => v === 0));
  return allZero ? [] : seriesData;
}
