import useCatalogRequests from './useCatalogRequests';
// eslint-disable-next-line no-unused-vars
import { NebulaRequestStats } from './useFetchNebulaRequestStats';

const useRequestsByMonth = () => {
  //Hooks
  const { groupRequestType } = useCatalogRequests();

  const constructRequestStatsByMonth = (topLevel: 1 | 2 | 3 | 4 = 1, groups: NebulaRequestStats['getRequestSummaryByMonth']) => {
    const mappedRequestGroup = groupRequestType(topLevel);
    const requestStatsByTopLevel = {} as { [key: string]: number[] };
    for (const topLevelName in mappedRequestGroup) {
      groups.forEach((group) => {
        if (mappedRequestGroup[topLevelName].includes(group.requestType)) {
          if (!requestStatsByTopLevel[topLevelName]) {
            requestStatsByTopLevel[topLevelName] = Array(12).fill(0);
          }
          requestStatsByTopLevel[topLevelName].forEach((_, index) => {
            requestStatsByTopLevel[topLevelName][index] =
              requestStatsByTopLevel[topLevelName][index] +
              group.summary.filter((obj) => obj.month === index + 1).reduce((acc, value) => acc + value.count, 0);
          });
        }
      });
    }
    return requestStatsByTopLevel;
  };

  return {
    constructRequestStatsByMonth,
  };
};

export default useRequestsByMonth;
