{"name": "self-service-portal", "version": "1.1.2", "private": true, "homepage": "/", "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons": "^4.7.0", "@apollo/client": "^3.11.10", "@axa-fr/react-oidc": "^7.19.0", "@emotion/cache": "^11.10.3", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@mui/icons-material": "^5.14.13", "@mui/lab": "^5.0.0-alpha.100", "@mui/material": "^5.10.6", "@mui/x-charts": "^7.12.0", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^7.20.0", "@mui/x-tree-view": "^7.28.1", "@reduxjs/toolkit": "^1.8.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.5", "@types/node": "^20.8.4", "@types/react": "^18.2.28", "apexcharts": "^3.35.5", "axios": "^1.5.1", "axios-retry": "^3.8.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "formik": "^2.4.5", "framer-motion": "^7.3.6", "history": "^5.3.0", "html-to-image": "^1.11.11", "husky": "^9.1.7", "ip-address": "^9.0.5", "ip-num": "^1.5.1", "jspdf": "^2.5.2", "jwt-decode": "^4.0.0", "jwt-encode": "^1.0.1", "lint-staged": "^16.1.2", "lodash": "^4.17.21", "mui-tel-input": "^4.0.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-device-detect": "^2.2.2", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-json-tree": "^0.19.0", "react-material-ui-carousel": "^3.4.2", "react-number-format": "^4.9.4", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.0.4", "react-router": "^6.4.1", "react-router-dom": "^6.4.1", "react-scripts": "^5.0.1", "react-simple-maps": "^3.0.0", "react-toastify": "^9.1.3", "react-window": "^1.8.7", "react-zoom-pan-pinch": "^3.6.1", "recharts": "^2.13.3", "redux": "^4.2.0", "simplebar": "^5.3.8", "simplebar-react": "^2.4.1", "typescript": "4.8.3", "web-vitals": "^3.0.2", "yup": "^0.32.11"}, "overrides": {"svgo": {".": "^2.3.1", "css-select": {"nth-check": "^2.0.1", "dependencies": {"nth-check": "^2.0.1"}}, "nth-check": "^2.0.1"}, "css-select": {"nth-check": "^2.0.1"}, "@adobe/css-tools": "^4.3.2", "postcss": "^8.4.31", "follow-redirects": "^1.15.6"}, "scripts": {"start": "node ./build-scripts/nebulastart.js", "build": "node ./build-scripts/nebulabuild.js", "test": "react-scripts test", "test:cov": "npm test -- --coverage", "eject": "react-scripts eject", "prepare": "husky install", "lint:pretty": "prettier --write \"src/**/*.{cjs,mjs,js,jsx,ts,tsx,css}\"", "lint:pretty:check": "prettier --check \"src/**/*.{cjs,mjs,js,jsx,ts,tsx,css}\"", "eslint": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,cjs,mjs}": "prettier --write"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "plugin:storybook/recommended"]}, "babel": {"presets": ["@babel/preset-react", "@babel/preset-typescript"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"transformIgnorePatterns": ["/node_modules/(?!(react-syntax-highlighter|react-toastify|mui-tel-input|axios|axios-retry|@bundled-es-modules))", "^.+\\.module\\.(css|sass|scss)$"], "testMatch": ["<rootDir>/src/sharedComponents/**/*.test.{js,jsx,ts,tsx}", "<rootDir>/src/componentsV2/SecretsManagement/**/*.test.{js,jsx,ts,tsx}", "<rootDir>/src/componentsV2/Administration/VMSize/**/*.test.{js,jsx,ts,tsx}", "<rootDir>/src/pages/Administration/VMSizing/**/*.test.{js,jsx,ts,tsx}", "<rootDir>/src/componentsV2/IaaS/CreateNamespace/ResourceDetails/**/*.test.{js,jsx,ts,tsx}", "<rootDir>/src/componentsV2/IaaS/SecretDeviceAssociation/**/*.test.{js,jsx,ts,tsx}", "<rootDir>/src/componentsV2/Administration/Roles/**/*.test.{js,jsx,ts,tsx}", "<rootDir>/src/componentsV2/Administration/Permissions/**/*.test.{js,jsx,ts,tsx}", "<rootDir>/src/pages/Administration/Permission/**/*.test.{js,jsx,ts,tsx}"], "collectCoverageFrom": ["src/sharedComponents/**/*.{js,jsx,ts,tsx}", "src/componentsV2/SecretsManagement/**/*.{js,jsx,ts,tsx}", "src/componentsV2/Administration/VMSize/**/*.test.{js,jsx,ts,tsx}", "src/pages/Administration/VMSizing/**/*.test.{js,jsx,ts,tsx}", "src/componentsV2/IaaS/SecretDeviceAssociation/**/*.{js,jsx,ts,tsx}", "src/componentsV2/IaaS/CreateNamespace/ResourceDetails/**/*.{js,jsx,ts,tsx}", "src/componentsV2/Administration/Roles/**/*.{js,jsx,ts,tsx}", "src/componentsV2/Administration/Permissions/**/*.{js,jsx,ts,tsx}", "src/pages/Administration/Permission/**/*.{js,jsx,ts,tsx}", "!**/*.test.{js,jsx,ts,tsx}"]}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/eslint-parser": "^7.21.3", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@chromatic-com/storybook": "^3.2.2", "@storybook/addon-essentials": "^8.4.4", "@storybook/addon-interactions": "^8.4.4", "@storybook/addon-onboarding": "^8.4.4", "@storybook/blocks": "^8.4.4", "@storybook/preset-create-react-app": "^8.4.4", "@storybook/react": "^8.4.4", "@storybook/react-webpack5": "^8.4.4", "@storybook/test": "^8.4.4", "@types/axios": "^0.14.0", "@types/crypto-js": "^4.2.2", "@types/jwt-encode": "^1.0.3", "@types/react-simple-maps": "^3.0.6", "@types/redux-mock-store": "^1.0.6", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "enzyme": "^3.11.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.11.1", "html-webpack-plugin": "^5.6.0", "jest": "^27.5.1", "prettier": "^2.8.8", "react-app-rewired": "^2.2.1", "redux-mock-store": "^1.5.4", "serve": "^14.2.3", "storybook": "^8.4.4", "style-loader": "^4.0.0", "ts-loader": "^9.5.1", "webpack": "^5.95.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0", "webpack-merge": "^6.0.1"}}