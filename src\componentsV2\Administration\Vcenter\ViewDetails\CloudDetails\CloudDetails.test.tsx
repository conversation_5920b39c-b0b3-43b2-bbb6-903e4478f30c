import { render, screen, fireEvent } from '@testing-library/react';
import CloudDetails from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';

jest.mock('./Clusters', () => () => <div>Mock Clusters Component</div>);
jest.mock('./Hosts', () => () => <div>Mock Hosts Component</div>);
jest.mock('./DataStores', () => () => <div>Mock DataStores Component</div>);
jest.mock('./Networks', () => () => <div>Mock Networks Component</div>);
jest.mock('./OperatingSystems', () => () => <div>Mock OperatingSystems Component</div>);

const mockSetFieldValue = jest.fn();
const mockCloudDetails = [
  {
    clusters: ['cluster1'],
    hosts: ['host1'],
    datastore: ['datastore1'],
    networks: ['network1'],
    osLayouts: ['os1'],
  },
  {
    clusters: ['cluster2'],
    hosts: ['host2'],
    datastore: ['datastore2'],
    networks: ['network2'],
    osLayouts: ['os2'],
  },
];

jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  useNblForms: jest.fn(),
}));

describe('CloudDetails Component', () => {
  beforeEach(() => {
    (useNblForms as jest.Mock).mockReturnValue({
      nblFormValues: { cloudDetails: mockCloudDetails },
      nblFormProps: { setFieldValue: mockSetFieldValue },
    });
    jest.clearAllMocks();
  });

  it('renders default Clusters tab content', () => {
    render(
      <NebulaThemeProvider>
        <CloudDetails activeCloudIndex={0} />
      </NebulaThemeProvider>
    );

    expect(screen.getByText('Cloud Details')).toBeInTheDocument();
    expect(screen.getByText('Mock Clusters Component')).toBeInTheDocument();
  });

  it('renders Hosts tab content when selected', () => {
    render(
      <NebulaThemeProvider>
        <CloudDetails activeCloudIndex={0} />
      </NebulaThemeProvider>
    );

    fireEvent.click(screen.getByText('Hosts'));
    expect(screen.getByText('Mock Hosts Component')).toBeInTheDocument();
  });

  it('renders Datastores tab content when selected', () => {
    render(
      <NebulaThemeProvider>
        <CloudDetails activeCloudIndex={0} />
      </NebulaThemeProvider>
    );

    fireEvent.click(screen.getByText('Datastores'));
    expect(screen.getByText('Mock DataStores Component')).toBeInTheDocument();
  });

  it('renders Networks tab content when selected', () => {
    render(
      <NebulaThemeProvider>
        <CloudDetails activeCloudIndex={0} />
      </NebulaThemeProvider>
    );

    fireEvent.click(screen.getByText('Networks'));
    expect(screen.getByText('Mock Networks Component')).toBeInTheDocument();
  });

  it('renders Operating Systems tab content when selected', () => {
    render(
      <NebulaThemeProvider>
        <CloudDetails activeCloudIndex={0} />
      </NebulaThemeProvider>
    );

    fireEvent.click(screen.getByText('Operating Systems'));
    expect(screen.getByText('Mock OperatingSystems Component')).toBeInTheDocument();
  });

  it('calls setFieldValue when tab content changes its value', () => {
    render(
      <NebulaThemeProvider>
        <CloudDetails activeCloudIndex={0} />
      </NebulaThemeProvider>
    );

    fireEvent.click(screen.getByText('Hosts'));

    const newHosts = ['hostA', 'hostB'];

    const updateCloudField = (useNblForms as jest.Mock).mock.results[0].value.nblFormProps.setFieldValue;
    updateCloudField('cloudDetails', [
      {
        ...mockCloudDetails[0],
        hosts: newHosts,
      },
      mockCloudDetails[1],
    ]);

    expect(mockSetFieldValue).toHaveBeenCalledWith(
      'cloudDetails',
      expect.arrayContaining([expect.objectContaining({ hosts: newHosts }), expect.anything()])
    );
  });

  it('resets to Clusters tab when activeCloudIndex changes', () => {
    const { rerender } = render(
      <NebulaThemeProvider>
        <CloudDetails activeCloudIndex={0} />
      </NebulaThemeProvider>
    );

    fireEvent.click(screen.getByText('Hosts'));
    expect(screen.getByText('Mock Hosts Component')).toBeInTheDocument();

    rerender(
      <NebulaThemeProvider>
        <CloudDetails activeCloudIndex={1} />
      </NebulaThemeProvider>
    );

    expect(screen.getByText('Mock Clusters Component')).toBeInTheDocument();
  });
});
