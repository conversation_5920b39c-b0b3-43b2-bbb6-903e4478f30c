type Credentials = {
  name: string;
  path: string;
  provider: string;
};

type CreateSpecFormEKS = {
  project: string;
  projectName: string;
  domain: string;
  application: string;
  environment: string;
  iacProjectName: string;
  namespaceId: number;
  zone_id: string;
  cluster_name: string;
  cluster_version: string;
  ami_id: string;
  min_size: number;
  desired_size: number;
  max_size: number;
  disk_size: number;
  vpc_id: string;
  aws_region: string;
  control_plane_subnets: Array<string>;
  public_subnets: Array<string>;
  node_group_subnets: Array<string>;
  create_alb: boolean;
  alb_internal: boolean;
  waf_name: string;
  default_action: string;
  falcon_cid: string;
  falcon_client: string;
  falcon_secret: string;
  acm_domain_name: string;
  credentials: Array<Credentials>;
};

export default CreateSpecFormEKS;
