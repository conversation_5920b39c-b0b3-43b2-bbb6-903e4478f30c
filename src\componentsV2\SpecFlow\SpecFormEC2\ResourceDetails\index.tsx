import NavigateToRequestDetails from 'componentsV2/NavigateToRequestDetails';
import React from 'react';
import NblChip from 'sharedComponents/NblChip';
// eslint-disable-next-line no-unused-vars
import { ViewDetailsFields } from 'sharedComponents/NblContainers/NblViewDetailsContainer';
import NblViewDetailsOverview from 'sharedComponents/NblContainers/NblViewDetailsContainer/NblViewDetailsOverview';
// eslint-disable-next-line no-unused-vars
import { ResourcesDetails } from 'types';
import { getResourceStatusChipColor } from 'utils/common';

interface ResourceDetailsProps {
  data: ResourcesDetails;
  loading: boolean;
}

const ResourceDetails: React.FC<ResourceDetailsProps> = ({ data: resourceData, loading }) => {
  const resourceViewFields: ViewDetailsFields[] = [
    {
      title: 'Request ID',
      value: <NavigateToRequestDetails requestId={resourceData.requestId} />,

      subValue: resourceData.createdAt,
    },
    {
      title: 'Resource Details',
      value: resourceData.resourceId,
    },
    {
      title: 'Project Name',
      value: resourceData.projectName,
    },
    {
      title: 'Status Date',
      value: resourceData.statusDate,
    },
    {
      title: 'Resource Status',
      value: (
        <NblChip
          id="ResourceStatus"
          borderRadius="lg"
          color={getResourceStatusChipColor(resourceData.status)}
          label={resourceData.status?.toUpperCase()}
        />
      ),
    },
    {
      title: 'Catalog Level 3',
      value: resourceData.catalogLevel03,
    },
    {
      title: 'Catalog Item',
      value: resourceData.catalogType,
    },
    {
      title: 'Resource Name',
      value: resourceData.instancename,
    },
    {
      title: 'Instance Type',
      value: resourceData.instanceType || '-',
    },
    {
      title: 'Status',
      value: resourceData.status || '-',
    },
    {
      title: 'Tags',
      value: resourceData.tags?.map((tag: any) => tag.name).join(', ') || '-',
    },
  ];

  return (
    <>
      <NblViewDetailsOverview viewDetailsFields={resourceViewFields} loading={loading} />
    </>
  );
};

export default ResourceDetails;
