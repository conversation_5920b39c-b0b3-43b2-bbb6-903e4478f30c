// eslint-disable-next-line no-unused-vars
import { ResourcesDetails } from 'types';

export const ReconfigureResourceData: ResourcesDetails = {
  domain: 'APVS-RED',
  datacenter: 'STAMP-VMWARE',
  requestType: 'CREATE_VM_LINUX89_VMWARE',
  resourceId: 'NEB-RES-VM-8587',
  projectId: '',
  resourceStatus: 'running',
  customMemory: 8,
  customCores: 4,
  coresPerSocket: '1',
  requestId: 'NEB-IAAS-VM-29662',
  createdAt: '2025-06-13T02:48:24.837Z',
  catalogLevel03: 'IaaS-Compute-Virtual-Server',
  catalogType: 'Linux 8 & 9 V3',
  instancename: 'alma88-vm009',
  status: 'running',
  statusMessage: 'NA',
  rubrikSLA: 'Unprotected',
  diskCount: '2',
  tags: [{ name: 'Func', value: 'Func' }],
  systemUpdate: {
    someKey: {
      message: 'Some message',
      status: 'SUCCESS',
      deletedSteps: [],
    },
  },
  application: '',
  environment: '',
  platformContext: {
    catalogId: '',
    envId: 'env-1',
    domainId: 'domain-1',
    applicationId: 'app-1',
    applicationName: '',
    environmentName: '',
    domainName: '',
  },
  id: 1,
  updatedAt: '',
  createdBy: '',
  projectName: '',
  resourceName: '',
  instanceType: '',
  cloud: '',
  cloudId: '',
  group: '',
  layout: '',
  plan: '',
  instancePrice: '',
  ipaddress: '',
  crowdStrikeStatus: '',
  cpuUsage: 0,
  maxMemory: 0,
  maxStorage: 0,
  usedCPU: 0,
  usedMemory: 0,
  usedStorage: 0,
  externalFqdn: '',
  ipAddress: '',
  clusterSize: 0,
  appCode: '',
  dbName: '',
  dbUsername: '',
  dbVersion: '',
  clusterName: '',
  enableDelete: true,
  enableUpdate: true,
  ipv4Address: '',
  ipv6Address: '',
  addDisks: [],
  statusDate: '',
  groupQuota: 0,
  nfsCreatedAt: '',
  rules: '',
  userQuota: 0,
  bucketName: '',
  accountName: '',
  bucketCreatedAt: '',
  versioning: '',
  users: [],
  isCopied: false,
  isRegeneratable: false,
  version: '',
  displayName: '',
  swap: 0,
  secretExpiry: '',
  ttlInHours: 0,
  path: '',
  parentID: '',
  network: '',
  ipMode: '',
  subnetIpv4: '',
  subnetIpv6: '',
  networkDetails: [],
  namespaceName: '',
  namespacePath: '',
  resourcesDetails: {
    data: {
      path: '',
    },
  },
};
