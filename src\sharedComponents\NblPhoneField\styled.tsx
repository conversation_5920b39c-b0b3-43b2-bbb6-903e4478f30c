import { styled } from '@mui/system';
import { MuiTelInput } from 'mui-tel-input';
import { NebulaTheme } from 'NebulaTheme/type';
// import { getOutlinedBaseStyles, getOutlinedInputStyles, getScrollbarStyles } from 'sharedComponents/NblFormInputs/common';

export const StyledMuiTelInput = styled(MuiTelInput)<{ theme?: NebulaTheme }>(({ theme, disabled }) => {
  const { palette } = theme;
  const { textfield, select } = palette;

  return {
    ...(disabled && {
      opacity: 0.3,
      cursor: 'not-allowed',
      '&::placeholder': {
        opacity: 0.3,
      },
    }),
    '& .MuiInputBase-input.MuiOutlinedInput-input': {
      height: '0.3em',
      color: textfield.color,
    },
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: textfield.borderColor,
    },
    '& .MuiInputBase-root.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: textfield.toggledBorderColor,
      boxShadow: `0px 0px 6px ${select.boxShadowColor}`,
    },
    '& .MuiInputBase-root.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: textfield.hoveredBorderColor,
    },
    '& .MuiInputBase-root.MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline': {
      border: `2px solid ${textfield.errorColor}`,
      boxShadow: `0px 0px 6px ${select.errorBoxShadowColor}`,
    },
  };
});
