import { act, render, fireEvent } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import VerticalTabs from './index';

describe('VerticalTabs component', () => {
  const onChangeHandler = jest.fn();
  const TABS = [
    { label: 'IPv4 Ruleset 1', id: 'ruleset-1' },
    { label: 'IPv4 Ruleset 2', id: 'ruleset-2' },
    { label: 'IPv4 Ruleset 3', id: 'ruleset-3' },
    { label: 'IPv6 Ruleset', id: 'ruleset-4' },
  ];

  test('Should render component passed', async () => {
    const component = await act(
      async () => render(<VerticalTabs currentTab={0} tabsList={TABS} onTabChangeHandler={onChangeHandler} />).baseElement
    );
    expect(component).toBeInTheDocument();
  });

  test('Should render all the tabs', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <VerticalTabs currentTab={0} tabsList={TABS} onTabChangeHandler={onChangeHandler} />
        </ThemeProvider>
      )
    );

    TABS.map((tab) => {
      expect(getByText(tab.label)).toBeInTheDocument();
    });

    fireEvent.click(getByText(TABS[1]['label']));
    expect(onChangeHandler).toHaveBeenCalled();
  });
});
