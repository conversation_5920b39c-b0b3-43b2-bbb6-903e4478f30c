import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import NblDataViewer from 'sharedComponents/NblDataViewer';
import AddProject from '../../Clusters/ClusterDetails/AddProject';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { hexToRgb } from 'utils/common';
import NblButton from 'sharedComponents/Buttons/NblButton';
import { OSLayout } from '..';
import { useEffect, useState } from 'react';

interface OperatingSystemDetailsProps {
  selectedOS: OSLayout;
  onOSChange: (updatedOS: OSLayout) => void;
  onClose: () => void;
}

const OperatingSystemDetails: React.FC<OperatingSystemDetailsProps> = ({ selectedOS, onOSChange, onClose }) => {
  const theme = useTheme<NebulaTheme>();
  const { palette } = theme;
  const [saved, setSaved] = useState(false);
  const [projectRows, setProjectRows] = useState<{ id: string; project: string }[]>([]);
  const [lastLayoutMor, setLastLayoutMor] = useState<string | null>(null);

  useEffect(() => {
    if (selectedOS.layoutMor !== lastLayoutMor) {
      const initialProjects = selectedOS.projects?.map((p) => ({
        id: `${selectedOS.layoutMor}-${p}`,
        project: p,
      })) ?? [
        {
          id: `${selectedOS.layoutMor}-${Date.now()}`,
          project: '',
        },
      ];
      setProjectRows(initialProjects);
      setLastLayoutMor(selectedOS.layoutMor);
      setSaved(false);
    }
  }, [selectedOS, lastLayoutMor]);

  const OS_Details = [
    { name: 'Selected OS', value: selectedOS?.osName },
    { name: 'Selected Layout', value: selectedOS?.layoutName },
  ];

  return (
    <NblBorderContainer padding="20px" backgroundColor={hexToRgb(theme.palette.secondary.shade4, 0.2)} width="50%">
      <NblTypography variant="h4" color="shade1" weight="bold">
        Add Project
      </NblTypography>
      <NblGridContainer columns={12}>
        <NblGridItem colspan={4} height="auto">
          <NblFlexContainer padding="0px 14px 0px 0px">
            <NblFlexContainer direction="column" justifyContent="start">
              <NblDataViewer data={OS_Details} columns={1} />
              <NblButton
                variant="contained"
                color="info"
                onClick={() => {
                  setSaved(true);
                  onClose();
                }}
                buttonID={'save-project-btn'}
                disabled={saved}
              >
                {saved ? 'Saved' : 'Save'}
              </NblButton>
            </NblFlexContainer>
          </NblFlexContainer>
        </NblGridItem>
        <NblGridItem colspan={8} height="auto">
          {selectedOS.restricted ? (
            <AddProject
              rows={projectRows}
              setRows={setProjectRows}
              onProjectsChange={(updatedProjects) => {
                onOSChange({ ...selectedOS, projects: updatedProjects });
              }}
            />
          ) : (
            <NblFlexContainer backgroundColor={palette.secondary.shade1} center>
              <NblTypography variant="h6" color="shade6" textAlign="center" weight="semiBold">
                Enable restricted to add projects.
              </NblTypography>
            </NblFlexContainer>
          )}
        </NblGridItem>
      </NblGridContainer>
    </NblBorderContainer>
  );
};

export default OperatingSystemDetails;
