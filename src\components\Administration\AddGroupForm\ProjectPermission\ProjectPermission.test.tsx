import { act, render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import ProjectPermission from '.';
import ThemeProvider from 'mock/ThemeProvider';
import { ProjectPermissions } from 'mock/Groups';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});

describe('Render Project Permission form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  const handleData = jest.fn();

  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <ProjectPermission
              open={true}
              onClose={handleClose}
              onSuccess={handleSuccess}
              projectPermissions={ProjectPermissions}
              selectedProjectPermissions={[]}
              setProjectData={handleData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(screen.getByText('Projects *')).toBeInTheDocument();
    expect(screen.getByText('Project Roles *')).toBeInTheDocument();
    const saveButton = getByText('Save');
    const cancelButton = getByText('Cancel');
    expect(saveButton).toBeEnabled();
    expect(cancelButton).toBeEnabled();
  });

  test('displays validation error if no project is selected', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <ProjectPermission
              open={true}
              onClose={handleClose}
              onSuccess={handleSuccess}
              projectPermissions={ProjectPermissions}
              selectedProjectPermissions={[]}
              setProjectData={handleData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    fireEvent.click(getByText('Save'));
    waitFor(() => {
      expect(getByText('Projects is required')).toBeInTheDocument();
      expect(getByText('Please select atleast one project role')).toBeInTheDocument();
    });
  });

  // test('submits form after selecting options and clicking save', async () => {
  //   const setProjectDataMock = jest.fn();

  //   const { debug, getByText, getByLabelText, container } = await act(async () =>
  //     render(
  //       <ThemeProvider>
  //         <ProjectPermission
  //           open={true}
  //           onClose={handleClose}
  //           onSuccess={handleSuccess}
  //           projectPermissions={ProjectPermissions}
  //           selectedProjectPermissions={[]}
  //           setProjectData={handleData}
  //         />
  //       </ThemeProvider>
  //     )
  //   );

  //   expect(getByText('Projects *')).toBeInTheDocument();
  //   debug();

  //   const cidrBlockDropdown = container.querySelector('#projectRoles') as HTMLDivElement;
  //   fireEvent.mouseDown(cidrBlockDropdown);
  //   fireEvent.click(getByText('testproject'));
  //   await waitFor(() => {
  //     expect(getByText('testproject')).toBeInTheDocument();
  //   });

  //   // Click the save button
  //   fireEvent.click(getByText('Save'));

  //   // Wait for form submission to complete
  //   await waitFor(() => {
  //     expect(setProjectDataMock).toHaveBeenCalledWith({
  //       projectId: '65fd4595fada3fc9c74bfbac',
  //       projectName: 'testproject',
  //       roles: ['660f92577c3199ad86d41a86'], // Assuming 'MY-RESOURCE' is selected
  //     });
  //   });
  // });
});
