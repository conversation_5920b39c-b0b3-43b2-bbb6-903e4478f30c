// eslint-disable-next-line
import React from 'react';
import { InputLabel, MenuItem, Checkbox, ListItemText, FormHelperText, Stack, Typography, useTheme, TextField } from '@mui/material';
// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';

interface MultiSelectProps {
  label: string;
  name: string;
  options: any;
  value: string[];
  error?: string | string[] | boolean;
  placeholder?: string;
  handleChange: (event: any) => void;
  disabled?: boolean;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

const MultiSelect: React.FunctionComponent<MultiSelectProps> = ({
  label,
  name,
  options,
  value,
  error,
  disabled,
  placeholder,
  handleChange,
  onMouseEnter,
  onMouseLeave,
}: MultiSelectProps) => {
  const {
    palette: {
      forms: { fieldPlaceHolderColor },
    },
  }: NebulaTheme = useTheme();

  return (
    <Stack spacing={1}>
      <InputLabel htmlFor={name}>{label}</InputLabel>
      <TextField
        select
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        id={name}
        name={name}
        value={value && Array.isArray(value) ? value : []}
        disabled={disabled}
        onChange={handleChange}
        SelectProps={{
          displayEmpty: true,
          multiple: true,
          renderValue: (selected: any) => {
            const selectedArray = Array.isArray(selected) ? selected : [];
            if (selectedArray.length === 0) {
              return <Typography sx={{ color: fieldPlaceHolderColor, opacity: 0.5 }}>{placeholder || 'Select'}</Typography>;
            }
            return selectedArray
              .map(
                (selectedOption: string | number) =>
                  options.find((option: { value: string | number }) => option.value === selectedOption)?.label
              )
              .join(', ');
          },
        }}
        error={Boolean(error)}
      >
        {options.map((option: any, index: any) => (
          <MenuItem key={`${name}-option-${index}`} value={option.value} className="multiSelect-option">
            <Checkbox checked={value.indexOf(option.value) > -1} />
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </TextField>
      <FormHelperText error>{error}</FormHelperText>
    </Stack>
  );
};
export default MultiSelect;
