import React from 'react';
import { StyledOutlinedInput } from './styled';
import NblFieldWrapper from 'sharedComponents/NblFormInputs/NblFieldWrapper';

interface TextFieldProps {
  startAdornment?: React.ReactElement | string;
  endAdornment?: React.ReactElement;
  inputProps?: { 'data-testid': string };
  type: 'text' | 'number' | 'password';
  disabled?: boolean;
  error?: boolean;
  label?: string;
  name: string;
  placeholder?: string;
  value?: any;
  rows?: number;
  minRows?: number;
  maxRows?: number;
  readOnly?: boolean;
  handleChange?: (event: any) => void;
  helperText?: string | JSX.Element;
  multiline?: boolean;
  mandatory?: boolean;
  handleBlur?: (event: any) => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  autoFocus?: boolean;
  tooltipText?: string;
}

const NblTextField: React.FunctionComponent<TextFieldProps> = ({
  disabled,
  error,
  label,
  name,
  placeholder,
  startAdornment,
  endAdornment,
  readOnly,
  inputProps,
  type,
  value,
  handleBlur,
  handleChange,
  onMouseEnter,
  onMouseLeave,
  onKeyDown,
  mandatory = false,
  maxRows,
  minRows,
  multiline = false,
  helperText = '',
  autoFocus,
  tooltipText,
  ...rest
}: TextFieldProps) => {
  // JSX
  return (
    <NblFieldWrapper
      label={label}
      name={name}
      mandatory={mandatory}
      disabled={Boolean(disabled)}
      error={Boolean(error)}
      helperText={helperText}
      tooltipText={tooltipText}
    >
      <StyledOutlinedInput
        disabled={disabled}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        onKeyDown={(e: any) => {
          e.stopPropagation();
          onKeyDown?.(e);
        }}
        autoFocus={autoFocus}
        onDoubleClick={(e) => e.stopPropagation()}
        readOnly={readOnly}
        startAdornment={startAdornment}
        endAdornment={endAdornment}
        id={name}
        type={type}
        inputProps={inputProps}
        value={value}
        name={name}
        onBlur={handleBlur}
        onChange={handleChange}
        placeholder={readOnly ? '' : placeholder}
        error={Boolean(error)}
        {...(multiline ? { multiline, maxRows, minRows } : {})}
        {...rest}
      />
    </NblFieldWrapper>
  );
};

export default NblTextField;
