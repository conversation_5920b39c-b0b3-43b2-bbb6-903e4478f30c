import React, { useEffect, useState } from 'react';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
// eslint-disable-next-line no-unused-vars
import NblFormContainer, { NblFormHelpers, useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import VCenterDetails from './VCenterDetails';
import { CloudData, Cluster, Datastore, HostDetails, LookupPayload, Network, OSLayout } from 'types/Interfaces/LookUpVCenterResponse';
import { dispatch } from 'store';
import { showSpinner } from 'store/reducers/spinner';
import { useApiService } from 'api/ApiService/context';
import { SuccessPageProps } from 'sharedComponents/NblSuccessfulPage';
import CloudDetails from './CloudDetails';
import { toast } from 'react-toastify';
import useNblNavigate from 'hooks/useNblNavigate';

export type FormValues = {
  vcenterHost: string;
  vcenterName: string;
  cloudDetails: {
    cloudId: string;
    cloudName: string;
    vcenterHost: string;
    domain: string;
    datacenter: string;
    hostname: string;
    password: string;
    port: number;
    protocol: string;
    username: string;
    hosts: HostDetails[];
    clusters: Cluster[];
    datastore: Datastore[];
    networks: Network[];
    osLayouts: OSLayout[];
  }[];
};

export const initialValues: FormValues = {
  vcenterHost: '',
  vcenterName: '',
  cloudDetails: [],
};

interface ViewDetailsProps {
  payload: LookupPayload;
  response: CloudData;
}

const ViewDetails: React.FC<ViewDetailsProps> = ({ payload, response }) => {
  const [cloudDatacenter, setCloudDatacenter] = useState<string>('');
  const [activeCloudIndex, setActiveCloudIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNblNavigate();
  const { apiVCenterService } = useApiService();
  const [responseData, setResponseData] = useState<SuccessPageProps>({
    buttonTitle: 'Track Request',
    title: 'Add VCenter',
    requestId: '',
  });

  useEffect(() => {
    if (responseData) {
      setIsLoading(false);
    }
  }, [responseData]);

  const handleSubmitForm = (values: FormValues, helpers: NblFormHelpers<FormValues>) => {
    if (!responseData) return;

    const payload = {
      vcenterHost: values.vcenterHost,
      vcenterName: values.vcenterName,
      cloudDetails: values.cloudDetails,
    };
    dispatch(showSpinner({ message: 'Submitting details...', status: true }));
    apiVCenterService
      .addVCenter(payload)
      .then((res) => {
        if (res.status) {
          toast.success(res.data?.message || 'Request submitted successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
        }
        navigate('/administration/vcenter');
        setResponseData(res?.message);
      })
      .finally(() => {
        helpers.setSubmitting(false);
        dispatch(showSpinner({ message: '', status: false }));
      });
  };

  useEffect(() => {
    if (isLoading) {
      dispatch(showSpinner({ message: 'Loading details...', status: true }));
    } else {
      dispatch(showSpinner({ message: '', status: false }));
    }
  }, [isLoading]);

  if (isLoading) {
    return null;
  }

  return (
    <NblFormContainer<FormValues>
      title=""
      caption=""
      formInitialValues={initialValues}
      steps={[
        {
          caption: '',
          errorFields: [' '],
          icon: '',
          status: 'pending',
          title: '',
        },
      ]}
      formType="simple"
      onSubmit={handleSubmitForm}
      responseData={responseData}
      showPreview={false}
    >
      <NblFlexContainer direction="column" spacing={2}>
        <VCenterDetails
          cloudDatacenter={cloudDatacenter}
          setCloudDatacenter={setCloudDatacenter}
          setActiveCloudIndex={setActiveCloudIndex}
          responseData={response}
          payload={payload}
        />
        {cloudDatacenter && <CloudDetails activeCloudIndex={activeCloudIndex} />}
      </NblFlexContainer>
    </NblFormContainer>
  );
};

export default ViewDetails;
