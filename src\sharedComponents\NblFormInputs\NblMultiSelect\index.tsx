import React, { useEffect } from 'react';
import { useTheme, IconButton } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import CloseIcon from '@mui/icons-material/Close';
import { StyledTypography, StyledMenuItem, StyledSelect, CustomScrollbarStyles } from './styled';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { NebulaTheme } from 'NebulaTheme/type';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblTypography from 'sharedComponents/NblTypography';
import NblFieldWrapper from '../NblFieldWrapper';
import { getPaperPropStyles, truncateLabel } from '../common';

interface Option {
  value: string | number;
  label: string;
  icon?: React.ComponentType;
  disabled?: boolean;
}

interface NblMultiSelectProps {
  disabled?: boolean;
  error?: boolean;
  label: string;
  name: string;
  options: Option[] | undefined;
  placeholder: string;
  value: (string | number | undefined)[];
  maxLength?: number;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  handleBlur?: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleChange: (event: any) => void;
  datatestid?: any;
  helperText?: string;
  mandatory?: boolean;
  contained?: boolean;
}

const NblMultiSelect: React.FunctionComponent<NblMultiSelectProps> = ({
  disabled,
  error,
  label,
  name,
  placeholder,
  value,
  options = [],
  maxLength,
  onMouseEnter,
  onMouseLeave,
  handleBlur = () => {},
  handleChange,
  datatestid,
  helperText = '',
  mandatory = false,
  contained = false,
}: NblMultiSelectProps) => {
  // Hooks
  const theme = useTheme<NebulaTheme>();
  const { palette } = theme;
  const { secondary } = palette;

  // Utils
  const handleDelete = (event: React.MouseEvent<HTMLElement>, optionValue: string | number) => {
    event.stopPropagation();
    const newValue = value.filter((val) => val !== optionValue);
    handleChange({ target: { value: newValue } });
  };

  useEffect(() => {
    if (mandatory && options?.length === 1 && (!value || value.length === 0)) {
      handleChange({ target: { value: [options[0].value] } });
    }
  }, [mandatory, options, value]);
  // JSX
  return (
    <NblFieldWrapper
      label={label}
      name={name}
      mandatory={mandatory}
      disabled={Boolean(disabled)}
      error={Boolean(error)}
      helperText={helperText}
    >
      <StyledSelect
        multiple
        disabled={disabled}
        id={name}
        value={value}
        name={name}
        onChange={handleChange}
        onBlur={handleBlur}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        displayEmpty
        {...(contained ? { contained } : {})}
        error={Boolean(error)}
        IconComponent={KeyboardArrowDownIcon}
        inputProps={{ 'data-testid': `${datatestid}` }}
        MenuProps={{
          disableAutoFocusItem: true,
          PaperProps: {
            sx: getPaperPropStyles(theme),
            component: CustomScrollbarStyles,
          },
        }}
        renderValue={(selected: unknown) => {
          if (!selected || (selected as (string | number)[]).length === 0) {
            return <StyledTypography>{placeholder}</StyledTypography>;
          }
          const selectedValues = selected as (string | number)[];
          const selectedOptions = options.filter((option: { value: string | number }) => selectedValues.includes(option.value));
          return (
            <NblFlexContainer wrap="wrap" alignItems="center" padding={'8px 20px 8px 0'}>
              {selectedOptions.map((selectedOption) => (
                <NblFlexContainer
                  key={selectedOption.value}
                  alignItems="center"
                  height="24px"
                  width="auto"
                  backgroundColor={secondary.shade2}
                  borderRadius="4px"
                  margin="1px 0px"
                >
                  <StyledTypography optionlabel="true">
                    {maxLength ? truncateLabel(selectedOption.label, maxLength) : selectedOption.label}
                  </StyledTypography>
                  <IconButton
                    size="small"
                    onMouseDown={(event: React.MouseEvent<HTMLElement>) => handleDelete(event, selectedOption.value)}
                  >
                    <CloseIcon fontSize="inherit" style={{ fontSize: '12px' }} />
                  </IconButton>
                </NblFlexContainer>
              ))}
            </NblFlexContainer>
          );
        }}
      >
        {options?.map((option: Option, index: number) => {
          const label = maxLength ? truncateLabel(option.label, maxLength) : option.label;
          return (
            <StyledMenuItem key={`${name}-option-${index}`} value={option.value} disabled={Boolean(option.disabled)} className="menuItem">
              {/* @ts-ignore */}
              <NblCheckBox label={''} checked={value.includes(option.value)} />
              <NblTypography variant={'subtitle1'}>{label}</NblTypography>
            </StyledMenuItem>
          );
        })}
      </StyledSelect>
    </NblFieldWrapper>
  );
};

export default NblMultiSelect;
