import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { ViewDetailsFields } from '..';
import { Skeleton } from '@mui/material';
import NblTypography from 'sharedComponents/NblTypography';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblTooltip from 'sharedComponents/NblTooltip';

export interface NblViewDetailsOverviewProps {
  viewDetailsFields?: ViewDetailsFields[];
  loading?: boolean;
  columns?: number;
  showBorder?: boolean;
  spacingX?: number;
  spacingY?: number;
}

const NblViewDetailsOverview: React.FC<NblViewDetailsOverviewProps> = ({
  viewDetailsFields,
  loading,
  columns = 6,
  showBorder = true,
  spacingX = 2,
  spacingY = 4,
}) => {
  //Renders

  const renderFieldValue = (
    value: ViewDetailsFields['value'],
    subValue: ViewDetailsFields['subValue'],
    reason: ViewDetailsFields['reason']
  ) => {
    if (loading) {
      return <Skeleton variant="rounded" height={30} />;
    } else {
      return (
        <NblFlexContainer height="auto" spacing={0.5} direction="column">
          {reason ? (
            <NblTooltip tooltipMessage={reason}>
              <NblTypography variant="subtitle2" weight="medium" color="shade2">
                {value || '-'}
              </NblTypography>
            </NblTooltip>
          ) : (
            <NblTypography variant="subtitle2" weight="medium" color="shade2">
              {value || '-'}
            </NblTypography>
          )}
          {subValue && (
            <NblTypography variant="body4" weight="medium" color="shade6">
              {subValue}
            </NblTypography>
          )}
        </NblFlexContainer>
      );
    }
  };

  if (viewDetailsFields) {
    return (
      <NblBorderContainer height="auto" border={showBorder ? undefined : 'unset'}>
        <NblGridContainer spacingX={spacingX} columns={columns} padding={showBorder ? '20px' : undefined} spacingY={spacingY}>
          {viewDetailsFields.map((item, index) => (
            <NblGridItem key={index} height="auto">
              <NblFlexContainer height="auto" spacing={1.5} direction="column">
                {item.title && (
                  <NblTypography variant="subtitle1" weight="medium" color="shade1">
                    {item.title}
                  </NblTypography>
                )}
                {renderFieldValue(item.value, item.subValue, item?.reason)}
              </NblFlexContainer>
            </NblGridItem>
          ))}
        </NblGridContainer>
      </NblBorderContainer>
    );
  } else {
    return null;
  }
};

export default NblViewDetailsOverview;
