import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblDropdown from '.';

describe('NblDropdown component', () => {
  const props = {
    options: [
      { label: '<PERSON>', value: 'opt1' },
      { label: '<PERSON>', value: 'opt2' },
      { label: '<PERSON>', value: 'opt3' },
      { label: 'Jack', value: 'opt4' },
      { label: 'Tom', value: 'opt5' },
      { label: '<PERSON>', value: 'opt6' },
    ],
    value: 'opt1',
  };
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblDropdown {...props} onChange={() => jest.fn()} value={'opt1'} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
