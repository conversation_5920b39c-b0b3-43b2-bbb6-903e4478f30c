import React from 'react';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
// eslint-disable-next-line no-unused-vars
import { ColumnData, NblTable } from 'sharedComponents/NblTable';

export interface DataStore {
  datastoreMor: string;
  name: string;
  type: string;
  freeSpace: number;
  capacity: number;
  disabled: boolean;
}

interface DataStoresProps {
  datastores: DataStore[];
  onChange: (updatedDatastores: DataStore[]) => void;
}

const DataStores: React.FC<DataStoresProps> = ({ datastores, onChange }) => {
  const handleChecked = (datastoreMor: string, fieldName: keyof DataStore) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const updated = datastores.map((ds) => (ds.datastoreMor === datastoreMor ? { ...ds, [fieldName]: e.target.checked } : ds));
    onChange(updated);
  };

  const columns: ColumnData[] = [
    { field: 'datastoreMor', headerName: 'Datastore ID', flex: 1 },
    { field: 'name', headerName: 'Datastore Name', flex: 1 },
    { field: 'type', headerName: 'Type', flex: 1 },
    {
      field: 'capacity',
      headerName: 'Capacity (TB)',
      flex: 1,
    },
    {
      field: 'freeSpace',
      headerName: 'Free Space (TB)',
      flex: 1,
    },
    {
      field: 'disabled',
      headerName: 'Disable',
      flex: 1,
      renderCell: (params) => {
        const id = params.row.datastoreMor;
        return (
          <NblFlexContainer center>
            <NblCheckBox checked={!!params.row.disabled} label="" name="" onChange={handleChecked(id, 'disabled')} onBlur={() => {}} />
          </NblFlexContainer>
        );
      },
    },
  ];

  const rows = datastores.map((ds) => ({
    ...ds,
    id: ds.datastoreMor,
  }));

  return <NblTable columns={columns} rows={rows} />;
};

export default DataStores;
