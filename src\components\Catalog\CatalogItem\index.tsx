import React from 'react';

// eslint-disable-next-line
import { Grid, Stack, Typography, Tooltip, styled, useTheme, tooltipClasses, TooltipProps, useMediaQuery, Box } from '@mui/material';

import useExposureParams from 'hooks/useExposureParams';
import icons from 'assets/images/icons';
import HexaIconWrapper from '../../HexaIconWrapper';
import CatalogCard from '../CatalogCard';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import useNblNavigate from 'hooks/useNblNavigate';

interface CatalogItemProps {
  description: string;
  disabled: boolean;
  canAccess: boolean;
  accessDeniedMessage?: string;
  id: string;
  icon: string;
  path: string;
  title: string;
}

interface StyledCatalogCardProps {
  theme: NebulaTheme;
  isDisabled: boolean;
}

const StyledCatalogCard = styled(CatalogCard, { shouldForwardProp: (prop) => prop !== 'isDisabled' })(
  ({ theme, isDisabled }: StyledCatalogCardProps) => {
    const {
      palette: { contentCard },
    } = theme;
    return {
      width: 305,
      backgroundColor: contentCard.backgroundColor,
      boxShadow: contentCard.boxShadow,
      backdropFilter: 'unset',
      ...(isDisabled && {
        '& .hexagon-svg-icon': {
          color: contentCard.disabledColor,
        },
        '& .MuiTypography-root': {
          color: contentCard.disabledColor,
        },
      }),
    };
  }
);

const StyledTooltip = styled(({ className, ...props }: TooltipProps) => <Tooltip {...props} classes={{ popper: className }} />)({
  [`& .${tooltipClasses.tooltip}`]: {
    padding: '10px',
    fontSize: '12px',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
});

const CatalogItem: React.FC<CatalogItemProps> = ({
  description,
  disabled,
  canAccess,
  accessDeniedMessage = '',
  path,
  id,
  icon,
  title,
}: CatalogItemProps) => {
  const navigate = useNblNavigate();
  const { exposureParamsEnabled } = useExposureParams();
  const theme: NebulaTheme = useTheme();
  const { typography, palette } = theme;
  const matchDown = useMediaQuery(theme.breakpoints.down('xl'));

  const renderIcon = (icon: any) => {
    /* @ts-ignore */
    const Icon = icons[icon];
    return (
      <Grid container mb={1} width={'auto'} mt={title.length > 16 ? 0 : 1.5}>
        <HexaIconWrapper>
          {!matchDown ? (
            <Icon
              style={{
                fontSize: '1.5rem',
              }}
            />
          ) : (
            <Icon
              style={{
                fontSize: '1.5rem',
              }}
            />
          )}
        </HexaIconWrapper>
      </Grid>
    );
  };
  const isDisabled = (disabled && !exposureParamsEnabled(title)) || !canAccess;

  return (
    <>
      <StyledTooltip title={!canAccess ? accessDeniedMessage : isDisabled ? 'Coming soon...' : ''} arrow sx={{ textAlign: 'center' }}>
        {/* @ts-ignore */}
        <StyledCatalogCard
          id={`${id}-card`}
          className={isDisabled ? 'isDisabled' : ''}
          isDisabled={isDisabled}
          onClick={() => !isDisabled && path && navigate(path)}
          sx={{
            [theme.breakpoints.down('xl')]: {
              minWidth: 186,
              minHeight: 150,
              width: 210,
              height: 172,
            },
          }}
        >
          <Stack spacing={5}>
            <Grid>
              <Box display="flex" alignItems="center">
                {icon && renderIcon(icon)}
                <Typography
                  variant="h3"
                  title={title}
                  sx={{
                    marginBottom: 3,
                    marginLeft: 2,
                    marginTop: title.length > 16 ? 0.5 : 2,
                    ...typography.medium,
                    color: palette.contentCard.textPrimaryColor,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    [theme.breakpoints.down('xl')]: {
                      marginBottom: 1.06,
                    },
                  }}
                >
                  {title}
                </Typography>
              </Box>
              <Typography
                sx={{
                  fontSize: '16px',
                  marginBottom: 2,
                  color: palette.contentCard.textSecondaryColor,
                  marginTop: title.length > 16 ? 0 : 2,
                  [theme.breakpoints.down('xl')]: {
                    marginTop: 1.06,
                  },
                }}
              >
                {description}
              </Typography>
            </Grid>
          </Stack>
        </StyledCatalogCard>
      </StyledTooltip>
    </>
  );
};

export default CatalogItem;
