import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { NblTable } from '.';

describe('NblTable component', () => {
  const props = {
    rows: [
      { id: 1, a: 'NEB-PAAS-DB-22258', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: 'P3252272', g: '12/06/2024 11:10 AM' },
      { id: 2, a: 'NEB-PAAS-DB-22257', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: 'P3252272', g: '12/06/2024 11:10 AM' },
      { id: 8, a: 'NEB-PAAS-DB-22254', c: 'running', d: 'Nebula Q1 Release', e: 'f', f: 'P3252272', g: '12/06/2024 11:10 AM' },
    ],
    columns: [
      { field: 'id', headerName: 'ID' },
      { field: 'a', headerName: 'Request ID' },
      { field: 'c', headerName: 'Status' },
      { field: 'd', headerName: 'Project Name' },
      { field: 'e', headerName: 'Approval Date' },
      { field: 'f', headerName: 'Created By' },
      { field: 'g', headerName: 'Created Date' },
    ],
    rowSize: '5',
    pageSizeOptions: ['5', '20', '40', '60'],
    showResetFilter: false,
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblTable {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
