import { StyledBanner } from '../NblBanner/styled';

type NblBannerProps = {
  onClose?: () => void;
  show: boolean;
  children: string;
};

export default function NblBanner({ onClose, show, children }: NblBannerProps) {
  if (!show) return null;

  return (
    <StyledBanner severity="warning" variant="filled" onClose={onClose}>
      <div
        dangerouslySetInnerHTML={{ __html: children }}
        style={{ overflow: 'hidden', fontSize: '1rem', display: 'inline', fontWeight: 'normal' }}
      />
    </StyledBanner>
  );
}
