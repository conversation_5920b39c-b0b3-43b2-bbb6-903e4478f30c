import { render, screen, fireEvent } from '@testing-library/react';
import OperatingSystemDetails from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { OSLayout } from '..';

const mockOnOSChange = jest.fn();
const mockOnClose = jest.fn();

jest.mock('../../Clusters/ClusterDetails/AddProject', () => (props: { onProjectsChange: any }) => {
  const { onProjectsChange } = props;
  return (
    <button data-testid="mock-add-project-btn" onClick={() => onProjectsChange(['New Project A', 'New Project B'])}>
      Update Projects
    </button>
  );
});

const restrictedOS: OSLayout = {
  disabled: false,
  imageName: 'Ubuntu Image',
  imagePath: '/images/ubuntu.img',
  layoutMor: 'layout-1',
  layoutName: 'Ubuntu Layout',
  osName: 'Ubuntu',
  restricted: true,
  shortName: 'UBT',
  projects: ['Project A'],
};

const unrestrictedOS: OSLayout = {
  disabled: false,
  imageName: 'Debian Image',
  imagePath: '/images/debian.img',
  layoutMor: 'layout-2',
  layoutName: 'Debian Layout',
  osName: 'Debian',
  restricted: false,
  shortName: 'DEB',
  projects: ['Project X'],
};

describe('OperatingSystemDetails Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders selected OS and layout details', () => {
    render(
      <NebulaThemeProvider>
        <OperatingSystemDetails selectedOS={restrictedOS} onOSChange={mockOnOSChange} onClose={mockOnClose} />
      </NebulaThemeProvider>
    );

    expect(screen.getByText('Add Project')).toBeInTheDocument();
    expect(screen.getByText('Selected OS')).toBeInTheDocument();
    expect(screen.getByText('Ubuntu')).toBeInTheDocument();
    expect(screen.getByText('Selected Layout')).toBeInTheDocument();
    expect(screen.getByText('Ubuntu Layout')).toBeInTheDocument();
  });

  it('renders AddProject only if restricted is true', () => {
    const { rerender } = render(
      <NebulaThemeProvider>
        <OperatingSystemDetails selectedOS={restrictedOS} onOSChange={mockOnOSChange} onClose={mockOnClose} />
      </NebulaThemeProvider>
    );

    expect(screen.getByText('Update Projects')).toBeInTheDocument();

    rerender(
      <NebulaThemeProvider>
        <OperatingSystemDetails selectedOS={unrestrictedOS} onOSChange={mockOnOSChange} onClose={mockOnClose} />
      </NebulaThemeProvider>
    );

    expect(screen.queryByText('Update Projects')).not.toBeInTheDocument();
    expect(screen.getByText('Enable restricted to add projects.')).toBeInTheDocument();
  });

  it('calls onOSChange when AddProject updates projects', () => {
    render(
      <NebulaThemeProvider>
        <OperatingSystemDetails selectedOS={restrictedOS} onOSChange={mockOnOSChange} onClose={mockOnClose} />
      </NebulaThemeProvider>
    );

    const updateBtn = screen.getByTestId('mock-add-project-btn');
    fireEvent.click(updateBtn);

    expect(mockOnOSChange).toHaveBeenCalledWith(
      expect.objectContaining({
        projects: ['New Project A', 'New Project B'],
      })
    );
  });
});
