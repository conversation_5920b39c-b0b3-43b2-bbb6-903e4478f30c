import { render, act } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import ThemeProvider from 'mock/ThemeProvider';
import AdministrationService from 'api/ApiService/AdministrationService';
import AdminTilePermissions from '.';
import { ADMIN_TILE_PERMISSIONS } from 'mock/permission';

describe('AdminTilePermissions component', () => {
  let getApiService: jest.SpyInstance;

  const mockStore = configureMockStore();
  const store = mockStore({
    authorization: {
      adminPermissions: [],
    },
  });

  beforeEach(() => {
    getApiService = jest.spyOn(AdministrationService.prototype, 'getAdminTilePermissions');
    getApiService.mockResolvedValue(ADMIN_TILE_PERMISSIONS);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AdminTilePermissions>
                <p>Admin authorized page</p>
              </AdminTilePermissions>
            </ThemeProvider>
          </ReduxProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });

  test('Should show the unauthorized page if api fails', async () => {
    getApiService.mockResolvedValue({ status: false, data: [] });
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <AdminTilePermissions>
              <p>Admin authorized page</p>
            </AdminTilePermissions>
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(getByText('Oops! you are not authorized to access this service. Please contact Nebula admin')).toBeInTheDocument();
  });
});
