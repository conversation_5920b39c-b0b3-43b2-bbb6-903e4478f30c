import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';

const tagSchema = yup.object().shape({
  key: yup.string().trim().required('Key is required'),
  value: yup.string().trim().required('Value is required'),
  active: yup.boolean().required('Active is required'),
  description: yup.string().trim().nullable().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
  overridable: yup.boolean().required('Override is required'),
});

const settingsSchema = yup.object().shape({
  domain: yup.string().required('Domain is required'),
  dataCenter: yup.string().required('DC is required'),
  type: yup.string().required('Type is required'),
  value: yup.object().shape({ id: yup.string().trim().required('Value is required') }),
  active: yup.boolean().required('Active is required'),
  description: yup.string().trim().nullable(),
});

const environmentSchema = yup.object().shape({
  name: yup.string().trim().required('Environment name is required'),
  approvalRequiredToModifyResources: yup.boolean(),
  settings: yup
    .array()
    .of(settingsSchema)
    .test('unique', 'Duplicate settings are not allowed', (configurations) => {
      if (!configurations) {
        return true;
      } else {
        const set = new Set(configurations.map((config) => `${config.domain}+${config.dataCenter}+${config.value?.id}`));
        return set.size === configurations.length;
      }
    }),
  tags: yup.array().of(tagSchema),
});

const applicationSchema = yup.object().shape({
  name: yup.string().trim().required('Application name is required'),
  tags: yup.array().of(tagSchema),
  environments: yup.array().of(environmentSchema),
});

export const projectNameSchema = (projectList: string[], projectName: string) =>
  yup
    .string()
    .trim()
    .required('Project name is required')
    .matches(yupMatchesParams.projectName.pattern, yupMatchesParams.projectName.errorMessage)
    .test('unique-project-name', 'Project Name already exists', async (value) => {
      if (!value) {
        return true;
      } else {
        return !projectList.filter((p) => p !== projectName).includes(value);
      }
    });

const manageMultiEnvProjectSchema = (projectList: string[], projectName: string) =>
  yup.object().shape({
    projectName: projectNameSchema(projectList, projectName),
    emailDistribution: yup
      .string()
      .email('Invalid email format')
      .matches(/@charter.com|@spectrum.com/, 'Please provide charter or spectrum email id')
      .required('Email DL is required'),
    projectAdminGroup: yup
      .array()
      .of(yup.string())
      .min(1, 'At least one group should be provided')
      .required('Project admin group is required'),
    description: yup.string().trim().nullable().max(500, 'Description cannot exceed 500 characters').default('Max 500 characters'),
    tags: yup.array().of(tagSchema),
    applications: yup.array().of(applicationSchema),
  });

export default manageMultiEnvProjectSchema;
