import React from 'react';
import { Box, useMediaQuery, useTheme } from '@mui/material';
import { MetricType } from '../../utils/statisticinfo';
import { getUsageColor } from '../../utils/colors';
import { StyledDanger } from '../../styled';
import { processUtilization } from '../../utils/utilization';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import { useSelector } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import NblTypography from 'sharedComponents/NblTypography';
import NblFlexContainer from '../../../../../sharedComponents/NblContainers/NblFlexContainer';
import { NblGridContainer, NblGridItem } from '../../../../../sharedComponents/NblContainers/NblGridContainer';
import NblDivider from '../../../../../sharedComponents/NblDivider';

export const useUsageDetailsFunction = (
  name: string,
  label: string,
  utilized: number,
  allocated: number | undefined,
  divider: boolean,
  sunburstDivider?: boolean
) => {
  const theme: NebulaTheme = useTheme();
  const matchDown = useMediaQuery(theme.breakpoints.down('2K'));

  const utilizedColor = utilized ? getUsageColor(utilized) : `${theme.palette.secondary.main}`;
  const allocatedColor = allocated ? getUsageColor(allocated) : `${theme.palette.primary.main}`;
  const utilization = processUtilization(label);
  const OverUtilized = (utilized: number) => {
    if (utilized >= 100) {
      return (
        <NblFlexContainer
          backgroundColor={theme.palette.secondary.shade6}
          height="1.5rem"
          justifyContent="center"
          alignItems="center"
          margin="0 0 0.5rem 0"
          width={matchDown ? '108%' : '100%'}
          borderRadius="24px"
        >
          <NblGridItem textAlign="center">
            <span>
              <StyledDanger />
            </span>
            <NblTypography variant={matchDown ? 'body4' : 'body3'} textAlign="center" color="shade4">
              Utilized:{utilized}%
            </NblTypography>
          </NblGridItem>
        </NblFlexContainer>
      );
    } else if (utilized < 100) {
      return (
        <NblTypography variant="body2" textAlign="right" color="shade1" margin="0 0 0.5rem 0">
          Utilized : <span style={{ color: utilizedColor }}>{utilized}%</span>
        </NblTypography>
      );
    }
  };
  const OverAllocated = (allocated: number | undefined) => {
    if (allocated && allocated >= 100) {
      return (
        <NblFlexContainer
          backgroundColor={theme.palette.secondary.shade6}
          height="1.5rem"
          justifyContent="center"
          alignItems="center"
          margin="0 0 0.5rem 0"
          width={matchDown ? '108%' : '100%'}
          borderRadius="24px"
        >
          <NblGridItem textAlign="center">
            <span>
              <StyledDanger />
            </span>
            <NblTypography variant={matchDown ? 'body4' : 'body3'} textAlign="center" color="shade4">
              Allocated:{allocated}%
            </NblTypography>
          </NblGridItem>
        </NblFlexContainer>
      );
    } else {
      if (allocated) {
        return (
          <NblTypography variant="body2" textAlign="right" color="shade1">
            Allocated : <span style={{ color: allocatedColor }}>{allocated}%</span>
          </NblTypography>
        );
      } else {
        return (
          <NblTypography variant="body2" textAlign="right" color="shade1">
            Allocated : <span style={{ color: allocatedColor }}>NA</span>
          </NblTypography>
        );
      }
    }
  };
  return (
    <>
      <NblGridItem colspan={4}>
        <Box sx={{ display: 'flex' }}>
          <Box sx={{ flex: 1, mr: 1 }}>
            <NblTypography variant="h3" textAlign="right" color="shade1">
              {utilization?.numericPart}
              <span>
                <NblTypography variant="h5" display="inline">
                  {utilization?.unitPart}
                </NblTypography>
              </span>
            </NblTypography>

            <Box sx={{ mb: 1 }}>
              <NblTypography variant="body1" textAlign="right" color="shade1">
                {name}
              </NblTypography>
            </Box>
            {OverUtilized(utilized)}
            {OverAllocated(allocated)}
          </Box>
          {divider ? <NblDivider orientation="vertical" length={matchDown ? '6rem' : '8rem'} color={theme.palette.secondary.shade6} /> : ''}
          {sunburstDivider ? (
            <NblDivider orientation="vertical" length={matchDown ? '2rem' : '4rem'} color={theme.palette.primary.shade4} opacity={1} />
          ) : (
            ''
          )}
        </Box>
      </NblGridItem>
    </>
  );
};
interface ResourceOverviewProps {}
const ResourceOverview: React.FunctionComponent<ResourceOverviewProps> = () => {
  const overviewResource = useSelector((state: State) => state.capacityPlanning.overviewResource);

  return (
    <>
      <NblTypography variant="h3" textAlign="right" textTransform="uppercase" width="auto" color="shade1" margin="1rem 0 0 0">
        {overviewResource?.resourcelabel || overviewResource?.resourcename}
      </NblTypography>
      <NblTypography variant="h5" textAlign="right" color="shade1" margin="0 0 1rem 0">
        Overview
      </NblTypography>
      <NblGridContainer columns={12}>
        {useUsageDetailsFunction(
          MetricType.CPU,
          overviewResource?.cpu,
          overviewResource?.cpuUtilized,
          overviewResource?.cpuAllocated,
          true
        )}
        {useUsageDetailsFunction(
          MetricType.Memory,
          overviewResource?.memory,
          overviewResource?.memoryUtilized,
          overviewResource?.memoryAllocated,
          true
        )}
        {useUsageDetailsFunction(
          MetricType.Storage,
          overviewResource?.storage,
          overviewResource?.storageUtilized,
          overviewResource?.storageAllocated,
          false
        )}
      </NblGridContainer>
    </>
  );
};
export default ResourceOverview;
