//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblDropdown from 'sharedComponents/NblDropdown';

type StoryProps = ComponentProps<typeof NblDropdown>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblDropdown',
  component: NblDropdown,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    options: { control: 'object' },
  },
};

export default meta;

export const Select: Story = {
  args: {
    options: [
      { label: 'John', value: 'opt1' },
      { label: 'Steven', value: 'opt2' },
      { label: 'Alex', value: 'opt3' },
      { label: 'Jack', value: 'opt4' },
      { label: 'Tom', value: 'opt5' },
      { label: 'Jane', value: 'opt6' },
    ],
    value: 'opt1',
  },
  render: (args) => {
    const WrapperComponent = () => {
      const [selectedValue, setSelectedValue] = useState<string | number>('');

      return (
        <NebulaTheme>
          <NblFlexContainer width="300px">
            <NblDropdown {...args} onChange={setSelectedValue} value={selectedValue} />
          </NblFlexContainer>
        </NebulaTheme>
      );
    };

    return <WrapperComponent />;
  },
};
