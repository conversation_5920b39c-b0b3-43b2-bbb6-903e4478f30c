import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid, Button } from '@mui/material';
import { formWrapperError, removeEmptyValues, yupMatchesParams } from 'utils/common';

import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import withAdminPermissions from 'hoc/withAdminPermissions';
import TextField from 'components/TextField';
import Select from 'components/Select';
import FormWrapper from 'components/FormWrapper';
import icons from 'assets/images/icons';
// eslint-disable-next-line
import { FormProps, AdminComponent, TagDetailProps, TagMappingData } from 'types';

import TagMappingForm from './TagMappingForm';
import TagDataTable from './TagDataTable';
import TagFormDataProps from 'types/Interfaces/TagFormDataProps';
import AdministrationService from 'api/ApiService/AdministrationService';
import { CatalogOptions } from 'api/ApiService/type';
import { toast } from 'react-toastify';
import { SPINNER_IDS, showSpinner } from 'store/reducers/spinner';
import { useDispatch } from 'react-redux';
// eslint-disable-next-line
import AddTagDetailsPayload from 'types/Interfaces/AddTagDetailsPayload';
import useNblNavigate from 'hooks/useNblNavigate';

interface AddGroupFormProps extends FormProps {
  tagFormData?: TagFormDataProps;
  editTagDetails?: TagDetailProps;
}

const validationSchema = yup.object().shape({
  serviceCatalogItem: yup.string().required('Service Catalog Item is required'),
  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});

const AddTagForm: AdminComponent<AddGroupFormProps> = ({
  title,
  onClose,
  tagFormData,
  editTagDetails,
  permissions: { canUpdate, canCreate },
}: AddGroupFormProps) => {
  const dispatch = useDispatch();
  const apiAdministrationService = new AdministrationService();

  const navigate = useNblNavigate();
  const [catalogItems, setCatalogItems] = useState<CatalogOptions>([]);
  const [tagDialog, setTagDialog] = useState<boolean>(false);
  const [tagData, setTagData] = useState<TagMappingData[]>([]);
  const [isFormSubmitting, setIsFormSubmitting] = useState<boolean>(false);
  const [tagKeys, setTagKeys] = useState<{ id: string; name: string }[]>([]);
  const [tagValues, settagValues] = useState<{ id: string; name: string }[]>([]);
  const [tagKeysLength, setTagKeysLength] = useState<Number>(0);

  useEffect(() => {
    async function getCatalogDataItems() {
      dispatch(showSpinner({ id: SPINNER_IDS.tagCatalogItems, status: true, message: 'Loading Service catalog items...' }));
      apiAdministrationService
        .getLevel4Items()
        .then((res) => {
          if (res.status) {
            setCatalogItems(res.data);
          }
        })
        .finally(() => {
          dispatch(showSpinner({ id: SPINNER_IDS.tagCatalogItems, status: false, message: '' }));
        });
    }
    getCatalogDataItems();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchTagKeys = () => {
    dispatch(showSpinner({ id: SPINNER_IDS.tagKeysData, status: true, message: 'Loading tag keys...' }));
    apiAdministrationService
      .getTagKeys()
      .then((res) => {
        if (res.status) {
          setTagKeys(res.data.tagKeys);
          setTagKeysLength(res.data.tagKeys.length);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: SPINNER_IDS.tagKeysData, status: false, message: '' }));
      });
  };

  const fetchTagValues = () => {
    dispatch(showSpinner({ id: SPINNER_IDS.tagValuesData, status: true, message: 'Loading tag values...' }));
    apiAdministrationService
      .getTagValues()
      .then((res) => {
        if (res.status) {
          settagValues(res.data.tagValues);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: SPINNER_IDS.tagValuesData, status: false, message: '' }));
      });
  };

  useEffect(() => {
    fetchTagKeys();
    fetchTagValues();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (editTagDetails) {
      formik.setFieldValue('serviceCatalogItem', editTagDetails.serviceCatalogId);
      formik.setFieldValue('description', editTagDetails.description);

      const tagDetails = editTagDetails.tags.map((tag) => ({
        tagKey: tagKeys.find((item) => item.id === tag.tagKeyId)?.name || '',
        tagKeyId: tagKeys.find((item) => item.id === tag.tagKeyId)?.id || '',

        tagValueName: tagValues.find((item) => item.id === tag.tagValueId)?.name || '',
        tagValue: tagValues.find((item) => item.id === tag.tagValueId)?.id || '',

        description: tag.comment[0],
        dynamic: tag.dynamic,
        staticTagValue: tag.tagValue,
      }));
      //@ts-ignore
      setTagData(tagDetails);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editTagDetails, tagKeys, tagValues]);

  const addOrEditTagHandler = (payload: AddTagDetailsPayload) => {
    setIsFormSubmitting(true);
    const method = editTagDetails ? 'PUT' : 'POST';
    apiAdministrationService
      .addTagDetails(payload, method)
      .then((res) => {
        if (res.status) {
          toast.success(res.data.message || 'Request submitted successfully', {
            position: toast.POSITION.BOTTOM_CENTER,
          });
          navigate('/administration/tags/view-tags');
        } // Don't need the else block as the default error handling is provided by API Service class
      })
      .finally(() => {
        setIsFormSubmitting(false);
      });
  };

  const formik = useFormik({
    initialValues: {
      serviceCatalogItem: '',
      description: '',
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const { serviceCatalogItem, description } = values;
      const selectedServiceCatalogItem = catalogItems?.find((catalog) => catalog.id === serviceCatalogItem);

      const payload = editTagDetails
        ? {
            id: editTagDetails?._id || '',
            tags: tagData.map((tagDetail) => ({
              tagKeyId: tagDetail.tagKeyId,
              comment: tagDetail.description,
              dynamic: tagDetail.dynamic,
              ...(tagDetail.dynamic ? { tagValueId: tagDetail.tagValue } : { tagValue: tagDetail.staticTagValue }),
            })),
            ...removeEmptyValues({ description }),
            isDeleted: 'false',
          }
        : {
            serviceCatalogId: selectedServiceCatalogItem?.id || '',
            tags: tagData.map((tagDetail) => ({
              tagKeyId: tagDetail.tagKeyId,
              comment: tagDetail.description,
              dynamic: tagDetail.dynamic,
              ...(tagDetail.dynamic ? { tagValueId: tagDetail.tagValue } : { tagValue: tagDetail.staticTagValue }),
            })),
            ...removeEmptyValues({ description }),
            isDeleted: 'false',
          };
      addOrEditTagHandler(payload);
    },
  });

  useEffect(() => {
    if (tagFormData) {
      formik.setValues({
        serviceCatalogItem: tagFormData.serviceCatalogItem,
        description: tagFormData.description,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tagFormData]);

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const handleTagDialog = () => {
    setTagDialog(true);
  };

  const setTagsData = (values: TagMappingData) => {
    setTagData([...tagData, values]);
    setTagDialog(false);
  };

  const dialogCloseHandler = () => {
    setTagDialog(false);
  };

  const renderFormFields = () => {
    return (
      <>
        <Grid container spacing={1}>
          <Grid container item justifyContent={'center'} spacing={3}>
            <Grid item xs={6}>
              <Select
                datatestid={'catalog-item'}
                value={formik.values.serviceCatalogItem}
                label="Service Catalog Item *"
                placeholder="Select"
                name="serviceCatalogItem"
                handleChange={formik.handleChange}
                error={formik.touched.serviceCatalogItem && formik.errors.serviceCatalogItem}
                options={catalogItems?.map((data) => ({ value: data.id, label: data.name }))}
                disabled={editTagDetails ? true : false}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                type="text"
                value={formik.values.description}
                label="Description"
                name="description"
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.description && formik.errors.description}
              />
            </Grid>
            <Grid item xs={12}>
              <Button data-testid={'add-tag'} variant="contained" onClick={handleTagDialog} disabled={tagKeysLength === tagData?.length}>
                Add Tag
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </>
    );
  };

  const renderTables = () => {
    return (
      <Grid container item justifyContent={'center'} spacing={3}>
        <Grid item xs={12}>
          <TagDataTable
            data={tagData.map((item, index) => ({ ...item, id: index }))}
            setTagData={setTagData}
            tagKeys={tagKeys}
            tagValues={tagValues}
          />
        </Grid>
      </Grid>
    );
  };

  return (
    <>
      <FormWrapper
        title={title}
        isSubmitting={isFormSubmitting || !tagData.length}
        errors={formWrapperError(formik)}
        submitText={'Submit'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        Icon={icons.AddOutlined}
        canCreate={canCreate}
        canUpdate={canUpdate}
      >
        <>
          {renderFormFields()}
          {renderTables()}
        </>
      </FormWrapper>
      {tagDialog && (
        <TagMappingForm
          onClose={dialogCloseHandler}
          setTagData={setTagsData}
          open={tagDialog}
          tagData={tagData}
          tagKeys={tagKeys}
          tagValues={tagValues}
        />
      )}
    </>
  );
};

AddTagForm.type = ADMIN_TILE_PERMISSION_TYPE.form;

export default withAdminPermissions(AddTagForm);
