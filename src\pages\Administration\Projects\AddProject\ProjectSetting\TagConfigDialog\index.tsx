import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import { Grid } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import { filterSelectedOptions, formWrapperError, yupMatchesParams } from 'utils/common';
import Select from 'components/Select';
import FormWrapper from 'components/FormWrapper';
import TextField from 'components/TextField';
import { SPINNER_IDS, showSpinner } from 'store/reducers/spinner';
import AdministrationService from 'api/ApiService/AdministrationService';
import { EditProjectTags, ProjectTagData } from 'types';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import DialogBox from 'components/DialogBox/Dialog';
import { setProjectTagDetails } from 'store/reducers/projectTags';

interface TagMappingFormProps {
  onClose: () => void;
  onSuccess?: () => void;
  openTagDialog: boolean;
  setTagData: (values: any) => void;
  tagData?: ProjectTagData[];
  editProjectTags?: EditProjectTags;
}

const validationSchema = yup.object().shape({
  tagKey: yup.string().required('Tag Key is required'),
  tagValue: yup.string().trim().matches(yupMatchesParams.tagValue.pattern, yupMatchesParams.tagValue.errorMessage),
  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});

const TagConfigDialog: React.FunctionComponent<TagMappingFormProps> = ({
  onClose,
  openTagDialog,
  setTagData,
  tagData,
}: TagMappingFormProps) => {
  const [tagKeys, setTagKeys] = useState<{ id: string; name: string }[]>([]);

  const dispatch = useDispatch();
  const apiAdministrationService = new AdministrationService();
  const { ProjectTagValues } = useSelector((state: State) => state.projectTags);

  const fetchTagKeys = () => {
    dispatch(showSpinner({ id: SPINNER_IDS.tagKeysData, status: true, message: 'Loading tag keys...' }));
    apiAdministrationService
      .getTagKeys()
      .then((res) => {
        if (res.status) {
          setTagKeys(res.data.tagKeys);
        }
      })
      .finally(() => {
        dispatch(showSpinner({ id: SPINNER_IDS.tagKeysData, status: false, message: '' }));
      });
  };

  useEffect(() => {
    fetchTagKeys();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const formik = useFormik({
    initialValues: {
      settingName: 'Tags',
      tagKey: '',
      tagValue: '',

      description: '',
      id: '',
    },
    validationSchema: validationSchema,

    onSubmit: (values) => {
      const selectedTagKey = tagKeys?.find((tagKey) => tagKey.id === values.tagKey);
      if (selectedTagKey) {
        setTagData({
          id: ProjectTagValues?.id ? ProjectTagValues?.id : (Math.random() * 100).toString(),
          settingName: values.settingName,
          tagKey: selectedTagKey?.name,
          tagKeyId: values?.tagKey,
          tagValue: values?.tagValue,

          description: values?.description,
        });
      }
      dispatch(
        setProjectTagDetails({
          settingName: '',
          id: '',
          tagKey: '',
          tagValue: '',
          description: '',
          tagKeyId: '',
          tagValueName: '',
        })
      );
      formik.resetForm();
    },
  });

  useEffect(() => {
    if (ProjectTagValues.id !== '' && ProjectTagValues.id !== null && typeof ProjectTagValues.id !== 'undefined') {
      formik.setValues({
        settingName: 'Tags',
        id: ProjectTagValues.id,
        tagKey: ProjectTagValues.tagKeyId,
        tagValue: ProjectTagValues.tagValue,

        description: ProjectTagValues.description,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ProjectTagValues]);

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const getTagKeyOptions = () => {
    const tagKeyOptions = tagKeys?.map((data) => ({ value: data.id, label: data.name }));
    const selectedTagKeys = tagData?.map((tags) => tags.tagKeyId) || [];
    return filterSelectedOptions(tagKeyOptions, selectedTagKeys, ProjectTagValues?.tagKeyId);
  };

  const renderDialogContent = () => {
    return (
      <FormWrapper
        title={'Add Tag Details'}
        isSubmitting={formik.isSubmitting}
        errors={formWrapperError(formik)}
        submitText={'Save'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        isPopUpView
      >
        <Grid container spacing={1}>
          <Grid container item justifyContent={'center'} spacing={3}>
            <Grid item xs={12}>
              <Select
                value={formik.values.tagKey}
                label="Tag Key *"
                placeholder="Select"
                name="tagKey"
                handleChange={formik.handleChange}
                error={formik.touched.tagKey && formik.errors.tagKey}
                options={getTagKeyOptions()}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                type="text"
                value={formik.values.tagValue}
                label="Tag Value *"
                name="tagValue"
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.tagValue && formik.errors.tagValue}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                type="text"
                value={formik.values.description}
                label="Description"
                name="description"
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.description && formik.errors.description}
              />
            </Grid>
          </Grid>
        </Grid>
      </FormWrapper>
    );
  };

  return (
    <>
      <DialogBox fullWidth maxWidth={'sm'} open={openTagDialog} onClose={onCancel}>
        {renderDialogContent()}
      </DialogBox>
    </>
  );
};

export default TagConfigDialog;
