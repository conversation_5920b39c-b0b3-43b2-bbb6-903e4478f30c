import React, { useCallback } from "react";
import useNblNavigate from "hooks/useNblNavigate";
import { Link, Typography, useTheme } from "@mui/material";
import { NebulaTheme } from "NebulaTheme/type";
import { getStatusChipColor } from "utils/common";
import NblBorderContainer from "sharedComponents/NblContainers/NblBorderContainer";
import NblFlexContainer from "sharedComponents/NblContainers/NblFlexContainer";
import NblAccordion from "sharedComponents/Accordion/NblAccordion";
import { NblTable } from "sharedComponents/NblTable";
import NblChip from "sharedComponents/NblChip";
import NblTypography from "sharedComponents/NblTypography";
import NblSpinner from "sharedComponents/NblSpinner";
import { NULL_DISPLAY_VALUE } from "utils/constant";

interface NblSearchResultsProps {
  results: {[key: string]: any}, 
  isLoading: <PERSON><PERSON><PERSON>,
  searchTriggered: <PERSON>ole<PERSON>,
}

const NblSearchResults: React.FC<NblSearchResultsProps> = ({results, isLoading, searchTriggered}) => {
  const theme = useTheme<NebulaTheme>();
  const navigate = useNblNavigate();
  
  const typeMapping: {[key: string]: string} = {
    'menvprojects': 'Projects',
    'servicerequests': 'Requests',
    'resources': 'Resources',
  }
  
   const projectColumns = [
    {
      field: 'projectName',
      headerName: 'Project Name',
      flex: 1,
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
    },
    {
      field: 'createdBy',
      headerName: 'Created By',
      flex: 1,
    },
    {
      field: 'emailDistribution',
      headerName: 'Email Distribution',
      flex: 1,
    },
  ]
  
   const serviceRequestsColumns = [
    {
      field: 'serviceRequestId',
      headerName: 'Request Id',
      flex: 1,
      renderCell: (params: any) => {
        const {
          row: { serviceRequestId },
        } = params;
        return (
          <Typography variant="h6">
            <Link variant="button" sx={{ textDecoration: 'none', cursor: 'pointer' }} onClick={() => navigate(`/requests/${serviceRequestId}`)}><strong>{serviceRequestId}</strong></Link>
          </Typography>
        );
      },
    },
    {
      field: 'projectName',
      headerName: 'Project Name',
      flex: 1,
    },
    {
      field: 'requesterEmail',
      headerName: 'Requester Email',
      flex: 1,
    },
    {
      field: 'createdBy',
      headerName: 'Created By',
      flex: 1,
    },
    {
      field: 'createdAt',
      headerName: 'Created Date',
      flex: 1,
    },
    {
      field: 'approvedBy',
      headerName: 'Approved By',
      flex: 1,
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      renderCell: (params: any) => {
        const {
          row: { status },
        } = params;
        return (
          <div>
            <div>
              <NblChip id={status} label={status} borderRadius="lg" color={getStatusChipColor(status)} />
            </div>
          </div>
        );
      },
    },
  ]
  
   const resourcesColumns = [
    {
      field: 'catalogType',
      headerName: 'Catalog',
      flex: 1,
    },
    {
      field: 'resourceId',
      headerName: 'Resource Id',
      flex: 1,
      renderCell: (params: any) => {
        const {
          row: { resourceId },
        } = params;
        return (
          <Typography variant="h6">
            <Link variant="button" sx={{ textDecoration: 'none', cursor: 'pointer' }} onClick={() => navigate(`/resources/${resourceId}`)}><strong>{resourceId}</strong></Link>
          </Typography>
        );
      },
    },
    {
      field: 'resourcesName',
      headerName: 'Resource Name',
      flex: 1,
    },
    {
      field: 'requestId',
      headerName: 'Request Id',
      flex: 1,
    },
    {
      field: 'createdBy',
      headerName: 'Created By',
      flex: 1,
    },
    {
      field: 'createdAt',
      headerName: 'Created Date',
      flex: 1,
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      renderCell: (params: any) => {
        const {
          row: { status },
        } = params;
        return (
          <div>
            <div>
              <NblChip id={status} label={status} borderRadius="lg" color={getStatusChipColor(status)} />
            </div>
          </div>
        );
      },
    },
  ];

  const handleProjectRow = useCallback((row: any) => {
    return row.map((item: any) => ({
      id: item._id,
      projectName: item.projectName,
      description: item.description,
      emailDistribution: item.emailDistribution,
      createdBy: item.createdBy ? item.createdBy : NULL_DISPLAY_VALUE, // use the encoding for emdash as a single dash looks odd.
    }))
  }, [results]);
  
  const handleServiceRequestsRow = useCallback((row: any) => {
    return row.map((item: any) => ({
      id: item._id,
      serviceRequestId: item.serviceRequestId,
      createdBy: item.createdBy,
      status: item.status,
      requesterEmail: item.requesterEmail ? item.requesterEmail : NULL_DISPLAY_VALUE,
      createdAt: item.createdAt,
      approvedBy: item?.approvalDetails?.length ? item.approvalDetails?.map((item: any) => item?.approvedOrRejectedBy).join(', ') : NULL_DISPLAY_VALUE,
      projectName: item.payload.projectName ? item.payload.projectName : NULL_DISPLAY_VALUE,
    }))
  }, [results]);
  
  const handleResoucesRow = useCallback((row: any) => {
    return row.map((item: any) => ({
      catalogType: item.catalogType,
      id: item._id,
      resourcesName: item.resourcesName,
      requestId: item.requestId,
      resourceId: item.resourceId ? item.resourceId : NULL_DISPLAY_VALUE,
      status: item.status,
      createdBy: item.createdBy,
      createdAt: item.createdAt
    }))
  }, [results]);

  
  const generateProjectsTable = useCallback((type: string, data: any) => {
    return (
      <React.Fragment key={type}>
        <NblTable
          rows={handleProjectRow(data)}
          columns={projectColumns}
          showResetFilter={false}
        />
      </React.Fragment>
    )
  }, [results]);
  
  const generateServiceRequestsTable = useCallback((type: string, data: any) => {
    return (
      <React.Fragment key={type}>
        <NblTable
          rows={handleServiceRequestsRow(data)}
          columns={serviceRequestsColumns}
          showResetFilter={false}
        />
      </React.Fragment>
    )
  }, [results]);
  
  const generateResourcesTable = useCallback((type: string, data: any) => {
    return (
      <React.Fragment key={type}>
        <NblTable
          rows={handleResoucesRow(data)}
          columns={resourcesColumns}
          showResetFilter={false}
        />
      </React.Fragment>
    )
  }, [results]);

  const generateTable = useCallback((type: any, data: any) => {
    switch(type) {
      case 'menvprojects': 
        return generateProjectsTable(type, data)
      case 'servicerequests':
        return generateServiceRequestsTable(type, data)
      case 'resources':
        return generateResourcesTable(type, data)
    }
  }, [results, searchTriggered]);

  return (
    <NblBorderContainer backgroundColor={theme.palette.common.white} height="auto" margin="0 0 1rem">
      <NblFlexContainer spacing={1} direction='column' alignItems='center' justifyContent='flex-start' padding="0 0 1rem">
        <NblTypography variant="h3" color="shade1" margin="1rem 0 0">Search Results</NblTypography>
        {isLoading
          ? <NblSpinner
              display="inline"
              spinnerData={[
                {
                  id: 'nebulaSearch',
                  message: 'Please wait while loading search results',
                  status: true,
                },
              ]}
            />
          : Object.keys(results).length > 0
            ? Object.keys(results)
                .filter(item => results[item].length !== 0)
                .map(item => {
                  return (
                    <NblAccordion
                      key={item}
                      summary={typeMapping[item]}
                      hasDivider
                      defaultExpanded
                      bgColor={theme.palette.common.white}
                    >
                      {generateTable(item, results[item])}
                    </NblAccordion>
                  )
                })
            : searchTriggered
              ? <NblTypography textAlign="center" color="shade1" variant="body1">No results.</NblTypography> 
              : <NblTypography textAlign="center" color="shade1" variant="body1">Make a search to see results.</NblTypography>
        }
      </NblFlexContainer>
    </NblBorderContainer>
  )
};

export default NblSearchResults;