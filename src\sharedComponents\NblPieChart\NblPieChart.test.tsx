import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblPieChart from '.';

describe('NblPieChart component', () => {
  const props = {
    data: [
      { value: 50, label: 'IaaS' },
      { value: 50, label: 'PaaS' },
    ],
    title: 'Overall Tool Usage',
    subtitle: 'Nebula Metrics',
    loading: false,
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblPieChart {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
