import React, { useState } from 'react';
import { Icon<PERSON>utton, Stack, useTheme, Typography, Box } from '@mui/material';
import { EditOutlined, DeleteOutlineOutlined } from '@mui/icons-material';
// eslint-disable-next-line no-unused-vars
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import DataGridTable from 'components/DataGridTable';
// eslint-disable-next-line
import { SelectedGroupPermissions, Roles, AdminTilePermissions } from 'types';
import AdminPermissionForm from '../AdminPermission';

interface AdminDataTableProps {
  data: SelectedGroupPermissions['adminTilePermissions'];
  setSelectedAdminPermissions: (rules: SelectedGroupPermissions['adminTilePermissions']) => void;
  adminTilePermissions: AdminTilePermissions;
}

const AdminDataTable: React.FunctionComponent<AdminDataTableProps> = ({ data, adminTilePermissions, setSelectedAdminPermissions }) => {
  const [adminPermissionData, setAdminPermissionData] = useState({
    open: false,
    updateAdminPermissions: {
      id: null,
      adminTileId: '',
      roles: [],
    },
  });
  const theme: NebulaTheme = useTheme();
  const {
    palette: { table },
  } = theme;

  const handleEdit = (row: any) => {
    setAdminPermissionData({
      open: true,
      updateAdminPermissions: {
        id: row.id,
        adminTileId: row.adminTileId,
        roles: row.roles.map((role: { _id: string }) => role._id),
      },
    });
  };

  const handleDelete = (rowId: number) => {
    const deletedRow = data.filter((item) => item.id !== rowId);
    setSelectedAdminPermissions(deletedRow);
  };

  const columns: GridColDef[] = [
    {
      field: 'tileName',
      headerName: 'Admin Tile',
      flex: 1,
      width: 500,
    },
    {
      field: 'roles',
      headerName: 'Admin Roles',
      flex: 1,
      width: 700,
      renderCell: (params: GridRenderCellParams) => {
        const {
          row: { roles },
        } = params;
        const roleNames = roles.map((role: { roleName: string }) => role.roleName).join(', ');
        return (
          <Typography title={roleNames} sx={{ width: '100%', textOverflow: 'ellipsis', overflow: 'hidden' }}>
            {roleNames}
          </Typography>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      flex: 0.5,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        return (
          <Stack spacing={0} direction="row" alignItems={'center'}>
            <IconButton
              data-testid={`edit-icon-${params.id}`}
              onClick={() => {
                handleEdit(params.row);
              }}
            >
              <EditOutlined style={{ color: table.editIconColor }} />
            </IconButton>
            <IconButton
              data-testid={`delete-icon-${params.id}`}
              onClick={() => {
                handleDelete(params.row.id);
              }}
            >
              <DeleteOutlineOutlined style={{ color: table.deleteIconColor }} />
            </IconButton>
          </Stack>
        );
      },
    },
  ];

  const handleCloseDialog = () => {
    setAdminPermissionData({
      ...adminPermissionData,
      open: false,
    });
  };

  const setAdminData = (values: { adminTileId: string; tileName: string; roles: Roles[] }) => {
    setAdminPermissionData({
      ...adminPermissionData,
      open: false,
    });
    setSelectedAdminPermissions(
      data.map((currRow, id: number) => (id !== adminPermissionData.updateAdminPermissions.id ? currRow : values))
    );
  };

  const renderForm = () => {
    return (
      <AdminPermissionForm
        selectedAdminPermissions={data}
        open={adminPermissionData.open}
        adminTilePermissions={adminTilePermissions}
        onClose={handleCloseDialog}
        setAdminData={setAdminData}
        adminTileDetails={adminPermissionData.updateAdminPermissions}
      />
    );
  };

  return (
    <Box sx={{ mt: 2 }}>
      {data.length ? <DataGridTable columns={columns} rows={data} pageSize={5} showResetFilter={false} /> : null}
      {renderForm()}
    </Box>
  );
};

export default AdminDataTable;
