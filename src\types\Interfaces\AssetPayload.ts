import MetaData from './MetaData';
import DownStreamError from './DownStreamError';
import Organization from './Organization';
import DBaasViewDetails from './DBaasViewDetails';
import DBaasViewDetailsV2 from './DBaasViewDetailsV2';
import { ApprovalDetail } from 'api/ApiService/type';
import { FirewallTicketDetails } from 'types';

export default interface AssetPayload {
  id: string;
  name?: string;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  requestType?: string;
  approvalDetails?: ApprovalDetail[] | any;
  status?: string;
  datacenter?: string;
  createdBy?: string;
  metadata?: MetaData;
  payload?: DBaasViewDetailsV2 | DBaasViewDetails | Organization | any;
  downstreamError?: DownStreamError[];
  resourceDetails?: any;
  catalogItem?: string;
  systemUpdate?: any;
  projectName?: string;
  serviceRequestId?: string;
  approvalStatus?: string;
  startDate?: string;
  updatedAt?: string;
  resourceId?: string;
  ticketDetails?: FirewallTicketDetails[];
}
