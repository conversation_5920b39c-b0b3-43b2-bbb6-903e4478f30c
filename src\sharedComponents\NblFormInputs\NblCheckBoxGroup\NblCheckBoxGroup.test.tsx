import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblCheckBoxGroup from '.';

describe('NblCheckBoxGroup component', () => {
  const props = {
    label: 'Nbl Checkbox',
    disabled: false,
    error: false,
    helperText: 'Helper text will display here',
    name: 'test',
    options: [
      { name: 'option1', label: 'Option 1', checked: false, disabled: false },
      { name: 'option2', label: 'Option 2', checked: true, disabled: false },
      { name: 'option3', label: 'Option 3', checked: false, disabled: true },
    ],

    mandatory: true
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblCheckBoxGroup {...props} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
