import React from 'react';
import { StyledInputLabel, StyledMandatoryLabel } from './styled';

interface NblInputLabelProps {
  label: string;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
}

const NblInputLabel: React.FC<NblInputLabelProps> = ({ label, name, disabled, mandatory }) => {
  return (
    <StyledInputLabel htmlFor={name} disabled={disabled}>
      {label}
      {mandatory && <StyledMandatoryLabel>*</StyledMandatoryLabel>}
    </StyledInputLabel>
  );
};

export default NblInputLabel;
