import React from 'react';
import { StyledInputLabel, StyledMandatoryLabel } from './styled';
// eslint-disable-next-line no-unused-vars
import { Tooltip, IconButton } from '@mui/material';
import { InfoOutlinedIcon } from 'assets/images/icons/custom-icons';
import NblTooltip from 'sharedComponents/NblTooltip';

interface NblInputLabelProps {
  label: string;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  tooltipText?: string;
}

const NblInputLabel: React.FC<NblInputLabelProps> = ({ label, name, disabled, mandatory, tooltipText }) => {
  return (
    <StyledInputLabel htmlFor={name} disabled={disabled}>
      {label}
      {mandatory && (
        <>
          <StyledMandatoryLabel>*</StyledMandatoryLabel>

          {tooltipText && (
            <NblTooltip tooltipMessage={tooltipText}>
              <IconButton size="small">
                <InfoOutlinedIcon fontSize="small" />
              </IconButton>
            </NblTooltip>
          )}
        </>
      )}
    </StyledInputLabel>
  );
};

export default NblInputLabel;
