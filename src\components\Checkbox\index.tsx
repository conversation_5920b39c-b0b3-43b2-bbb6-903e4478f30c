import React, { useEffect } from 'react';
import { Checkbox as <PERSON>i<PERSON>heck<PERSON>, FormControlLabel, FormHelperText, Stack } from '@mui/material';

interface CheckboxProps {
  disabled?: boolean;
  label: string;
  labelPlacement?: 'end';
  name: string;
  checked?: boolean;
  handleChange: (event: any) => void;
  handleBlur: (event: any) => void;
  error?: boolean | string;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  showDefaultChecked?: (name: string) => void;
}

const Checkbox: React.FC<CheckboxProps> = ({
  disabled,
  label,
  name,
  labelPlacement = 'end',
  checked = false,
  handleChange,
  handleBlur,
  error,
  onMouseEnter,
  onMouseLeave,
  showDefaultChecked,
}) => {
  useEffect(() => {
    if (!disabled && !checked && showDefaultChecked) {
      showDefaultChecked(name);
    }
  }, []);
  return (
    <Stack spacing={1}>
      <FormControlLabel
        disabled={disabled}
        id={name}
        control={
          <MuiCheckbox
            name={name}
            checked={checked}
            onChange={handleChange}
            onBlur={handleBlur}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
          />
        }
        label={label}
        labelPlacement={labelPlacement}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      />
      <FormHelperText error>{error}</FormHelperText>
    </Stack>
  );
};

export default Checkbox;
