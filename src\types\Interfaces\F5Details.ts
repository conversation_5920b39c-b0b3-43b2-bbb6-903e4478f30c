import F5Datacenter from './F5Datacenter';

type DownstreamResponseData = {
  key: string;
  href: string;
};

type RequestPayload = {
  applicationDetails: {
    domain: string;
    project: string;
    application: string;
    environment: string;
    appId: string;
    applicationName: string;
    reason: string;
  };
  platformContext: {
    catalogId: string;
    domainId: string;
    envId: string;
    applicationName: string;
    domainName: string;
    environmentName: string;
  };
  loadBalancerDetails: {
    fqdnName: string;
    lbMethod: string;
    healthCheckType: string;
    healthCheckUrl: string;
    appHealthResponse: string;
    listeningPort: string;
    forwardingPort: string;
    dataCenters: F5Datacenter[];
    protocol: string;
  };
  certificateDetails: {
    certificateOption: string;
    certificateName: string;
    divisionName: string;
    orgName: string;
    locality: string;
    state: string;
    country: string;
    subjectAlternateNames: string[];
    keyType: string;
    keySize: string;
    caConfig: { name: string; value: string };
    policyFolder: { name: string; value: string };
  };
  deeplinkUrl: string;
};

export interface F5RequestMetaData {
  downstreamResponseData?: F5Details['downstreamResponseData'] & {
    key?: string;
    href?: string;
  };
}

export interface F5Details {
  downstreamResponseData: DownstreamResponseData;
  payload: RequestPayload;
}
