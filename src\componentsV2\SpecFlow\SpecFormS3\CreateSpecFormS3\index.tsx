import React, { useEffect } from 'react';
// eslint-disable-next-line no-unused-vars
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import { FormValues } from '..';
import NblTypography from 'sharedComponents/NblTypography';
import { generateEnum } from 'utils/common';
import ProjectAppEnvDropdown from 'componentsV2/Administration/ProjectAppEnvDropdown';
import { MultiENVProjectsResponse } from 'types/Interfaces/MultiENVProjectsResponse';

interface CreateSpecFormS3Props {
  projectData?: MultiENVProjectsResponse;
  catalogShortName?: string;
}

const CreateSpecFormS3: React.FunctionComponent<CreateSpecFormS3Props> = ({ projectData, catalogShortName }) => {
  const { nblFormProps, nblFormValues } = useNblForms<FormValues>();
  const FIELDNAMES = generateEnum<FormValues>(nblFormValues);
  const getProjectSettings = () => {
    return projectData?.map((project) => ({ label: project.name, value: project.id }));
  };

  useEffect(() => {
    const selectedProject = projectData?.find((project) => project.id === nblFormValues.projectName);
    if (selectedProject) {
      nblFormProps.setFieldValue(FIELDNAMES.project, selectedProject.id);
    }
  }, [nblFormValues.projectName, projectData]);

  const projectAppEnvValues = {
    domain: nblFormValues.domain,
    projectName: nblFormValues.projectName,
    application: nblFormValues.application,
    environment: nblFormValues.environment,
  };

  const formErrors = {
    domain: nblFormProps.errors.domain,
    projectName: nblFormProps.errors.projectName,
    application: nblFormProps.errors.application,
    environment: nblFormProps.errors.environment,
  };

  const formTouched = {
    domain: nblFormProps.touched.domain,
    projectName: nblFormProps.touched.projectName,
    application: nblFormProps.touched.application,
    environment: nblFormProps.touched.environment,
  };

  const renderOrganizationDetails = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'Project Details'}</NblTypography>
        <NblGridContainer columns={3} spacing={2}>
          <ProjectAppEnvDropdown
            values={projectAppEnvValues}
            formErrors={formErrors}
            formTouched={formTouched}
            handleChange={(field, value) => {
              nblFormProps.setFieldValue(field, value);
            }}
            handleBlur={(field) => nblFormProps.setFieldTouched(field, true)}
            defaultProjectOptions={getProjectSettings()}
            showDomain={false}
            catalogShortName={catalogShortName}
          />
        </NblGridContainer>
        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              label={'IAC Repo Name'}
              mandatory
              name={FIELDNAMES.iacProjectName}
              placeholder="Enter Repo Name"
              value={nblFormValues.iacProjectName}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.iacProjectName}
              error={Boolean(nblFormProps.touched.iacProjectName && nblFormProps.errors.iacProjectName)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'number'}
              label={'Namespace Id'}
              name={FIELDNAMES.namespaceId}
              placeholder={'Enter namespaceId'}
              value={nblFormValues.namespaceId}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.namespaceId}
              error={Boolean(nblFormProps.touched.namespaceId && nblFormProps.errors.namespaceId)}
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };
  const renderBucketDetails = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'Bucket Details'}</NblTypography>

        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.bucket}
              label={'Bucket Name'}
              placeholder="Enter Bucket Name"
              value={nblFormValues.bucket}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.bucket}
              error={Boolean(nblFormProps.touched.bucket && nblFormProps.errors.bucket)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.versioning}
              label={'Versioning'}
              placeholder="Enter Versioning details"
              value={nblFormValues.versioning}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.versioning}
              error={Boolean(nblFormProps.touched.versioning && nblFormProps.errors.versioning)}
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };
  return (
    <NblGridContainer>
      {renderOrganizationDetails()}
      {renderBucketDetails()}
    </NblGridContainer>
  );
};

export default CreateSpecFormS3;
