import { useEffect, useState } from 'react';
import { getVCenterData } from 'api/static-data';
import Catalog from 'components/Catalog';
import { AdministrationTabsData } from 'types';

export default function VCenters() {
  const [content, setContent] = useState<AdministrationTabsData[]>([]);
  useEffect(() => {
    async function getCatalogItems() {
      try {
        const data = await getVCenterData();
        setContent(data);
      } catch (error) {
        console.log(error);
      }
    }
    getCatalogItems();
  }, []);

  return content?.length ? <Catalog catalogItems={content} isAdminTiles /> : <div>No items to display</div>;
}
