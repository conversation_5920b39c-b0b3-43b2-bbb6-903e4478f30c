import React, { useState } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import AddProject from './index';
import { useApiService } from 'api/ApiService/context';
import { Provider as ReduxProvider } from 'react-redux';
import configureStore from 'redux-mock-store';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { MemoryRouter } from 'react-router';

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

const mockStore = configureStore([]);
const store = mockStore({});

describe('AddProject (simple tests)', () => {
  const mockGetProjects = jest.fn().mockResolvedValue({
    status: true,
    data: [
      { id: 'p1', name: 'Project One' },
      { id: 'p2', name: 'Project Two' },
    ],
  });

  beforeEach(() => {
    (useApiService as jest.Mock).mockReturnValue({
      apiComputeService: { getMultiENVProjectDetails: mockGetProjects },
    });
  });

  const Wrapper = ({ initialRows }: { initialRows: any[] }) => {
    const [rows, setRows] = useState(initialRows);
    return (
      <MemoryRouter>
        <ReduxProvider store={store}>
          <NebulaThemeProvider>
            <AddProject rows={rows} setRows={setRows} onProjectsChange={jest.fn()} />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );
  };

  it('renders with one empty row and disables Add button', async () => {
    render(<Wrapper initialRows={[{ id: '1', project: '' }]} />);
    await waitFor(() => expect(screen.getByPlaceholderText('Select Project')).toBeInTheDocument());
    expect(screen.getByRole('button', { name: /Add Project/i })).toBeDisabled();
  });

  it('enables Add button if last row has value', async () => {
    render(<Wrapper initialRows={[{ id: '1', project: 'p1' }]} />);
    const addButton = await screen.findByRole('button', {
      name: /Add Project/i,
    });
    expect(addButton).toBeEnabled();
  });
});
