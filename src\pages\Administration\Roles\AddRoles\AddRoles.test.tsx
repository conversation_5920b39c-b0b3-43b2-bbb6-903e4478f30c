import { render, act, fireEvent } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import { MemoryRouter as Router, Routes, Route } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import AddRole from './index';
import { RolePermissions } from 'mock/roles';
import RoleService from 'api/ApiService/RoleService';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'roles', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/roles/add-role'];

describe('AddGroup component', () => {
  let getRolePermissionsSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getRolePermissionsSpy = jest.spyOn(RoleService.prototype, 'getRolePermissions');
    getRolePermissionsSpy.mockResolvedValue(RolePermissions);

    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <ThemeProvider>
            <Router initialEntries={BASE_ROUTE}>
              <ReduxProvider store={store}>
                <AddRole />
              </ReduxProvider>
            </Router>
          </ThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });

  test('Should render the Add Role form', async () => {
    const { getByText, getByLabelText } = await act(async () =>
      render(
        <ThemeProvider>
          <Router initialEntries={BASE_ROUTE}>
            <ReduxProvider store={store}>
              <AddRole />
            </ReduxProvider>
          </Router>
        </ThemeProvider>
      )
    );

    expect(getByText('Add Role')).toBeInTheDocument();
    expect(getByText('Role Name *')).toBeInTheDocument();
    expect(getByText('Role Type *')).toBeInTheDocument();
    expect(getByText('Permission Name *')).toBeInTheDocument();
    expect(getByLabelText('Description')).toBeInTheDocument();
  });

  test('Should navigate to adminstration groups page when user clicks on  Cancel button', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <Router initialEntries={BASE_ROUTE}>
            <ReduxProvider store={store}>
              <Routes>
                <Route path="/administration/roles/add-role" element={<AddRole />}></Route>
                <Route path="/administration/roles" element={<div>Adminstration Roles</div>} />
              </Routes>
            </ReduxProvider>
          </Router>
        </ThemeProvider>
      )
    );
    fireEvent.click(getByText('Cancel'));
    expect(getByText('Adminstration Roles')).toBeInTheDocument();
  });
});
