//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NblCard from 'sharedComponents/NblCard';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

type StoryProps = ComponentProps<typeof NblCard>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'Cards/NblCard',
  component: NblCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    expanded: { control: 'boolean' },
    isFavourite: { control: 'boolean' },
    onClickCard: { action: 'onClickCard', type: 'function' },
    onFavourite: { action: 'onaFavourite', type: 'function' },
    color: { type: 'string', control: 'radio', options: ['primary', 'success', 'warning', 'error'] },
    active: { type: 'boolean' },
  },
};

export default meta;

const cardobj = {
  disabled: false,
  icon: 'CloudOutlined',
  title: 'Reserve IP Address Block',
  id: 'test',
  shortName: 'reserveipaddressblock',
};

const Template = (props: StoryProps) => {
  const [active, setActive] = useState(false);
  return <NblCard {...props} onClickCard={() => setActive((prev) => !prev)} active={props.active || active} />;
};

export const Expanded: Story = {
  args: {
    color: 'primary',
    ...cardobj,
    isFavourite: false,
    expanded: true,
    label: 'IPAM',
    chip: 'Network',
    width: '400px',
    description: 'Used to raise request to reserve the IP address block',
    active: false,
  },
  render: (args) => (
    <NebulaTheme>
      <Template {...args} />
    </NebulaTheme>
  ),
};
export const Minimized: Story = {
  args: {
    color: 'primary',
    ...cardobj,
    expanded: false,
  },
  render: (args) => (
    <NebulaTheme>
      <NblFlexContainer width="400px">
        <Template {...args} />
      </NblFlexContainer>
    </NebulaTheme>
  ),
};
