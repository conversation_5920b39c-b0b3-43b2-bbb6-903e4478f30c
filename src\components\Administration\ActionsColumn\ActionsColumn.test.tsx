import { act, render, fireEvent } from '@testing-library/react';
import { MemoryRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import ActionsColumn from './index';
import ThemeProvider from 'mock/ThemeProvider';

describe('Adminstration ActionsColumn Component', () => {
  const onDeleteHandler = jest.fn();
  const editUrl = '/administration/groups/IPAM-GROUP-ADMIN';

  const permissions = {
    canUpdate: true,
    canDelete: true,
  };

  const mockStore = configureMockStore();
  const store = mockStore({
    common: {
      disableDialogContentScroll: false,
    },
  });

  test('Should render the edit and delete icons', async () => {
    const { getByTestId } = await act(async () =>
      render(
        <Router>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <ActionsColumn editUrl={editUrl} permissions={permissions} onDeleteHandler={onDeleteHandler} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    expect(getByTestId('update-icon')).toBeInTheDocument();
    expect(getByTestId('delete-icon')).toBeInTheDocument();
  });

  test('Should navigate to edit route when user click on edit icon', async () => {
    const { getByTestId, getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <Router initialEntries={['/administration/groups/manage-registered-ad-groups']}>
              <Routes>
                <Route
                  path="/administration/groups/manage-registered-ad-groups"
                  element={<ActionsColumn editUrl={editUrl} permissions={permissions} onDeleteHandler={onDeleteHandler} />}
                />
                <Route path="/administration/groups/IPAM-GROUP-ADMIN" element={<div>Edit Registered AD group</div>} />
              </Routes>
            </Router>
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    const editIcon = getByTestId('update-icon');
    fireEvent.click(editIcon);
    expect(getByText('Edit Registered AD group')).toBeInTheDocument();
  });

  test('Should trigger the onDeleteHandler after user confirm deletion', async () => {
    const { getByTestId, getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <Router>
            <ThemeProvider>
              <ActionsColumn editUrl={editUrl} permissions={permissions} onDeleteHandler={onDeleteHandler} />
            </ThemeProvider>
          </Router>
        </ReduxProvider>
      )
    );

    const deleteIcon = getByTestId('delete-icon');
    fireEvent.click(deleteIcon);
    expect(getByText('Shall we proceed with this delete request?')).toBeInTheDocument();
    fireEvent.click(getByText('Delete'));
    expect(onDeleteHandler).toHaveBeenCalled();
  });

  test('Should close the delete confirmation popup when user clicks on Cancel', async () => {
    const { getByTestId, getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <Router>
            <ThemeProvider>
              <ActionsColumn editUrl={editUrl} permissions={permissions} onDeleteHandler={onDeleteHandler} />
            </ThemeProvider>
          </Router>
        </ReduxProvider>
      )
    );

    const deleteIcon = getByTestId('delete-icon');
    fireEvent.click(deleteIcon);
    expect(getByText('Confirmation')).toBeInTheDocument();
    fireEvent.click(getByText('Cancel'));
    expect(getByTestId('delete-icon')).toBeInTheDocument();
  });
});
