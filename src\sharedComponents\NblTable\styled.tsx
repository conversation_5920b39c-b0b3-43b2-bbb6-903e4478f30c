import { DataGrid } from '@mui/x-data-grid';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';
import { PaginationItem, Select, Typography } from '@mui/material';
import { TypographyVariant } from 'sharedComponents/NblTypography/styled';

interface StyledNblDataGridProps {
  theme?: NebulaTheme;
  onChange?: (event: any) => void;
  component?: string;
  disableRowSelectionOnClick?: boolean;
  styles: {
    header: {
      height: string;
    };
    footer: {
      fontSize: TypographyVariant;
    };
    pagination: {
      minWidth: string;
      height: string;
    };
  };
}

export const StyledNblTypography = styled(Typography)<StyledNblDataGridProps>(({ theme }) => {
  const typography = theme.palette.typography.shade1;
  return {
    color: `${typography}`,
  };
});

export const StyledNblPaginationItem = styled(PaginationItem)<StyledNblDataGridProps>(({ theme, styles }) => {
  const paginationItem = theme.palette.table.paginationItem;
  return {
    '&.Mui-selected': {
      borderColor: `${paginationItem.selected.borderColor}`,
      color: `${paginationItem.selected.color}`,
    },
    '&.MuiPaginationItem-root': {
      height: styles.pagination.height,
      minWidth: styles.pagination.minWidth,
    },
    '& .MuiButtonBase-root': {
      pointerEvents: 'auto',
    },
  };
});

export const StyledNblDataGrid = styled(DataGrid)<StyledNblDataGridProps>(({ theme, loading, disableRowSelectionOnClick, styles }) => {
  const { root, headers, headerSvg, Icon, row, cell, checkbox, footer, virtualScroller } = theme.palette.table.datagrid;
  return {
    overflowX: 'auto',
    width: '100%',
    height: '100%',
    minHeight: loading ? '300px' : '100%', // Set minimum height when loading
    '&.MuiDataGrid-root': {
      overflowX: 'auto',
      overflowY: 'hidden',
      border: 'none !important',
      background: `${root.bgColor} 0% 0% no-repeat padding-box`,
      opacity: 1,
      cursor: disableRowSelectionOnClick ? '' : 'pointer',
      '& .MuiDataGrid-cell:focus': {
        outline: 'none',
      },
      '& .MuiDataGrid-cell:focus-within': {
        outline: 'none',
      },
      '&.MuiDataGrid-root .MuiDataGrid-columnHeader:focus-within, &.MuiDataGrid-root .MuiDataGrid-cell:focus-within': {
        outline: 'none',
      },

      '.MuiDataGrid-columnHeaders': {
        textAlign: 'left',
        letterSpacing: '0px',
        opacity: 1,
        backgroundColor: `${headers.bgColor}`,
        position: 'sticky !important',
        top: '0',
        border: 'none',
        '.MuiDataGrid-columnHeader': {
          '.MuiDataGrid-columnHeaderDraggableContainer': {
            display: 'contents',
            '.MuiDataGrid-columnHeaderTitleContainer': {
              flex: '0 1 auto',
              '.MuiDataGrid-columnHeaderTitleContainerContent': {
                '.MuiDataGrid-columnHeaderTitle': {
                  textAlign: 'left',
                  ...theme.typography.h5,
                  letterSpacing: '0px',
                  color: `${headers.headerTitle}`,
                  opacity: 1,
                },
              },
              '.MuiDataGrid-iconButtonContainer': {
                paddingLeft: '10px',
                visibility: 'visible',
                '.MuiDataGrid-sortIcon': {
                  opacity: 'inherit !important',
                  '.MuiSvgIcon-root': {
                    width: '14px',
                    height: '14px',
                    border: `1px solid ${Icon.borderColor}`,
                    borderRadius: '2px',
                    opacity: 1,
                  },
                },
              },
            },
            '.MuiDataGrid-menuIcon': {
              flex: '1',
            },
          },
          '.MuiDataGrid-columnSeparator--sideRight': {
            display: 'none',
          },
        },
        '.MuiDataGrid-columnHeader:focus': {
          outline: 'none',
        },
        '.MuiDataGrid-columnHeader:not(:last-child)': {
          borderRight: 'none',
        },
        '.MuiDataGrid-columnHeader svg': {
          color: `${headerSvg.color} !important`,
          visibility: 'visible !important',
        },
      },
      '.MuiDataGrid-row': {
        color: `${row.color}`,
        '&:hover': {
          backgroundColor: `${row.hover.bgColor}`,
        },
        '&.Mui-selected': {
          backgroundColor: `${row.selected.bgColor}`,
          '&:hover': {
            backgroundColor: `${row.selected.hover} !important`,
          },
        },
        '.MuiDataGrid-cell': {
          borderBottom: `1px solid ${cell.borderBottom}`,
          textAlign: 'left',
          ...theme.typography.body1,
          letterSpacing: '0px',
          color: `${cell.color}`,
          opacity: 1,
          paddingTop: '12px',
          paddingBottom: '12px',
          textWrap: 'wrap',
          wordBreak: 'break-word',
        },
      },
      '.MuiDataGrid-row:last-child .MuiDataGrid-cell': {
        borderBottom: 'none',
      },
      '& .MuiCheckbox-root': {
        color: `${checkbox.color} !important`,
        '&.Mui-checked': {
          color: `${checkbox.checked.color} !important`,
        },
        '& .MuiSvgIcon-root': {
          width: '14px',
          height: '14px',
          border: `1px solid ${checkbox.svg.root.borderColor}`,
          borderRadius: '2px',
          opacity: 1,
        },
      },
      '& .MuiCheckbox-root svg path': {
        display: 'none',
      },
      '& .MuiCheckbox-root.Mui-checked svg path': {
        display: 'block',
        color: `${checkbox.svg.path.color}`,
      },
      '& .MuiDataGrid-footerContainer': {
        borderColor: `${footer.borderColor}`,
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        padding: '12px 0px',
      },
      '& .MuiTablePagination-toolbar': {
        justifyContent: 'flex-start',
      },
      '& .MuiDataGrid-virtualScroller::-webkit-scrollbar': {
        width: '6px',
        height: '6px',
      },
      '& .MuiDataGrid-virtualScroller::-webkit-scrollbar-thumb': {
        background: `${virtualScroller.bgColor} 0% 0% no-repeat padding-box`,
        borderRadius: '4px',
        opacity: 1,
      },
      '& .MuiDataGrid-virtualScroller::-webkit-scrollbar-thumb:hover': {
        backgroundColor: `${virtualScroller.hover.bgColor}`,
      },
    },
    '.MuiDataGrid-selectedRowCount': {
      flexShrink: '0 !important',
      ...(styles.footer.fontSize === 'h6' ? theme.typography.h6 : theme.typography.body4),
      fontWeight: theme.typography.medium.fontWeight,
      textTransform: 'capitalize',
    },
  };
});

export const StyledNblSelect = styled(Select)<StyledNblDataGridProps>(({ theme }) => {
  const select = theme.palette.table.Select;
  return {
    marginRight: 2,
    background: `${select.background} 0% 0% no-repeat padding-box`,
    opacity: 1,
    boxShadow: 'none',
    '.MuiSelect-select': {
      color: `${select.color}`,
      ...theme.typography.body1,
      padding: '8px',
    },
    '.MuiOutlinedInput-notchedOutline': {
      border: 'none ! important',
    },
    '&.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline': {
      border: 0,
    },
    '&.MuiOutlinedInput-root.Mui-focused': {
      border: 0,
    },
  };
});
