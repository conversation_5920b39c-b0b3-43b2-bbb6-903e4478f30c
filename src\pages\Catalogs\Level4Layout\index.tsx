import React from 'react';

import CatalogCards from '../CatalogCards';
import { CatalogsItem } from 'store/reducers/catalogs';

interface Level4LayoutProps {
  catalogLevel1: string;
  catalogLevel2: string;
  level3CatalogItems: CatalogsItem[];
  getLevel2CatalogName: (level3ShortName?: string) => { catalogLevel2ShortName: string; catalogLevel2Name: string };
  getLevel4CatalogItems: (level3ShortName: string) => CatalogsItem[];
}

const Level4Layout: React.FunctionComponent<Level4LayoutProps> = ({
  catalogLevel1,
  catalogLevel2,
  level3CatalogItems,
  getLevel2CatalogName,
  getLevel4CatalogItems,
}) => {
  if (level3CatalogItems?.length) {
    return (
      <CatalogCards
        catalogLevel1={catalogLevel1 || ''}
        catalogLevel2={catalogLevel2 || ''}
        level3CatalogItems={level3CatalogItems}
        getLevel2CatalogName={getLevel2CatalogName}
        getLevel4CatalogItems={getLevel4CatalogItems}
      />
    );
  }
  return null;
};

const arePropsEqual = (prevProps: Level4LayoutProps, nextProps: Level4LayoutProps) => {
  const prevCatalogItems = prevProps.level3CatalogItems || [];
  const nextCatalogItems = nextProps.level3CatalogItems || [];

  if (prevProps.catalogLevel1 !== nextProps.catalogLevel1 || prevProps.catalogLevel2 !== nextProps.catalogLevel2) {
    return false;
  }

  if (prevCatalogItems.length !== nextCatalogItems.length) {
    return false;
  }

  for (let i = 0; i < prevCatalogItems.length; i++) {
    if (prevCatalogItems[i].shortName !== nextCatalogItems[i].shortName) {
      return false;
    }
  }

  // If no differences are found, return true to indicate the props haven't changed
  return true;
};

export default React.memo(Level4Layout, arePropsEqual);
