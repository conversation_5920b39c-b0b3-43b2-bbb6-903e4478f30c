import { renderHook, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import useSecretCommonFields from '.';
import { useApiService } from 'api/ApiService/context';

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

const mockStore = configureStore([thunk]);

describe('useSecretCommonFields', () => {
  const mockDispatch = jest.fn();
  const mockGetMyResourcesv2 = jest.fn();

  beforeEach(() => {
    mockDispatch.mockClear();
    mockGetMyResourcesv2.mockClear();

    (useApiService as jest.Mock).mockReturnValue({
      apiAssetService: {
        getMyResourcesv2: mockGetMyResourcesv2,
      },
    });
  });

  it('fetches data and sets fields correctly', async () => {
    const fakeAsset = {
      projectName: 'Test Project',
      resourceName: 'nebula-stamp',
      platformContext: {
        domainName: 'test.domain.com',
        applicationName: 'Test App',
        environmentName: 'Test Env',
      },
      resourcesDetails: {
        namespace: 'nebula-stamp',
      },
    };

    mockGetMyResourcesv2.mockResolvedValue({
      status: true,
      data: { items: [fakeAsset] },
    });

    const store = mockStore({});
    store.dispatch = mockDispatch;

    const wrapper = ({ children }: any) => (
      <Provider store={store}>
        <MemoryRouter initialEntries={['/resource/123?metadata=true']}>
          <Routes>
            <Route path="/resource/:resourceId" element={children} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );

    const { result } = renderHook(() => useSecretCommonFields(), { wrapper });

    await waitFor(() => {
      expect(result.current.length).toBeGreaterThan(0);
      expect(result.current).toEqual([
        { title: 'Domain', value: 'test.domain.com' },
        { title: 'Project', value: 'Test Project' },
        { title: 'Application', value: 'Test App' },
        { title: 'Environment', value: 'Test Env' },
        { title: 'Namespace Name', value: 'nebula-stamp' },
      ]);
    });
  });
});
