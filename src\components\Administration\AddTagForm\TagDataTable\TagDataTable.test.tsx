import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';

import TagDataTable from '.';
import ThemeProvider from 'mock/ThemeProvider';
import { GetTagKeysData } from 'mock/GetTagKeysData';
import { GetTagValuesData } from 'mock/GetTagValuesData';

jest.mock('react-toastify');

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    disableDialogContentScroll: false,
  },
});

describe('Render TagData Table form', () => {
  const handleData = jest.fn();
  const tagKeys = GetTagKeysData.data.tagKeys;
  const tagValues = GetTagValuesData.data.tagValues;

  const data = [
    {
      id: 1,
      tagKey: 'Organization',
      tagKeyId: '6622174e72130d729af754t4',
      tagValue: '6622174e72130d729af754b7',
      tagValueName: 'project.oraganization',
      description: 'test',
      dynamic: false,
      staticTagValue: 'test',
    },
  ];
  test('Should render the table with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <TagDataTable data={data} setTagData={handleData} tagKeys={tagKeys} tagValues={tagValues} />
          </ThemeProvider>
        </ReduxProvider>
      )
    );
    expect(screen.getByText('Label')).toBeInTheDocument();
    expect(screen.getByText('Tag Value')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(getByText('Actions')).toBeInTheDocument();
  });
  test('Should remove the deleted row from the table', async () => {
    const { getByText, getByTestId, queryByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <TagDataTable data={data} setTagData={handleData} tagKeys={tagKeys} tagValues={tagValues} />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    expect(getByText(data[0]['tagKey'])).toBeInTheDocument();
    const deleteIcon = getByTestId('delete-icon-tag');
    await act(() => {
      fireEvent.click(deleteIcon);
    });
    waitFor(() => {
      expect(queryByText(data[0]['tagKey'])).toBeNull();
    });
  });

  test('Should open edit tag popup on click of edit icon', async () => {
    const { getByTestId, queryByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <TagDataTable data={data} setTagData={handleData} tagKeys={tagKeys} tagValues={tagValues} />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    const editIcon = getByTestId('edit-icon-tag');
    await act(() => {
      fireEvent.click(editIcon);
    });
    waitFor(() => {
      expect(queryByText('Edit Tag')).toBeInTheDocument();
    });
  });
});
