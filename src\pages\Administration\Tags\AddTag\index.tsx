import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import AddTagForm from 'components/Administration/AddTagForm';
import useNblNavigate from 'hooks/useNblNavigate';

const AddTag = () => {
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const navigateToTagDetails = () => {
    navigate('/administration/tags');
  };

  return <AddTagForm title="Add Tag" permissions={{}} onSuccess={navigateToTagDetails} onClose={navigateToTagDetails} />;
};

export default AddTag;
