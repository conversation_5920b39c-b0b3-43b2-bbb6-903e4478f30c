import React, { useEffect } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid } from '@mui/material';

import { formWrapperError, filterSelectedOptions } from 'utils/common';
import MultiSelect from 'components/MultiSelect';
import Select from 'components/Select';
import FormWrapper from 'components/FormWrapper';
import { CatalogPermissions, SelectedGroupPermissions } from 'types';
import DialogBox from 'components/DialogBox/Dialog';

interface CatalogPermissionProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  catalogPermissions: CatalogPermissions;
  selectedCatalogPermissions: SelectedGroupPermissions['catalogPermissions'];
  setCatalogData: (values: any) => void;
  catalogDetails?: { catalogId: string; roles: string[] };
}

const validationSchema = yup.object().shape({
  catalogId: yup.string().trim().required('Catalogs is required'),
  catalogRoles: yup.array().of(yup.string()).min(1, 'Please select atleast one catalog role').required('Catalog Role is required'),
});

const CatalogPermission: React.FunctionComponent<CatalogPermissionProps> = ({
  open,
  onClose,
  setCatalogData,
  catalogDetails,
  catalogPermissions,
  selectedCatalogPermissions,
}: CatalogPermissionProps) => {
  const formik = useFormik({
    initialValues: {
      catalogId: '',
      catalogRoles: [],
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const { catalogNames, catalogRoles } = catalogPermissions;
      const selectedCatalog = catalogNames.find((catalog) => catalog._id === values.catalogId);
      setCatalogData({
        catalogId: selectedCatalog?._id,
        catalogName: selectedCatalog?.name,
        roles: values.catalogRoles.map((id) => catalogRoles.find((roles) => roles._id === id)),
      });
    },
  });

  useEffect(() => {
    if (catalogDetails) {
      formik.setValues({
        catalogId: catalogDetails.catalogId,
        /* @ts-ignore */
        catalogRoles: catalogDetails.roles,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [catalogDetails]);

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  const getCatalogOptions = () => {
    const catalogNames = catalogPermissions.catalogNames.map((catalog) => ({ value: catalog._id, label: catalog.name }));
    const selectedCatalogIds = selectedCatalogPermissions.map((selectedPermissions) => selectedPermissions.catalogId);
    return filterSelectedOptions(catalogNames, selectedCatalogIds, catalogDetails?.catalogId);
  };

  const renderFormFields = () => {
    return (
      <Grid container spacing={1}>
        <Grid container item justifyContent={'center'} spacing={3}>
          <Grid item xs={12}>
            <Select
              value={formik.values.catalogId}
              label="Catalogs *"
              placeholder="Select"
              name="catalogId"
              handleChange={formik.handleChange}
              error={formik.touched.catalogId && formik.errors.catalogId}
              options={getCatalogOptions()}
            />
          </Grid>
          <Grid item xs={12}>
            <MultiSelect
              value={formik.values.catalogRoles}
              label="Catalog Roles *"
              placeholder="Select"
              name="catalogRoles"
              handleChange={formik.handleChange}
              error={formik.touched.catalogRoles && formik.errors.catalogRoles}
              options={catalogPermissions.catalogRoles.map((roles) => ({ label: roles.roleName, value: roles._id }))}
            />
          </Grid>
        </Grid>
      </Grid>
    );
  };

  return (
    <DialogBox fullWidth maxWidth={'sm'} open={open} onClose={onCancel}>
      <FormWrapper
        title={`${catalogDetails?.catalogId ? 'Edit' : 'Add'} Catalog Permissions`}
        errors={formWrapperError(formik)}
        submitText={'Save'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        isPopUpView
      >
        {renderFormFields()}
      </FormWrapper>
    </DialogBox>
  );
};

export default CatalogPermission;
