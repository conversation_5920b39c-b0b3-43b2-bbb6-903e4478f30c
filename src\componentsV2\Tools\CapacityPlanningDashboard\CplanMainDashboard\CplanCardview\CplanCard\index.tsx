import { Card, CardActionArea, useMediaQuery, useTheme } from '@mui/material';
import { NblGridContainer, NblGridItem } from '../../../../../../sharedComponents/NblContainers/NblGridContainer';
import NblTypography from '../../../../../../sharedComponents/NblTypography';
import { MetricType } from '../../../utils/statisticinfo';
import CardBody from '../CardBody';
import CardHead from '../CardHead';
import { CalculatedVropsResource } from '../../../utils/types';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from '../../../../../../NebulaTheme/type';
import { setOverviewResource, setSelectedDomainIds } from 'store/reducers/capacityplanning';
import useNblNavigate from '../../../../../../hooks/useNblNavigate';
import { useDispatch } from 'react-redux';

interface CplanCardProps {
  data: CalculatedVropsResource;
}

const CplanCard: React.FunctionComponent<CplanCardProps> = ({ data }) => {
  const theme: NebulaTheme = useTheme();
  const dispatch = useDispatch();
  const navigate = useNblNavigate();
  const matchDown = useMediaQuery(theme.breakpoints.down('2K'));
  const handleClick = () => {
    navigate(data.resourceid);
    dispatch(setSelectedDomainIds([data.domainid]));
  };

  const handleMouseEnter = () => {
    dispatch(setOverviewResource(data));
  };

  return (
    <>
      <Card
        sx={{
          margin: 0,
          padding: 0,
          maxWidth: matchDown ? '250px' : '360px',
          '&:hover': {
            boxShadow: `3px 3px 6px ${theme.palette.primary.shade6}`,
          },
        }}
      >
        <CardActionArea onClick={handleClick} onMouseEnter={handleMouseEnter}>
          <NblGridContainer spacing={0} columns={3} margin="0px" padding="0px" overflowY="hidden">
            <NblGridItem borderRadius="10px 10px 0 0" colspan={3}>
              <CardHead data={data} />
            </NblGridItem>
            <NblGridItem colspan={3} backgroundColor={theme.palette.secondary.shade2}>
              <NblTypography variant="body1" color="shade6">
                Utilization%
              </NblTypography>
            </NblGridItem>
            <NblGridItem colspan={3} backgroundColor={theme.palette.secondary.shade2}>
              <NblGridContainer columns={12} overflowY="hidden">
                <CardBody label={MetricType.CPU} usage={data.cpuUtilized} divider />
                <CardBody label={MetricType.Memory} usage={data.memoryUtilized} divider />
                <CardBody label={MetricType.Storage} usage={data.storageUtilized} divider={false} />
              </NblGridContainer>
            </NblGridItem>
          </NblGridContainer>
        </CardActionArea>
      </Card>
    </>
  );
};

export default CplanCard;
