import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { NblTabBar } from 'sharedComponents/NblNavigation/NblTabBar';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import useNblNavigate from 'hooks/useNblNavigate';

interface Level2LayoutProps {}

const Level2Layout: React.FunctionComponent<Level2LayoutProps> = () => {
  const navigate = useNblNavigate();
  const { catalogLevel1, catalogLevel2 } = useParams();
  const [tabDetails, setTabDetails] = useState<{ activeTab: number; tabs: Array<{ label: string }> }>({ activeTab: 0, tabs: [] });

  const { level2 } = useSelector((state: State) => state.catalogs);

  const level2CatalogItems = (catalogLevel1 && level2?.[catalogLevel1]) || [];

  useEffect(() => {
    if (level2CatalogItems.length) {
      let activeTab = 0;
      const TABS = [
        { label: 'All', disabled: false },
        ...level2CatalogItems.map((catalogItem) => ({
          label: catalogItem.name,
          disabled: false,
        })),
      ];

      if (catalogLevel2) {
        const selectedTab = level2CatalogItems.find((catalogItem) => catalogItem.shortName === catalogLevel2);
        activeTab = TABS.findIndex((catalogItem) => catalogItem.label === selectedTab?.name);
      }

      setTabDetails({
        tabs: TABS,
        activeTab: activeTab,
      });
    }
  }, [level2CatalogItems, catalogLevel1, catalogLevel2]);

  const onTabChange = (label: string) => {
    if (label === 'All') {
      navigate(`/${catalogLevel1}`);
    } else {
      const selectedTab = level2CatalogItems.find((catalogItem) => catalogItem.name === label);
      if (selectedTab) {
        navigate(`/${catalogLevel1}/${selectedTab['shortName']}`);
      }
    }
  };

  if (tabDetails.tabs.length) {
    return <NblTabBar tabs={tabDetails.tabs} activeTab={tabDetails.activeTab} onTabChange={onTabChange} />;
  }

  return null;
};

export default Level2Layout;
