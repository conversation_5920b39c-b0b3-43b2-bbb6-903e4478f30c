type Namespace = {
  namespaceName: string;
  resourceId: string;
  status: string;
  catalogType: string;
  catalogLevel03: string;
  platformContext: {
    catalogId: string;
    envId: string;
    domainId: string;
  };
  requestType: string;
  resourcesDetails?: {
    request_id: string;
    lease_id: string;
    renewable: boolean;
    lease_duration: number;
    data?: {
      id: string;
      path: string;
    };
    wrap_info: any;
    warnings: any;
    auth: any;
    namespace?: string;
  };
};

export default interface NamespaceList {
  items: Namespace[];
}
