import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import NetworkConfigDialog from './index';
import ThemeProvider from 'mock/ThemeProvider';

jest.mock('react-toastify');

describe('Create Network Config new request form', () => {
  const handleClose = jest.fn();
  const handleSuccess = jest.fn();
  const handleProjectSettingsData = jest.fn();
  const dataCenter = [
    {
      id: '66216af38b22d42be179edde',
      name: 'NCW',
    },
    {
      id: '66216ae28b22d42be179eddc',
      name: 'NCE',
    },
    {
      id: '660ff0b92188d2daadf7f458',
      name: 'STAMP',
    },
  ];
  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <NetworkConfigDialog
            setNetworkData={handleProjectSettingsData}
            openNetworkDialog={true}
            onClose={handleClose}
            onSuccess={handleSuccess}
            dataCenter={dataCenter}
          />
        </ThemeProvider>
      )
    );
    expect(screen.getByText('Network Setting Config')).toBeInTheDocument();
    expect(screen.getByText('Network(VLAN) *')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Data Center *')).toBeInTheDocument();
    const cancelButton = getByText('Cancel');
    expect(cancelButton).toBeEnabled();
    const saveButton = getByText('Save');
    expect(saveButton).toBeEnabled();
  });

  test('On Click of Save button on Network config', async () => {
    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <NetworkConfigDialog
            setNetworkData={handleProjectSettingsData}
            openNetworkDialog={true}
            onClose={handleClose}
            onSuccess={handleSuccess}
            dataCenter={dataCenter}
          />
        </ThemeProvider>
      )
    );
    fireEvent.click(getByText('Save'));
    waitFor(() => {
      expect(getByText('Label is required')).toBeInTheDocument();
    });
  });
});
