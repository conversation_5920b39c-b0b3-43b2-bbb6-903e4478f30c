import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { Grid } from '@mui/material';
import { formWrapperError, yupMatchesParams } from 'utils/common';

import TextField from 'components/TextField';
import Select from 'components/Select';
import FormWrapper from 'components/FormWrapper';
import icons from 'assets/images/icons';
import { GroupNameData, TeamDataProps } from 'types';
import { getGroupNames } from 'api/static-data';

interface AddTeamFormProps {
  title: string;
  onClose: () => void;
  onSuccess?: () => void;
  teamData?: TeamDataProps;
}

const validationSchema = yup.object().shape({
  teamName: yup
    .string()
    .trim()
    .required('Team Name is required')
    .matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),
  groups: yup.string().required('Groups is required'),
  description: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});

const AddTeamForm: React.FunctionComponent<AddTeamFormProps> = ({ title, onClose, teamData }: AddTeamFormProps) => {
  const [groupNames, setGroupNames] = useState<GroupNameData[]>([]);
  useEffect(() => {
    async function getGroupItems() {
      try {
        const data = await getGroupNames();
        console.log(data);
        setGroupNames(data);
      } catch (error) {
        console.log(error);
      }
    }
    getGroupItems();
  }, []);
  useEffect(() => {
    if (teamData) {
      formik.setValues({
        teamName: teamData.teamName,
        groups: teamData.groups,
        description: teamData.description,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [teamData]);
  const formik = useFormik({
    initialValues: {
      teamName: '',
      groups: '',
      description: '',
    },
    validationSchema: validationSchema,
    onSubmit: () => {},
  });

  const onCancel = () => {
    formik.resetForm();
    onClose();
  };

  return (
    <>
      <FormWrapper
        title={title}
        isSubmitting={formik.isSubmitting}
        errors={formWrapperError(formik)}
        submitText={'Submit'}
        onCancel={onCancel}
        onSubmit={formik.handleSubmit}
        Icon={icons.AddOutlined}
      >
        <Grid container spacing={1}>
          <Grid container item justifyContent={'center'} spacing={3}>
            <Grid item xs={6}>
              <TextField
                type="text"
                name="teamName"
                label="Team Name *"
                placeholder="Please Enter"
                value={formik.values.teamName}
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.teamName && formik.errors.teamName}
              />
            </Grid>
            <Grid item xs={6}>
              <Select
                value={formik.values.groups}
                label="Groups *"
                placeholder="Select"
                name="groups"
                handleChange={formik.handleChange}
                error={formik.touched.groups && formik.errors.groups}
                options={groupNames?.map((data) => ({ value: data.option, label: data.option }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                type="text"
                value={formik.values.description}
                label="Description"
                name="description"
                handleChange={formik.handleChange}
                handleBlur={formik.handleBlur}
                error={formik.touched.description && formik.errors.description}
              />
            </Grid>
          </Grid>
        </Grid>
      </FormWrapper>
    </>
  );
};

export default AddTeamForm;
