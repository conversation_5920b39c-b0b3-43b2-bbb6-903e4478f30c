import { CapacityPlanningDonutIcon, CapacityPlanningUpTrendIcon } from 'assets/images/icons/custom-icons';
import IconToggleGroup from '../components/IconToggleGroup';
import { useEffect, useState } from 'react';
import TimeSliceDropdown from '../components/TimeSliceDropdown';
import TimeStamp from '../components/TimeStamp';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import CplanSunburstView from './CplanSunburstView';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { useParams } from 'react-router';
import { useApiService } from 'api/ApiService/context';
// eslint-disable-next-line no-unused-vars
import { CalculatedVropsResource } from '../utils/types';
import CplanTable from '../CplanMainDashboard/CplanTableView2/CplanTable';
import { CalculateVropsResource } from '../utils/utilization';
import { useDispatch, useSelector } from 'react-redux';
import useNblNavigate from '../../../../hooks/useNblNavigate';
import { setSunburstLevel, setSelectedHour } from '../../../../store/reducers/capacityplanning';
import NblDivider from '../../../../sharedComponents/NblDivider';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import { useTheme } from '@mui/material';
import { CplanResourceType, TimeInterval } from '../utils/constant';

const CplanSunburstScreen: React.FunctionComponent = () => {
  const theme: NebulaTheme = useTheme();
  const { apiCapacityPlanningService } = useApiService();
  const [label, setLabel] = useState('Vcenter');
  const [calcResource, setCalcResource] = useState({} as CalculatedVropsResource);
  const [calcResources, setCalcResources] = useState([] as CalculatedVropsResource[]);
  const hours = useSelector((state: State) => state.capacityPlanning.hours);
  const navigate = useNblNavigate();
  const dispatch = useDispatch();
  const sunburstLevel = useSelector((state: State) => state.capacityPlanning.sunburstLevel);

  const chartToggleicons = [
    { value: 'chartview', label: 'Chart View', component: <CapacityPlanningDonutIcon /> },
    { value: 'trendview', label: 'Trend View', component: <CapacityPlanningUpTrendIcon />, disabled: true },
  ];
  const { vcenterId, datacenterId, clusterId, vmId } = useParams();

  const [resourceId, setResourceId] = useState<string>('');
  const [resourceType, setResourceType] = useState<string>('');

  //getting resource by Id
  const getResourceById = async (vcenterId: string, hours: number, interval?: string) => {
    const actualInterval = interval ?? hours > 24 ? TimeInterval.Daily : TimeInterval.Hourly;
    const resources = (await apiCapacityPlanningService.getResourcesById(vcenterId, hours, 2, actualInterval)).data;
    const calculatedResources: CalculatedVropsResource[] = resources
      .slice(1)
      .map((res) => ({ ...CalculateVropsResource(res), id: res.resourceid }));
    setCalcResources(calculatedResources);

    setCalcResource(CalculateVropsResource(resources[0]));
  };
  useEffect(() => {
    if (vmId) {
      dispatch(setSelectedHour(48));
      getResourceById(vmId, 48, TimeInterval.Daily);
      setLabel('Virtual Machine');
      dispatch(setSunburstLevel(4));
      setResourceId(vmId);
      setResourceType(CplanResourceType.VM);
    } else if (clusterId) {
      getResourceById(clusterId, hours);
      setLabel('Hypervisor');
      dispatch(setSunburstLevel(3));
      setResourceId(clusterId);
      setResourceType(CplanResourceType.HOST);
    } else if (datacenterId) {
      getResourceById(datacenterId, hours);
      setLabel('Cluster');
      dispatch(setSunburstLevel(2));
      setResourceId(datacenterId);
      setResourceType(CplanResourceType.CLUSTER);
    } else if (vcenterId) {
      getResourceById(vcenterId, hours);
      setLabel('Datacenter');
      dispatch(setSunburstLevel(1));
      setResourceId(vcenterId);
      setResourceType(CplanResourceType.DATACENTER);
    }
  }, [vcenterId, datacenterId, clusterId, vmId, hours, sunburstLevel]);

  //rowClick on Sunburst Table
  const handleRowClick = (row: any) => {
    sunburstLevel !== 4 && navigate(`${row.resourceid}`);
  };

  return (
    <NblGridContainer columns={19} id="exportSection">
      <NblGridItem colspan={9}>
        <CplanSunburstView inner={calcResource} outer={calcResources} resourceId={resourceId} resourceType={resourceType} />
      </NblGridItem>
      <NblFlexContainer justifyContent="center" alignContent="center" margin="6rem 0 0 0">
        <NblDivider orientation="vertical" color={theme.palette.secondary.shade6} length="70%" />
      </NblFlexContainer>
      <NblGridItem colspan={9} overflowY="hidden">
        <NblFlexContainer justifyContent="flex-end" position="static" top="60" right="30" height="auto">
          <TimeStamp />
        </NblFlexContainer>
        <NblGridContainer columns={4} rows={10}>
          <NblGridItem colspan={4} rowspan={1}>
            <NblFlexContainer justifyContent="end" alignItems="center" height="auto">
              <TimeSliceDropdown />
              <IconToggleGroup icons={chartToggleicons} selected={'chartview'} onChange={() => {}}></IconToggleGroup>
            </NblFlexContainer>
          </NblGridItem>
          <NblGridItem rowspan={7} colspan={4}>
            <CplanTable data={calcResources} onRowClick={handleRowClick} label={label} />
          </NblGridItem>
        </NblGridContainer>
      </NblGridItem>
    </NblGridContainer>
  );
};

export default CplanSunburstScreen;
