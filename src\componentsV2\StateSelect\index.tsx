import React from 'react';
import STATE from './constant';
import NblSelect from 'sharedComponents/NblFormInputs/NblSelect';

interface StateSelectProps {
  name: string;
  value: string;
  helperText?: string;
  error?: boolean;
  onChange: (event: any) => void;
  onBlur: (event: any) => void;
}

const StateSelect: React.FunctionComponent<StateSelectProps> = ({ name, value, helperText, error, onChange, onBlur }: StateSelectProps) => {
  return (
    <NblSelect
      value={value}
      label="State"
      placeholder="Select State"
      name={name}
      options={STATE.map((state) => ({ value: state.code, label: state.label }))}
      handleChange={onChange}
      handleBlur={onBlur}
      helperText={helperText}
      error={error}
    />
  );
};

export default StateSelect;
