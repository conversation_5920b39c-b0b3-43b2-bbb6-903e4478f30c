import React from 'react';
import { StyledFormControlLabel, StyledRadio } from './styled';

interface NblRadioProps {
  disabled?: boolean;
  checked: boolean;
  label: string;
  name: string;
  value: string;
  labelPlacement?: 'start' | 'end';
  onChange: (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => void;
  onBlur: (event: React.FocusEvent<HTMLButtonElement>) => void;
  error?: boolean;
  onMouseEnter?: (event: React.MouseEvent<HTMLLabelElement>) => void;
  onMouseLeave?: (event: React.MouseEvent<HTMLLabelElement>) => void;
}

const NblRadioButton: React.FunctionComponent<NblRadioProps> = ({
  disabled,
  label,
  onChange,
  onBlur,
  error,
  onMouseEnter,
  onMouseLeave,
  labelPlacement = 'end',
  checked = false,
  name,
  value,
}) => {
  return (
    <StyledFormControlLabel
      disabled={disabled}
      control={
        <StyledRadio
          checked={checked}
          onChange={onChange}
          id={name}
          name={name}
          onBlur={onBlur}
          color={error ? 'error' : 'primary'}
          disableRipple
          value={value}
        />
      }
      label={label}
      labelPlacement={labelPlacement}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    />
  );
};

export default NblRadioButton;
