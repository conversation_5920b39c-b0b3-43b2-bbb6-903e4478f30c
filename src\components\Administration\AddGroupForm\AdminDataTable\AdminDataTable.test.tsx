import { act, render, fireEvent, waitFor } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import { AdminTilePermissions } from 'mock/Groups';

import { Provider as ReduxProvider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import { GroupAdminTileData } from 'mock/GroupAdminTileData';
import AdminDataTable from '.';

const mockStore = configureMockStore();
const store = mockStore({
  common: {
    exposureParams: [],
  },
});
describe('Render Admin tile Permission  table', () => {
  const setAdminData = jest.fn();

  test('Should render admin tile permissions table ', async () => {
    const { getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <AdminDataTable
              adminTilePermissions={AdminTilePermissions}
              setSelectedAdminPermissions={setAdminData}
              data={GroupAdminTileData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    expect(getByText('Actions')).toBeInTheDocument();
    expect(getByText('Admin Tile')).toBeInTheDocument();
    expect(getByText('Admin Roles')).toBeInTheDocument();
    expect(getByText(GroupAdminTileData[0]['tileName'])).toBeInTheDocument();
    expect(
      getByText(`${GroupAdminTileData[0]['roles'][0]['roleName']}, ${GroupAdminTileData[0]['roles'][1]['roleName']}`)
    ).toBeInTheDocument();
  });

  test('Should close the Edit Admin Permissions Dialog on clicking Cancel button', async () => {
    const { getByText, getByTestId, queryByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <AdminDataTable
              adminTilePermissions={AdminTilePermissions}
              setSelectedAdminPermissions={setAdminData}
              data={GroupAdminTileData}
            />
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    const editIcon = getByTestId(`edit-icon-${GroupAdminTileData[0]['id']}`);
    act(() => {
      fireEvent.click(editIcon);
    });
    await waitFor(() => {
      expect(getByText('Edit Admin Tile Permissions')).toBeInTheDocument();
    });

    act(() => {
      fireEvent.click(getByText('Cancel'));
    });
    await waitFor(() => {
      expect(queryByText('Edit Admin Tile Permissions')).toBeNull();
    });
  });
});
