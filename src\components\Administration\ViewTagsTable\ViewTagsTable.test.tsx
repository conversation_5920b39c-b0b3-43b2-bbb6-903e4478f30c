import { act, fireEvent, render, waitFor } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import { Route, MemoryRouter as Router, Routes } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';

import ThemeProvider from 'mock/ThemeProvider';
import AdministrationService from 'api/ApiService/AdministrationService';
import { TagRows } from 'mock/Tags';
import ViewTag from 'pages/Administration/Tags/ViewTags';
import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';

const mockStore = configureMockStore();
const store = mockStore({
  user: {
    userDetails: {
      isAdmin: true,
    },
  },
  authorization: {
    adminPermissions: [{ shortName: 'tags', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/tags/view-tags'];

describe('viewTags Component', () => {
  let getTagsSpy: jest.SpyInstance;
  let getAdminCatalogItemsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getTagsSpy = jest.spyOn(AdministrationService.prototype, 'getTags');

    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);
  });

  afterEach(() => {
    getTagsSpy.mockRestore();
  });

  test('Should display the No Record Found message', async () => {
    getTagsSpy.mockResolvedValue({ status: false, data: [] });
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <ViewTag />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    await waitFor(() => {
      expect(getTagsSpy).toHaveBeenCalled();
    });
    expect(getByText('No Record Found')).toBeInTheDocument();
  });
  test('Should render the table with data ', async () => {
    getTagsSpy.mockResolvedValue({ status: false, data: TagRows.data });
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <ViewTag />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    await waitFor(() => {
      expect(getTagsSpy).toHaveBeenCalled();
    });

    expect(getByText('Tag Name')).toBeInTheDocument();
    expect(getByText('Service Catalog Item')).toBeInTheDocument();
    expect(getByText('Actions')).toBeInTheDocument();
  });
  test('Clicking on edit icon should trigger the popup', async () => {
    getTagsSpy.mockResolvedValue({ status: true, data: TagRows.data });
    const { getAllByTestId, getByText } = await act(async () =>
      render(
        <ReduxProvider store={store}>
          <ThemeProvider>
            <Router initialEntries={BASE_ROUTE}>
              <Routes>
                <Route path="/administration/tags/view-tags" element={<ViewTag />} />
                <Route path="/administration/tags/*" element={<div>Edit Tag</div>} />
              </Routes>
            </Router>
          </ThemeProvider>
        </ReduxProvider>
      )
    );

    const editIcon = getAllByTestId('update-icon');
    fireEvent.click(editIcon[0]);

    await waitFor(() => {
      expect(getByText('Edit Tag')).toBeInTheDocument();
    });
  });
});
