import { useEffect, useState } from 'react';

import { useApiService } from 'api/ApiService/context';
import { extractCatalogItemsByLevel, getCatalogItemsQuery } from 'utils/common';
import { CatalogItem } from 'types';

interface CatalogItems {
  allCatalogItems: CatalogItem[];
  level1: CatalogItem[];
  level2: CatalogItem[];
  level3: CatalogItem[];
  level4: CatalogItem[];
}

const useFetchAllCatalogItems = () => {
  const { apiUsageMetricsService } = useApiService();
  const [catalogItems, setCatalogItems] = useState<CatalogItems>({ allCatalogItems: [], level1: [], level2: [], level3: [], level4: [] });

  const fetchAllCatalogItems = async () => {
    const catalogItemsResponse = await apiUsageMetricsService.getAllCatalogItems(getCatalogItemsQuery());
    if (catalogItemsResponse.status) {
      const { catalogs } = catalogItemsResponse.data.data;
      const { level1, level2, level3, level4 } = extractCatalogItemsByLevel(catalogs);

      setCatalogItems({
        allCatalogItems: catalogs,
        level1,
        level2,
        level3,
        level4,
      });
    }
  };

  useEffect(() => {
    fetchAllCatalogItems();
  }, []);

  return {
    ...catalogItems,
  };
};

export default useFetchAllCatalogItems;
