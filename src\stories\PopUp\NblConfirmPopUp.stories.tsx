//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps, useState } from 'react';
import { Provider } from 'react-redux';
import { store } from 'store';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblConfirmPopUp from 'sharedComponents/NblConfirmPopUp';

type StoryProps = ComponentProps<typeof NblConfirmPopUp>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblConfirmPopUp',
  component: NblConfirmPopUp,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

const Template: React.FC<StoryProps> = (args) => {
  const [open, setOpen] = useState(args.open);

  const handleCancel = () => {
    setOpen(false);
  };

  const handleSubmit = () => {
    setOpen(false);
  };

  const handleOpen = () => {
    setOpen(true);
  };

  return (
    <Provider store={store}>
      <NebulaTheme>
        <NblFlexContainer width="100%">
          <NblButton buttonID={'confirm-open-btn'} onClick={handleOpen} variant="outlined" color="primary">
            Open PopUp
          </NblButton>
          <NblConfirmPopUp {...args} open={open} onClose={handleCancel} onSubmit={handleSubmit} showCloseIcon={true} maxWidth={'500px'} />
        </NblFlexContainer>
      </NebulaTheme>
    </Provider>
  );
};

export const PopUp: Story = {
  args: {
    title: 'Confirmation',
    content: 'Do you want to submit this VM Request?',
    open: false,
  },
  render: (args) => <Template {...args} />,
};
