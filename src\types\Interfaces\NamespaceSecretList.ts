interface RotatableSecret {
  id: string;
  key: string;
  value: string;
  secretId: string;
  type: string;
  status: 'SUCCESS' | 'FAILED' | string;
  rotationType: 'Auto' | 'Manual' | string;
  secretTTLInHours: number | string;
  nextRotationDate: string;
  updatedAt: string;
  updatedBy: string;
  policyId: string;
  path: string;
  lastDeviceSyncStatus: string;
  active: boolean;
}

interface NormalSecret {
  id: string;
  key: string;
  secretId: string;
  value: string;
  type: string;
  updatedAt: string;
  updatedBy: string;
  policyId: string;
  path: string;
  lastDeviceSyncStatus: string;
  active: boolean;
}

type NamespaceSecretList = RotatableSecret | NormalSecret;

export default NamespaceSecretList;
