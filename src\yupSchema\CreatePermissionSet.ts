import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

const isValidPermissionSetName = (value: string) => {
  const minLength = 1;
  const maxLength = 12;
  const allowedChars = /^[a-zA-Z0-9.+,=@-]+$/;

  if (value?.length < minLength || value?.length > maxLength) {
    return `Permission Set Name must be between ${minLength} and ${maxLength} characters`;
  }
  if (value.trim() !== value) {
    return 'No Leading or Trailing Spaces allowed';
  }
  if (!allowedChars.test(value)) {
    return 'Allowed special characters are + = . , @ -';
  }
  return true;
};
export const CreatePermissionSetSchema = yup.object().shape({
  permissionSetName: yup
    .string()
    .trim()
    .required('Permission set name is required')
    .test('is-valid-aws-name', 'Please enter valid Permission Set name', function (value) {
      if (!value) {
        return true;
      }
      const validationResult = isValidPermissionSetName(value);
      if (validationResult !== true) {
        return this.createError({ message: validationResult });
      }
      return true;
    }),
  description: yup
    .string()
    .trim()
    .required('Description is required')
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
});
