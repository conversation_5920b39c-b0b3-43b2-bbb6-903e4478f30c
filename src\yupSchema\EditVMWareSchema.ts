import * as yup from 'yup';
import TAG_ACTION from 'types/Enums/EditVMWareTagsActions';
import TOOLS_ACTION from 'types/Enums/EditVMWareToolsActions';
import { ToolStatus } from 'componentsV2/IaaS/EditVMWare/EditVMWareTools';
import VM_ACTION from 'types/Enums/VMReConfigureActions';
import { IP_MODES } from 'types/Enums/VmIpModes';

const ipRegex = /^[0-9]{1,3}(\.[0-9]{1,3}){3}$/;
const ipv6Regex = /^[a-fA-F0-9:]+$/;

const networkSchema = yup.object().shape({
  id: yup.string().required(),
  network: yup.string().required(),
  ipMode: yup.string().oneOf([IP_MODES.static, IP_MODES.dynamic]).required(),
  subnetIpv4: yup.string().optional(),
  subnetIpv6: yup.string().optional(),
  ipv4: yup.boolean().optional(),
  ipv6: yup.boolean().optional(),

  ipv4address: yup
    .string()
    .transform((v) => (typeof v === 'string' ? v.trim() : v))
    .when(['ipMode', 'ipv4'], {
      is: (ipMode: string, ipv4: boolean) => ipMode === IP_MODES.static && ipv4,
      then: (s) =>
        s.required('IPv4 required').test('ipv4-valid-or-empty', 'Invalid IPv4', (value) => {
          if (!value) return false; // required error
          return ipRegex.test(value);
        }),
      otherwise: (s) => s.notRequired().test('ipv4-skip-validation', () => true),
    }),

  ipv6address: yup
    .string()
    .transform((v) => (typeof v === 'string' ? v.trim() : v))
    .when(['ipMode', 'ipv6'], {
      is: (ipMode: string, ipv6: boolean) => ipMode === IP_MODES.static && ipv6,
      then: (s) =>
        s.required('IPv6 required').test('ipv6-valid-or-empty', 'Invalid IPv6', (value) => {
          if (!value) return false; // required error
          return ipv6Regex.test(value);
        }),
      otherwise: (s) => s.notRequired().test('ipv6-skip-validation', () => true),
    }),

  action: yup.string().oneOf([VM_ACTION.add, VM_ACTION.delete]).optional(),
  isExisting: yup.boolean().optional(),
});

const tagSchema = yup.object().shape({
  tagName: yup.string().when('action', {
    is: TAG_ACTION.add,
    then: (schema) => schema.required('Tag name is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  tagValue: yup.string().when('action', {
    is: TAG_ACTION.add,
    then: (schema) => schema.required('Tag value is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  isExisting: yup.boolean().optional(),
  action: yup.string().oneOf([TAG_ACTION.add]).optional(),
});

const toolSchema = yup.object().shape({
  toolName: yup.string().when('action', {
    is: (val: TOOLS_ACTION | undefined) => val === TOOLS_ACTION.install || val === TOOLS_ACTION.uninstall,
    then: (schema) => schema.required('Tool name is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  toolType: yup.string().optional(),
  status: yup.mixed<ToolStatus>().oneOf([ToolStatus.success, ToolStatus.failed]).optional(),
  action: yup.mixed<TOOLS_ACTION>().oneOf([TOOLS_ACTION.install, TOOLS_ACTION.uninstall]).optional(),
});

const EditVMWareSchema = yup.object().shape({
  hostName: yup
    .string()
    .trim()
    .when([], {
      is: () => true,
      then: (schema) => schema.notRequired().test('valid-host', 'Invalid host', (val) => !val || val.trim().length > 0),
    }),

  owner: yup.string().nullable().optional(),

  description: yup
    .string()
    .trim()
    .when([], {
      is: () => true,
      then: (schema) => schema.notRequired().test('valid-description', 'Invalid description', (val) => !val || val.trim().length > 0),
    }),

  networks: yup.array().of(networkSchema).optional(),

  tags: yup.array().of(tagSchema).optional(),

  tools: yup.array().of(toolSchema).optional(),
});

export default EditVMWareSchema;
