interface Contact {
  name: string;
  email: string;
  title: string;
  phone: string;
}

type CreateSubAccount = {
  status: string;
  serviceRequestId: string;
  basicAccountDetails: {
    organization: string;
    accountDetails: {
      description: string;
      name: string;
      owner: string;
      ownerEmail: string;
    };
    evpName: string;
    vpDetails: {
      name: string;
      email: string;
    };
    environment: string;
    costCenterId: string;
    organizationName: string;
  };
  operationsContact: Contact;
  billingContact: Contact;
  securityContact: Contact;
  access: [
    {
      email: string;
      pid: string;
      name: string;
      role: string;
    }
  ];
  vpcSettings: {
    cidrsGua: string;
    cidrsBase: string;
    description: string;
    requirements: string;
    onPremConnectivity: boolean;
    eksSubnet: boolean;
  };
  deeplinkUrl: string;
};

export default CreateSubAccount;
