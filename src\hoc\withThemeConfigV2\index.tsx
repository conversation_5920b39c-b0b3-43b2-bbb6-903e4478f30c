import React, { useState, useEffect } from 'react';

import Spinner from 'components/Spinner';
import { THEMES } from 'NebulaTheme/bundle';
import { ThemeConfigJson } from 'NebulaTheme/type';

export type ThemeConfigState = ThemeConfigJson & {
  backgroundImage: string;
  nebulaLogo: string;
  spectrumLogo: string;
};

function withThemeConfig<P>(WrappedComponent: React.ComponentType<P>) {
  const WithThemeConfig: React.FC<P> = (props) => {
    const [isThemeLoading, setIsThemeLoading] = useState<boolean>(false);
    const [themeConfig, setThemeConfig] = useState(THEMES['light']);

    useEffect(() => {
      const timeoutId = setTimeout(() => {
        setIsThemeLoading(false);
        clearTimeout(timeoutId);
      }, 1000);
    }, [themeConfig]);

    const themeSwitcher = (themeName: string) => {
      setIsThemeLoading(true);
      switch (themeName) {
        case THEMES.light.name:
          setThemeConfig({
            ...THEMES.light,
          });
          break;
      }
    };

    if (isThemeLoading) {
      return <Spinner color={themeConfig.palette.spinner.themeLoaderIconColor} />;
    }

    return <WrappedComponent {...props} themeConfig={themeConfig} themeSwitcher={themeSwitcher} />;
  };

  return WithThemeConfig;
}

export default withThemeConfig;
