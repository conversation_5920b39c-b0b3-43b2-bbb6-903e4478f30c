import React, { useEffect } from 'react';
// eslint-disable-next-line no-unused-vars
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import { FormValues } from '..';
import NblTypography from 'sharedComponents/NblTypography';
import { generateEnum } from 'utils/common';
import ProjectAppEnvDropdown from 'componentsV2/Administration/ProjectAppEnvDropdown';
import { MultiENVProjectsResponse } from 'types/Interfaces/MultiENVProjectsResponse';

interface CreateSpecFormEC2Props {
  projectData?: MultiENVProjectsResponse;
  catalogShortName?: string;
}

const CreateSpecFormEC2: React.FunctionComponent<CreateSpecFormEC2Props> = ({ projectData, catalogShortName }) => {
  const { nblFormProps, nblFormValues } = useNblForms<FormValues>();
  const FIELDNAMES = generateEnum<FormValues>(nblFormValues);
  const getProjectSettings = () => {
    return projectData?.map((project) => ({ label: project.name, value: project.id }));
  };

  useEffect(() => {
    const selectedProject = projectData?.find((project) => project.id === nblFormValues.projectName);
    if (selectedProject) {
      nblFormProps.setFieldValue(FIELDNAMES.project, selectedProject.id);
    }
  }, [nblFormValues.projectName, projectData]);

  const projectAppEnvValues = {
    domain: nblFormValues.domain,
    projectName: nblFormValues.projectName,
    application: nblFormValues.application,
    environment: nblFormValues.environment,
  };

  const formErrors = {
    domain: nblFormProps.errors.domain,
    projectName: nblFormProps.errors.projectName,
    application: nblFormProps.errors.application,
    environment: nblFormProps.errors.environment,
  };

  const formTouched = {
    domain: nblFormProps.touched.domain,
    projectName: nblFormProps.touched.projectName,
    application: nblFormProps.touched.application,
    environment: nblFormProps.touched.environment,
  };

  const renderOrganizationDetails = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'Project Details'}</NblTypography>
        <NblGridContainer columns={3} spacing={2}>
          <ProjectAppEnvDropdown
            values={projectAppEnvValues}
            formErrors={formErrors}
            formTouched={formTouched}
            handleChange={(field, value) => {
              nblFormProps.setFieldValue(field, value);
            }}
            handleBlur={(field) => nblFormProps.setFieldTouched(field, true)}
            defaultProjectOptions={getProjectSettings()}
            showDomain={false}
            catalogShortName={catalogShortName}
          />
        </NblGridContainer>
        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              label={'IAC Repo Name'}
              mandatory
              name={FIELDNAMES.iacProjectName}
              placeholder="Enter Repo Name"
              value={nblFormValues.iacProjectName}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.iacProjectName}
              error={Boolean(nblFormProps.touched.iacProjectName && nblFormProps.errors.iacProjectName)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'number'}
              label={'Namespace Id'}
              name={FIELDNAMES.namespaceId}
              placeholder={'Enter namespaceId'}
              value={nblFormValues.namespaceId}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.namespaceId}
              error={Boolean(nblFormProps.touched.namespaceId && nblFormProps.errors.namespaceId)}
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };
  const renderEC2Details = () => {
    return (
      <>
        <NblTypography variant="subtitle2">{'EC2 Details'}</NblTypography>

        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.ec2_name}
              label={'Instance Name'}
              placeholder="Enter Instance Name"
              value={nblFormValues.ec2_name}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.ec2_name}
              error={Boolean(nblFormProps.touched.ec2_name && nblFormProps.errors.ec2_name)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.subnet_id}
              label={'Subnet Id'}
              placeholder="Enter Subnet details"
              value={nblFormValues.subnet_id}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.subnet_id}
              error={Boolean(nblFormProps.touched.subnet_id && nblFormProps.errors.subnet_id)}
            />
          </NblGridItem>
        </NblGridContainer>
        <NblGridContainer columns={3} spacing={3}>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.ami_id}
              label={'AMI Id'}
              placeholder="Enter AMI Id"
              value={nblFormValues.ami_id}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.ami_id}
              error={Boolean(nblFormProps.touched.ami_id && nblFormProps.errors.ami_id)}
            />
          </NblGridItem>
          <NblGridItem>
            <NblTextField
              type={'text'}
              name={FIELDNAMES.instance_type}
              label={'Instance Type'}
              placeholder="Enter Instance Type"
              value={nblFormValues.instance_type}
              handleChange={nblFormProps.handleChange}
              handleBlur={nblFormProps.handleBlur}
              helperText={nblFormProps.errors.instance_type}
              error={Boolean(nblFormProps.touched.instance_type && nblFormProps.errors.instance_type)}
            />
          </NblGridItem>
        </NblGridContainer>
      </>
    );
  };

  return (
    <NblGridContainer>
      {renderOrganizationDetails()}
      {renderEC2Details()}
    </NblGridContainer>
  );
};

export default CreateSpecFormEC2;
