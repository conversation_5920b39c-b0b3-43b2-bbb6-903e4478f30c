import { Box, useTheme } from '@mui/material';
import NoRecordFoundImage from 'assets/images/icons/noRecordsFound.svg';
import { StyledNoDataGrid } from '../../styled';
import NblTypography from 'sharedComponents/NblTypography';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';

const NoData: React.FunctionComponent = () => {
  const theme: NebulaTheme = useTheme();
  return (
    <>
      <StyledNoDataGrid>
        <Box
          component="img"
          src={NoRecordFoundImage}
          alt="No Records Found"
          sx={{
            marginBottom: '8px',
            height: '200px',
            width: '223px',
            [theme.breakpoints.down('2K')]: {
              height: '143px',
              width: '140px',
            },
          }}
        />

        <NblTypography variant="h5" color="shade6">
          No Records Found!
        </NblTypography>
      </StyledNoDataGrid>
    </>
  );
};

export default NoData;
