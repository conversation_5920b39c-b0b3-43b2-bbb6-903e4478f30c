// eslint-disable-next-line
import { Tooltip, TooltipProps, tooltipClasses } from '@mui/material';
import { styled } from '@mui/system';

export const StyledTooltip = styled(({ className, ...props }: TooltipProps) => <Tooltip {...props} classes={{ popper: className }} />)({
  [`& .${tooltipClasses.tooltip}`]: {
    whiteSpace: 'pre-line',
    padding: '10px',
    fontSize: '12px',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: '6px',
  },
});
