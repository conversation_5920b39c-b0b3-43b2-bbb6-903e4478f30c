// eslint-disable-next-line
import { SvgIcon, SvgIconProps } from '@mui/material';

export const AdminPanelSettingsOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169211">
            <rect id="Rectangle_22041-21" width="32" height="32" fill="none" />
            <g id="Group_169209">
              <path
                id="padding_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
                d="M8.53,20.04c.26,0,.51-.1.69-.28.19-.18.29-.43.29-.69,0-.26-.1-.51-.29-.69-.18-.19-.43-.29-.69-.28-.26,0-.51.1-.69.28-.19.18-.29.43-.29.69,0,.26.1.51.29.69.18.19.43.29.69.28ZM12.88,20.04c.26,0,.51-.1.69-.28.19-.18.29-.43.29-.69,0-.26-.1-.51-.29-.69-.18-.19-.43-.29-.69-.28-.26,0-.51.1-.69.28-.19.18-.29.43-.29.69,0,.26.1.51.29.69.18.19.43.29.69.28h0ZM17.22,20.04c.26,0,.51-.1.69-.28.19-.18.29-.43.29-.69,0-.26-.1-.51-.29-.69-.18-.19-.43-.29-.69-.28-.26,0-.51.1-.69.28-.19.18-.29.43-.29.69,0,.26.1.51.29.69.18.19.43.29.69.28h0ZM5.49,23.12c-.53.01-1.04-.2-1.41-.58-.38-.37-.59-.88-.58-1.41v-4.25c-.01-.53.2-1.04.58-1.41.37-.38.88-.59,1.41-.58h14.76c.53-.01,1.04.2,1.41.58.38.37.59.88.58,1.41v4.25c.01.53-.2,1.04-.58,1.41-.37.38-.88.59-1.41.58H5.49ZM5.49,21.47h14.76c.09,0,.17-.04.23-.11.07-.06.1-.14.11-.23v-4.25c0-.09-.04-.17-.11-.23-.06-.07-.14-.1-.23-.11H5.49c-.09,0-.17.04-.23.11-.07.06-.1.14-.11.23v4.25c0,.09.04.17.11.23.06.07.14.1.23.11Z"
                fill="currentColor"
              />
            </g>
            <path
              id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24-6"
              d="M23.29,11.35v2.2c0,.13.05.25.14.33.09.09.21.14.33.14.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33v-2.2h2.2c.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33,0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14h-2.2v-2.2c0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33v2.2h-2.2c-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33,0,.13.05.25.14.33.09.09.21.14.33.14h2.2Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const OutputOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169212">
            <rect id="Rectangle_22041-23" width="32" height="32" fill="none" />
            <g id="Group_169209-3">
              <path
                id="padding_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-3"
                d="M8.53,20.04c.26,0,.51-.1.69-.28.19-.18.29-.43.29-.69,0-.26-.1-.51-.29-.69-.18-.19-.43-.29-.69-.28-.26,0-.51.1-.69.28-.19.18-.29.43-.29.69,0,.26.1.51.29.69.18.19.43.29.69.28ZM12.88,20.04c.26,0,.51-.1.69-.28.19-.18.29-.43.29-.69,0-.26-.1-.51-.29-.69-.18-.19-.43-.29-.69-.28-.26,0-.51.1-.69.28-.19.18-.29.43-.29.69,0,.26.1.51.29.69.18.19.43.29.69.28h0ZM17.22,20.04c.26,0,.51-.1.69-.28.19-.18.29-.43.29-.69,0-.26-.1-.51-.29-.69-.18-.19-.43-.29-.69-.28-.26,0-.51.1-.69.28-.19.18-.29.43-.29.69,0,.26.1.51.29.69.18.19.43.29.69.28h0ZM5.49,23.12c-.53.01-1.04-.2-1.41-.58-.38-.37-.59-.88-.58-1.41v-4.25c-.01-.53.2-1.04.58-1.41.37-.38.88-.59,1.41-.58h14.76c.53-.01,1.04.2,1.41.58.38.37.59.88.58,1.41v4.25c.01.53-.2,1.04-.58,1.41-.37.38-.88.59-1.41.58H5.49ZM5.49,21.47h14.76c.09,0,.17-.04.23-.11.07-.06.1-.14.11-.23v-4.25c0-.09-.04-.17-.11-.23-.06-.07-.14-.1-.23-.11H5.49c-.09,0-.17.04-.23.11-.07.06-.1.14-.11.23v4.25c0,.09.04.17.11.23.06.07.14.1.23.11Z"
                fill="currentColor"
              />
            </g>
            <path
              id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24-7"
              d="M23.29,11.35s.96-.02.95,0h2.2c.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33,0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14h-2.2s-.93-.02-.95,0h-2.2c-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33,0,.13.05.25.14.33.09.09.21.14.33.14h2.2Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ConsoleOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="26.705" height="18.353" viewBox="0 0 26.705 18.353">
        <g id="noun-terminal-7813464" transform="translate(-16.664 -27.082)">
          <path
            id="Path_90982"
            data-name="Path 90982"
            d="M18.5,27.082a1.847,1.847,0,0,0-1.835,1.835V43.6A1.847,1.847,0,0,0,18.5,45.435H41.522A1.858,1.858,0,0,0,43.369,43.6V28.917a1.858,1.858,0,0,0-1.848-1.835Zm0,1.675H41.522c.11,0,.172.052.172.16V43.6a.157.157,0,0,1-.172.172H18.5c-.11,0-.16-.064-.16-.172V28.917a.139.139,0,0,1,.16-.16Z"
            fill="#04213b"
          />
          <path
            id="Path_90983"
            data-name="Path 90983"
            d="M32.071,38.646a.844.844,0,0,0-.579,1.428l3.154,3.116-3.154,3.116a.836.836,0,1,0,1.183,1.183l3.756-3.708a.835.835,0,0,0,0-1.183l-3.758-3.708h0a.833.833,0,0,0-.6-.247Z"
            transform="translate(-8.742 -6.932)"
            fill="#04213b"
          />
          <path
            id="Path_90984"
            data-name="Path 90984"
            d="M48.753,57.164a.838.838,0,1,0,0,1.675h6.675a.838.838,0,0,0,0-1.675Z"
            transform="translate(-18.735 -18.035)"
            fill="#04213b"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AssignmentTurnedInOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169213">
            <rect id="Rectangle_22041-24" width="32" height="32" fill="none" />
            <g id="Group_169209-4">
              <path
                id="padding_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-4"
                d="M8.53,20.04c.26,0,.51-.1.69-.28.19-.18.29-.43.29-.69,0-.26-.1-.51-.29-.69-.18-.19-.43-.29-.69-.28-.26,0-.51.1-.69.28-.19.18-.29.43-.29.69,0,.26.1.51.29.69.18.19.43.29.69.28ZM12.88,20.04c.26,0,.51-.1.69-.28.19-.18.29-.43.29-.69,0-.26-.1-.51-.29-.69-.18-.19-.43-.29-.69-.28-.26,0-.51.1-.69.28-.19.18-.29.43-.29.69,0,.26.1.51.29.69.18.19.43.29.69.28h0ZM17.22,20.04c.26,0,.51-.1.69-.28.19-.18.29-.43.29-.69,0-.26-.1-.51-.29-.69-.18-.19-.43-.29-.69-.28-.26,0-.51.1-.69.28-.19.18-.29.43-.29.69,0,.26.1.51.29.69.18.19.43.29.69.28h0ZM5.49,23.12c-.53.01-1.04-.2-1.41-.58-.38-.37-.59-.88-.58-1.41v-4.25c-.01-.53.2-1.04.58-1.41.37-.38.88-.59,1.41-.58h14.76c.53-.01,1.04.2,1.41.58.38.37.59.88.58,1.41v4.25c.01.53-.2,1.04-.58,1.41-.37.38-.88.59-1.41.58H5.49ZM5.49,21.47h14.76c.09,0,.17-.04.23-.11.07-.06.1-.14.11-.23v-4.25c0-.09-.04-.17-.11-.23-.06-.07-.14-.1-.23-.11H5.49c-.09,0-.17.04-.23.11-.07.06-.1.14-.11.23v4.25c0,.09.04.17.11.23.06.07.14.1.23.11Z"
                fill="currentColor"
              />
            </g>
            <path
              id="deployed_code_update_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
              d="M23.78,12.77l-1.64-1.64c-.09-.1-.22-.16-.36-.16-.14,0-.28.06-.37.16-.1.09-.16.23-.16.37,0,.14.06.27.16.37l2.14,2.15c.1.1.22.18.35.23.13.05.26.07.4.07.13,0,.27-.02.4-.07.13-.05.25-.13.35-.23l2.15-2.15c.1-.09.16-.22.16-.36,0-.14-.06-.28-.16-.37-.09-.1-.23-.16-.37-.17-.14,0-.28.06-.37.17l-1.64,1.63v-4.08c0-.14-.06-.27-.16-.36-.09-.1-.23-.16-.36-.16-.14,0-.27.06-.36.16-.1.09-.16.23-.16.36v4.08Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AddOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169172">
            <rect id="Rectangle_22041" width="32" height="32" fill="none" />
            <path
              id="screen_share_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
              d="M4.33,25.54c-.24,0-.47-.09-.63-.26-.17-.17-.26-.4-.26-.63,0-.24.09-.47.26-.63.17-.17.4-.26.63-.26h23.35c.24,0,.47.09.63.26.17.17.26.4.25.63,0,.24-.09.47-.25.63-.17.17-.4.26-.63.26H4.33ZM6.9,22.58c-.57.01-1.12-.21-1.52-.62-.41-.4-.63-.95-.62-1.52v-12.29c-.01-.57.21-1.12.62-1.52.4-.41.95-.63,1.52-.62h18.21c.57-.01,1.12.21,1.52.62.41.4.63.95.62,1.52v12.29c.01.57-.21,1.12-.62,1.52-.4.41-.95.63-1.52.62H6.9ZM6.9,20.81h18.21c.1,0,.19-.04.25-.11.07-.06.11-.16.11-.25v-12.29c0-.1-.04-.19-.11-.25-.06-.07-.15-.11-.25-.11H6.9c-.1,0-.19.04-.25.11-.07.06-.11.15-.11.25v12.29c0,.1.04.19.11.25.06.07.15.11.25.11h0Z"
              fill="currentColor"
            />
            <path
              id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24"
              d="M15.53,14.62v2.2c0,.13.05.25.14.33.09.09.21.14.33.14.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33v-2.2h2.2c.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33,0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14h-2.2v-2.2c0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33v2.2h-2.2c-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33,0,.13.05.25.14.33.09.09.21.14.33.14h2.2Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const EjectOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169180">
            <rect id="Rectangle_22041-2" width="32" height="32" fill="none" />
            <path
              id="screen_share_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-2"
              d="M4.47,25.42c-.23,0-.46-.09-.62-.25-.17-.16-.26-.39-.25-.62,0-.23.09-.46.25-.62.16-.17.39-.26.62-.25h23.06c.23,0,.46.09.62.25.17.16.26.39.25.62,0,.23-.09.46-.25.62-.16.17-.39.26-.62.25H4.47ZM7.01,22.5c-.56.01-1.11-.21-1.5-.61-.4-.39-.63-.94-.61-1.5v-12.14c-.01-.56.21-1.11.61-1.5.39-.4.94-.63,1.5-.61h17.98c.56-.01,1.11.21,1.5.61.4.39.63.94.61,1.5v12.14c.01.56-.21,1.11-.61,1.5-.39.4-.94.63-1.5.61H7.01ZM7.01,20.74h17.98c.09,0,.18-.04.25-.11.07-.06.11-.15.11-.25v-12.14c0-.09-.04-.18-.11-.25-.06-.07-.15-.11-.25-.11H7.01c-.09,0-.18.04-.25.11-.07.06-.11.15-.11.25v12.14c0,.09.04.18.11.25.06.07.15.11.25.11Z"
              fill="currentColor"
            />
            <path
              id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24-2"
              d="M15.51,15s1.01-.02.99,0h2.3c.13,0,.26-.05.34-.15.09-.09.15-.21.15-.35,0-.13-.05-.26-.15-.35-.09-.09-.21-.15-.34-.15h-2.3s-.97-.02-.99,0h-2.3c-.13,0-.26.05-.34.15-.09.09-.15.21-.15.35,0,.13.05.26.15.35.09.09.21.15.34.15h2.3Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FoundationOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169222">
            <rect id="Rectangle_22041-25" width="32" height="32" fill="none" />
            <g id="Group_169207">
              <path
                id="apartment_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
                d="M6.74,29.26c-.46,0-.91-.18-1.24-.51-.33-.32-.52-.77-.51-1.24v-12.02c0-.46.18-.91.51-1.24.32-.33.77-.52,1.24-.51h2.85v-2.85c0-.46.18-.91.51-1.24.32-.33.77-.52,1.24-.51h7.42c.46,0,.91.18,1.24.51.33.32.52.77.51,1.24v7.45h2.85c.46,0,.91.18,1.24.51.33.32.52.77.51,1.24v7.42c0,.46-.18.91-.51,1.24-.32.33-.77.52-1.24.51h-6.87v-4.6h-2.87v4.6h-6.87ZM6.72,27.53h2.88v-2.87h-2.87v2.87ZM6.72,22.93h2.88v-2.87h-2.87v2.87ZM6.72,18.33h2.88v-2.86h-2.87v2.86ZM11.32,22.93h2.87v-2.87h-2.87v2.87ZM11.32,18.33h2.87v-2.86h-2.87v2.86ZM11.32,13.73h2.87v-2.86h-2.87v2.86ZM15.92,22.92h2.87v-2.87h-2.87v2.87ZM15.92,18.32h2.87v-2.85h-2.87v2.85ZM15.92,13.72h2.87v-2.85h-2.87v2.85ZM20.52,27.51h2.87v-2.87h-2.87v2.87ZM20.52,22.91h2.87v-2.87h-2.87v2.87Z"
                fill="currentColor"
              />
              <path
                id="person_add_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
                d="M22.83,6.92h-2.59c-.23,0-.45-.08-.61-.25-.16-.16-.25-.38-.25-.61,0-.23.08-.45.25-.61.16-.16.38-.25.61-.25h2.59v-2.59c0-.23.08-.45.25-.61.16-.16.38-.25.61-.25.23,0,.45.08.61.25.16.16.25.38.25.61v2.59h2.59c.23,0,.45.08.61.25.16.16.25.38.25.61,0,.23-.08.45-.25.61-.16.16-.38.25-.61.25h-2.59v2.59c0,.23-.08.45-.25.61-.16.16-.38.25-.61.25-.23,0-.45-.08-.61-.25-.16-.16-.25-.38-.25-.61v-2.58Z"
                fill="currentColor"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CloseOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169189">
            <g id="Rectangle_22041-11">
              <path
                d="M14.69,17.76h-2.27c-.23,0-.43-.08-.58-.23-.16-.18-.24-.36-.24-.59s.09-.42.24-.57c.16-.16.36-.24.58-.24h.64l-2.9-2.9c-.34.52-.61,1.07-.81,1.65-.21.66-.33,1.35-.33,2.06,0,1.92.69,3.57,2.03,4.92,1.35,1.34,3,2.02,4.91,2.02.74,0,1.43-.11,2.07-.32.59-.2,1.14-.47,1.66-.8l-5-5ZM14.69,17.76h-2.27c-.23,0-.43-.08-.58-.23-.16-.18-.24-.36-.24-.59s.09-.42.24-.57c.16-.16.36-.24.58-.24h.64l-2.9-2.9c-.34.52-.61,1.07-.81,1.65-.21.66-.33,1.35-.33,2.06,0,1.92.69,3.57,2.03,4.92,1.35,1.34,3,2.02,4.91,2.02.74,0,1.43-.11,2.07-.32.59-.2,1.14-.47,1.66-.8l-5-5ZM0,0v32h32V0H0ZM11.81,9.73c.06-.21.19-.38.39-.48.58-.29,1.18-.51,1.82-.65.65-.15,1.28-.22,1.94-.22,1.14,0,2.24.22,3.28.65,1.03.42,1.96,1.05,2.78,1.85.81.81,1.44,1.75,1.87,2.79.42,1.05.64,2.15.64,3.27,0,.66-.08,1.3-.22,1.92-.15.65-.36,1.25-.63,1.82-.1.2-.26.34-.48.4-.08.02-.16.03-.24.03-.14,0-.27-.03-.39-.1-.18-.11-.31-.28-.38-.49-.07-.22-.05-.43.05-.64.21-.46.38-.94.49-1.44.11-.48.17-.98.17-1.5,0-1.92-.68-3.58-2.01-4.91-1.34-1.34-3-2.02-4.93-2.02-.48,0-.98.06-1.49.17-.49.11-.96.27-1.44.49-.2.1-.42.12-.64.06-.21-.07-.37-.19-.49-.38-.12-.2-.15-.4-.09-.62ZM20.32,16.94c0,.24-.08.44-.23.59-.17.15-.36.23-.59.23h-.43l-1.63-1.63h2.09c.22,0,.41.08.57.24.14.15.22.35.22.57ZM24.34,26.24c-.16.16-.35.24-.57.24s-.43-.08-.59-.23l-2.3-2.3c-.69.49-1.44.87-2.23,1.14-.83.28-1.71.42-2.69.42-1.17,0-2.3-.23-3.34-.67-1.02-.44-1.94-1.05-2.72-1.83-.76-.77-1.38-1.69-1.83-2.72-.45-1.04-.67-2.17-.67-3.35,0-.93.14-1.84.43-2.68.26-.79.64-1.54,1.13-2.23l-2.11-2.13c-.17-.16-.25-.36-.25-.57,0-.23.09-.43.25-.58.3-.33.83-.34,1.16,0l16.34,16.33c.16.17.24.35.24.58,0,.21-.08.41-.25.58ZM12.42,17.76c-.23,0-.43-.08-.58-.23-.16-.18-.24-.36-.24-.59s.09-.42.24-.57c.16-.16.36-.24.58-.24h.64l-2.9-2.9c-.34.52-.61,1.07-.81,1.65-.21.66-.33,1.35-.33,2.06,0,1.92.69,3.57,2.03,4.92,1.35,1.34,3,2.02,4.91,2.02.74,0,1.43-.11,2.07-.32.59-.2,1.14-.47,1.66-.8l-5-5h-2.27Z"
                fill="none"
              />
            </g>
            <g>
              <g>
                <path
                  d="M20.32,16.94c0,.24-.08.44-.23.59-.17.15-.36.23-.59.23h-.43l-1.63-1.63h2.09c.22,0,.41.08.57.24.14.15.22.35.22.57Z"
                  fill="currentColor"
                />
                <path
                  d="M24.53,16.94c0,.66-.08,1.3-.22,1.92-.15.65-.36,1.25-.63,1.82-.1.2-.26.34-.48.4-.08.02-.16.03-.24.03-.14,0-.27-.03-.39-.1-.18-.11-.31-.28-.38-.49-.07-.22-.05-.43.05-.64.21-.46.38-.94.49-1.44.11-.48.17-.98.17-1.5,0-1.92-.68-3.58-2.01-4.91-1.34-1.34-3-2.02-4.93-2.02-.48,0-.98.06-1.49.17-.49.11-.96.27-1.44.49-.2.1-.42.12-.64.06-.21-.07-.37-.19-.49-.38-.12-.2-.15-.4-.09-.62.06-.21.19-.38.39-.48.58-.29,1.18-.51,1.82-.65.65-.15,1.28-.22,1.94-.22,1.14,0,2.24.22,3.28.65,1.03.42,1.96,1.05,2.78,1.85.81.81,1.44,1.75,1.87,2.79.42,1.05.64,2.15.64,3.27Z"
                  fill="currentColor"
                />
              </g>
              <path
                d="M24.35,25.08L8.01,8.75c-.33-.34-.86-.33-1.16,0-.16.15-.25.35-.25.58,0,.21.08.41.25.57l2.11,2.13c-.49.69-.87,1.44-1.13,2.23-.29.84-.43,1.75-.43,2.68,0,1.18.22,2.31.67,3.35.45,1.03,1.07,1.95,1.83,2.72.78.78,1.7,1.39,2.72,1.83,1.04.44,2.17.67,3.34.67.98,0,1.86-.14,2.69-.42.79-.27,1.54-.65,2.23-1.14l2.3,2.3c.16.15.36.23.59.23s.41-.08.57-.24c.17-.17.25-.37.25-.58,0-.23-.08-.41-.24-.58ZM15.96,23.88c-1.91,0-3.56-.68-4.91-2.02-1.34-1.35-2.03-3-2.03-4.92,0-.71.12-1.4.33-2.06.2-.58.47-1.13.81-1.65l2.9,2.9h-.64c-.22,0-.42.08-.58.24-.15.15-.24.35-.24.57s.08.41.24.59c.15.15.35.23.58.23h2.27l5,5c-.52.33-1.07.6-1.66.8-.64.21-1.33.32-2.07.32Z"
                fill="currentColor"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const F5Icon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169244">
            <rect id="Rectangle_22041-28" width="32" height="32" fill="none" />
            <g id="Group_169241-2">
              <rect id="Rectangle_22041-29" width="32" height="32" fill="none" />
              <g id="F5Logo">
                <path
                  id="Path_58-2"
                  d="M26.66,18.8c.12-1.38-.33-2.75-1.25-3.79-1.11-1.2-2.98-2.21-6.83-2.45.2-.62.37-1.2.56-1.78,2.3.08,4.35.25,6.09.47.14-.62.21-1.23.36-1.82-.18-.26-.36-.51-.56-.75-.83-.1-1.66-.3-2.54-.42-1.27-.16-2.54-.27-3.82-.33-1.02,3.02-2.02,6.05-3.02,9.08,5.73.5,7.93,2.04,7.8,4.22-.2,1.3-1.28,2.29-2.6,2.37-1.16.19-2.32-.32-2.95-1.31-.46-.69-.91-1.38-1.36-2.07-.13-.21-.3-.07-.45.07-.34.33-.68.66-1.02.98-.17.12-.21.35-.09.52.3.71.61,1.43.92,2.13,1.49.5,3.06.71,4.62.6,1.42-.13,2.78-.65,3.92-1.49,1.36-1,2.18-2.56,2.24-4.24Z"
                  fill="currentColor"
                />
                <path
                  id="Path_59"
                  d="M6.63,22.89c.22.3.45.59.7.86,2.32.39,4.67.62,7.02.68l-.02-.8c-1.58-.09-2.3-.33-2.43-.64-.1-.32-.15-.65-.15-.99-.12-2.56-.16-5.12-.12-7.69.88-.02,1.76-.02,2.69-.04.45-.2.87-.4,1.31-.6v-.91c-1.37,0-2.67.03-3.97.07.04-1.11.08-2.13.15-3.13,0-.57.41-1.06.97-1.17.73.02,1.44.21,2.08.56.36.17.71.35,1.08.54.18.05.39.09.51-.06.22-.26.43-.5.64-.75.12-.17.07-.27.02-.33-.45-.35-.9-.7-1.35-1.04-.35-.18-.75-.26-1.14-.23-.17,0-.34,0-.5,0-.65.04-1.29.15-1.92.32-2.06.56-4.54,2-4.81,4.35-.03.39-.06.77-.09,1.16-.58.05-1.12.09-1.62.14-.04.45-.06.89-.08,1.34.51-.03,1.04-.06,1.63-.08-.09,2.41-.01,4.82.23,7.22.05.3.06.61.03.91-.03.2-.38.31-.87.31Z"
                  fill="currentColor"
                />
                <path
                  id="Path_60"
                  d="M25.6,9.44c-.14.59-.22,1.2-.36,1.82-1.74-.23-3.79-.39-6.09-.47-.19.58-.36,1.16-.56,1.78,3.85.24,5.72,1.25,6.83,2.45.92,1.04,1.37,2.41,1.24,3.79-.06,1.68-.89,3.24-2.25,4.23-1.15.85-2.5,1.37-3.92,1.5-1.56.11-3.13-.1-4.62-.6-.31-.71-.62-1.42-.92-2.14-.12-.17-.07-.4.09-.52.34-.33.68-.65,1.02-.98.15-.15.32-.29.45-.07.47.73.92,1.4,1.36,2.07.63.99,1.8,1.51,2.96,1.31,1.32-.08,2.4-1.07,2.6-2.37.12-2.18-2.08-3.72-7.81-4.22,1.1-3.32,2.17-6.53,3.03-9.08,1.36.06,2.62.17,3.82.33.89.11,1.72.32,2.54.41-4.04-4.99-11.36-5.77-16.35-1.73-4.84,3.91-5.74,10.94-2.05,15.94.49,0,.84-.11.87-.32.03-.3.02-.61-.03-.91-.24-2.4-.31-4.81-.23-7.22-.54.02-1.09.05-1.63.08.02-.46.05-.89.08-1.34.5-.05,1.03-.09,1.62-.14.02-.4.05-.77.09-1.16.26-2.36,2.74-3.8,4.81-4.35.63-.17,1.27-.27,1.92-.32.16,0,.33-.01.5-.01.39-.03.79.05,1.14.23.45.34.88.67,1.35,1.04.08.1.07.24-.02.33l-.64.75c-.13.15-.34.11-.51.07-.37-.19-.72-.36-1.08-.53-.64-.35-1.35-.54-2.07-.56-.56.11-.97.6-.97,1.17-.07,1-.11,2.02-.15,3.13,1.3-.04,2.6-.06,3.97-.07v.92c-.45.2-.87.4-1.32.6-.92.01-1.8.02-2.69.03-.04,2.56,0,5.12.12,7.69,0,.33.05.67.15.98.13.31.85.55,2.43.64l.02.8c-2.35-.06-4.7-.28-7.02-.67,4.3,4.77,11.65,5.16,16.42.87,4.32-3.89,5.11-10.38,1.83-15.19h0Z"
                  fill="none"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const Windows = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169184">
            <rect id="Rectangle_22041-9" width="32" height="32" fill="none" />
            <g id="Group_169185">
              <path id="rect65" d="M5,5h10.62v10.62H5V5Z" fill="currentColor" />
              <path id="rect165" d="M16.38,15.62V5h10.62v10.62h-10.62Z" fill="currentColor" />
              <path id="rect413" d="M5,16.38h10.62v10.62H5v-10.62Z" fill="currentColor" />
              <path id="rect415" d="M16.38,27v-10.62h10.62v10.62h-10.62Z" fill="currentColor" />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const WindowsCorpnet = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169187">
            <rect id="Rectangle_22041-10" width="32" height="32" fill="none" />
            <path
              id="Path_55-2"
              d="M27.81,14.66h-.72v-7.98c0-.11-.09-.19-.19-.19h-5.97c-.11,0-.19.09-.19.19v7.98h-.72c-.11,0-.19.09-.19.19v.53c0,.11.09.19.19.19h2.99v-2.53c0-.11.09-.19.19-.19h1.43c.11,0,.19.09.19.19v2.53h2.99c.11,0,.19-.09.19-.19v-.53c0-.11-.09-.19-.19-.19ZM22.55,11.75c0,.11-.09.19-.19.19h-.53c-.11,0-.19-.09-.19-.19v-.53c0-.11.09-.19.19-.19h.53c.11,0,.19.09.19.19v.53ZM22.55,9.93c0,.11-.09.19-.19.19h-.53c-.11,0-.19-.09-.19-.19v-.53c0-.11.09-.19.19-.19h.53c.11,0,.19.09.19.19v.53ZM22.55,8.12c0,.11-.09.19-.19.19h-.53c-.11,0-.19-.09-.19-.19v-.52c0-.11.09-.19.19-.19h.53c.11,0,.19.09.19.19v.52ZM24.37,11.75c0,.11-.09.19-.19.19h-.53c-.11,0-.19-.09-.19-.19v-.53c0-.11.09-.19.19-.19h.53c.11,0,.19.09.19.19v.53ZM24.37,9.93c0,.11-.09.19-.19.19h-.53c-.11,0-.19-.09-.19-.19v-.53c0-.11.09-.19.19-.19h.53c.11,0,.19.09.19.19v.53ZM24.37,8.12c0,.11-.09.19-.19.19h-.53c-.11,0-.19-.09-.19-.19v-.52c0-.11.09-.19.19-.19h.53c.11,0,.19.09.19.19v.52ZM26.18,11.75c0,.11-.09.19-.19.19h-.53c-.11,0-.19-.09-.19-.19v-.53c0-.11.09-.19.19-.19h.53c.11,0,.19.09.19.19v.53ZM26.18,9.93c0,.11-.09.19-.19.19h-.53c-.11,0-.19-.09-.19-.19v-.53c0-.11.09-.19.19-.19h.53c.11,0,.19.09.19.19v.53ZM26.18,8.12c0,.11-.09.19-.19.19h-.53c-.11,0-.19-.09-.19-.19v-.52c0-.11.09-.19.19-.19h.53c.11,0,.19.09.19.19v.52Z"
              fill="currentColor"
            />
            <g id="Group_169188">
              <path id="rect65-2" d="M5,12.04h6.5v6.5h-6.5v-6.5Z" fill="currentColor" />
              <path id="rect165-2" d="M11.96,18.54v-6.5h6.5v6.5h-6.5Z" fill="currentColor" />
              <path id="rect413-2" d="M5,19.01h6.5v6.5h-6.5v-6.5Z" fill="currentColor" />
              <path id="rect415-2" d="M11.96,25.51v-6.5h6.5v6.5h-6.5Z" fill="currentColor" />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RedHat = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169181">
            <rect id="Rectangle_22041-6" width="32" height="32" fill="none" />
            <g id="Group_169182">
              <path
                id="Path_51"
                d="M24.72,21.34c.51.23.85.71.91,1.26.07.58.33,1.13.73,1.56.06.06.11.13.15.2.45.45.45,1.18,0,1.63-.12.12-.27.21-.43.27-.38.22-.78.41-1.18.6-.59.29-1.12.69-1.55,1.18-.53.65-1.27,1.1-2.1,1.26-.98.23-1.98-.28-2.38-1.21-.06-.18-.23-.29-.42-.28-.55-.03-1.09-.09-1.63-.17-.94-.06-1.88.01-2.8.23-.35.03-.66.21-.87.49-.47.56-1.22.82-1.94.68-.6-.07-1.19-.23-1.74-.48-.77-.34-1.6-.56-2.44-.63-.32-.03-.64-.08-.95-.14-.64-.04-1.13-.59-1.09-1.23,0-.11.03-.23.07-.33.18-.66.22-1.36.12-2.03-.22-.72.19-1.48.9-1.69.09-.03.17-.04.26-.05.79-.23,1.09-.91,1.58-1.43.04-.04.03-.14.03-.21-.08-1.31.21-2.63.83-3.78.64-1.29,1.44-2.5,2.36-3.61.95-1.18,1.41-2.69,1.28-4.2-.14-1.17-.11-2.36.11-3.52.27-1.46,1.5-2.55,2.98-2.64.67-.08,1.35-.07,2.02.04,1.37.3,2.4,1.44,2.57,2.83.24,1.11.33,2.24.25,3.37-.08,1.05.23,2.1.86,2.95.47.63.99,1.22,1.43,1.87.58.8,1.1,1.64,1.56,2.51.8,1.44.98,3.14.51,4.71ZM12.69,24.64c.32.65.87,1.15,1.54,1.41,1.51.51,3.17.27,4.48-.65.17-.11.29-.27.35-.46.15-.83.25-1.67.37-2.5-.01-.79.4-1.54,1.08-1.95.07-.04.1-.16.13-.25.15-.48.39-.71.87-.64.97.15,1.93.37,2.26,1.55.18-.26.13-.62-.11-.82-.23-.25-.51-.46-.81-.62-.35-.1-.57-.47-.48-.83.05-1.53-.57-3.01-1.71-4.04-.03-.02-.06-.04-.1-.05,1.1,1.3,1.83,2.68,1.31,4.44-.58-.04-.58-.04-.64-.29-.24-1.03-.62-2.03-1.11-2.97-.39-.8-.74-1.62-1.06-2.45-.29-.72-.51-1.47-.76-2.19-.27.18-.52.37-.8.52-.35.2-.71.36-1.06.55-.73.51-1.73.42-2.35-.23-.21-.18-.44-.34-.71-.54-.16,1.31-.54,2.58-1.12,3.76-.24.53-.48,1.06-.72,1.59-.46,1.02-.74,2.11-.81,3.23-.38-.54-.56-1.19-.5-1.85.08-.81.33-1.59.73-2.3.15-.27.27-.56.41-.84l-.08-.04c-.65.93-1.11,1.97-1.37,3.07-.35,1.28.27,2.63,1.47,3.19.83.43,1.51,1.1,1.96,1.92.35.69.12,1.12-.65,1.27h0ZM11,28.23c.74-.06,1.27-.16,1.57-.7.28-.5.23-1.11-.12-1.56-.21-.31-.44-.6-.65-.92-.39-.59-.77-1.18-1.14-1.78-.27-.43-.49-.9-.8-1.3-.26-.5-.88-.69-1.38-.42-.2.11-.36.28-.45.49-.3.49-.78.85-1.34,1-.6.18-.77.54-.72,1.17.07.48.07.97,0,1.44-.27,1.05-.09,1.38.99,1.49.98.06,1.93.3,2.82.72.4.17.81.29,1.24.36ZM20.91,21.41c-.16.16-.29.34-.4.53-.13.31-.22.63-.25.97-.05,1.01-.22,2.02-.53,2.99-.07.2-.12.4-.15.61-.18.6,0,1.25.48,1.66.48.41,1.16.47,1.72.17.44-.2.84-.47,1.17-.82.55-.59,1.22-1.07,1.95-1.4.29-.13.56-.28.82-.46.26-.13.36-.45.23-.71,0-.02-.02-.04-.03-.05-.1-.22-.24-.42-.39-.6-.26-.3-.44-.67-.51-1.06-.05-.25-.12-.49-.21-.72-.24-.47-.81-.65-1.27-.42-.06.03-.11.06-.16.1-.31.23-.66.4-1.02.52-.43.16-.91-.03-1.1-.44-.14-.28-.25-.57-.35-.87ZM15.39,12.21c.2-.03.4-.09.59-.16.54-.25,1.06-.53,1.59-.81.1-.06.19-.12.27-.2.35-.21.47-.66.27-1.01-.1-.18-.28-.31-.48-.35-.49-.22-.98-.44-1.47-.66-.59-.33-1.31-.28-1.84.13-.38.29-.74.59-1.11.88-.21.15-.28.43-.16.66.53.68,1.24,1.19,2.05,1.49.07,0,.14.01.3.02ZM17.29,9.25c.11.04.22.09.32.14.3.18.5.06.55-.24.11-.43.15-.88.13-1.33-.05-.64-.55-1.16-1.19-1.24-.73.02-1.33.61-1.36,1.34-.09.63-.08.65.54.8.04-.27.11-.53.21-.78.09-.17.26-.29.45-.31.19.04.35.17.44.34.18.41.15.88-.1,1.26h0ZM21.41,20.98c-.03.37.15.73.48.92.35.12.73.03.98-.24.07-.05.14-.12.2-.19.19-.16.2-.44.04-.63-.03-.03-.06-.06-.1-.08-.25-.17-.52-.31-.81-.41-.53-.18-.8.03-.8.63ZM14.3,8.84c.18-.14.45-.25.47-.39.08-.52-.1-1.04-.48-1.4-.26-.28-.7-.29-.97-.03-.04.04-.08.09-.11.13-.49.74-.42,1.71.17,2.38l.31-.23c-.26-.28-.36-.68-.26-1.05.04-.16.21-.28.32-.42.13.11.3.19.37.33.08.22.15.44.18.67h0Z"
                fill="currentColor"
                fillRule="evenodd"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RedHatCorpnet = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169186">
            <rect id="Rectangle_22041-7" width="32" height="32" fill="none" />
            <g id="Group_169182-2">
              <path
                id="Path_51-2"
                d="M20,22.58c.38.18.65.54.69.96.05.44.25.86.55,1.18.04.05.08.1.11.15.34.34.34.9,0,1.24-.09.09-.2.16-.33.21-.29.17-.6.31-.89.46-.45.22-.85.52-1.18.9-.41.5-.97.84-1.6.96-.75.17-1.5-.22-1.8-.93-.05-.14-.18-.22-.32-.21-.41-.02-.83-.07-1.24-.13-.71-.05-1.43.01-2.13.17-.26.02-.5.16-.66.37-.36.43-.92.63-1.47.51-.46-.05-.9-.18-1.32-.37-.59-.26-1.21-.42-1.86-.48-.24-.02-.49-.06-.72-.11-.49-.03-.86-.45-.83-.94,0-.09.02-.17.05-.25.13-.5.17-1.03.09-1.55-.17-.55.14-1.12.69-1.29.07-.02.13-.03.2-.04.6-.17.83-.69,1.2-1.09.03-.03.02-.1.02-.16-.06-1,.16-2,.63-2.88.49-.98,1.1-1.9,1.8-2.75.73-.9,1.07-2.04.98-3.19-.11-.89-.08-1.79.08-2.68.21-1.11,1.14-1.94,2.26-2.01.51-.06,1.03-.05,1.53.03,1.04.23,1.83,1.09,1.96,2.15.18.84.25,1.71.19,2.57-.06.8.17,1.6.65,2.24.36.48.75.93,1.09,1.42.44.61.84,1.25,1.19,1.91.6,1.09.74,2.39.38,3.58h0ZM10.85,25.09c.24.49.66.88,1.17,1.07,1.15.39,2.41.2,3.41-.5.13-.08.22-.21.27-.35.11-.63.19-1.27.28-1.9,0-.6.3-1.17.82-1.48.05-.03.08-.12.1-.19.11-.37.29-.54.66-.49.74.11,1.47.28,1.72,1.18.13-.2.1-.47-.08-.63-.18-.19-.39-.35-.62-.47-.27-.08-.43-.35-.37-.63.04-1.17-.43-2.29-1.3-3.07-.02-.02-.05-.03-.07-.03.92.86,1.3,2.15,1,3.38-.44-.03-.44-.03-.48-.22-.18-.79-.47-1.55-.85-2.26-.3-.61-.56-1.23-.81-1.86-.22-.55-.39-1.12-.58-1.67-.21.14-.4.28-.61.4-.26.15-.54.27-.81.42-.56.39-1.32.32-1.79-.17-.16-.14-.34-.26-.54-.41-.12.99-.41,1.96-.85,2.86-.18.41-.36.81-.55,1.21-.35.77-.56,1.61-.62,2.45-.29-.41-.42-.91-.38-1.41.06-.62.25-1.22.56-1.75.11-.21.21-.42.31-.64l-.06-.03c-.49.71-.85,1.5-1.04,2.33-.27.97.21,2,1.12,2.42.63.33,1.15.84,1.49,1.46.26.52.09.85-.49.97h0ZM9.57,27.81c.56-.04.97-.12,1.2-.53.21-.38.17-.85-.09-1.18-.16-.24-.34-.46-.5-.7-.3-.45-.59-.9-.87-1.35-.21-.33-.37-.68-.61-.99-.2-.38-.67-.53-1.05-.33-.16.08-.28.21-.35.37-.23.37-.59.65-1.02.76-.46.14-.58.41-.55.89.06.36.05.74,0,1.1-.2.8-.07,1.05.75,1.14.74.04,1.47.23,2.15.54.3.13.62.21.94.27ZM17.1,22.63c-.12.12-.22.26-.31.41-.1.23-.16.48-.19.73-.03.77-.17,1.54-.4,2.28-.05.15-.09.31-.11.47-.14.46,0,.95.37,1.26.37.31.89.36,1.31.13.33-.15.64-.36.89-.62.42-.45.93-.81,1.49-1.06.22-.1.43-.22.63-.35.2-.1.28-.34.18-.54,0-.02-.02-.03-.03-.05-.08-.17-.18-.32-.3-.46-.2-.23-.33-.51-.39-.81-.04-.19-.09-.37-.16-.55-.18-.35-.61-.5-.97-.32-.04.02-.08.05-.12.08-.24.17-.5.31-.78.4-.32.12-.69-.03-.83-.34-.1-.22-.19-.44-.27-.67ZM12.9,15.63c.15-.02.3-.07.45-.12.41-.19.81-.41,1.21-.62.07-.04.14-.09.21-.15.27-.15.36-.5.21-.77-.08-.14-.22-.24-.37-.27-.38-.16-.75-.33-1.12-.51-.44-.25-1-.21-1.4.1-.28.22-.56.45-.84.67-.16.12-.21.33-.12.51.41.51.95.91,1.56,1.14.05,0,.1,0,.23.01h0ZM14.35,13.38c.08.03.17.07.24.11.23.13.38.04.42-.18.08-.33.12-.67.1-1.01-.04-.49-.42-.88-.91-.94-.56.02-1.01.46-1.03,1.02-.07.48-.06.5.41.61.03-.2.09-.4.16-.59.07-.13.2-.22.35-.24.14.03.27.13.33.26.14.31.11.67-.07.95h0ZM17.48,22.3c-.02.28.12.56.36.7.26.09.56.02.75-.18.06-.04.11-.09.15-.14.14-.12.15-.34.03-.48-.02-.02-.05-.04-.07-.06-.19-.13-.4-.24-.61-.31-.41-.14-.61.03-.61.48h0ZM12.07,13.07c.13-.11.34-.19.35-.3.06-.39-.08-.79-.37-1.06-.2-.21-.53-.22-.74-.02-.03.03-.06.07-.08.1-.37.56-.32,1.3.13,1.81l.24-.17c-.2-.21-.28-.52-.2-.8.03-.12.16-.21.25-.32.1.08.23.15.28.25.06.17.11.34.14.51h0Z"
                fill="currentColor"
                fillRule="evenodd"
              />
            </g>
            <path
              id="Path_55"
              d="M27.95,12.65h-.79V3.81c0-.12-.09-.21-.21-.21h-6.62c-.12,0-.21.09-.21.21v8.84h-.79c-.12,0-.21.09-.21.21v.58c0,.12.09.21.21.21h3.31v-2.8c0-.12.09-.21.21-.21h1.59c.12,0,.21.09.21.21v2.81h3.31c.12,0,.21-.09.21-.21v-.58c0-.12-.09-.21-.21-.21,0,0,0,0,0,0ZM22.13,9.42c0,.12-.09.21-.21.21h-.58c-.12,0-.21-.09-.21-.21v-.58c0-.12.09-.21.21-.21h.58c.12,0,.21.09.21.21v.58ZM22.13,7.41c0,.12-.09.21-.21.21h-.58c-.12,0-.21-.09-.21-.21v-.58c0-.12.09-.21.21-.21h.58c.12,0,.21.09.21.21v.58ZM22.13,5.4c0,.12-.09.21-.21.21h-.58c-.12,0-.21-.09-.21-.21v-.58c0-.12.09-.21.21-.21h.58c.12,0,.21.09.21.21v.58ZM24.14,9.42c0,.12-.09.21-.21.21h-.58c-.12,0-.21-.09-.21-.21v-.58c0-.12.09-.21.21-.21h.58c.12,0,.21.09.21.21v.58ZM24.14,7.41c0,.12-.09.21-.21.21h-.58c-.12,0-.21-.09-.21-.21v-.58c0-.12.09-.21.21-.21h.58c.12,0,.21.09.21.21v.58ZM24.14,5.4c0,.12-.09.21-.21.21h-.58c-.12,0-.21-.09-.21-.21v-.58c0-.12.09-.21.21-.21h.58c.12,0,.21.09.21.21v.58ZM26.15,9.42c0,.12-.09.21-.21.21h-.58c-.12,0-.21-.09-.21-.21v-.58c0-.12.09-.21.21-.21h.58c.12,0,.21.09.21.21v.58ZM26.15,7.41c0,.12-.09.21-.21.21h-.58c-.12,0-.21-.09-.21-.21v-.58c0-.12.09-.21.21-.21h.58c.12,0,.21.09.21.21v.58ZM26.15,5.4c0,.12-.09.21-.21.21h-.58c-.12,0-.21-.09-.21-.21v-.58c0-.12.09-.21.21-.21h.58c.12,0,.21.09.21.21v.58Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const Ubuntu = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169183">
            <rect id="Rectangle_22041-8" width="32" height="32" fill="none" />
            <g id="CoF_white">
              <circle id="Ellipse_6" cx="8.12" cy="15.4" r="3.12" fill="currentColor" />
              <circle id="Ellipse_7" cx="21.11" cy="8.56" r="3.12" fill="currentColor" />
              <path
                id="Path_52"
                d="M7.23,19.74c1.57,3.97,5.44,6.54,9.7,6.47-.61-.77-.94-1.71-.96-2.69-.28-.04-.55-.08-.8-.14-2.24-.47-4.14-1.94-5.17-3.98-.86.41-1.83.53-2.77.34h0Z"
                fill="currentColor"
              />
              <circle id="Ellipse_8" cx="20.41" cy="23.44" r="3.12" fill="currentColor" />
              <path
                id="Path_53"
                d="M23.19,19.97c.76.61,1.3,1.45,1.54,2.4,2.82-3.5,3.03-8.42.52-12.15-.36.89-.99,1.63-1.81,2.13,1.29,2.4,1.2,5.31-.25,7.62Z"
                fill="currentColor"
              />
              <path
                id="Path_54"
                d="M8.13,10.96c.31,0,.**********.51.11,1,.31,1.45.59,1.39-2.03,3.68-3.25,6.15-3.29,0-.25.04-.49.08-.73h0c.15-.69.46-1.33.9-1.88-4.01-.32-7.85,1.71-9.83,5.22.11-.01.22-.02.33-.02Z"
                fill="currentColor"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const NFS = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169238">
            <g id="Group_169236">
              <rect id="Rectangle_22041-26" width="32" height="32" fill="none" />
            </g>
            <g id="Group_169237">
              <path
                id="lan_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
                d="M7.11,26.35v-3.98c0-.39.16-.76.45-1.01.3-.28.69-.43,1.09-.42h1.5v-2.06c0-.46.19-.9.54-1.2.35-.33.82-.51,1.3-.5h3.24v-1.43h1.52v1.43h3.24c.48-.01.95.17,1.3.5.34.3.54.74.54,1.2v2.06h1.5c.4,0,.8.14,1.09.42.29.26.45.63.45,1.01v3.26c0,.39-.16.76-.45,1.01-.3.28-.69.43-1.09.42h-4.52c-.4,0-.8-.14-1.09-.42-.29-.26-.45-.63-.45-1.01v-3.26c0-.39.16-.76.45-1.01.3-.28.69-.43,1.09-.42h1.5v-2.06c0-.08-.03-.15-.09-.21-.06-.06-.14-.08-.22-.08h-7.99c-.08,0-.16.03-.22.08-.06.05-.09.13-.09.21v2.06h1.5c.4,0,.8.14,1.09.42.29.26.45.63.45,1.01v3.26c0,.39-.16.76-.45,1.01-.3.28-.69.43-1.09.42h-4.53c-.4,0-1.24-.43-1.54-.71h0ZM8.63,25.64h4.56v-3.29h-4.56v3.29ZM18.77,25.64h4.56v-3.29h-4.56v3.29Z"
                fill="currentColor"
              />
              <path
                id="folder_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
                d="M9.32,15.93c-.41.01-.81-.14-1.11-.42-.29-.26-.46-.63-.45-1.02V5.4c0-.39.16-.77.45-1.02.3-.28.7-.43,1.11-.42h4.11c.21,0,.41.04.6.11.18.07.35.18.5.31l1.28,1.18h6.83c.41-.01.81.14,1.11.42.29.26.46.63.45,1.02v7.49c0,.39-.16.77-.45,1.02-.3.28-.7.43-1.11.42h-13.31ZM9.32,14.73h13.32c.07,0,.14-.02.19-.07.05-.05.08-.11.07-.18v-7.49c0-.07-.03-.13-.07-.18-.05-.05-.12-.07-.19-.07h-7.36l-1.66-1.53s-.05-.04-.09-.05c-.03-.01-.07-.02-.1-.02h-4.11c-.07,0-.14.02-.19.07-.05.05-.08.11-.07.18v9.09c0,.07.03.13.07.18.05.05.12.08.2.07h0Z"
                fill="currentColor"
                stroke="currentColor"
                strokeWidth=".2"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const PureS3 = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169240">
            <rect id="Rectangle_22041-5" width="32" height="32" fill="none" />
            <path
              d="M11.33,10.06c-.35,0-.64.12-.89.36-.24.24-.37.54-.37.88s.12.64.36.89c.24.24.54.37.88.37s.64-.12.89-.36c.24-.24.37-.54.37-.88s-.12-.64-.36-.89c-.24-.24-.54-.37-.88-.37ZM11.33,19.44c-.35,0-.64.12-.89.36-.24.24-.37.54-.37.88s.12.64.36.89c.24.24.54.37.88.37s.64-.12.89-.36c.24-.24.37-.54.37-.88s-.12-.64-.36-.89c-.24-.24-.54-.37-.88-.37ZM8.22,7.31h15.23c.25,0,.46.08.63.25.17.17.25.38.25.63v6.18c0,.27-.08.5-.25.67-.17.18-.38.27-.63.27h-15.23c-.25,0-.46-.09-.63-.27-.17-.18-.25-.4-.25-.67v-6.18c0-.25.08-.46.25-.63.17-.17.38-.25.63-.25ZM8.83,8.81v5h14v-5h-14ZM8.22,16.69h15.21c.26,0,.48.09.65.27.17.18.25.4.25.66v6.12c0,.28-.08.51-.25.69-.17.18-.39.27-.65.27h-15.19c-.26,0-.48-.09-.65-.27-.17-.18-.25-.41-.25-.69v-6.12c0-.26.08-.48.24-.66.16-.18.38-.27.64-.27ZM8.83,18.19v5h14v-5h-14ZM8.83,8.81v5-5ZM8.83,18.19v5-5Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AddToQueue = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169190">
            <rect id="Rectangle_22041-12" width="32" height="32" fill="none" />
            <path
              id="bookmark_24dp_5F6368_FILL1_wght400_GRAD0_opsz24"
              d="M14,20.51l-2.26.97c-.33.15-.72.12-1.02-.09-.31-.19-.5-.53-.49-.9v-6.99c0-.29.11-.56.32-.76.2-.21.47-.32.76-.32h5.38c.29,0,.56.11.76.32.21.2.32.47.32.76v6.99c.01.37-.17.71-.49.9-.3.21-.69.24-1.02.09l-2.26-.97Z"
              fill="currentColor"
            />
            <path
              id="shield_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
              d="M14,29.85c-.14,0-.28-.01-.42-.03-.14-.02-.27-.05-.4-.1-2.86-1-5.3-2.93-6.92-5.48-1.68-2.52-2.58-5.47-2.57-8.5v-6.19c0-.51.15-1,.45-1.41.29-.41.7-.73,1.16-.91l7.82-2.92c.28-.1.57-.16.87-.16.3,0,.59.06.87.16l7.82,2.92c.47.18.87.5,1.16.91.3.41.46.9.45,1.41v6.19c0,3.03-.89,5.98-2.57,8.5-1.62,2.55-4.06,4.49-6.92,5.48-.13.05-.26.08-.4.1-.14.02-.28.03-.42.03ZM14,27.83c2.43-.79,4.52-2.39,5.91-4.54,1.54-2.22,2.36-4.86,2.34-7.56v-6.2c0-.08-.02-.17-.07-.24-.05-.07-.12-.13-.2-.16l-7.82-2.92s-.1-.03-.15-.03c-.05,0-.1,0-.15.03l-7.82,2.92c-.08.03-.15.09-.2.16-.05.07-.07.15-.07.24v6.2c-.02,2.7.79,5.34,2.34,7.56,1.39,2.14,3.47,3.74,5.9,4.54Z"
              fill="currentColor"
            />
            <path
              id="Line_1-2"
              d="M13.38,18.87v-3.71c0-.34.28-.62.62-.62s.62.28.62.62v3.71c0,.34-.28.62-.62.62s-.62-.28-.62-.62h0Z"
              fill="currentColor"
            />
            <path
              id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24-3"
              d="M25.29,5.62v2.2c0,.13.05.25.14.33.09.09.21.14.33.14.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33v-2.2h2.2c.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33,0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14h-2.2v-2.2c0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33v2.2h-2.2c-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33,0,.13.05.25.14.33.09.09.21.14.33.14h2.2Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AddToQueueV2 = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169251">
            <rect id="Rectangle_22041-13" width="32" height="32" fill="none" />
            <path
              id="bookmark_24dp_5F6368_FILL1_wght400_GRAD0_opsz24-2"
              d="M14,20.51l-2.26.97c-.33.15-.72.12-1.02-.09-.31-.19-.5-.53-.49-.9v-6.99c0-.29.11-.56.32-.76.2-.21.47-.32.76-.32h5.38c.29,0,.56.11.76.32.21.2.32.47.32.76v6.99c.01.37-.17.71-.49.9-.3.21-.69.24-1.02.09l-2.26-.97Z"
              fill="currentColor"
            />
            <path
              id="shield_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-2"
              d="M14,29.85c-.14,0-.28-.01-.42-.03-.14-.02-.27-.05-.4-.1-2.86-1-5.3-2.93-6.92-5.48-1.68-2.52-2.58-5.47-2.57-8.5v-6.19c0-.51.15-1,.45-1.41.29-.41.7-.73,1.16-.91l7.82-2.92c.28-.1.57-.16.87-.16.3,0,.59.06.87.16l7.82,2.92c.47.18.87.5,1.16.91.3.41.46.9.45,1.41v6.19c0,3.03-.89,5.98-2.57,8.5-1.62,2.55-4.06,4.49-6.92,5.48-.13.05-.26.08-.4.1-.14.02-.28.03-.42.03ZM14,27.83c2.43-.79,4.52-2.39,5.91-4.54,1.54-2.22,2.36-4.86,2.34-7.56v-6.2c0-.08-.02-.17-.07-.24-.05-.07-.12-.13-.2-.16l-7.82-2.92s-.1-.03-.15-.03c-.05,0-.1,0-.15.03l-7.82,2.92c-.08.03-.15.09-.2.16-.05.07-.07.15-.07.24v6.2c-.02,2.7.79,5.34,2.34,7.56,1.39,2.14,3.47,3.74,5.9,4.54Z"
              fill="currentColor"
            />
            <path
              id="Line_1-3"
              d="M15.38,18.86v-3.71c0-.34.28-.62.62-.62s.62.28.62.62v3.71c0,.34-.28.62-.62.62s-.62-.28-.62-.62h0Z"
              fill="currentColor"
            />
            <path
              id="Line_1-4"
              d="M11.38,18.85v-3.71c0-.34.28-.62.62-.62s.62.28.62.62v3.71c0,.34-.28.62-.62.62s-.62-.28-.62-.62h0Z"
              fill="currentColor"
            />
            <path
              id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24-4"
              d="M25.29,5.62v2.2c0,.13.05.25.14.33.09.09.21.14.33.14.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33v-2.2h2.2c.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33,0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14h-2.2v-2.2c0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33v2.2h-2.2c-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33,0,.13.05.25.14.33.09.09.21.14.33.14h2.2Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AddToQueueMigrate = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169205">
            <rect id="Rectangle_22041-20" width="32" height="32" fill="none" />
            <path
              id="shield_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-6"
              d="M21.92,17.34c-.07,0-.14,0-.21-.02-.07-.01-.13-.03-.2-.05-1.41-.49-2.61-1.44-3.41-2.7-.83-1.24-1.27-2.7-1.26-4.19v-3.05c0-.25.07-.5.22-.7.14-.2.34-.36.57-.45l3.86-1.44c.14-.05.28-.08.43-.08.15,0,.29.03.43.08l3.86,1.44c.23.09.43.25.57.45.15.2.23.45.22.7v3.05c0,1.49-.44,2.95-1.27,4.19-.8,1.26-2,2.21-3.41,2.7-.07.02-.13.04-.2.05-.07.01-.14.02-.21.02ZM21.92,16.34c1.2-.39,2.23-1.18,2.92-2.23.76-1.09,1.16-2.4,1.15-3.73v-3.06s-.01-.08-.04-.12c-.02-.04-.06-.06-.1-.08l-3.86-1.44s-.05-.01-.07-.01c-.02,0-.05,0-.07.01l-3.86,1.44s-.08.04-.1.08c-.02.03-.04.08-.04.12v3.06c-.01,1.33.39,2.63,1.15,3.73.69,1.06,1.71,1.84,2.91,2.24h0Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
            <path
              id="shield_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-7"
              d="M10.08,28.34c-.07,0-.14,0-.21-.02-.07-.01-.13-.03-.2-.05-1.41-.49-2.61-1.44-3.41-2.7-.83-1.24-1.27-2.7-1.26-4.19v-3.05c0-.25.07-.5.22-.7.14-.2.34-.36.57-.45l3.86-1.44c.14-.05.28-.08.43-.08.15,0,.29.03.43.08l3.86,1.44c.23.09.43.25.57.45.15.2.23.45.22.7v3.05c0,1.49-.44,2.95-1.27,4.19-.8,1.26-2,2.21-3.41,2.7-.07.02-.13.04-.2.05-.07.01-.14.02-.21.02ZM10.08,27.34c1.2-.39,2.23-1.18,2.92-2.23.76-1.09,1.16-2.4,1.15-3.73v-3.06s-.01-.08-.04-.12c-.02-.04-.06-.06-.1-.08l-3.86-1.44s-.05-.01-.07-.01c-.02,0-.05,0-.07.01l-3.86,1.44s-.08.04-.1.08c-.02.03-.04.08-.04.12v3.06c-.01,1.33.39,2.63,1.15,3.73.69,1.06,1.71,1.84,2.91,2.24h0Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
            <path
              id="bookmark_24dp_5F6368_FILL1_wght400_GRAD0_opsz24-4"
              d="M21.92,12.69l-1.09.47c-.16.07-.35.06-.49-.04-.15-.09-.24-.26-.23-.43v-3.37c0-.14.05-.27.15-.37.1-.1.23-.15.37-.15h2.6c.14,0,.27.05.37.15.1.1.15.23.15.37v3.37c0,.18-.08.34-.23.43-.15.1-.33.12-.49.04l-1.09-.47Z"
              fill="currentColor"
            />
            <path
              id="bookmark_24dp_5F6368_FILL1_wght400_GRAD0_opsz24-5"
              d="M10.08,23.69l-1.09.47c-.16.07-.35.06-.49-.04-.15-.09-.24-.26-.23-.43v-3.37c0-.14.05-.27.15-.37.1-.1.23-.15.37-.15h2.6c.14,0,.27.05.37.15.1.1.15.23.15.37v3.37c0,.18-.08.34-.23.43-.15.1-.33.12-.49.04l-1.09-.47Z"
              fill="currentColor"
            />
            <path
              id="cycle_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
              d="M23.31,23.44c.8-.53,1.48-1.22,1.99-2.03.51-.8.85-1.7,1-2.64.04-.19.14-.35.3-.46.14-.11.33-.15.51-.1.19.05.34.18.42.35.09.18.11.38.07.57-.17,1.02-.54,2-1.09,2.88-.55.88-1.26,1.66-2.1,2.27-.75.56-1.59.99-2.48,1.28-.89.29-1.83.42-2.77.4l.72.72c.13.12.19.3.18.47,0,.18-.07.35-.2.47-.12.12-.29.19-.46.19-.18,0-.34-.07-.47-.2l-2.11-2.12c-.15-.15-.24-.35-.23-.57,0-.21.08-.42.23-.57l2.1-2.09c.12-.12.29-.19.46-.19.18,0,.34.07.47.2.13.12.2.3.19.47,0,.18-.07.35-.19.47l-1.4,1.38c.84.11,1.7.06,2.53-.13.83-.19,1.62-.54,2.32-1.02Z"
              fill="currentColor"
            />
            <path
              id="cycle_24dp_5F6368_FILL0_wght300_GRAD0_opsz24_-_Outline"
              d="M19.18,27.97c-.15-.04-.28-.12-.38-.22l-2.11-2.12c-.19-.19-.3-.44-.29-.71,0-.27.1-.52.29-.71l2.1-2.09c.16-.17.38-.26.61-.25.23,0,.45.09.61.25.16.16.25.38.25.61,0,.23-.08.45-.25.62l-1.09,1.08c.68.04,1.37-.01,2.04-.17.81-.19,1.57-.52,2.25-.99.77-.51,1.43-1.18,1.93-1.97.5-.78.83-1.65.97-2.57h0c.05-.24.18-.45.37-.59.19-.15.45-.19.68-.13.24.06.45.23.55.46.11.21.14.46.09.69-.18,1.04-.56,2.05-1.12,2.95-.56.9-1.29,1.69-2.15,2.32-.77.58-1.63,1.02-2.54,1.31-.76.24-1.55.38-2.35.4l.38.38c.17.16.26.39.25.62,0,.23-.09.45-.25.61-.16.17-.38.26-.61.25-.08,0-.15-.01-.23-.03ZM19.52,22.28s-.08-.02-.12-.02c-.12,0-.24.04-.32.13l-2.1,2.09c-.11.11-.18.27-.17.43,0,.16.06.31.17.43l2.11,2.12c.09.09.2.14.33.14.12,0,.24-.04.32-.13.09-.09.14-.21.14-.33,0-.12-.04-.24-.13-.33l-1.07-1.08h.5c.92.04,1.83-.09,2.7-.37.87-.28,1.69-.7,2.42-1.25.82-.6,1.51-1.36,2.04-2.22.53-.86.89-1.81,1.06-2.81h0c.03-.16.01-.31-.06-.45-.06-.12-.17-.21-.3-.25-.12-.03-.24,0-.34.07-.11.08-.19.2-.22.34-.15.97-.5,1.89-1.03,2.72-.53.83-1.23,1.54-2.05,2.09-.72.49-1.53.85-2.39,1.04-.85.2-1.73.24-2.6.13l-.41-.05,1.69-1.67c.09-.09.14-.21.13-.33,0-.13-.04-.25-.13-.33-.05-.05-.12-.09-.2-.11h0Z"
              fill="currentColor"
            />
            <path
              id="cycle_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-2"
              d="M8.72,8.03c-.69.66-1.24,1.46-1.61,2.34-.37.88-.54,1.82-.52,2.78,0,.19-.08.37-.21.51-.12.14-.3.21-.49.19-.19-.01-.37-.11-.48-.27-.12-.16-.18-.35-.17-.55,0-1.03.19-2.06.57-3.02.39-.97.95-1.85,1.67-2.6.64-.68,1.39-1.26,2.22-1.69.83-.44,1.73-.73,2.66-.87l-.83-.58c-.15-.1-.24-.26-.26-.43-.03-.17,0-.35.11-.5.1-.14.25-.24.42-.26.17-.03.35,0,.49.11l2.44,1.72c.18.12.3.31.33.52.04.21,0,.43-.13.61l-1.71,2.42c-.1.14-.25.24-.42.26-.17.03-.35,0-.49-.11-.15-.1-.24-.26-.27-.43-.03-.17,0-.35.11-.5l1.14-1.6c-.85.04-1.68.23-2.47.57-.78.33-1.5.81-2.11,1.4Z"
              fill="currentColor"
            />
            <path
              id="cycle_24dp_5F6368_FILL0_wght300_GRAD0_opsz24_-_Outline-2"
              d="M12,2.85c.15.01.29.07.42.15l2.45,1.72c.22.15.37.38.41.65.05.26,0,.53-.16.75l-1.71,2.43c-.13.19-.33.32-.56.35-.22.04-.45-.01-.64-.14-.19-.13-.32-.33-.35-.56-.05-.23,0-.46.14-.65l.89-1.25c-.68.08-1.35.25-1.98.52-.76.32-1.45.79-2.05,1.36-.67.64-1.2,1.42-1.56,2.27-.35.85-.53,1.77-.51,2.7h0c0,.25-.1.48-.27.65-.17.18-.4.27-.65.24-.25-.02-.48-.15-.62-.36-.14-.19-.22-.43-.21-.67,0-1.06.19-2.11.59-3.09.4-.99.98-1.89,1.71-2.66.66-.7,1.42-1.28,2.27-1.73.71-.37,1.46-.64,2.24-.8l-.44-.31c-.19-.13-.32-.34-.35-.57-.04-.23.01-.46.14-.64.13-.19.33-.32.56-.35.08-.01.15-.01.23,0ZM12.66,8.51s.08,0,.13,0c.12-.02.23-.08.29-.18l1.71-2.42c.09-.13.13-.3.09-.46-.02-.16-.11-.3-.25-.39l-2.45-1.72c-.1-.07-.22-.1-.35-.08-.12.02-.23.08-.29.18-.07.1-.1.23-.08.35.01.12.08.23.18.3l1.24.87-.5.07c-.91.13-1.78.42-2.6.85-.81.42-1.54.98-2.17,1.65-.7.74-1.25,1.6-1.63,2.54-.38.94-.57,1.94-.56,2.95h0c0,.16.04.31.13.43.08.11.2.18.34.19.12.01.24-.03.32-.13.1-.1.15-.23.16-.37-.02-.98.16-1.95.54-2.85.38-.91.94-1.73,1.65-2.41.63-.61,1.36-1.1,2.17-1.44.8-.34,1.66-.54,2.53-.58l.41-.02-1.37,1.94c-.07.1-.1.23-.07.35.02.12.09.23.19.31.06.04.14.07.21.08h0Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const MoveToInboxOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169251">
            <rect id="Rectangle_22041-13" width="32" height="32" fill="none" />
            <path
              id="shield_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-2"
              d="M14,29.85c-.14,0-.28-.01-.42-.03-.14-.02-.27-.05-.4-.1-2.86-1-5.3-2.93-6.92-5.48-1.68-2.52-2.58-5.47-2.57-8.5v-6.19c0-.51.15-1,.45-1.41.29-.41.7-.73,1.16-.91l7.82-2.92c.28-.1.57-.16.87-.16.3,0,.59.06.87.16l7.82,2.92c.47.18.87.5,1.16.91.3.41.46.9.45,1.41v6.19c0,3.03-.89,5.98-2.57,8.5-1.62,2.55-4.06,4.49-6.92,5.48-.13.05-.26.08-.4.1-.14.02-.28.03-.42.03ZM14,27.83c2.43-.79,4.52-2.39,5.91-4.54,1.54-2.22,2.36-4.86,2.34-7.56v-6.2c0-.08-.02-.17-.07-.24-.05-.07-.12-.13-.2-.16l-7.82-2.92s-.1-.03-.15-.03c-.05,0-.1,0-.15.03l-7.82,2.92c-.08.03-.15.09-.2.16-.05.07-.07.15-.07.24v6.2c-.02,2.7.79,5.34,2.34,7.56,1.39,2.14,3.47,3.74,5.9,4.54Z"
              fill="currentColor"
            />
            <path
              id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24-4"
              d="M27.29,5.62v2.2c0,.13.05.25.14.33.09.09.21.14.33.14.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33v-2.2h2.2c.13,0,.25-.05.33-.14.09-.09.14-.21.14-.33,0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14h-2.2v-2.2c0-.13-.05-.25-.14-.33-.09-.09-.21-.14-.33-.14-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33v2.2h-2.2c-.13,0-.25.05-.33.14-.09.09-.14.21-.14.33,0,.13.05.25.14.33.09.09.21.14.33.14h2.2Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const VisibilityOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169197">
            <rect id="Rectangle_22041-15" width="32" height="32" fill="none" />
            <path
              id="book_4_spark_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
              d="M6.73,21.14c.2-.09.4-.17.61-.24.22-.07.44-.1.67-.1h1.24V6.63h-1.24c-.34,0-.68.13-.91.38-.24.24-.38.57-.37.91v13.22ZM8.01,26.78c-.8.01-1.57-.3-2.13-.87-.57-.55-.89-1.32-.88-2.12V7.92c-.01-.8.3-1.57.88-2.13.56-.58,1.33-.89,2.13-.88h7.35c.23,0,.45.08.62.25.16.16.25.38.25.61,0,.23-.08.45-.25.61-.16.16-.38.25-.62.25h-4.38v14.17h7.62v-3.23c0-.23.08-.45.25-.61.16-.16.38-.25.62-.25.23,0,.45.08.62.25.16.16.25.38.25.61v4.96h-12.31c-.34,0-.67.12-.91.36-.25.23-.38.56-.38.9,0,.34.13.67.38.9.24.24.57.37.91.36h14.48c.1,0,.19-.03.26-.1.07-.07.1-.16.1-.26v-8.28c0-.23.08-.45.25-.61.16-.16.38-.25.62-.25.23,0,.45.08.62.25.16.16.25.38.25.61v8.28c0,.55-.21,1.09-.61,1.47-.38.4-.92.62-1.47.61h-14.48ZM21.12,15.55c-.03-1.61.61-3.16,1.76-4.29,1.12-1.15,2.68-1.79,4.29-1.76-1.61.03-3.16-.61-4.29-1.76-1.15-1.12-1.79-2.68-1.76-4.29.03,1.61-.61,3.16-1.76,4.29-1.12,1.15-2.68,1.79-4.29,1.76,1.61-.03,3.16.61,4.29,1.76,1.15,1.12,1.79,2.68,1.76,4.29Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DynamicAccessPolicy = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169201">
            <rect id="Rectangle_22041-17" width="32" height="32" fill="none" />
            <g id="Group_169204">
              <g id="Group_169203">
                <path
                  id="graph_2_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-3"
                  d="M8.18,27.61c-.85.01-1.67-.32-2.26-.93-.61-.59-.94-1.41-.93-2.26,0-.7.22-1.38.65-1.93.41-.55.99-.94,1.65-1.12v-2.75c-.01-.85.33-1.67.94-2.27.59-.61,1.41-.95,2.27-.94h4.91s-2.74,0-2.74,0h4.91c.85-.01,1.67.33,2.27.94.61.59.95,1.41.94,2.27v2.75c.66.18,1.24.58,1.65,1.12.43.55.66,1.23.65,1.93.01.85-.33,1.67-.94,2.26-.6.61-1.41.95-2.27.93-.85.01-1.66-.32-2.25-.93-.61-.59-.94-1.41-.93-2.26,0-.7.22-1.38.65-1.93.41-.55.99-.94,1.65-1.12v-2.75c0-.38-.14-.74-.41-1.01-.27-.27-.63-.42-1.01-.41h-4.91s2.74.04,2.74,0h-4.91c-.38,0-.75.14-1.01.41-.27.27-.42.63-.41,1.01v2.75c.66.18,1.24.58,1.65,1.12.43.55.66,1.23.65,1.93.01.85-.33,1.67-.94,2.26-.6.61-1.42.95-2.27.93ZM8.18,25.82c.37,0,.73-.14,1-.4.27-.26.42-.62.41-1,0-.38-.14-.74-.41-1-.27-.27-.63-.42-1.01-.41-.37,0-.73.14-.99.41-.27.26-.41.63-.4,1,0,.37.14.74.4,1,.26.27.63.41,1,.4h0ZM19.89,25.82c.37,0,.73-.14,1-.4.27-.26.42-.62.41-1,0-.38-.14-.74-.41-1-.27-.27-.63-.42-1.01-.41-.37,0-.73.14-.99.41-.27.26-.41.63-.4,1,0,.37.14.74.4,1,.27.27.63.41,1.01.4h0Z"
                  fill="currentColor"
                />
                <path
                  id="graph_2_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-4"
                  d="M14.06,6.39c.85-.01,1.66.32,2.25.93.61.59.94,1.41.93,2.26,0,.7-.22,1.38-.65,1.93-.41.55-.99.94-1.65,1.13v2.75s-1.82.02-1.79,0v-2.75c-.66-.18-1.24-.58-1.65-1.13-.43-.55-.66-1.23-.65-1.93-.01-.85.33-1.67.94-2.26.6-.61,1.41-.95,2.27-.93ZM14.06,8.17c-.37,0-.73.14-1,.4-.27.26-.42.62-.41,1,0,.38.14.74.41,1,.27.27.63.42,1.01.41.37,0,.73-.14.99-.41.54-.55.54-1.44,0-1.99-.26-.27-.63-.41-1-.4h0Z"
                  fill="currentColor"
                />
              </g>
              <path
                id="bookmark_24dp_5F6368_FILL1_wght400_GRAD0_opsz24-3"
                d="M23.1,12.22l-2.08.89c-.31.14-.66.11-.94-.08-.29-.17-.46-.49-.45-.82v-6.42c0-.26.1-.52.29-.7.18-.19.44-.29.7-.29h4.95c.26,0,.52.1.7.29.19.18.29.44.29.7v6.42c.01.33-.16.65-.45.82-.28.19-.63.22-.94.08l-2.08-.89Z"
                fill="currentColor"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const Security = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169200">
            <rect id="Rectangle_22041-16" width="32" height="32" fill="none" />
            <path
              id="shield_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-4"
              d="M16,29.72c-.15,0-.3-.01-.45-.04-.14-.02-.29-.06-.43-.11-3.05-1.07-5.65-3.13-7.38-5.85-1.79-2.68-2.75-5.84-2.74-9.07v-6.61c0-.54.16-1.06.48-1.5.31-.44.74-.77,1.24-.97l8.35-3.12c.3-.11.61-.17.93-.17.32,0,.63.06.93.17l8.35,3.12c.5.19.93.53,1.24.97.32.44.49.96.48,1.5v6.61c0,3.23-.95,6.38-2.74,9.07-1.73,2.72-4.33,4.79-7.38,5.85-.14.05-.28.09-.43.11-.15.02-.3.04-.45.04ZM16,27.56c2.59-.85,4.82-2.55,6.31-4.84,1.65-2.37,2.52-5.18,2.49-8.07v-6.62c0-.09-.03-.18-.08-.25-.05-.08-.13-.14-.22-.17l-8.35-3.12s-.1-.03-.15-.03c-.05,0-.11,0-.15.03l-8.35,3.12c-.09.03-.16.09-.22.17-.05.07-.08.16-.08.25v6.62c-.02,2.88.85,5.7,2.49,8.07,1.49,2.29,3.71,3.99,6.31,4.84h0Z"
              fill="currentColor"
            />
            <path
              id="graph_2_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
              d="M12.51,21.46c-.5,0-.99-.19-1.34-.55-.36-.35-.56-.84-.55-1.34,0-.42.13-.82.39-1.15.24-.33.59-.56.98-.67v-1.64c0-.51.19-.99.56-1.35.35-.36.84-.57,1.35-.56h2.93-1.63,2.92c.51,0,.99.19,1.35.56.36.35.57.84.56,1.35v1.64c.39.11.74.34.98.67.26.33.39.73.39,1.15,0,.51-.19.99-.56,1.34-.35.36-.84.56-1.35.55-.5,0-.99-.19-1.34-.55-.36-.35-.56-.84-.55-1.34,0-.42.13-.82.39-1.15.24-.33.59-.56.98-.67v-1.64c0-.22-.08-.44-.24-.6-.16-.16-.38-.25-.6-.24h-2.92s1.63.02,1.63,0h-2.92c-.22,0-.44.08-.6.24-.16.16-.25.38-.24.6v1.64c.39.11.74.34.98.67.26.33.39.73.39,1.15,0,.51-.19.99-.56,1.34-.36.36-.85.56-1.35.55ZM12.51,20.4c.22,0,.44-.08.6-.24.16-.16.25-.37.24-.6,0-.23-.08-.44-.24-.6-.16-.16-.38-.25-.6-.24-.22,0-.43.08-.59.24-.16.16-.25.37-.24.59,0,.22.08.44.24.59.15.16.37.26.59.25h0ZM19.47,20.4c.22,0,.44-.08.6-.24.16-.16.25-.37.24-.6,0-.23-.08-.44-.24-.6-.16-.16-.38-.25-.6-.24-.22,0-.44.08-.59.24-.16.16-.25.37-.24.59,0,.22.08.44.24.59.15.16.37.25.59.25h0Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
            <path
              id="graph_2_24dp_5F6368_FILL0_wght300_GRAD0_opsz24-2"
              d="M16.01,8.84c.5,0,.99.19,1.34.55.36.35.56.84.55,1.35,0,.42-.13.82-.39,1.15-.24.33-.59.56-.98.67v1.64s-1.08.01-1.06,0v-1.64c-.39-.11-.74-.34-.98-.67-.26-.33-.39-.73-.39-1.15,0-.51.19-.99.56-1.35.35-.36.84-.56,1.35-.55ZM16,9.91c-.22,0-.44.08-.6.24-.16.16-.25.37-.24.6,0,.23.08.44.24.6.16.16.38.25.6.24.23,0,.45-.09.6-.25.32-.33.32-.86,0-1.19-.16-.16-.37-.25-.6-.24Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth=".4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const PathAnalysisIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169243">
            <g id="Group_169236-2">
              <rect id="Rectangle_22041-27" width="32" height="32" fill="none" />
            </g>
            <path
              id="conversion_path_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
              d="M23.81,25.95c-.69,0-1.36-.22-1.9-.64-.54-.41-.93-.98-1.1-1.64h-6.12c-1.14.02-2.23-.43-3.02-1.25-.82-.79-1.27-1.88-1.25-3.02-.02-1.14.44-2.23,1.25-3.02.79-.82,1.89-1.27,3.02-1.25h2.28c.68,0,1.34-.26,1.81-.75.49-.47.76-1.13.75-1.81,0-.68-.26-1.34-.75-1.81-.47-.49-1.13-.76-1.81-.75h-6.12c-.18.65-.57,1.23-1.11,1.64-.54.42-1.21.65-1.89.64-.83.01-1.64-.32-2.22-.91-.6-.58-.93-1.39-.91-2.22-.01-.83.32-1.64.91-2.22.58-.6,1.38-.93,2.22-.92.69,0,1.35.22,1.89.64.54.41.93.99,1.11,1.64h6.12c1.14-.02,2.23.43,3.02,1.25.82.79,1.27,1.89,1.25,3.02.02,1.14-.44,2.23-1.25,3.02-.79.82-1.89,1.27-3.02,1.25h-2.28c-.68,0-1.34.26-1.81.75-.49.47-.76,1.13-.75,1.81,0,.68.26,1.34.75,1.81.47.49,1.13.76,1.81.75h6.12c.18-.65.57-1.23,1.11-1.64.54-.42,1.21-.65,1.89-.64.83-.01,1.64.32,2.22.91.6.58.93,1.39.91,2.22.01.83-.32,1.64-.91,2.22-.58.6-1.39.93-2.22.92ZM7.85,10.56c.38,0,.75-.14,1.01-.42.27-.26.42-.63.42-1.01,0-.38-.14-.75-.42-1.01-.26-.27-.63-.42-1.01-.42-.38,0-.75.14-1.01.42-.27.26-.42.63-.42,1.01,0,.38.14.75.42,1.01.26.27.63.42,1.01.42Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DeviceHubRounded = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169243">
            <g id="Group_169236-2">
              <rect id="Rectangle_22041-27" width="32" height="32" fill="none" />
            </g>
            <path
              id="conversion_path_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
              d="M23.81,25.95c-.69,0-1.36-.22-1.9-.64-.54-.41-.93-.98-1.1-1.64h-6.12c-1.14.02-2.23-.43-3.02-1.25-.82-.79-1.27-1.88-1.25-3.02-.02-1.14.44-2.23,1.25-3.02.79-.82,1.89-1.27,3.02-1.25h2.28c.68,0,1.34-.26,1.81-.75.49-.47.76-1.13.75-1.81,0-.68-.26-1.34-.75-1.81-.47-.49-1.13-.76-1.81-.75h-6.12c-.18.65-.57,1.23-1.11,1.64-.54.42-1.21.65-1.89.64-.83.01-1.64-.32-2.22-.91-.6-.58-.93-1.39-.91-2.22-.01-.83.32-1.64.91-2.22.58-.6,1.38-.93,2.22-.92.69,0,1.35.22,1.89.64.54.41.93.99,1.11,1.64h6.12c1.14-.02,2.23.43,3.02,1.25.82.79,1.27,1.89,1.25,3.02.02,1.14-.44,2.23-1.25,3.02-.79.82-1.89,1.27-3.02,1.25h-2.28c-.68,0-1.34.26-1.81.75-.49.47-.76,1.13-.75,1.81,0,.68.26,1.34.75,1.81.47.49,1.13.76,1.81.75h6.12c.18-.65.57-1.23,1.11-1.64.54-.42,1.21-.65,1.89-.64.83-.01,1.64.32,2.22.91.6.58.93,1.39.91,2.22.01.83-.32,1.64-.91,2.22-.58.6-1.39.93-2.22.92ZM7.85,10.56c.38,0,.75-.14,1.01-.42.27-.26.42-.63.42-1.01,0-.38-.14-.75-.42-1.01-.26-.27-.63-.42-1.01-.42-.38,0-.75.14-1.01.42-.27.26-.42.63-.42,1.01,0,.38.14.75.42,1.01.26.27.63.42,1.01.42Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const InternalCertificateIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169206">
            <rect id="Rectangle_22041-3" width="32" height="32" fill="none" />
            <g id="Group_169241">
              <rect id="Rectangle_22041-4" width="32" height="32" fill="none" />
              <g id="Group_169242">
                <path
                  id="Path_58"
                  d="M25.31,23.52h-3.2c-.44,0-.79-.35-.79-.79s.35-.79.79-.79h2.41v-12.47H7.48v12.47h15.41c.44,0,.79.35.79.79s-.35.79-.79.79H6.69c-.44,0-.79-.35-.79-.79t0,0v-14.05c0-.44.35-.79.79-.79h18.63c.44,0,.79.35.79.79v14.05c0,.44-.35.79-.79.79h0Z"
                  fill="currentColor"
                />
                <path
                  id="Line_1"
                  d="M6.69,14.86c-.21,0-.4-.08-.55-.22-.31-.3-.32-.8-.02-1.12,0,0,0,0,0,0l5.23-5.39c.3-.31.8-.32,1.12-.02,0,0,0,0,0,0,.31.3.32.8.02,1.12,0,0,0,0,0,0l-5.23,5.39c-.15.15-.35.24-.57.24Z"
                  fill="currentColor"
                />
                <path
                  id="Line_2"
                  d="M25.31,14.86c-.21,0-.42-.09-.57-.24l-5.23-5.39c-.3-.31-.3-.81.02-1.12,0,0,0,0,0,0,.31-.3.81-.3,1.12.02,0,0,0,0,0,0l5.23,5.39c.3.31.3.81-.02,1.12,0,0,0,0,0,0-.15.14-.34.22-.55.22Z"
                  fill="currentColor"
                />
                <path
                  id="Line_3"
                  d="M17.58,14.44h-3.16c-.44,0-.79-.35-.79-.79h0c0-.44.35-.79.79-.79h3.16c.44,0,.79.35.79.79,0,.44-.35.79-.79.79h0Z"
                  fill="currentColor"
                />
                <path
                  id="Line_3-2"
                  d="M19.58,17.44h-7.16c-.44,0-.79-.35-.79-.79h0c0-.44.35-.79.79-.79h7.16c.44,0,.79.35.79.79,0,.44-.35.79-.79.79h0Z"
                  fill="currentColor"
                />
                <path
                  id="Line_3-3"
                  d="M17.58,20.44h-3.16c-.44,0-.79-.35-.79-.79h0c0-.44.35-.79.79-.79h3.16c.44,0,.79.35.79.79,0,.44-.35.79-.79.79h0Z"
                  fill="currentColor"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const MongoDBDBaaS = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169211">
            <rect id="Rectangle_22041-21" width="32" height="32" fill="none" />
            <g>
              <circle cx="16" cy="16" r="12" fill="currentColor" />
              <path
                d="M19.21,14.53c-.76-3.35-2.55-4.45-2.74-4.87-.21-.3-.42-.82-.42-.82,0,0,0-.02-.02-.04-.02.3-.03.41-.31.71-.43.34-2.66,2.21-2.84,6.01-.17,3.54,2.56,5.66,2.93,5.93l.04.03h0s.12.84.2,1.72h.29c.07-.62.17-1.23.3-1.84l.02-.02c.17-.12.32-.25.47-.39l.02-.02c.78-.72,2.2-2.4,2.18-5.07,0-.45-.04-.89-.12-1.33h0ZM16.01,19.45s0-4.97.16-4.97c.13,0,.29,6.41.29,6.41-.23-.03-.46-1.06-.46-1.44Z"
                fill="#fff"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RedisDBaaS = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169211">
            <rect id="Rectangle_22041-21" width="32" height="32" fill="none" />
            <path
              d="M25.3,17.81c0,.18-.24.38-.72.63-.99.52-6.13,2.63-7.23,3.2-1.1.57-1.7.57-2.57.15-.87-.41-6.34-2.63-7.33-3.09-.49-.24-.74-.43-.75-.62v1.88c0,.19.26.38.75.62.99.47,6.46,2.68,7.33,3.09.87.42,1.47.42,2.57-.15,1.1-.57,6.24-2.69,7.23-3.2.51-.26.73-.47.73-.65v-1.85s0,0,0,0h0ZM25.3,14.75c0,.18-.24.37-.72.63-.99.51-6.13,2.63-7.23,3.2-1.1.57-1.7.57-2.57.15-.87-.42-6.34-2.63-7.33-3.1-.49-.23-.74-.43-.75-.62v1.88c0,.19.26.39.75.62.99.47,6.46,2.68,7.33,3.1.87.41,1.47.42,2.57-.15,1.1-.57,6.24-2.69,7.23-3.2.51-.26.73-.47.73-.65v-1.85h0,0ZM25.3,11.57c0-.19-.24-.35-.74-.54-.97-.35-6.08-2.39-7.06-2.75-.98-.36-1.38-.34-2.53.07-1.15.42-6.59,2.55-7.56,2.93-.48.19-.72.37-.71.55v1.88c0,.19.26.38.75.62.98.47,6.46,2.68,7.33,3.1.86.41,1.47.42,2.57-.15,1.09-.57,6.24-2.69,7.23-3.2.5-.26.73-.47.73-.65v-1.86h0ZM13.36,13.35l4.31-.66-1.3,1.91-3.01-1.25ZM22.89,11.63l-2.83,1.12-2.55-1.01,2.82-1.11,2.55,1.01ZM15.41,9.78l-.42-.77,1.3.51,1.23-.4-.33.79,1.25.47-1.61.17-.36.87-.58-.97-1.86-.17,1.39-.5ZM12.2,10.87c1.28,0,2.3.4,2.3.89s-1.03.89-2.3.89-2.3-.4-2.3-.89,1.03-.89,2.3-.89Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const OracleDBaaS = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169211">
            <rect id="Rectangle_22041-21" width="32" height="32" fill="none" />
            <path
              d="M12.94,21.35c-3.07,0-5.56-2.49-5.56-5.56s2.49-5.57,5.56-5.57h6.47c3.07,0,5.56,2.49,5.56,5.57s-2.49,5.56-5.56,5.56h-6.47ZM19.26,19.39c1.99,0,3.6-1.61,3.6-3.6s-1.61-3.6-3.6-3.6h-6.18c-1.99,0-3.6,1.62-3.6,3.6s1.61,3.6,3.6,3.6h6.18Z"
              fill="currentColor"
              fillRule="evenodd"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const PostgresDBaaS = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169211">
            <rect id="Rectangle_22041-21" width="32" height="32" fill="none" />
            <path
              d="M19.45,7.22c-.73,0-1.45.11-2.15.31l-.05.02c-.44-.08-.89-.12-1.33-.13-.92-.02-1.71.21-2.34.58-.63-.22-1.93-.59-3.3-.52-.95.05-2,.34-2.77,1.16-.77.81-1.18,2.08-1.09,3.79.02.47.16,1.25.38,2.24.22,1,.54,2.17.93,3.24.39,1.07.82,2.03,1.49,2.68.34.33.8.6,1.34.58.38-.02.73-.18,1.03-.43.15.19.3.27.44.35.18.1.35.16.53.21.32.08.87.19,1.52.08.22-.04.45-.11.68-.21,0,.26.02.51.03.76.03.81.05,1.55.29,2.21.04.11.15.66.57,1.14.42.49,1.25.79,2.19.59.66-.14,1.51-.4,2.07-1.19.55-.79.8-1.92.85-3.75.01-.1.03-.18.04-.26h.13s.02.01.02.01c.71.03,1.47-.07,2.12-.37.57-.26,1-.53,1.31-1,.08-.12.16-.26.19-.5s-.12-.62-.35-.8c-.46-.35-.76-.22-1.07-.15-.31.07-.62.11-.94.11.9-1.52,1.55-3.13,1.92-4.56.22-.84.34-1.62.35-2.3.01-.68-.05-1.28-.45-1.8-1.27-1.62-3.06-2.07-4.44-2.09-.04,0-.09,0-.13,0h0ZM19.42,7.72c1.31-.01,2.98.35,4.18,1.89.27.34.35.85.34,1.47-.01.62-.12,1.36-.33,2.18-.41,1.57-1.18,3.41-2.26,5.06.04.03.08.05.12.07.23.09.74.17,1.77-.04.26-.05.45-.09.64.06.1.08.15.2.14.33-.01.09-.05.18-.1.26-.2.3-.59.58-1.09.81-.45.21-1.08.32-1.65.32-.28,0-.55-.02-.77-.09h-.01c-.09.82-.28,2.45-.41,3.2-.1.6-.28,1.08-.63,1.43-.34.36-.83.57-1.48.71-.81.17-1.4-.01-1.78-.33-.38-.32-.55-.74-.66-1-.07-.18-.11-.41-.15-.72s-.06-.69-.08-1.12c-.02-.66-.03-1.31-.02-1.97-.34.31-.76.52-1.21.59-.54.09-1.02,0-1.3-.07-.14-.04-.28-.09-.41-.16-.13-.07-.26-.15-.34-.31-.05-.09-.07-.2-.04-.3.03-.1.09-.19.17-.26.15-.13.36-.2.67-.26.56-.12.76-.19.88-.29.1-.08.22-.24.42-.48,0-.01,0-.02,0-.03-.36-.01-.72-.11-1.04-.28-.12.12-.71.75-1.44,1.63-.31.37-.64.58-1,.59-.36.02-.68-.16-.95-.43-.55-.53-.99-1.45-1.37-2.48-.38-1.04-.69-2.19-.91-3.17-.22-.98-.35-1.77-.37-2.16-.08-1.62.3-2.72.95-3.41.65-.69,1.55-.95,2.42-1,1.56-.09,3.05.46,3.35.57.58-.39,1.32-.64,2.25-.62.44,0,.88.06,1.31.17h.02c.19-.07.38-.13.58-.17.53-.12,1.07-.19,1.61-.2h0ZM19.54,8.24h-.11c-.45,0-.89.06-1.33.15.97.43,1.71,1.09,2.22,1.75.36.46.66.96.88,1.5.09.21.14.38.18.51.02.07.03.12.03.18,0,.03,0,.06,0,.11,0,0,0,0,0,.01.02.68-.15,1.15-.17,1.8-.02.47.11,1.03.13,1.63.03.57-.04,1.19-.41,1.81.03.04.06.07.09.11.98-1.54,1.68-3.24,2.06-4.7.2-.78.31-1.49.32-2.05,0-.56-.1-.97-.23-1.14-1.05-1.34-2.46-1.68-3.65-1.69h0ZM15.8,8.44c-.92,0-1.58.28-2.08.7-.52.43-.86,1.02-1.09,1.63-.27.72-.36,1.41-.4,1.88h.01c.28-.16.64-.32,1.04-.41.39-.09.81-.12,1.2.03s.7.5.81,1.03c.55,2.54-.17,3.48-.44,4.2-.1.26-.19.52-.26.79.03,0,.07-.02.1-.02.19-.02.33.05.42.08.27.11.45.34.55.61.03.07.04.14.06.22.01.03.02.07.02.1-.03.97-.03,1.94.01,2.91.02.42.04.79.08,1.08.03.29.08.51.11.59.1.25.25.58.51.8.26.22.64.37,1.33.22.6-.13.97-.31,1.22-.56.25-.26.39-.61.49-1.16.14-.82.42-3.19.46-3.64-.02-.34.03-.6.14-.79.11-.2.28-.33.43-.39.07-.03.14-.06.2-.07-.06-.09-.12-.17-.19-.25-.21-.26-.39-.55-.52-.86-.06-.13-.13-.25-.2-.38-.1-.19-.23-.42-.37-.68-.27-.53-.57-1.16-.73-1.79-.15-.62-.18-1.26.22-1.72.35-.4.97-.57,1.89-.48-.03-.08-.04-.15-.09-.26-.21-.49-.48-.95-.81-1.38-.78-1-2.05-2-4.01-2.03h-.09,0ZM10.62,8.48c-.1,0-.2,0-.3,0-.79.05-1.53.27-2.06.84-.53.56-.88,1.49-.81,3.02.01.29.14,1.1.36,2.07.22.97.52,2.1.89,3.1.37,1,.82,1.88,1.24,2.29.21.21.4.29.57.28.17,0,.37-.11.62-.4.45-.55.92-1.08,1.41-1.6-.7-.61-1.05-1.54-.91-2.46.08-.58.09-1.11.08-1.54,0-.41-.04-.69-.04-.86,0,0,0,0,0-.01h0s0,0,0,0h0c0-.9.16-1.79.46-2.63.22-.58.54-1.17,1.03-1.65-.48-.16-1.33-.4-2.25-.44-.1,0-.2,0-.3,0h0ZM20.29,12.6c-.53,0-.83.14-.98.32-.22.25-.24.7-.1,1.25.14.55.42,1.16.68,1.67.13.25.26.48.36.67.1.19.18.32.23.43.04.1.09.2.14.28.21-.43.24-.86.22-1.3-.03-.55-.15-1.11-.14-1.68.02-.66.15-1.1.16-1.61-.19-.02-.38-.04-.58-.04ZM13.87,12.69c-.16,0-.32.02-.48.06-.31.08-.62.19-.9.35-.1.05-.19.11-.27.18l-.02.02c0,.11.03.39.04.8,0,.44,0,1.01-.09,1.62-.19,1.33.78,2.44,1.92,2.44.07-.27.18-.55.28-.84.32-.85.94-1.48.41-3.9-.09-.4-.26-.56-.49-.65-.13-.05-.27-.07-.41-.07h0ZM20.04,12.85h.04c.05,0,.1,0,.14.02.04,0,.08.02.11.04.03.02.05.05.06.09h0s0,0,0,0h0s0,.07-.03.11c-.02.04-.05.08-.09.12-.08.09-.19.15-.3.17-.11.01-.23-.01-.32-.08-.04-.03-.07-.06-.1-.09-.03-.03-.04-.06-.05-.1,0-.04,0-.07.03-.1.03-.03.06-.05.09-.07.07-.04.18-.07.29-.09.04,0,.08,0,.12-.01h0ZM13.94,12.98s.08,0,.13.01c.12.02.23.05.31.1.04.02.07.05.1.08.03.04.05.08.04.13,0,.04-.03.09-.06.12-.03.04-.07.07-.11.1-.1.07-.23.1-.35.09-.13-.02-.24-.08-.33-.18-.04-.04-.07-.08-.09-.13-.03-.04-.04-.09-.03-.14.01-.08.08-.13.15-.15.08-.02.16-.04.24-.03h0ZM21.01,18.71h0c-.11.04-.21.06-.29.09-.08.03-.15.09-.19.17-.05.09-.09.25-.08.52.04.02.07.04.12.05.13.04.36.07.61.06.5,0,1.11-.12,1.43-.27.27-.12.51-.29.74-.48h0c-1.09.22-1.7.16-2.08,0-.09-.04-.18-.09-.26-.16h0ZM14.75,18.78h-.02s-.1.02-.22.15c-.27.3-.37.5-.59.67-.22.18-.51.27-1.1.39-.18.04-.29.08-.36.11.02.02.02.02.05.04.08.05.19.09.28.11.25.06.66.13,1.09.06.43-.07.87-.28,1.25-.81.07-.09.07-.23.02-.37-.05-.15-.17-.27-.26-.31-.05-.02-.1-.04-.16-.05h0Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FileDoneOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169180">
            <rect id="Rectangle_22041-2" width="32" height="32" fill="none" />
            <path
              d="M11.98,21.75c-.51,0-.93-.18-1.28-.53s-.53-.78-.53-1.28v-11.38c0-.51.18-.93.53-1.28s.78-.53,1.28-.53h11.38c.51,0,.93.18,1.28.53s.53.78.53,1.28v11.38c0,.51-.18.93-.53,1.28s-.78.53-1.28.53h-11.38ZM11.98,20.25h11.38c.08,0,.15-.03.21-.1.06-.06.1-.13.1-.21v-11.38c0-.08-.03-.15-.1-.21-.06-.06-.13-.1-.21-.1h-11.38c-.08,0-.15.03-.21.1-.06.06-.1.13-.1.21v11.38c0,.08.03.15.1.21.06.06.13.1.21.1ZM8.48,25.25c-.51,0-.93-.18-1.28-.53s-.53-.78-.53-1.28v-12.13c0-.21.07-.39.22-.53.14-.14.32-.22.53-.22s.39.07.53.22c.14.14.22.32.22.53v12.13c0,.08.03.15.1.21.06.06.13.1.21.1h12.13c.21,0,.39.07.53.22.14.14.22.32.22.53s-.07.39-.22.53c-.14.14-.32.22-.53.22,0,0-12.13,0-12.13,0ZM11.67,8.25v12-12ZM16.26,13.67c-.39-.39-.58-.86-.58-1.41s.19-1.03.58-1.41.86-.58,1.41-.58,1.03.19,1.41.58.58.86.58,1.41-.19,1.03-.58,1.41-.86.58-1.41.58-1.03-.19-1.41-.58ZM15.58,15.72l4.21.04c.26,0,.49.09.69.28s.3.42.3.71c0,.26-.1.49-.3.69s-.43.3-.69.3h-4.21c-.28,0-.52-.1-.72-.29s-.29-.43-.29-.72c.01-.27.11-.51.3-.71s.42-.3.71-.3Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FundProjectionScreenOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169251">
            <rect id="Rectangle_22041-13" width="32" height="32" fill="none" />
            <g>
              <path
                d="M8.97,26.2c-.64,0-1.18-.22-1.62-.66-.44-.44-.66-.98-.66-1.62v-.98c0-.45.16-.84.48-1.16s.71-.48,1.16-.48h1.09v-10.77c0-.45.16-.84.48-1.16s.71-.48,1.16-.48h9.47c.45,0,.84.16,1.16.48s.48.71.48,1.16v13.4c0,.64-.22,1.18-.66,1.62-.44.44-.98.66-1.62.66h-10.94ZM19.91,24.83c.26,0,.47-.09.65-.26s.26-.39.26-.65v-13.4c0-.08-.03-.15-.08-.2-.05-.05-.12-.08-.2-.08h-9.47c-.08,0-.15.03-.2.08-.05.05-.08.12-.08.2v10.77h6.56c.45,0,.84.16,1.16.48s.48.71.48,1.16v.98c0,.26.09.47.26.65s.39.26.65.26ZM12.56,14.52c-.19,0-.36-.07-.49-.2-.13-.13-.2-.29-.2-.49,0-.19.07-.36.2-.49.13-.13.29-.2.49-.2h6.49c.19,0,.36.07.49.2.13.13.2.29.2.49s-.07.36-.2.49c-.13.13-.29.2-.49.2h-6.49ZM12.56,17.15c-.19,0-.36-.07-.49-.2-.13-.13-.2-.29-.2-.49,0-.19.07-.36.2-.49.13-.13.29-.2.49-.2h6.49c.19,0,.36.07.49.2.13.13.2.29.2.49s-.07.36-.2.49c-.13.13-.29.2-.49.2h-6.49ZM8.97,24.83h8.66v-1.89c0-.08-.03-.15-.08-.2-.05-.05-.12-.08-.2-.08h-9.01c-.08,0-.15.03-.2.08-.05.05-.08.12-.08.2v.98c0,.26.09.47.26.65.17.17.39.26.65.26ZM8.97,24.83h-.91,9.57-8.66Z"
                fill="currentColor"
              />
              <path
                d="M23.5,5.8c.51,0,.93.18,1.28.53s.53.78.53,1.28v12.13c0,.21-.07.39-.22.53-.14.14-.32.22-.53.22s-.39-.07-.53-.22c-.14-.14-.22-.32-.22-.53V7.61c0-.08-.03-.15-.1-.21-.06-.06-.13-.1-.21-.1h-10.13c-.21,0-.39-.07-.53-.22-.14-.14-.22-.32-.22-.53s.07-.39.22-.53c.14-.14.32-.22.53-.22,0,0,10.13,0,10.13,0Z"
                fill="currentColor"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CodeSandboxOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169222">
            <rect id="Rectangle_22041-25" width="32" height="32" fill="none" />
            <path
              d="M12.06,21.75c-.51,0-.93-.18-1.28-.53s-.53-.78-.53-1.28v-11.38c0-.51.18-.93.53-1.28s.78-.53,1.28-.53h11.38c.51,0,.93.18,1.28.53s.53.78.53,1.28v11.38c0,.51-.18.93-.53,1.28s-.78.53-1.28.53h-11.38ZM12.06,20.25h11.38c.08,0,.15-.03.21-.1.06-.06.1-.13.1-.21v-11.38c0-.08-.03-.15-.1-.21-.06-.06-.13-.1-.21-.1h-11.38c-.08,0-.15.03-.21.1-.06.06-.1.13-.1.21v11.38c0,.08.03.15.1.21.06.06.13.1.21.1ZM8.56,25.25c-.51,0-.93-.18-1.28-.53s-.53-.78-.53-1.28v-12.13c0-.21.07-.39.22-.53.14-.14.32-.22.53-.22s.39.07.53.22c.14.14.22.32.22.53v12.13c0,.08.03.15.1.21.06.06.13.1.21.1h12.13c.21,0,.39.07.53.22.14.14.22.32.22.53s-.07.39-.22.53c-.14.14-.32.22-.53.22,0,0-12.13,0-12.13,0ZM11.75,8.25v12-12ZM13.88,16.68c-.16,0-.3-.07-.41-.22-.11-.14-.17-.32-.17-.53s.06-.39.17-.53c.11-.14.25-.22.41-.22h4.64c.16,0,.3.07.41.22.11.14.17.32.17.53s-.06.39-.17.53c-.11.14-.25.22-.41.22h-4.64ZM13.88,13.32c-.16,0-.3-.07-.41-.22-.11-.14-.17-.32-.17-.53s.06-.39.17-.53c.11-.14.25-.22.41-.22h7.74c.16,0,.3.07.41.22.11.14.17.32.17.53s-.06.39-.17.53c-.11.14-.25.22-.41.22h-7.74Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DiamondOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169189">
            <rect id="Rectangle_22041-11" width="32" height="32" fill="none" />
            <path
              d="M8,18c-.55,0-1.02-.2-1.41-.59s-.59-.86-.59-1.41.2-1.02.59-1.41.86-.59,1.41-.59,1.02.2,1.41.59.59.86.59,1.41-.2,1.02-.59,1.41-.86.59-1.41.59ZM8.95,21.6l2.95-2.95c.18-.18.42-.28.7-.28s.52.09.7.28.28.42.28.7-.09.52-.28.7l-2.95,2.95c-.18.18-.42.28-.7.28s-.52-.09-.7-.28-.28-.42-.28-.7.09-.52.28-.7ZM11.95,13.3l-2.95-2.95c-.18-.18-.28-.42-.28-.7s.09-.52.28-.7.42-.28.7-.28.52.09.7.28l2.95,2.95c.18.18.28.42.28.7s-.09.52-.28.7-.42.28-.7.28-.52-.09-.7-.28ZM16,26c-.55,0-1.02-.2-1.41-.59s-.59-.86-.59-1.41.2-1.02.59-1.41.86-.59,1.41-.59,1.02.2,1.41.59.59.86.59,1.41-.2,1.02-.59,1.41-.86.59-1.41.59ZM16,10c-.55,0-1.02-.2-1.41-.59s-.59-.86-.59-1.41.2-1.02.59-1.41.86-.59,1.41-.59,1.02.2,1.41.59.59.86.59,1.41-.2,1.02-.59,1.41-.86.59-1.41.59ZM18.65,11.9l3-2.95c.18-.18.41-.28.69-.29s.51.09.71.29c.18.18.28.42.28.7s-.09.52-.28.7l-2.98,2.98c-.2.2-.44.3-.71.3s-.51-.1-.71-.3c-.18-.2-.28-.44-.29-.71s.09-.51.29-.71ZM21.65,23l-2.95-2.95c-.18-.18-.28-.42-.28-.7s.09-.52.28-.7.42-.28.7-.28.52.09.7.28l2.95,2.95c.18.18.28.42.28.7s-.09.52-.28.7-.42.28-.7.28-.52-.09-.7-.28ZM24,18c-.55,0-1.02-.2-1.41-.59s-.59-.86-.59-1.41.2-1.02.59-1.41.86-.59,1.41-.59,1.02.2,1.41.59.59.86.59,1.41-.2,1.02-.59,1.41-.86.59-1.41.59Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const VM = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="26.545" height="23.891" viewBox="0 0 26.545 23.891">
        <g data-name="Group 389" transform="translate(-382.728 -348.741)">
          <path
            d="M87.964-816.109v-2.655h2.655v-2.654H82.655a2.556,2.556,0,0,1-1.875-.78,2.556,2.556,0,0,1-.78-1.875v-13.273a2.556,2.556,0,0,1,.78-1.875,2.556,2.556,0,0,1,1.875-.78h21.236a2.556,2.556,0,0,1,1.875.78,2.556,2.556,0,0,1,.78,1.875v13.273a2.556,2.556,0,0,1-.78,1.875,2.556,2.556,0,0,1-1.875.78H95.927v2.654h2.655v2.655Zm-5.309-7.964h21.236v-13.273H82.655Zm0,0v0Z"
            transform="translate(302.728 1188.741)"
            fill="currentColor"
          />
          <path
            d="M124.07-871.135v-3.485l-3.053-1.768v3.485Zm1.018,0,3.053-1.768v-3.485l-3.053,1.768Zm-.509-4.363,3.014-1.742-3.014-1.742-3.014,1.742Zm-4.07,3.485a1.01,1.01,0,0,1-.375-.369.991.991,0,0,1-.134-.509v-4.045a.991.991,0,0,1,.134-.509,1.01,1.01,0,0,1,.375-.369l3.561-2.048a1,1,0,0,1,.509-.14,1,1,0,0,1,.509.14l3.561,2.048a1.011,1.011,0,0,1,.375.369.992.992,0,0,1,.134.509v4.045a.991.991,0,0,1-.134.509,1.01,1.01,0,0,1-.375.369l-3.561,2.048a1,1,0,0,1-.509.14,1,1,0,0,1-.509-.14ZM124.579-874.912Z"
            transform="translate(271.421 1233.098)"
            fill="currentColor"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const Baremetal = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="21.353" height="22.539" viewBox="0 0 21.353 22.539">
        <path
          d="M125.338-836.441a1.716,1.716,0,0,0-1.26.519,1.716,1.716,0,0,0-.519,1.26,1.716,1.716,0,0,0,.519,1.26,1.716,1.716,0,0,0,1.26.519,1.716,1.716,0,0,0,1.26-.519,1.716,1.716,0,0,0,.519-1.26,1.716,1.716,0,0,0-.519-1.26A1.716,1.716,0,0,0,125.338-836.441Zm0,11.863a1.716,1.716,0,0,0-1.26.519,1.716,1.716,0,0,0-.519,1.26,1.716,1.716,0,0,0,.519,1.26,1.716,1.716,0,0,0,1.26.519,1.716,1.716,0,0,0,1.26-.519,1.716,1.716,0,0,0,.519-1.26,1.716,1.716,0,0,0-.519-1.26A1.716,1.716,0,0,0,125.338-824.578ZM121.186-840h18.98a1.148,1.148,0,0,1,.845.341,1.148,1.148,0,0,1,.341.845v8.3a1.148,1.148,0,0,1-.341.845,1.148,1.148,0,0,1-.845.341h-18.98a1.148,1.148,0,0,1-.845-.341,1.148,1.148,0,0,1-.341-.845v-8.3a1.148,1.148,0,0,1,.341-.845A1.148,1.148,0,0,1,121.186-840Zm1.186,2.373v5.931H138.98v-5.931Zm-1.186,9.49h18.98a1.148,1.148,0,0,1,.845.341,1.148,1.148,0,0,1,.341.845v8.3a1.148,1.148,0,0,1-.341.845,1.148,1.148,0,0,1-.845.341h-18.98a1.148,1.148,0,0,1-.845-.341,1.148,1.148,0,0,1-.341-.845v-8.3a1.148,1.148,0,0,1,.341-.845A1.148,1.148,0,0,1,121.186-828.137Zm1.186,2.373v5.931H138.98v-5.931Zm0-11.863v0Zm0,11.863v0Z"
          transform="translate(-120 840)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const AWS = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="39" height="39" viewBox="0 0 39 39">
        <g transform="translate(0.5 0.65)">
          <path
            d="M19.1,31.4H30.927V19.571H19.1ZM31.873,19.571h1.893v.946H31.873V22.41h1.893v.946H31.873v1.42h1.893v.946H31.873v1.893h1.893v.946H31.873v1.893h1.893V31.4H31.873v.064a.883.883,0,0,1-.882.882h-.064v1.893H29.98V32.346H28.088v1.893h-.946V32.346h-1.42v1.893h-.946V32.346H22.883v1.893h-.946V32.346H20.044v1.893H19.1V32.346h-.064a.883.883,0,0,1-.882-.882V31.4h-1.42v-.946h1.42V28.561h-1.42v-.946h1.42V25.722h-1.42v-.946h1.42v-1.42h-1.42V22.41h1.42V20.517h-1.42v-.946h1.42v-.064a.883.883,0,0,1,.882-.882H19.1V16.732h.946v1.893h1.893V16.732h.946v1.893h1.893V16.732h.946v1.893h1.42V16.732h.946v1.893H29.98V16.732h.946v1.893h.064a.883.883,0,0,1,.882.882ZM25.722,37.492a.059.059,0,0,1-.059.059H13.005a.059.059,0,0,1-.059-.059V24.834a.059.059,0,0,1,.059-.059h2.78v-.946h-2.78A1.006,1.006,0,0,0,12,24.834V37.492A1.006,1.006,0,0,0,13.005,38.5H25.663a1.006,1.006,0,0,0,1.005-1.005V35.185h-.946ZM38.5,13.005V25.663a1.006,1.006,0,0,1-1.005,1.005h-2.78v-.946h2.78a.059.059,0,0,0,.059-.059V13.005a.059.059,0,0,0-.059-.059H24.834a.059.059,0,0,0-.059.059v2.78h-.946v-2.78A1.006,1.006,0,0,1,24.834,12H37.492A1.006,1.006,0,0,1,38.5,13.005Z"
            transform="translate(-6.322 -6.326)"
            fill="currentColor"
            stroke="currentColor"
            strokeWidth="1"
            fillRule="evenodd"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const Linux = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '1.5rem' }}>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0.28 799.99 799.44" width="2500" height="2497">
        <path
          d="M777.73 531.82c14.42-41.27 22.26-85.63 22.26-131.82C799.99 179.24 620.9.28 399.99.28S0 179.24 0 400c0 109.23 43.85 208.23 114.91 280.38l405.74-405.47 100.17 100.11zm-73.06 127.19L520.65 475.12 232.49 763.09c50.95 23.51 107.69 36.63 167.5 36.63 122.04 0 231.31-54.61 304.68-140.71z"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const Windows11 = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2499.6 2500" width="2500" height="2500">
        <g fill="currentColor">
          <path d="M1187.9 1187.9H0V0h1187.9zM2499.6 1187.9h-1188V0h1187.9v1187.9zM1187.9 2500H0V1312.1h1187.9zM2499.6 2500h-1188V1312.1h1187.9V2500z" />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const Mac = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '1.5rem' }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="800px" height="800px" viewBox="0 0 24 24" fill="none">
        <path
          d="M16.52 12.46C16.508 11.8438 16.6682 11.2365 16.9827 10.7065C17.2972 10.1765 17.7534 9.74476 18.3 9.46C17.9558 8.98143 17.5063 8.5883 16.9862 8.31089C16.466 8.03349 15.8892 7.87923 15.3 7.86C14.03 7.76 12.65 8.6 12.14 8.6C11.63 8.6 10.37 7.9 9.40999 7.9C7.40999 7.9 5.29999 9.49 5.29999 12.66C5.30963 13.6481 5.48194 14.6279 5.80999 15.56C6.24999 16.84 7.89999 20.05 9.61999 20C10.52 20 11.16 19.36 12.33 19.36C13.5 19.36 14.05 20 15.06 20C16.79 20 18.29 17.05 18.72 15.74C18.0689 15.4737 17.5119 15.0195 17.1201 14.4353C16.7282 13.8511 16.5193 13.1634 16.52 12.46ZM14.52 6.59C14.8307 6.23965 15.065 5.82839 15.2079 5.38245C15.3508 4.93651 15.3992 4.46569 15.35 4C14.4163 4.10239 13.5539 4.54785 12.93 5.25C12.6074 5.58991 12.3583 5.99266 12.1983 6.43312C12.0383 6.87358 11.9708 7.34229 12 7.81C12.4842 7.82361 12.9646 7.71974 13.3999 7.50728C13.8353 7.29482 14.2127 6.98009 14.5 6.59H14.52Z"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const AWSDBaaS = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
        <path
          d="M9.016 13.38c0 0.396 0.047 0.714 0.12 0.948 0.083 0.234 0.193 0.49 0.339 0.766 0.057 0.089 0.078 0.172 0.078 0.245 0 0.109-0.068 0.214-0.203 0.323l-0.672 0.443c-0.083 0.063-0.177 0.094-0.276 0.099-0.109 0-0.214-0.052-0.318-0.151-0.146-0.151-0.271-0.318-0.385-0.5-0.12-0.203-0.229-0.411-0.328-0.625-0.833 0.979-1.875 1.469-3.13 1.469-0.896 0-1.609-0.255-2.13-0.766-0.521-0.516-0.786-1.193-0.786-2.047 0-0.901 0.318-1.641 0.969-2.193s1.51-0.828 2.609-0.828c0.359 0 0.734 0.031 1.125 0.083 0.396 0.052 0.802 0.141 1.224 0.234v-0.776c0-0.807-0.167-1.375-0.5-1.703-0.339-0.328-0.911-0.49-1.734-0.49-0.37 0-0.755 0.042-1.151 0.135-0.391 0.099-0.776 0.214-1.146 0.365-0.125 0.057-0.25 0.099-0.375 0.141-0.052 0.016-0.109 0.026-0.167 0.031-0.151 0-0.224-0.109-0.224-0.333v-0.521c0-0.167 0.021-0.297 0.073-0.37 0.078-0.099 0.182-0.177 0.297-0.224 0.375-0.193 0.818-0.354 1.339-0.479 0.547-0.141 1.104-0.208 1.661-0.203 1.271 0 2.193 0.286 2.792 0.865 0.583 0.573 0.88 1.443 0.88 2.615v3.448zM4.698 15c0.349 0 0.714-0.063 1.094-0.193 0.385-0.125 0.724-0.359 1.010-0.677 0.172-0.203 0.302-0.427 0.365-0.682s0.104-0.568 0.104-0.927v-0.448c-0.323-0.078-0.646-0.141-0.979-0.182-0.328-0.042-0.661-0.063-1-0.063-0.714 0-1.234 0.141-1.583 0.427-0.354 0.286-0.521 0.693-0.521 1.224 0 0.5 0.125 0.87 0.391 1.125 0.255 0.266 0.63 0.396 1.12 0.396zM13.245 16.151c-0.193 0-0.323-0.031-0.406-0.109-0.083-0.063-0.161-0.214-0.224-0.411l-2.5-8.229c-0.047-0.141-0.083-0.281-0.094-0.427 0-0.172 0.083-0.266 0.25-0.266h1.047c0.203 0 0.339 0.031 0.411 0.104 0.089 0.063 0.151 0.214 0.214 0.417l1.792 7.047 1.661-7.047c0.052-0.214 0.115-0.354 0.198-0.417 0.13-0.078 0.276-0.115 0.427-0.104h0.849c0.203 0 0.344 0.031 0.427 0.104 0.083 0.063 0.161 0.214 0.203 0.417l1.682 7.13 1.839-7.13c0.068-0.214 0.141-0.354 0.214-0.417 0.125-0.078 0.271-0.115 0.417-0.104h0.99c0.172 0 0.266 0.083 0.266 0.266 0 0.052-0.010 0.104-0.021 0.172-0.016 0.089-0.042 0.177-0.073 0.266l-2.568 8.224c-0.063 0.214-0.135 0.354-0.224 0.417-0.12 0.078-0.26 0.115-0.401 0.104h-0.917c-0.203 0-0.339-0.031-0.427-0.104-0.083-0.073-0.156-0.214-0.198-0.427l-1.651-6.865-1.641 6.854c-0.052 0.214-0.115 0.354-0.203 0.427-0.083 0.073-0.234 0.104-0.427 0.104zM26.917 16.438c-0.552 0-1.104-0.068-1.635-0.193s-0.948-0.266-1.224-0.427c-0.172-0.094-0.286-0.198-0.328-0.297-0.042-0.094-0.068-0.193-0.068-0.297v-0.542c0-0.224 0.089-0.333 0.245-0.333 0.063 0 0.13 0.010 0.193 0.031 0.063 0.026 0.161 0.068 0.266 0.109 0.365 0.161 0.755 0.286 1.172 0.37 0.427 0.089 0.839 0.13 1.266 0.13 0.672 0 1.193-0.12 1.552-0.354 0.354-0.214 0.568-0.599 0.557-1.010 0.005-0.276-0.099-0.542-0.286-0.745-0.193-0.198-0.557-0.38-1.078-0.552l-1.542-0.479c-0.776-0.245-1.354-0.604-1.703-1.083-0.344-0.443-0.531-0.984-0.536-1.547 0-0.448 0.099-0.839 0.292-1.182 0.193-0.339 0.448-0.635 0.766-0.87 0.318-0.245 0.682-0.427 1.104-0.552 0.427-0.13 0.875-0.182 1.344-0.182 0.234 0 0.479 0.010 0.714 0.042 0.245 0.031 0.469 0.073 0.693 0.12 0.208 0.052 0.411 0.104 0.604 0.167s0.344 0.13 0.448 0.193c0.125 0.063 0.234 0.156 0.318 0.266 0.073 0.104 0.104 0.229 0.099 0.354v0.5c0 0.224-0.089 0.339-0.245 0.339-0.146-0.016-0.281-0.057-0.406-0.125-0.641-0.286-1.339-0.427-2.042-0.417-0.609 0-1.089 0.094-1.417 0.297-0.333 0.203-0.5 0.51-0.5 0.948 0 0.297 0.104 0.552 0.318 0.755s0.609 0.406 1.172 0.589l1.51 0.474c0.766 0.245 1.323 0.589 1.651 1.026 0.328 0.432 0.49 0.932 0.49 1.49 0 0.453-0.099 0.87-0.276 1.234-0.193 0.359-0.448 0.677-0.776 0.938-0.333 0.266-0.724 0.453-1.182 0.594-0.479 0.146-0.979 0.224-1.526 0.224zM28.932 21.609c-3.505 2.589-8.589 3.958-12.964 3.958-6.13 0-11.656-2.266-15.828-6.036-0.328-0.297-0.031-0.703 0.365-0.464 4.51 2.615 10.078 4.203 15.833 4.203 3.885 0 8.151-0.813 12.083-2.469 0.583-0.271 1.083 0.38 0.51 0.807zM30.391 19.948c-0.448-0.573-2.964-0.276-4.099-0.135-0.339 0.042-0.396-0.26-0.083-0.484 2-1.401 5.286-1 5.672-0.531 0.38 0.484-0.109 3.771-1.979 5.344-0.286 0.245-0.568 0.12-0.438-0.203 0.427-1.052 1.375-3.422 0.927-3.99z"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const CassandraDBaaS = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg fill="#000000" width="800px" height="800px" viewBox="0 0 24 24" role="img" xmlns="http://www.w3.org/2000/svg">
        <title>Apache Cassandra icon</title>
        <path
          d="M17.51 6.535c-.002.003-.247.256-.387.408-.366.397-.92.975-1.312 1.12h-.004c-.052-.018-.103-.035-.159-.05l.704-1.425-.008.012c-.022.043-.719 1.415-1.666 1.218l-.002.002c-.014-.003-.026.002-.04-.002.455-.58.63-1.113.63-1.113s-.756 1.213-1.69.885a.075.075 0 0 1-.049-.047.153.153 0 0 1 0-.08c.01-.041.04-.092.067-.14.095-.176.256-.368.256-.368s-.156.152-.366.295l-.002.002c-.157.107-.345.208-.52.236a.512.512 0 0 1-.167 0c-.214-.037-.208-.25-.149-.455v-.002c.06-.204.174-.4.174-.4s-.19.298-.398.588c-.16.223-.311.396-.416.474l-.12.004c-.063-.176.1-.756.1-.76a4.983 4.983 0 0 1-.314.446 1.73 1.73 0 0 1-.34.336h-.027c-.082.007-.16.02-.239.031a.208.208 0 0 1-.064-.125c-.042-.268.123-.756.125-.762-.002.006-.09.26-.21.502v.004c-.032.067-.07.12-.107.178-.086.136-.176.244-.261.219-.127-.038-.202-.202-.246-.356-.044-.154-.057-.299-.057-.299s-.031.251-.117.485a.799.799 0 0 1-.145.265c-.024.027-.057.036-.086.055a12.55 12.55 0 0 0-2.617.764c-.223.082-.446.158-.674.256-.75.32-1.529.726-2.343 1.248a21.33 21.33 0 0 0-2.56 1.945c-.448.393-.906.82-1.376 1.29 3.558-1.917 4.97-4.743 11.633-4.995 5.044-.191 7.4 2.137 8.178 2.17 1.956.083 3.257-1.4 3.672-1.946.112-.142.179-.237.183-.244-.01.011-.328.373-.781.721l-.006.004c-.46.351-1.05.68-1.576.6h-.002c-.068-.01-.138-.007-.203-.032.743-.434 1.236-1.19 1.482-1.595.076-.125.233-.36.233-.36S21.395 9.53 20.094 9.41l-.002.002c-.051-.005-.105.007-.155-.004.567-.446.985-1.044 1.278-1.55v-.003h.002c.317-.548.482-.985.484-.992-.005.008-.326.498-.766 1.037l-.01.01c-.44.54-.998 1.121-1.464 1.299-.07.026-.14.066-.205.072 1.085-.959 1.539-2.322 1.539-2.322s-1.097 1.595-2.026 1.889c-.056.017-.114.047-.17.054.568-.512.869-.92 1.057-1.24V7.66c.238-.404.281-.65.281-.65l-.004.004a9.379 9.379 0 0 1-.271.334l-.016.017c-.255.3-.645.733-1.045 1.078l-.005.004a4.147 4.147 0 0 1-.32.248c-.03.021-.06.046-.089.065a1.7 1.7 0 0 1-.273.138c-.022.01-.044.022-.065.03-.068-.045-.132-.092-.205-.135-.046.06-.096.117-.142.176H17.5c-.02-.006-.044-.006-.063-.016.858-.934 1.44-2.119 1.44-2.119l-.004.004c-.008.01-1.193 1.61-1.73 1.71-.07-.035-.14-.07-.215-.103a.5.5 0 0 1 .044-.162c.122-.284.465-.717.465-.717s-.093.07-.123.096a4.25 4.25 0 0 1-.304.238c-.056.04-.114.072-.172.108-.064.04-.127.076-.188.105a.71.71 0 0 1-.263.086c-.043 0-.082-.005-.11-.027.453-.277 1.228-1.628 1.233-1.637zm-4.79 2.166a15.081 15.081 0 0 0-.913.006c-.286.01-.55.033-.817.053l-.002.002c-.28.123-.547.268-.783.459a.427.427 0 0 1 .035.168.431.431 0 0 1-.047.19l.885.642.022-.014-.36-1.264.623 1.114c.02-.008.042-.014.063-.02l-.02-1.236.397 1.162.476-1.149.018 1.268c.004.002.007.006.012.008l.712-1.006-.363 1.219.008.01 1.041-.692-.758 1.05v.007l.95-.34c.013-.017.03-.03.042-.047a.394.394 0 0 1 .395-.63c.119-.245.209-.504.268-.77a13.15 13.15 0 0 0-1.883-.19zm-2.507.133l-.02.002c-.308.036-.606.078-.892.125-.287.047-.562.1-.824.16l-.014.002c.115.388.294.756.531 1.08.108-.239.244-.461.402-.662a.436.436 0 0 1 .569-.557c.077-.058.166-.099.248-.15zm4.46.092a3.183 3.183 0 0 1-.163.826.394.394 0 0 1-.328.68 3.171 3.171 0 0 1-.55.63l.661.034-.994.236c-.026.018-.046.042-.072.059a3.194 3.194 0 0 1 1.537.691c.32-.574.504-1.235.504-1.94 0-.535-.237-.928-.594-1.216zm-6.318.226c-.296.07-.58.147-.85.23a1.462 1.462 0 0 0-.212.76c0 1.622.968 3.016 2.357 3.64l.002-.012a3.197 3.197 0 0 1-.58-.79.572.572 0 0 1-.336-1.078 3.18 3.18 0 0 1 .139-1.39 3.16 3.16 0 0 1-.52-1.36zm7.631.026c.14.41.225.842.225 1.293 0 2.44-2.203 4.418-4.92 4.418s-4.92-1.979-4.92-4.418c0-.236.023-.468.063-.694-.777.327-1.45.706-2.075 1.112-.214.388-.318.793-.283 1.2.085.985.951 1.807 2.274 2.364l-.256-.068c-2.29-.646-4.107.503-6.094-.494.46.364.984.717 2.498.722.513.002 2.158-.08 2.461.186.303.265-1.021 1.514-1.021 1.514s2.24-1.897 2.458-1.25c.137.402-.642 1.476-.642 1.476s.783-1.06 1.363-1.287c.4-.156.735-.136 1.022.266.19.265-.909 1.552-.909 1.552s1.476-1.439 1.74-1.363c.266.076 0 1.363 0 1.363s.545-1.306.835-1.402c.397-.133-.909 3.03-.909 3.03s1.595-2.925 1.893-2.991c.51-.114.852 2.043.852 2.043s-.249-1.885.02-2.006c1.694-.767.718 3.256.718 3.256s1.109-2.73.428-3.336C14.605 16.856 14.385 19 14.385 19s.472-1.02-.854-3.53c.72-.037 1.8 1.864 1.8 1.864s-1.173-2.081-.378-2.12c1.58-.074 1.74 2.765 1.74 2.765s.474-.398-.795-3.18c.808-.518 2.764 2.574 2.764 2.574s-1.804-3.017-1.514-3.219c.29-.202 1.35.96 1.35.96s-.808-1.11-.594-1.223c.215-.114 2.362 2.207 2.362 2.207s-1.895-2.271-1.567-2.55c.328-.277 1.717.784 1.717.784s-2.134-1.4-1.717-1.617c.58-.301 2.235.72 2.235.72s-.996-.82-.832-1.02c.164-.203 2.119 1.286 2.119 1.286s-1.728-1.364-1.817-1.705c-.088-.34 1.362.115 1.362.115s-1.602-.744-1.627-.959c-.026-.214 1.06.239 1.06.239s-1.49-1.224-1.855-.227c-.084.159-.187.3-.283.45.093-.27.135-.543.11-.82a1.87 1.87 0 0 0-.052-.28c-.683-.367-1.701-.933-3.133-1.336zm-5.83.455a.427.427 0 0 1-.508.164c-.054.073-.1.152-.148.23l1.277.471c.011-.014.02-.03.032-.043zm-.658.396c-.094.155-.18.316-.244.489.12.128.26.235.396.343l.922-.029.002-.004zm4.186.541l-.606.463.053.002c.201-.132.383-.29.553-.465zm-4.534.291c-.002.012-.007.023-.01.035a2.88 2.88 0 0 0-.048.958.567.567 0 0 1 .451.474l.937-.601c-.085-.03-.17-.06-.251-.096l-1.051-.076.638-.139a3.198 3.198 0 0 1-.666-.555zm.793.211c.138.086.28.16.43.223l.115-.024c0-.019.005-.037.006-.056zm3.12.68l.916.9-1.198-.466.703 1.078-1.072-.832-.012.006.346 1.279-.596-1.135-.097 1.33-.403-1.326-.47 1.262.113-1.36-.016-.007-.812 1.152.297-1.11a3.298 3.298 0 0 0-.793 1.19c.095.102.196.198.302.289a3.984 3.984 0 0 0 4.352-1.688 3.398 3.398 0 0 0-1.26-.539zm-2.436.223l-1.078.39c.001.018.01.033.01.051a.57.57 0 0 1-.184.42c.102.217.228.423.375.615a3.2 3.2 0 0 1 .32-.635l-.295.239zm3.633 2.79c-.186.049-.37.097-.56.137.192-.04.374-.09.56-.136zM13.172 15c-.114.02-.225.046-.34.063-.202.029-.391.042-.586.062.315-.033.624-.074.926-.125zm-1.35.17c-.11.008-.215.007-.324.012.11-.007.214-.003.324-.012z"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const MySqlDBaaS = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <title>folder_type_mysql</title>
        <path d="M27.917,6H18.143l-2,4H5V27H30V6ZM28,10H20.19l1.048-2H28Z" fill="currentColor" stroke="currentColor" />
        <path
          d="M16.018,14.715a2.267,2.267,0,0,0-.591.072v.029h.028a4.784,4.784,0,0,0,.461.591c.116.231.217.46.332.691l.028-.029a.81.81,0,0,0,.3-.721,3.194,3.194,0,0,1-.173-.3c-.086-.144-.274-.216-.39-.331"
          fill="currentColor"
          stroke="currentColor"
        />
        <path
          d="M30.328,27.286a6.676,6.676,0,0,0-2.8.4c-.216.086-.562.086-.591.36.116.115.13.3.232.462a3.376,3.376,0,0,0,.749.879c.3.231.605.46.923.662.562.347,1.2.548,1.743.894.318.2.634.461.953.678.158.115.258.3.46.374v-.044a2.918,2.918,0,0,0-.22-.462c-.144-.143-.288-.274-.433-.417a6.878,6.878,0,0,0-1.5-1.455c-.462-.318-1.471-.75-1.658-1.282l-.029-.029a5.843,5.843,0,0,0,1-.232c.489-.129.936-.1,1.441-.229.231-.058.649-.2.649-.2V27.42c-.258-.256-.442-.6-.713-.841a19.049,19.049,0,0,0-2.352-1.753c-.443-.285-1.013-.47-1.483-.713-.17-.086-.455-.128-.555-.271a5.714,5.714,0,0,1-.585-1.1c-.413-.783-.813-1.652-1.169-2.48a15.136,15.136,0,0,0-.727-1.625,14.371,14.371,0,0,0-5.517-5.331,6.818,6.818,0,0,0-1.824-.585c-.357-.015-.713-.043-1.069-.057a5.792,5.792,0,0,1-.656-.5c-.813-.513-2.907-1.625-3.506-.157-.385.927.57,1.839.9,2.31a6.683,6.683,0,0,1,.726,1.069c.1.242.128.5.229.756a17.035,17.035,0,0,0,.741,1.911,6.726,6.726,0,0,0,.527.883c.115.158.314.228.357.486a4.086,4.086,0,0,0-.328,1.069,6.276,6.276,0,0,0,.414,4.789c.228.356.77,1.141,1.5.841.641-.256.5-1.069.684-1.781.043-.172.014-.285.1-.4v.14s.364.8.544,1.209a8.152,8.152,0,0,0,1.8,1.951,2.634,2.634,0,0,1,.663.875v.258h.322a.8.8,0,0,0-.319-.593,7.1,7.1,0,0,1-.722-.874,18.765,18.765,0,0,1-1.555-2.651c-.223-.453-.417-.947-.6-1.4-.083-.174-.083-.437-.222-.524a5.458,5.458,0,0,0-.666.989,8.569,8.569,0,0,0-.389,2.2c-.055.015-.028,0-.055.029-.444-.116-.6-.6-.764-1a6.6,6.6,0,0,1-.125-3.89c.1-.3.515-1.267.347-1.558-.084-.278-.361-.437-.514-.656a5.881,5.881,0,0,1-.5-.932c-.333-.815-.5-1.719-.861-2.534a7.844,7.844,0,0,0-.694-1.122,7.236,7.236,0,0,1-.764-1.136.707.707,0,0,1-.056-.6.227.227,0,0,1,.2-.19c.18-.16.694.043.874.131a6.924,6.924,0,0,1,1.374.728c.2.146.652.516.652.516h.135c.461.1.981.028,1.413.158a9.189,9.189,0,0,1,2.075.994,12.786,12.786,0,0,1,4.5,4.93c.173.331.246.634.4.979.3.708.678,1.429.98,2.12a9.482,9.482,0,0,0,1.024,1.932c.216.3,1.081.461,1.47.62a10.54,10.54,0,0,1,1,.4c.49.3.979.649,1.441.981.23.173.951.533.994.822"
          fill="currentColor"
          stroke="currentColor"
          color="lightblue"
        />
      </svg>
    </SvgIcon>
  );
};

export const CapacityPlanningToolViewIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
        borderRadius: 0,
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" role="img" width="24" height="24" viewBox="0 0 24 24" style={{ padding: '0.15rem' }}>
        <title>CardView</title>
        <path
          d="M149.746-808.977h-4.4a1.234,1.234,0,0,1-.909-.367,1.234,1.234,0,0,1-.367-.909v-4.4a1.235,1.235,0,0,1,.367-.909,1.235,1.235,0,0,1,.909-.367h4.4a1.235,1.235,0,0,1,.909.367,1.234,1.234,0,0,1,.367.909v4.4a1.234,1.234,0,0,1-.367.909A1.234,1.234,0,0,1,149.746-808.977Zm0,8.9h-4.4a1.235,1.235,0,0,1-.909-.367,1.235,1.235,0,0,1-.367-.909v-4.4a1.235,1.235,0,0,1,.367-.909,1.234,1.234,0,0,1,.909-.367h4.4a1.234,1.234,0,0,1,.909.367,1.234,1.234,0,0,1,.367.909v4.4a1.234,1.234,0,0,1-.367.909A1.235,1.235,0,0,1,149.746-800.073Zm8.905-8.9h-4.4a1.234,1.234,0,0,1-.909-.367,1.234,1.234,0,0,1-.367-.909v-4.4a1.234,1.234,0,0,1,.367-.909,1.235,1.235,0,0,1,.909-.367h4.4a1.235,1.235,0,0,1,.909.367,1.235,1.235,0,0,1,.367.909v4.4a1.234,1.234,0,0,1-.367.909A1.234,1.234,0,0,1,158.652-808.977Zm0,8.9h-4.4a1.235,1.235,0,0,1-.909-.367,1.234,1.234,0,0,1-.367-.909v-4.4a1.234,1.234,0,0,1,.367-.909,1.234,1.234,0,0,1,.909-.367h4.4a1.234,1.234,0,0,1,.909.367,1.235,1.235,0,0,1,.367.909v4.4a1.235,1.235,0,0,1-.367.909A1.235,1.235,0,0,1,158.652-800.073ZM146.19-811.095h2.715v-2.715H146.19Zm8.905,0h2.715v-2.715h-2.715Zm-8.905,8.905h2.715v-2.715H146.19Zm8.905,0h2.715v-2.715h-2.715ZM148.905-811.095ZM155.095-811.095ZM155.095-804.905ZM148.905-804.905ZM142.552-796a2.464,2.464,0,0,1-1.811-.741,2.463,2.463,0,0,1-.741-1.811v-18.9a2.463,2.463,0,0,1,.741-1.811,2.463,2.463,0,0,1,1.811-.741h18.9a2.463,2.463,0,0,1,1.811.741,2.463,2.463,0,0,1,.741,1.811v18.9a2.464,2.464,0,0,1-.741,1.811,2.464,2.464,0,0,1-1.811.741Zm0-2.118h18.9a.414.414,0,0,0,.3-.136.415.415,0,0,0,.136-.3v-18.9a.414.414,0,0,0-.136-.3.414.414,0,0,0-.3-.136h-18.9a.414.414,0,0,0-.3.136.414.414,0,0,0-.136.3v18.9a.415.415,0,0,0,.136.3A.414.414,0,0,0,142.552-798.118Z"
          transform="translate(-140 820)"
          fill="#638098"
        />
      </svg>
    </SvgIcon>
  );
};

export const CapacityPlanningTableViewIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style={{ padding: '0.15rem' }}>
        <title>TableView</title>
        <path
          d="M161.882-798.552v-4.276H142.118v4.276a.423.423,0,0,0,.122.312.423.423,0,0,0,.312.122h18.9a.423.423,0,0,0,.312-.122A.423.423,0,0,0,161.882-798.552Zm0-6.393v-6.109H142.118v6.109Zm0-8.226v-4.276a.423.423,0,0,0-.122-.312.423.423,0,0,0-.312-.122h-18.9a.423.423,0,0,0-.312.122.423.423,0,0,0-.122.312v4.276ZM142.552-796a2.463,2.463,0,0,1-1.811-.741,2.464,2.464,0,0,1-.741-1.811v-18.9a2.464,2.464,0,0,1,.741-1.811,2.463,2.463,0,0,1,1.811-.741h18.9a2.463,2.463,0,0,1,1.811.741,2.464,2.464,0,0,1,.741,1.811v18.9a2.464,2.464,0,0,1-.741,1.811,2.463,2.463,0,0,1-1.811.741Z"
          transform="translate(-140 820)"
          fill="#638098"
        />
      </svg>
    </SvgIcon>
  );
};

export const CapacityPlanningMapViewIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style={{ padding: '0.15rem' }}>
        <title>MapView</title>
        <path
          id="public_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
          d="M112-836a11.691,11.691,0,0,1-4.68-.945,12.122,12.122,0,0,1-3.811-2.564,12.12,12.12,0,0,1-2.566-3.809A11.679,11.679,0,0,1,100-848a11.691,11.691,0,0,1,.945-4.68,12.121,12.121,0,0,1,2.565-3.811,12.12,12.12,0,0,1,3.809-2.566A11.679,11.679,0,0,1,112-860a11.691,11.691,0,0,1,4.68.945,12.12,12.12,0,0,1,3.811,2.565,12.12,12.12,0,0,1,2.566,3.809A11.68,11.68,0,0,1,124-848a11.69,11.69,0,0,1-.945,4.68,12.12,12.12,0,0,1-2.565,3.811,12.118,12.118,0,0,1-3.809,2.566A11.677,11.677,0,0,1,112-836Zm-1.265-1.958v-2.463a2.433,2.433,0,0,1-1.784-.742,2.433,2.433,0,0,1-.742-1.784v-1.263l-6.063-6.063q-.095.568-.174,1.137a8.252,8.252,0,0,0-.079,1.137,9.862,9.862,0,0,0,2.511,6.695A9.721,9.721,0,0,0,110.737-837.958Zm8.716-3.221a10.6,10.6,0,0,0,1.137-1.5,9.563,9.563,0,0,0,.837-1.674,10.454,10.454,0,0,0,.505-1.784,9.975,9.975,0,0,0,.174-1.863,9.984,9.984,0,0,0-1.715-5.669,9.671,9.671,0,0,0-4.6-3.7v.53a2.433,2.433,0,0,1-.742,1.784,2.433,2.433,0,0,1-1.784.742h-2.526v2.526a1.222,1.222,0,0,1-.363.9,1.222,1.222,0,0,1-.9.363h-2.526V-848h7.579a1.222,1.222,0,0,1,.9.363,1.222,1.222,0,0,1,.363.9v3.79h1.263a2.439,2.439,0,0,1,1.484.489A2.5,2.5,0,0,1,119.453-841.179Z"
          transform="translate(-100 860)"
          fill="#638098"
        />
      </svg>
    </SvgIcon>
  );
};

export const CapacityPlanningDonutIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <title>Utilization View</title>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="23.94" viewBox="0 0 24 23.94">
        <path
          id="donut_small_24dp_E8EAED_FILL0_wght400_GRAD0_opsz24_1_"
          data-name="donut_small_24dp_E8EAED_FILL0_wght400_GRAD0_opsz24(1)"
          d="M94.233-878a11.661,11.661,0,0,1,7.323,3.444A11.661,11.661,0,0,1,105-867.233H96.4a3.741,3.741,0,0,0-.827-1.368,3.307,3.307,0,0,0-1.338-.857Zm2.406,3.068v4.09q.331.271.632.571t.571.632h4.09a9.4,9.4,0,0,0-2.105-3.188A9.4,9.4,0,0,0,96.639-874.932ZM91.827-878v8.541a3.551,3.551,0,0,0-1.744,1.338,3.57,3.57,0,0,0-.662,2.09,3.437,3.437,0,0,0,.662,2.06,3.613,3.613,0,0,0,1.744,1.308v8.6a11.585,11.585,0,0,1-7.729-3.88,11.672,11.672,0,0,1-3.1-8.09,11.672,11.672,0,0,1,3.1-8.09A11.585,11.585,0,0,1,91.827-878Zm-2.406,3.068a9.088,9.088,0,0,0-4.376,3.489,9.485,9.485,0,0,0-1.639,5.414,9.485,9.485,0,0,0,1.639,5.414,9.446,9.446,0,0,0,4.376,3.549v-4.15a6.113,6.113,0,0,1-1.774-2.12,5.877,5.877,0,0,1-.632-2.692,5.877,5.877,0,0,1,.632-2.692,6.113,6.113,0,0,1,1.774-2.12ZM96.4-864.827H105a11.661,11.661,0,0,1-3.444,7.323,11.661,11.661,0,0,1-7.323,3.444v-8.6a3.467,3.467,0,0,0,1.338-.827A3.467,3.467,0,0,0,96.4-864.827Zm1.444,2.406a4.649,4.649,0,0,1-.556.632q-.316.3-.647.571v4.09a9.4,9.4,0,0,0,3.188-2.105,9.4,9.4,0,0,0,2.105-3.188ZM87.015-866ZM97.842-869.639ZM97.842-862.421Z"
          transform="translate(-81 878)"
          fill="#fff"
        />
      </svg>
    </SvgIcon>
  );
};

export const CapacityPlanningUpTrendIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
        pt: '0.3rem',
      }}
    >
      <title>Trend View</title>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="14.4" viewBox="0 0 24 14.4">
        <path
          id="trending_up_24dp_E8EAED_FILL0_wght400_GRAD0_opsz24_2_"
          data-name="trending_up_24dp_E8EAED_FILL0_wght400_GRAD0_opsz24(2)"
          d="M81.68-705.6,80-707.28l8.88-8.94,4.8,4.8,6.24-6.18H96.8V-720H104v7.2h-2.4v-3.12L93.68-708l-4.8-4.8Z"
          transform="translate(-80 720)"
          fill="#638098"
        />
      </svg>
    </SvgIcon>
  );
};

export const CapacityPlanningDropDown = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="24.582" height="24.582" viewBox="0 0 24.582 24.582">
        <g id="Group_168627" data-name="Group 168627" transform="translate(24.582 24.582) rotate(-180)">
          <g id="Ellipse_233" data-name="Ellipse 233" transform="translate(0)" fill="none" stroke="#fff" strokeWidth="1.5">
            <circle cx="12.291" cy="12.291" r="12.291" stroke="none" />
            <circle cx="12.291" cy="12.291" r="11.541" fill="none" />
          </g>
          <path
            id="Path_496"
            data-name="Path 496"
            d="M4.809,0,0,4.645,4.809,9.619"
            transform="translate(7.482 14.696) rotate(-90)"
            fill="none"
            stroke="#fff"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

interface CustomSvgIconProps extends SvgIconProps {
  fill?: string;
}

export const CapacityPlanningDownloadIcon = (props: CustomSvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: 'inherit',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
        <g id="Download_icom" transform="translate(-15036.873 -9619.851)">
          <g id="noun-download-7292546" transform="translate(15036.873 9619.851)">
            <path
              id="Path_51213"
              data-name="Path 51213"
              d="M22.216,26.042a.857.857,0,0,1,.146-1.226.913.913,0,0,1,1.26.142l2.13,2.613V18.663a.9.9,0,0,1,1.794,0v8.905l2.13-2.613a.915.915,0,0,1,1.26-.142.858.858,0,0,1,.146,1.226l-3.73,4.574a.913.913,0,0,1-1.405,0l-3.73-4.574Zm11.535,2.4a.885.885,0,0,0-.9.873v1.443a1.307,1.307,0,0,1-1.323,1.287H21.766a1.307,1.307,0,0,1-1.323-1.287V29.314a.9.9,0,0,0-1.794,0v1.443a3.08,3.08,0,0,0,3.117,3.034h9.766a3.08,3.08,0,0,0,3.117-3.034V29.314a.885.885,0,0,0-.9-.873Z"
              transform="translate(-18.649 -17.79)"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CapacityPlanningCloseIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="34" height="48.811" viewBox="0 0 34 48.811">
        <g id="Group_669" data-name="Group 669" transform="translate(-94 -35)">
          <g id="Group_667" data-name="Group 667" transform="translate(-6 2)">
            <g id="Group_668" data-name="Group 668" transform="translate(110 36)">
              <path
                id="Rectangle_561"
                data-name="Rectangle 561"
                d="M0,0H28a6,6,0,0,1,6,6V37a6,6,0,0,1-6,6H0a0,0,0,0,1,0,0V0A0,0,0,0,1,0,0Z"
                transform="translate(-10 -3)"
                fill="#4497d3"
              />
              <g id="Group_772" data-name="Group 772" transform="translate(13.353 12.146) rotate(90)">
                <path
                  id="Path_500"
                  data-name="Path 500"
                  d="M0,12.706,6.251,6.425,12.706,0"
                  transform="translate(12.706 12.706) rotate(180)"
                  fill="none"
                  stroke="#eff2f4"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                />
                <path
                  id="Path_503"
                  data-name="Path 503"
                  d="M0,12.706,6.251,6.425,12.706,0"
                  transform="translate(0.001 12.706) rotate(-90)"
                  fill="none"
                  stroke="#f0f3f5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                />
              </g>
            </g>
          </g>
          <path id="Path_493" data-name="Path 493" d="M2109.986,1826h-16l16,5.811Z" transform="translate(-1999.986 -1748)" fill="#26669a" />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const NetToolsIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
        <path
          fill="currentColor"
          d="M360.54-240.08q18.92-18.92 42.38-30.99 23.47-12.08 50.23-15.93-3.07 17.08-4.15 34.16-1.08 17.07.46 34.76 1.54 20.16 5.43 39.31 3.88 19.16 11.5 36.69l-4.24 4.23-101.61-102.23ZM254-346l-62.46-63.69q57.77-57.77 132.19-88.65 74.42-30.89 156.27-30.89 41.31 0 81.38 8.39 40.08 8.38 76.93 24.15-31.31 7.23-58.81 23.04-27.5 15.8-49.81 37.96-12.07-2.16-24.65-3.23Q492.46-440 480-440q-64 0-122.5 24.5T254-346ZM84-516l-62.46-62.46q91.15-92.38 209.88-141.58Q350.15-769.23 480-769.23q129.85 0 248.58 49.19 118.73 49.2 209.88 141.58L876-516q-79-79-181.5-121.5T480-680q-112 0-214.5 42.5T84-516Zm660.23 96 8.15 47.69q17.39 5 30 11.66 12.62 6.65 24.77 18.5l45.69-15.69 30.77 50.3L846.84-276q4.31 16.61 4.31 33.69t-4.31 33.7l36.77 31.53-30.77 50.31-45.69-15.69Q795-131 782.38-124.15q-12.61 6.84-30 11.84l-8.15 47.69h-60l-8.15-47.69q-17.39-5-30-11.84-12.62-6.85-24.77-18.31l-45.69 15.69-30.77-50.31 36.77-31.53q-4.31-16.62-4.31-33.7 0-17.08 4.31-33.69l-36.77-31.54 30.77-50.3 45.69 15.69q12.15-11.85 24.77-18.5 12.61-6.66 30-11.66l8.15-47.69h60Zm-30 94.61q-34.15 0-58.62 24.47-24.46 24.46-24.46 58.61 0 34.16 24.46 58.62 24.47 24.46 58.62 24.46t58.62-24.46q24.46-24.46 24.46-58.62 0-34.15-24.46-58.61-24.47-24.47-58.62-24.47Z"
        />
      </svg>
    </SvgIcon>
  );
};

export const CertificateIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="30.983" height="24.344" viewBox="0 0 30.983 24.344">
        <g id="noun-contract-6625614" transform="translate(-2 -5)">
          <g id="Group_168782" data-name="Group 168782" transform="translate(2 5)">
            <path
              id="Path_454"
              data-name="Path 454"
              d="M365.663,101a3.321,3.321,0,0,1,3.32,3.32v17.7a3.321,3.321,0,0,1-3.32,3.32H341.32a3.321,3.321,0,0,1-3.32-3.32v-17.7a3.321,3.321,0,0,1,3.32-3.32Zm0,2.213H341.32a1.107,1.107,0,0,0-1.107,1.107v17.7a1.107,1.107,0,0,0,1.107,1.107h24.344a1.107,1.107,0,0,0,1.107-1.107v-17.7a1.107,1.107,0,0,0-1.107-1.107Zm-22.131,16.6a1.107,1.107,0,1,1,0-2.213h8.852a1.107,1.107,0,0,1,0,2.213Zm0-11.065a1.107,1.107,0,1,1,0-2.213h5.533a1.107,1.107,0,1,1,0,2.213Zm0,6.639a1.107,1.107,0,1,1,0-2.213h7.746a1.107,1.107,0,0,1,0,2.213Zm12.172-1.851a4.979,4.979,0,1,1,7.746,0v6.277a1.106,1.106,0,0,1-1.517,1.027l-2.356-.942-2.356.942a1.106,1.106,0,0,1-1.517-1.027Zm2.213,1.568v3.075l1.249-.5a1.1,1.1,0,0,1,.821,0l1.249.5V115.1a5,5,0,0,1-3.32,0Zm3.318-6.909a2.765,2.765,0,1,0,1.108,2.213A2.75,2.75,0,0,0,361.236,108.192Z"
              transform="translate(-338 -101)"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const LoadBalancerIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="33.496" height="29.886" viewBox="0 0 33.496 29.886">
        <g id="noun-load-balancer-5418791" transform="translate(-5.504 -1.571)">
          <g id="Group_168780" data-name="Group 168780" transform="translate(5.504 1.571)">
            <path
              id="Path_430"
              data-name="Path 430"
              d="M109.695,20.729a1.24,1.24,0,1,0,0,2.479h5.478a1.24,1.24,0,1,0,0-2.479Z"
              transform="translate(-82.922 3.937)"
              fill="currentColor"
            />
            <path
              id="Path_431"
              data-name="Path 431"
              d="M112.436,21.09l-2.742,2.742a1.242,1.242,0,1,0,1.759,1.753l2.736-2.742a1.239,1.239,0,1,0-1.753-1.753Z"
              transform="translate(-81.058 3.936)"
              fill="currentColor"
            />
            <path
              id="Path_432"
              data-name="Path 432"
              d="M110.575,19.852a1.24,1.24,0,0,0-.879,2.119l2.742,2.736a1.239,1.239,0,1,0,1.753-1.753l-2.736-2.742A1.241,1.241,0,0,0,110.575,19.852Z"
              transform="translate(-81.061 2.072)"
              fill="currentColor"
            />
            <path
              id="Path_433"
              data-name="Path 433"
              d="M111.4,13.717a8.79,8.79,0,0,0-6.223,2.479,13.03,13.03,0,0,0-3.029,4.879,1.24,1.24,0,1,0,2.327.861,10.751,10.751,0,0,1,2.424-3.957,6.1,6.1,0,0,1,4.5-1.783h2.754a1.24,1.24,0,0,0,0-2.479Z"
              transform="translate(-96.499 -10.975)"
              fill="currentColor"
            />
            <path
              id="Path_434"
              data-name="Path 434"
              d="M102.882,18.709a1.241,1.241,0,0,0-.733,1.6,13.009,13.009,0,0,0,3.029,4.873,8.791,8.791,0,0,0,6.223,2.486h2.754a1.24,1.24,0,0,0,0-2.479H111.4a6.1,6.1,0,0,1-4.5-1.789,10.749,10.749,0,0,1-2.424-3.957,1.241,1.241,0,0,0-1.594-.733Z"
              transform="translate(-96.501 -0.523)"
              fill="currentColor"
            />
            <path
              id="Path_435"
              data-name="Path 435"
              d="M104.427,17.223a1.24,1.24,0,0,0,0,2.479h7.353a1.24,1.24,0,0,0,0-2.479Z"
              transform="translate(-94.125 -3.519)"
              fill="currentColor"
            />
            <path
              id="Path_436"
              data-name="Path 436"
              d="M109.695,17.223a1.24,1.24,0,1,0,0,2.479h5.478a1.24,1.24,0,1,0,0-2.479Z"
              transform="translate(-82.922 -3.519)"
              fill="currentColor"
            />
            <path
              id="Path_437"
              data-name="Path 437"
              d="M112.436,17.584l-2.742,2.742a1.242,1.242,0,1,0,1.759,1.753l2.736-2.742a1.239,1.239,0,0,0-1.753-1.753Z"
              transform="translate(-81.058 -3.52)"
              fill="currentColor"
            />
            <path
              id="Path_438"
              data-name="Path 438"
              d="M110.575,16.346a1.24,1.24,0,0,0-.879,2.119l2.742,2.736a1.239,1.239,0,0,0,1.753-1.753l-2.736-2.742a1.241,1.241,0,0,0-.879-.36Z"
              transform="translate(-81.061 -5.384)"
              fill="currentColor"
            />
            <path
              id="Path_439"
              data-name="Path 439"
              d="M109.695,13.717a1.24,1.24,0,1,0,0,2.479h5.478a1.24,1.24,0,1,0,0-2.479Z"
              transform="translate(-82.922 -10.975)"
              fill="currentColor"
            />
            <path
              id="Path_440"
              data-name="Path 440"
              d="M112.436,14.078l-2.742,2.742a1.242,1.242,0,1,0,1.759,1.753l2.736-2.742a1.239,1.239,0,1,0-1.753-1.753Z"
              transform="translate(-81.058 -10.976)"
              fill="currentColor"
            />
            <path
              id="Path_441"
              data-name="Path 441"
              d="M110.575,12.84a1.24,1.24,0,0,0-.879,2.119l2.742,2.736a1.239,1.239,0,1,0,1.753-1.753L111.455,13.2a1.241,1.241,0,0,0-.879-.36Z"
              transform="translate(-81.061 -12.84)"
              fill="currentColor"
            />
            <path
              id="Path_442"
              data-name="Path 442"
              d="M109.5,24.289a3.969,3.969,0,1,1,3.969-3.969A3.984,3.984,0,0,1,109.5,24.289Zm0-2.485a1.487,1.487,0,1,0-1.484-1.484A1.469,1.469,0,0,0,109.5,21.8Z"
              transform="translate(-89.124 -5.376)"
              fill="currentColor"
            />
            <path
              id="Path_443"
              data-name="Path 443"
              d="M106.06,15.771a5.774,5.774,0,1,0,5.771,5.777,5.794,5.794,0,0,0-5.771-5.777Zm0,2.486a3.292,3.292,0,1,1-3.292,3.292A3.273,3.273,0,0,1,106.06,18.257Z"
              transform="translate(-100.289 -6.605)"
              fill="currentColor"
            />
            <path
              id="Path_444"
              data-name="Path 444"
              d="M109.5,20.783a3.969,3.969,0,1,1,3.969-3.969A3.984,3.984,0,0,1,109.5,20.783Zm0-2.486a1.487,1.487,0,1,0-1.484-1.484A1.469,1.469,0,0,0,109.5,18.3Z"
              transform="translate(-89.124 -12.832)"
              fill="currentColor"
            />
            <path
              id="Path_445"
              data-name="Path 445"
              d="M109.5,27.794a3.969,3.969,0,1,1,3.969-3.969A3.984,3.984,0,0,1,109.5,27.794Zm0-2.486a1.487,1.487,0,1,0-1.484-1.484A1.469,1.469,0,0,0,109.5,25.309Z"
              transform="translate(-89.124 2.08)"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const MapOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="currentColor">
        <title>MapOutlined</title>
        <path d="M480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm-40-82v-78q-33 0-56.5-23.5T360-320v-40L168-552q-3 18-5.5 36t-2.5 36q0 121 79.5 212T440-162Zm276-102q20-22 36-47.5t26.5-53q10.5-27.5 16-56.5t5.5-59q0-98-54.5-179T600-776v16q0 33-23.5 56.5T520-680h-80v80q0 17-11.5 28.5T400-560h-80v80h240q17 0 28.5 11.5T600-440v120h40q26 0 47 15.5t29 40.5Z" />
      </svg>
    </SvgIcon>
  );
};

export const TableOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="currentColor">
        <title>TableOutlined</title>
        <path d="M760-200v-120H200v120h560Zm0-200v-160H200v160h560Zm0-240v-120H200v120h560ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Z" />
      </svg>
    </SvgIcon>
  );
};

export const CardOutlined = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" role="img" width="24" height="24" viewBox="0 0 24 24">
        <title>CardOutlined</title>
        <path
          d="M149.746-808.977h-4.4a1.234,1.234,0,0,1-.909-.367,1.234,1.234,0,0,1-.367-.909v-4.4a1.235,1.235,0,0,1,.367-.909,1.235,1.235,0,0,1,.909-.367h4.4a1.235,1.235,0,0,1,.909.367,1.234,1.234,0,0,1,.367.909v4.4a1.234,1.234,0,0,1-.367.909A1.234,1.234,0,0,1,149.746-808.977Zm0,8.9h-4.4a1.235,1.235,0,0,1-.909-.367,1.235,1.235,0,0,1-.367-.909v-4.4a1.235,1.235,0,0,1,.367-.909,1.234,1.234,0,0,1,.909-.367h4.4a1.234,1.234,0,0,1,.909.367,1.234,1.234,0,0,1,.367.909v4.4a1.234,1.234,0,0,1-.367.909A1.235,1.235,0,0,1,149.746-800.073Zm8.905-8.9h-4.4a1.234,1.234,0,0,1-.909-.367,1.234,1.234,0,0,1-.367-.909v-4.4a1.234,1.234,0,0,1,.367-.909,1.235,1.235,0,0,1,.909-.367h4.4a1.235,1.235,0,0,1,.909.367,1.235,1.235,0,0,1,.367.909v4.4a1.234,1.234,0,0,1-.367.909A1.234,1.234,0,0,1,158.652-808.977Zm0,8.9h-4.4a1.235,1.235,0,0,1-.909-.367,1.234,1.234,0,0,1-.367-.909v-4.4a1.234,1.234,0,0,1,.367-.909,1.234,1.234,0,0,1,.909-.367h4.4a1.234,1.234,0,0,1,.909.367,1.235,1.235,0,0,1,.367.909v4.4a1.235,1.235,0,0,1-.367.909A1.235,1.235,0,0,1,158.652-800.073ZM146.19-811.095h2.715v-2.715H146.19Zm8.905,0h2.715v-2.715h-2.715Zm-8.905,8.905h2.715v-2.715H146.19Zm8.905,0h2.715v-2.715h-2.715ZM148.905-811.095ZM155.095-811.095ZM155.095-804.905ZM148.905-804.905ZM142.552-796a2.464,2.464,0,0,1-1.811-.741,2.463,2.463,0,0,1-.741-1.811v-18.9a2.463,2.463,0,0,1,.741-1.811,2.463,2.463,0,0,1,1.811-.741h18.9a2.463,2.463,0,0,1,1.811.741,2.463,2.463,0,0,1,.741,1.811v18.9a2.464,2.464,0,0,1-.741,1.811,2.464,2.464,0,0,1-1.811.741Zm0-2.118h18.9a.414.414,0,0,0,.3-.136.415.415,0,0,0,.136-.3v-18.9a.414.414,0,0,0-.136-.3.414.414,0,0,0-.3-.136h-18.9a.414.414,0,0,0-.3.136.414.414,0,0,0-.136.3v18.9a.415.415,0,0,0,.136.3A.414.414,0,0,0,142.552-798.118Z"
          transform="translate(-140 820)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const PrevArrowIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.2rem',
      }}
    >
      <svg id="Group_168597" data-name="Group 168597" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
        <g id="Rectangle_21919" data-name="Rectangle 21919" fill="none" stroke="currentColor" strokeWidth="1">
          <rect width="18" height="18" rx="9" stroke="none" />
          <rect x="0.5" y="0.5" width="17" height="17" rx="8.5" fill="none" />
        </g>
        <path
          id="Path_51207"
          data-name="Path 51207"
          d="M3.59,8.489a.5.5,0,0,1-.366-.159L-.366,4.472A.5.5,0,0,1-.377,3.8L3.212-.328a.5.5,0,0,1,.705-.049.5.5,0,0,1,.049.705L.672,4.12,3.956,7.649a.5.5,0,0,1-.366.841Z"
          transform="translate(7.205 5.005)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const NextArrowIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.2rem',
      }}
    >
      <svg id="Group_168598" data-name="Group 168598" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
        <g id="Rectangle_21922" data-name="Rectangle 21922" fill="none" stroke="currentColor" strokeWidth="1">
          <rect width="18" height="18" rx="9" stroke="none" />
          <rect x="0.5" y="0.5" width="17" height="17" rx="8.5" fill="none" />
        </g>
        <path
          id="Path_51208"
          data-name="Path 51208"
          d="M0,8.489a.5.5,0,0,1-.34-.134.5.5,0,0,1-.025-.707L2.918,4.12-.377.328A.5.5,0,0,1-.328-.377a.5.5,0,0,1,.705.049L3.967,3.8a.5.5,0,0,1-.011.669L.366,8.33A.5.5,0,0,1,0,8.489Z"
          transform="translate(7.205 5.005)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const OnPrem = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '4rem',
      }}
    >
      <svg id="Group_390" data-name="Group 390" xmlns="http://www.w3.org/2000/svg" width="72" height="63" viewBox="0 0 72 63">
        <path
          id="On_Prem"
          data-name="On Prem"
          d="M28,3.5A3.5,3.5,0,0,0,24.5,0H3.5A3.5,3.5,0,0,0,0,3.5V7A3.478,3.478,0,0,0,.91,9.333,3.478,3.478,0,0,0,0,11.667v3.5A3.478,3.478,0,0,0,.91,17.5,3.478,3.478,0,0,0,0,19.833v7H28v-7a3.478,3.478,0,0,0-.91-2.333A3.478,3.478,0,0,0,28,15.167v-3.5a3.478,3.478,0,0,0-.91-2.333A3.478,3.478,0,0,0,28,7ZM2.333,3.5A1.167,1.167,0,0,1,3.5,2.333H5.833V4.667H8.167V2.333H10.5V4.667h2.333V2.333H24.5A1.167,1.167,0,0,1,25.667,3.5V7A1.167,1.167,0,0,1,24.5,8.167H3.5A1.167,1.167,0,0,1,2.333,7Zm23.333,8.167v3.5A1.167,1.167,0,0,1,24.5,16.333H3.5a1.167,1.167,0,0,1-1.167-1.167v-3.5A1.167,1.167,0,0,1,3.5,10.5H5.833v2.333H8.167V10.5H10.5v2.333h2.333V10.5H24.5A1.167,1.167,0,0,1,25.667,11.667Zm0,12.833H2.333V19.833A1.167,1.167,0,0,1,3.5,18.667H5.833V21H8.167V18.667H10.5V21h2.333V18.667H24.5a1.167,1.167,0,0,1,1.167,1.167Z"
          transform="translate(22.001 18.083)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const RegenerateKeyIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64">
        <g id="Group_168782" data-name="Group 168782" transform="translate(3230 23606.822)">
          <rect
            id="Rectangle_22019"
            data-name="Rectangle 22019"
            width="64"
            height="64"
            transform="translate(-3230 -23606.822)"
            fill="#17506c"
            opacity="0"
          />
          <path
            id="Union_5"
            data-name="Union 5"
            d="M17.737,61.62l-3.609-6.283a2.086,2.086,0,0,1,.9-2.922l6.528-3.139a2.089,2.089,0,1,1,1.813,3.765l-2.074,1A23.618,23.618,0,0,0,48.055,19.15a2.09,2.09,0,1,1,3.575-2.165A27.756,27.756,0,0,1,20.567,58.158l.794,1.384a2.089,2.089,0,1,1-3.624,2.079ZM4,45.688A27.757,27.757,0,0,1,35.058,4.513l-.794-1.384a2.089,2.089,0,0,1,3.622-2.081L41.5,7.331a2.09,2.09,0,0,1-.907,2.922l-6.528,3.139a2.06,2.06,0,0,1-.9.206,2.089,2.089,0,0,1-.909-3.972l2.073-1A23.618,23.618,0,0,0,7.571,43.52,2.09,2.09,0,0,1,4,45.688Zm7.153-14.351a7.719,7.719,0,0,1,14.075-4.383H40.709L44.475,31.4l-3.766,4.321-2.689-1.51-2.693,1.51-2.689-1.51-2.689,1.51-2.689-1.51-1.679.943a7.721,7.721,0,0,1-14.432-3.818Zm5.069-2.447a3.31,3.31,0,1,0,2.337-.966A3.306,3.306,0,0,0,16.217,28.89Zm.923,3.754a2,2,0,1,1,1.414.586A1.992,1.992,0,0,1,17.14,32.644Z"
            transform="translate(-3225.989 -23606.332)"
            fill="#17506c"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ResetFilterIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
        <path
          id="reset_settings_24dp_5F6368_FILL0_wght400_GRAD0_opsz24"
          d="M130.75-828.75h2.5a.728.728,0,0,1,.538.213A.728.728,0,0,1,134-828a.728.728,0,0,1-.212.537.728.728,0,0,1-.538.213h-2.5a.728.728,0,0,1-.538-.213A.728.728,0,0,1,130-828a.728.728,0,0,1,.213-.537A.728.728,0,0,1,130.75-828.75Zm.75,6v-.5h-.75a.729.729,0,0,1-.538-.213A.729.729,0,0,1,130-824a.728.728,0,0,1,.213-.537.729.729,0,0,1,.538-.212h.75v-.5a.728.728,0,0,1,.212-.537.728.728,0,0,1,.538-.213.728.728,0,0,1,.538.213.728.728,0,0,1,.212.537v2.5a.729.729,0,0,1-.212.538.729.729,0,0,1-.538.212.729.729,0,0,1-.538-.212A.729.729,0,0,1,131.5-822.75Zm3.25-2h2.5a.729.729,0,0,1,.537.212A.728.728,0,0,1,138-824a.729.729,0,0,1-.212.538.729.729,0,0,1-.537.213h-2.5a.729.729,0,0,1-.537-.213A.729.729,0,0,1,134-824a.728.728,0,0,1,.213-.537A.729.729,0,0,1,134.75-824.75Zm.25-2v-2.5a.729.729,0,0,1,.212-.538.729.729,0,0,1,.538-.212.729.729,0,0,1,.538.212.729.729,0,0,1,.212.538v.5h.75a.728.728,0,0,1,.537.213A.728.728,0,0,1,138-828a.728.728,0,0,1-.212.537.728.728,0,0,1-.537.213h-.75v.5a.729.729,0,0,1-.212.538.728.728,0,0,1-.538.213.728.728,0,0,1-.538-.213A.729.729,0,0,1,135-826.75ZM129-838a6.754,6.754,0,0,0-4.962,2.038A6.753,6.753,0,0,0,122-831a6.816,6.816,0,0,0,.813,3.3A6.962,6.962,0,0,0,125-825.25V-827a.968.968,0,0,1,.288-.713A.968.968,0,0,1,126-828a.968.968,0,0,1,.712.287A.968.968,0,0,1,127-827v4a.968.968,0,0,1-.287.713A.968.968,0,0,1,126-822h-4a.968.968,0,0,1-.713-.287A.968.968,0,0,1,121-823a.967.967,0,0,1,.288-.713A.967.967,0,0,1,122-824h1.35a9.061,9.061,0,0,1-2.45-3.062A8.73,8.73,0,0,1,120-831a8.708,8.708,0,0,1,.713-3.512,9.15,9.15,0,0,1,1.925-2.85,9.146,9.146,0,0,1,2.85-1.925A8.706,8.706,0,0,1,129-840a8.7,8.7,0,0,1,5.087,1.575,8.931,8.931,0,0,1,3.263,4.05,1.039,1.039,0,0,1,0,.775.9.9,0,0,1-.55.525,1.07,1.07,0,0,1-.787,0,.9.9,0,0,1-.537-.55,6.88,6.88,0,0,0-2.513-3.163A6.755,6.755,0,0,0,129-838Z"
          transform="translate(-120 840)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const ColumnIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 20 18">
        <path
          id="view_week_24dp_5F6368_FILL0_wght300_GRAD0_opsz24"
          d="M118.1-763.8h-4.241v-14.4H118.1a.3.3,0,0,1,.233.1.388.388,0,0,1,.091.266v13.661a.388.388,0,0,1-.091.266A.3.3,0,0,1,118.1-763.8Zm-5.82,0h-4.555v-14.4h4.555Zm-6.134,0H101.9a.3.3,0,0,1-.233-.1.388.388,0,0,1-.091-.266v-13.661a.388.388,0,0,1,.091-.266.3.3,0,0,1,.233-.1h4.241ZM118.1-762a1.73,1.73,0,0,0,1.35-.63,2.252,2.252,0,0,0,.553-1.539v-13.661a2.252,2.252,0,0,0-.553-1.539,1.73,1.73,0,0,0-1.35-.63H101.9a1.73,1.73,0,0,0-1.35.63,2.252,2.252,0,0,0-.553,1.539v13.661a2.252,2.252,0,0,0,.553,1.539,1.73,1.73,0,0,0,1.35.63Z"
          transform="translate(-100 780)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};
export const AddDiskIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
        <path
          id="add_circle_24dp_5F6368_FILL0_wght400_GRAD0_opsz24_1_"
          data-name="add_circle_24dp_5F6368_FILL0_wght400_GRAD0_opsz24 (1)"
          d="M87.2-871.2v2.4a.774.774,0,0,0,.23.57A.774.774,0,0,0,88-868a.774.774,0,0,0,.57-.23.774.774,0,0,0,.23-.57v-2.4h2.4a.774.774,0,0,0,.57-.23A.774.774,0,0,0,92-872a.774.774,0,0,0-.23-.57.774.774,0,0,0-.57-.23H88.8v-2.4a.774.774,0,0,0-.23-.57A.774.774,0,0,0,88-876a.774.774,0,0,0-.57.23.774.774,0,0,0-.23.57v2.4H84.8a.774.774,0,0,0-.57.23A.774.774,0,0,0,84-872a.774.774,0,0,0,.23.57.774.774,0,0,0,.57.23ZM88-864a7.789,7.789,0,0,1-3.12-.63,8.078,8.078,0,0,1-2.54-1.71,8.078,8.078,0,0,1-1.71-2.54A7.79,7.79,0,0,1,80-872a7.79,7.79,0,0,1,.63-3.12,8.081,8.081,0,0,1,1.71-2.54,8.08,8.08,0,0,1,2.54-1.71A7.791,7.791,0,0,1,88-880a7.791,7.791,0,0,1,3.12.63,8.08,8.08,0,0,1,2.54,1.71,8.081,8.081,0,0,1,1.71,2.54A7.79,7.79,0,0,1,96-872a7.79,7.79,0,0,1-.63,3.12,8.078,8.078,0,0,1-1.71,2.54,8.078,8.078,0,0,1-2.54,1.71A7.789,7.789,0,0,1,88-864Zm0-1.6a6.177,6.177,0,0,0,4.54-1.86A6.177,6.177,0,0,0,94.4-872a6.177,6.177,0,0,0-1.86-4.54A6.176,6.176,0,0,0,88-878.4a6.176,6.176,0,0,0-4.54,1.86A6.177,6.177,0,0,0,81.6-872a6.177,6.177,0,0,0,1.86,4.54A6.177,6.177,0,0,0,88-865.6ZM88-872Z"
          transform="translate(-80 880)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const DecreaseVMCountIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
        <path
          id="do_not_disturb_on_24dp_5F6368_FILL0_wght400_GRAD0_opsz24"
          d="M84.8-871.2h6.4a.774.774,0,0,0,.57-.23A.774.774,0,0,0,92-872a.774.774,0,0,0-.23-.57.774.774,0,0,0-.57-.23H84.8a.774.774,0,0,0-.57.23A.774.774,0,0,0,84-872a.774.774,0,0,0,.23.57A.774.774,0,0,0,84.8-871.2ZM88-864a7.79,7.79,0,0,1-3.12-.63,8.079,8.079,0,0,1-2.54-1.71,8.078,8.078,0,0,1-1.71-2.54A7.79,7.79,0,0,1,80-872a7.79,7.79,0,0,1,.63-3.12,8.079,8.079,0,0,1,1.71-2.54,8.079,8.079,0,0,1,2.54-1.71A7.789,7.789,0,0,1,88-880a7.789,7.789,0,0,1,3.12.63,8.079,8.079,0,0,1,2.54,1.71,8.079,8.079,0,0,1,1.71,2.54A7.79,7.79,0,0,1,96-872a7.79,7.79,0,0,1-.63,3.12,8.079,8.079,0,0,1-1.71,2.54,8.079,8.079,0,0,1-2.54,1.71A7.79,7.79,0,0,1,88-864Zm0-1.6a6.177,6.177,0,0,0,4.54-1.86A6.177,6.177,0,0,0,94.4-872a6.177,6.177,0,0,0-1.86-4.54A6.177,6.177,0,0,0,88-878.4a6.177,6.177,0,0,0-4.54,1.86A6.177,6.177,0,0,0,81.6-872a6.177,6.177,0,0,0,1.86,4.54A6.177,6.177,0,0,0,88-865.6ZM88-872Z"
          transform="translate(-80 880)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const IncreaseVMCountIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        fontSize: '1.5rem',
        ...props.sx,
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
        <path
          id="add_circle_24dp_5F6368_FILL0_wght400_GRAD0_opsz24_1_"
          data-name="add_circle_24dp_5F6368_FILL0_wght400_GRAD0_opsz24 (1)"
          d="M87.2-871.2v2.4a.774.774,0,0,0,.23.57A.774.774,0,0,0,88-868a.774.774,0,0,0,.57-.23.774.774,0,0,0,.23-.57v-2.4h2.4a.774.774,0,0,0,.57-.23A.774.774,0,0,0,92-872a.774.774,0,0,0-.23-.57.774.774,0,0,0-.57-.23H88.8v-2.4a.774.774,0,0,0-.23-.57A.774.774,0,0,0,88-876a.774.774,0,0,0-.57.23.774.774,0,0,0-.23.57v2.4H84.8a.774.774,0,0,0-.57.23A.774.774,0,0,0,84-872a.774.774,0,0,0,.23.57.774.774,0,0,0,.57.23ZM88-864a7.789,7.789,0,0,1-3.12-.63,8.078,8.078,0,0,1-2.54-1.71,8.078,8.078,0,0,1-1.71-2.54A7.79,7.79,0,0,1,80-872a7.79,7.79,0,0,1,.63-3.12,8.081,8.081,0,0,1,1.71-2.54,8.08,8.08,0,0,1,2.54-1.71A7.791,7.791,0,0,1,88-880a7.791,7.791,0,0,1,3.12.63,8.08,8.08,0,0,1,2.54,1.71,8.081,8.081,0,0,1,1.71,2.54A7.79,7.79,0,0,1,96-872a7.79,7.79,0,0,1-.63,3.12,8.078,8.078,0,0,1-1.71,2.54,8.078,8.078,0,0,1-2.54,1.71A7.789,7.789,0,0,1,88-864Zm0-1.6a6.177,6.177,0,0,0,4.54-1.86A6.177,6.177,0,0,0,94.4-872a6.177,6.177,0,0,0-1.86-4.54A6.176,6.176,0,0,0,88-878.4a6.176,6.176,0,0,0-4.54,1.86A6.177,6.177,0,0,0,81.6-872a6.177,6.177,0,0,0,1.86,4.54A6.177,6.177,0,0,0,88-865.6ZM88-872Z"
          transform="translate(-80 880)"
          fill="#0e99d8"
        />
      </svg>
    </SvgIcon>
  );
};

export const SearchIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
      }}
    >
      <svg id="Component_19_4" data-name="Component 19 – 4" xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 52 52">
        <rect id="Rectangle_22051" data-name="Rectangle 22051" width="52" height="52" rx="26" fill="#eff0f0" />
        <g id="Component_88_1" data-name="Component 88 – 1" transform="translate(10 11)">
          <g id="Search">
            <rect id="Rectangle_22039" data-name="Rectangle 22039" width="32" height="32" fill="#8897aa" opacity="0" />
            <path
              id="Union_2"
              data-name="Union 2"
              d="M20.123,21.677l-4.046-4.046a9.914,9.914,0,1,1,1.556-1.556l4.046,4.047a1.1,1.1,0,1,1-1.555,1.555ZM2.2,9.9a7.7,7.7,0,0,0,13.062,5.52,1.059,1.059,0,0,1,.075-.083,1.048,1.048,0,0,1,.083-.075A7.7,7.7,0,1,0,2.2,9.9Z"
              transform="translate(6 5)"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};
export const NotificationIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="52" height="52" viewBox="0 0 52 52">
        <g id="Group_169197" data-name="Group 169197" transform="translate(-1629.127 -20.127)">
          <g id="Group_169196" data-name="Group 169196" transform="translate(1629.127 20.127)">
            <circle id="Ellipse_9389" data-name="Ellipse 9389" cx="26" cy="26" r="26" fill="#eff0f0" />
            <g id="Notification" transform="translate(11.482 12.48)">
              <rect id="Rectangle_22040" data-name="Rectangle 22040" width="28.08" height="26" fill="#fff" opacity="0" />
              <path
                id="Union_3"
                data-name="Union 3"
                d="M8.391,21.82A1.032,1.032,0,0,1,8.8,20.364a1.172,1.172,0,0,1,1.546.387,1.167,1.167,0,0,0,1.956,0,1.172,1.172,0,0,1,1.546-.387,1.032,1.032,0,0,1,.41,1.456,3.5,3.5,0,0,1-5.87,0ZM1.148,18.093a1.137,1.137,0,0,1-1.1-.762,1.016,1.016,0,0,1,.468-1.183,5.47,5.47,0,0,0,1.428-1.781A14.21,14.21,0,0,0,3.41,7.451,7.7,7.7,0,0,1,11.326,0a7.7,7.7,0,0,1,7.918,7.451,14.213,14.213,0,0,0,1.469,6.915,5.472,5.472,0,0,0,1.428,1.781,1.018,1.018,0,0,1,.468,1.183,1.137,1.137,0,0,1-1.1.762ZM5.672,7.451a16.044,16.044,0,0,1-1.834,8.092c-.087.148-.172.289-.259.421h15.5c-.087-.132-.174-.273-.259-.421a16.044,16.044,0,0,1-1.834-8.092,5.5,5.5,0,0,0-5.656-5.322A5.5,5.5,0,0,0,5.672,7.451Z"
                transform="translate(2.885 1.442)"
                fill="currentColor"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const EditIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
        <g id="Group_171636" data-name="Group 171636" transform="translate(0 0)">
          <rect
            id="Rectangle_22053"
            data-name="Rectangle 22053"
            width="16"
            height="16"
            transform="translate(0 0)"
            fill="#fff"
            opacity="0"
          />
          <path
            id="Union_4"
            data-name="Union 4"
            d="M2.091,15.418A2.093,2.093,0,0,1,0,13.328V3.569A2.093,2.093,0,0,1,2.091,1.478H6.97a.7.7,0,1,1,0,1.394H2.091a.7.7,0,0,0-.7.7v9.758a.7.7,0,0,0,.7.7h9.758a.7.7,0,0,0,.7-.7V8.449a.7.7,0,1,1,1.394,0v4.879a2.093,2.093,0,0,1-2.091,2.091Zm2.3-4.386a.7.7,0,0,1-.183-.662l.7-2.788a.69.69,0,0,1,.183-.324L11.705.637a2.176,2.176,0,0,1,3.077,3.077L8.16,10.335a.694.694,0,0,1-.324.184l-2.788.7a.705.705,0,0,1-.17.021A.7.7,0,0,1,4.386,11.033Zm8.3-9.41L6.206,8.108,5.837,9.582l1.474-.368L13.8,2.728A.782.782,0,0,0,12.69,1.623Z"
            transform="translate(0.157 0.157)"
            fill="currentColor"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const EditVMIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1rem',
        width: '0.7em',
        height: '0.7em',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="22.061" height="21.828" viewBox="0 0 22.061 21.828">
        <path
          id="edit-2"
          d="M17,3a2.828,2.828,0,0,1,4,4L7.5,20.5,2,22l1.5-5.5Z"
          transform="translate(-1 -1.172)"
          fill="none"
          stroke="#04213b"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
      </svg>
    </SvgIcon>
  );
};

export const AccordionExpandIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1rem',
      }}
    >
      <svg id="Dropdown" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
        <g
          id="Ellipse_9387"
          data-name="Ellipse 9387"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        >
          <circle cx="12" cy="12" r="12" stroke="none" />
          <circle cx="12" cy="12" r="11" fill="none" />
        </g>
        <g id="Group_169164" data-name="Group 169164" transform="translate(6.706 4.777)">
          <g id="arrow-down-left" transform="translate(0 5.294) rotate(-45)">
            <path
              id="Path_83077"
              data-name="Path 83077"
              d="M7.487,7.487H0V0"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const HeaderBackIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
        <g id="Dropdown" transform="translate(32) rotate(90)">
          <g
            id="Ellipse_9387"
            data-name="Ellipse 9387"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          >
            <circle cx="16" cy="16" r="16" stroke="none" />
            <circle cx="16" cy="16" r="15" fill="none" />
          </g>
          <g id="Group_169164" data-name="Group 169164" transform="translate(10.706 7.627)">
            <g id="arrow-down-left" transform="translate(0 5.294) rotate(-45)">
              <path
                id="Path_83077"
                data-name="Path 83077"
                d="M7.487,7.487H0V0"
                fill="none"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DeleteIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="11.999" height="13.001" viewBox="0 0 11.999 13.001">
        <path
          id="Union_6"
          data-name="Union 6"
          d="M3300.5-11904a2.5,2.5,0,0,1-2.5-2.5v-7.5h-.5a.5.5,0,0,1-.5-.5.5.5,0,0,1,.5-.5h2.5v-.5a1.5,1.5,0,0,1,1.5-1.5h3a1.5,1.5,0,0,1,1.5,1.5v.5h2.5a.5.5,0,0,1,.5.5.5.5,0,0,1-.5.5h-.5v7.5a2.5,2.5,0,0,1-2.5,2.5Zm-1.5-2.5a1.5,1.5,0,0,0,1.5,1.5h5a1.5,1.5,0,0,0,1.5-1.5v-7.5h-8Zm6-8.5v-.5a.5.5,0,0,0-.5-.5h-3a.5.5,0,0,0-.5.5v.5Zm-1,7.5v-4a.5.5,0,0,1,.5-.5.5.5,0,0,1,.5.5v4a.5.5,0,0,1-.5.5A.5.5,0,0,1,3304-11907.5Zm-3,0v-4a.5.5,0,0,1,.5-.5.5.5,0,0,1,.5.5v4a.5.5,0,0,1-.5.5A.5.5,0,0,1,3301-11907.5Z"
          transform="translate(-3297.001 11917.001)"
          fill="#fd0a02r"
        />
      </svg>
    </SvgIcon>
  );
};
export const DownArrow = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" id="arrow_circle_down_24dp_5F6368" width="24" height="24" viewBox="0 0 24 24">
        <rect id="Rectangle_147701" data-name="Rectangle 147701" width="24" height="24" fill="none" />
        <path
          id="Path_83363"
          data-name="Path 83363"
          d="M12,4a8,8,0,1,1-8,8,8.011,8.011,0,0,1,8-8m0-2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm1,10V9a1,1,0,0,0-1-1h0a1,1,0,0,0-1,1v3H9.21a.5.5,0,0,0-.35.85l2.79,2.79a.5.5,0,0,0,.71,0l2.79-2.79A.5.5,0,0,0,14.8,12Z"
          fill="#f5443c"
        />
      </svg>
    </SvgIcon>
  );
};

export const UpArrow = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
        <g id="arrow_circle_down_24dp_5F6368" transform="translate(24 24) rotate(180)">
          <rect id="Rectangle_147701" data-name="Rectangle 147701" width="24" height="24" fill="none" />
          <path
            id="Path_83363"
            data-name="Path 83363"
            d="M12,4a8,8,0,1,1-8,8,8.011,8.011,0,0,1,8-8m0-2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm1,10V9a1,1,0,0,0-1-1h0a1,1,0,0,0-1,1v3H9.21a.5.5,0,0,0-.35.85l2.79,2.79a.5.5,0,0,0,.71,0l2.79-2.79A.5.5,0,0,0,14.8,12Z"
            fill="#17aa06"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};
export const CalendarIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
      }}
    >
      <svg id="Group_172440" data-name="Group 172440" xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18">
        <title>Calendar Icon</title>
        <path
          id="event_24dp_5F6368_FILL0_wght400_GRAD0_opsz24"
          d="M130.861-865.6a2.339,2.339,0,0,1-1.676-.652,2.122,2.122,0,0,1-.685-1.6,2.121,2.121,0,0,1,.685-1.6,2.339,2.339,0,0,1,1.676-.653,2.338,2.338,0,0,1,1.676.653,2.121,2.121,0,0,1,.685,1.6,2.122,2.122,0,0,1-.685,1.6A2.339,2.339,0,0,1,130.861-865.6Zm-8.972,3.6a1.865,1.865,0,0,1-1.334-.529A1.694,1.694,0,0,1,120-863.8v-12.6a1.694,1.694,0,0,1,.555-1.271,1.865,1.865,0,0,1,1.334-.529h.944V-880h1.889v1.8h7.556V-880h1.889v1.8h.944a1.865,1.865,0,0,1,1.334.529A1.694,1.694,0,0,1,137-876.4v12.6a1.694,1.694,0,0,1-.555,1.271,1.865,1.865,0,0,1-1.334.529Zm0-1.8h13.222v-9H121.889Zm0-10.8h13.222v-1.8H121.889Zm0,0v0Z"
          transform="translate(-120 880)"
          fill="#0e99d8"
        />
      </svg>
    </SvgIcon>
  );
};

export const CrowdStrikeIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '6.5rem',
      }}
    >
      <svg id="vlpb" xmlns="http://www.w3.org/2000/svg" width="117.967" height="19.964" viewBox="0 0 117.967 19.964">
        <g id="Group_171646" data-name="Group 171646" transform="translate(0 0)">
          <g id="Group_171644" data-name="Group 171644" transform="translate(9.383 1.855)">
            <path
              id="Path_83078"
              data-name="Path 83078"
              d="M10.149,8.419v-.21L8.374,6.681H8.167a2.854,2.854,0,0,1-2.085.944A2.318,2.318,0,0,1,3.73,5.244,2.318,2.318,0,0,1,6.082,2.862a2.854,2.854,0,0,1,2.085.944h.207l1.775-1.528v-.21A5.214,5.214,0,0,0,6.1.2,5.088,5.088,0,0,0,.743,5.244c0,.112.025.215.033.325C2.022,6.416,3.123,7.1,4.071,7.716a34.79,34.79,0,0,1,3.377,2.4,5.1,5.1,0,0,0,2.7-1.7m-8.3-.054a5.176,5.176,0,0,0,2.9,1.768c-.8-.45-1.575-.892-2.3-1.381-.212-.131-.4-.258-.6-.388"
              transform="translate(-0.743 -0.196)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83079"
              data-name="Path 83079"
              d="M25.412,9.732l-.444-1.917-.162-.105a.657.657,0,0,1-.384.105c-.341,0-.548-.36-.725-.644a2.229,2.229,0,0,0-.739-.794,2.816,2.816,0,0,0,1.8-2.666C24.761,1.718,23.4.46,20.872.46H16.464V10.2h2.928V6.781h.266c.651,0,1.479,1.408,1.864,2.037A2.6,2.6,0,0,0,24.1,10.375a1.881,1.881,0,0,0,1.243-.434ZM21.774,3.875a.932.932,0,0,1-1.021.959H19.392V2.856h1.361a.972.972,0,0,1,1.021,1.019Z"
              transform="translate(-5.731 -0.28)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83080"
              data-name="Path 83080"
              d="M41.15,5.249A5.092,5.092,0,0,0,35.782.2a5.092,5.092,0,0,0-5.369,5.048A5.092,5.092,0,0,0,35.782,10.3,5.1,5.1,0,0,0,41.15,5.249m-2.988,0a2.381,2.381,0,1,1-4.762,0,2.381,2.381,0,1,1,4.762,0"
              transform="translate(-10.156 -0.198)"
              fill="currentColor"
              fillRule="evenodd"
            />
          </g>
          <path
            id="Path_83081"
            data-name="Path 83081"
            d="M65.689,3.991,63.866,9.35,62.139,3.991h-2.8l-.13.225,3.578,9.511h1.775l2.014-5.3,2.021,5.3h1.775l3.584-9.511-.137-.225H71.028L69.294,9.384l-1.83-5.393Z"
            transform="translate(-18.628 -1.953)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Path_83082"
            data-name="Path 83082"
            d="M92.063,8.845c0-2.906-1.938-4.853-5.088-4.853H82.494v9.736h4.481c3.15,0,5.088-1.947,5.088-4.883m-2.988.015a2.194,2.194,0,0,1-2.218,2.472H85.422V6.388h1.435A2.194,2.194,0,0,1,89.076,8.86"
            transform="translate(-25.964 -1.954)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Path_83083"
            data-name="Path 83083"
            d="M110.7,6.387h2.868v7.339H116.5V6.387h2.936v-2.4H110.7Z"
            transform="translate(-34.843 -1.953)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Path_83084"
            data-name="Path 83084"
            d="M133.344,7.241c0-1.992-1.361-3.251-3.89-3.251h-4.408v9.736h2.929V10.311h.7l1.819,3.415H133.4l.133-.225-1.953-3.61a2.829,2.829,0,0,0,1.76-2.651m-2.988.165a.932.932,0,0,1-1.021.959h-1.36V6.386h1.36a.972.972,0,0,1,1.021,1.019"
            transform="translate(-39.36 -1.953)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Path_83085"
            data-name="Path 83085"
            d="M139.8,13.726h2.936V3.991H139.8Z"
            transform="translate(-43.953 -1.953)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Path_83086"
            data-name="Path 83086"
            d="M149.736,7.451V3.991H146.8v9.735h2.936v-3l.546-.628,2.458,3.625h3.072l.137-.225L152.4,8.346l3.277-4.131-.068-.225h-3.141Z"
            transform="translate(-46.174 -1.953)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Path_83087"
            data-name="Path 83087"
            d="M164.336,11.329V9.984h3.345V7.735h-3.345V6.387h4.37V3.99H161.4v9.736h7.374v-2.4Z"
            transform="translate(-50.806 -1.953)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <g id="Group_171645" data-name="Group 171645" transform="translate(0 0)">
            <path
              id="Path_83088"
              data-name="Path 83088"
              d="M101.821,13.2c2.145,0,4.007-1.048,4.007-3.069,0-2.2-1.877-2.772-3.533-3.251-.651-.195-1.318-.42-1.318-.9,0-.345.37-.554.933-.554a3.384,3.384,0,0,1,2.189.989h.207l1.375-1.647v-.21a5.287,5.287,0,0,0-3.83-1.452c-2.278,0-3.859,1.228-3.859,3.01,0,1.918,1.744,2.757,3.223,3.176.962.27,1.614.315,1.614.839,0,.375-.459.6-1.17.6A4.151,4.151,0,0,1,99.13,9.621h-.207l-1.36,1.708v.21a5.764,5.764,0,0,0,4.258,1.661"
              transform="translate(-30.953 -1.313)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83089"
              data-name="Path 83089"
              d="M21.159,24.26c-.709-1.624-2.134-3.707-7.715-6.681A41.11,41.11,0,0,1,2.52,9.754c.358,1.512,2.2,4.833,10.091,8.978,2.188,1.2,5.887,2.319,8.548,5.522"
              transform="translate(-0.799 -4.296)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83090"
              data-name="Path 83090"
              d="M20,17.665c-.673-1.826-1.888-4.163-7.654-7.634A53.872,53.872,0,0,1,0,.48C.388,1.991,2.1,5.919,10.74,11.018c2.837,1.83,6.5,2.958,9.265,6.647"
              transform="translate(0 -0.48)"
              fill="currentColor"
              fillRule="evenodd"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CentrifyIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '4.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="79.408" height="20.241" viewBox="0 0 79.408 20.241">
        <g id="g2167" transform="translate(0 0)">
          <g id="g3075">
            <path
              id="path3061"
              d="M7.636-36.968a.989.989,0,0,1-.474-.12A7.055,7.055,0,0,1,3.432-43.7a7.046,7.046,0,0,1,4.42-6.171,6.99,6.99,0,0,1,7.427,1.453,1.023,1.023,0,0,1,.035,1.442,1.012,1.012,0,0,1-1.435.035,4.967,4.967,0,0,0-4.892-1.267,5,5,0,0,0-3.48,3.679,5.022,5.022,0,0,0,1.507,4.845,5.025,5.025,0,0,0,1.1.8,1.024,1.024,0,0,1,.509,1.15,1.018,1.018,0,0,1-.986.775Zm6.695,5.8a1.019,1.019,0,0,1-1-.882,1.022,1.022,0,0,1,.723-1.122,4.994,4.994,0,0,0,3.684-3.571,5.023,5.023,0,0,0-1.392-4.95,4.966,4.966,0,0,0-5-1.106,5.04,5.04,0,0,0-1.278.568,1.012,1.012,0,0,1-1.4-.331,1.023,1.023,0,0,1,.33-1.4,6.986,6.986,0,0,1,7.587.087,7.062,7.062,0,0,1,3.07,6.973,7.037,7.037,0,0,1-5.04,5.7,1.116,1.116,0,0,1-.289.038Zm-7.31-.565A7.006,7.006,0,0,1,1.51-34.418,7.078,7.078,0,0,1,.194-40.426a1.014,1.014,0,0,1,1.22-.759,1.02,1.02,0,0,1,.755,1.226,5.024,5.024,0,0,0,1.765,5.115,4.967,4.967,0,0,0,5.363.517A5.015,5.015,0,0,0,12-39.012a1.018,1.018,0,0,1,.973-1.06,1.017,1.017,0,0,1,1.055.978,7.045,7.045,0,0,1-6.735,7.339H7.027Z"
              transform="translate(-0.002 50.37)"
              fill="currentColor"
            />
            <path
              id="path3063"
              d="M86.288-29.05a6.666,6.666,0,0,1-2.39.455,4.575,4.575,0,0,1-4.606-4.811,4.55,4.55,0,0,1,4.691-4.8,4.927,4.927,0,0,1,2.248.527.207.207,0,0,0,.286-.069.253.253,0,0,0,.028-.095l.1-1.234a.212.212,0,0,0-.132-.218,6.875,6.875,0,0,0-2.606-.464,6.115,6.115,0,0,0-6.465,6.314,6.043,6.043,0,0,0,6.45,6.412,8.033,8.033,0,0,0,2.638-.385.2.2,0,0,0,.132-.212l-.069-1.241a.211.211,0,0,0-.217-.2A.216.216,0,0,0,86.288-29.05Zm4.6-7.4c-2.512,0-4.183,1.894-4.183,4.543,0,2.92,1.451,4.868,4.628,4.868a6.542,6.542,0,0,0,2.685-.537.215.215,0,0,0,.132-.2v-1.121a.208.208,0,0,0-.207-.208.181.181,0,0,0-.119.022,5.489,5.489,0,0,1-2.443.688,2.93,2.93,0,0,1-2.936-2.778.211.211,0,0,1,.2-.218h5.966a.211.211,0,0,0,.21-.205c-.06-2.787-1.036-4.858-3.95-4.858Zm1.963,3.836H88.684a.208.208,0,0,1-.207-.2v-.032a2.394,2.394,0,0,1,2.364-2.26,2.216,2.216,0,0,1,2.217,2.276.211.211,0,0,1-.2.218Zm7.282-3.842a3.223,3.223,0,0,0-2.926,1.642h-.031v-1.209a.217.217,0,0,0-.21-.215H95.786a.211.211,0,0,0-.214.208v8.561a.211.211,0,0,0,.21.212h1.209a.211.211,0,0,0,.2-.212v-3.949c0-2.181.845-3.7,2.726-3.7,1.429.088,1.884,1.108,1.884,3.211v4.435a.217.217,0,0,0,.217.212h1.256a.2.2,0,0,0,.2-.208v-5.127C103.461-35.01,102.431-36.456,100.132-36.456Zm9.42,7.911a2.11,2.11,0,0,1-.835.164,1.422,1.422,0,0,1-1.382-1.61v-4.7a.208.208,0,0,1,.2-.212h1.953a.211.211,0,0,0,.21-.212v-.909a.214.214,0,0,0-.21-.212h-1.953a.2.2,0,0,1-.2-.2v-2.112a.211.211,0,0,0-.21-.212.2.2,0,0,0-.063,0l-1.228.4a.2.2,0,0,0-.154.2v1.717a.2.2,0,0,1-.2.205h-1.57a.224.224,0,0,0-.21.212v.919a.214.214,0,0,0,.21.212h1.6a.2.2,0,0,1,.2.205v5.051c0,1.8,1.061,2.614,2.763,2.614a4.594,4.594,0,0,0,1.256-.212.2.2,0,0,0,.151-.2v-.9a.2.2,0,0,0-.314-.208Zm6.092-7.816a4.02,4.02,0,0,0-.911-.095,2.729,2.729,0,0,0-2.336,1.6h-.019v-1.168a.217.217,0,0,0-.21-.215h-1.124a.211.211,0,0,0-.21.212v8.558a.211.211,0,0,0,.21.212h1.234a.211.211,0,0,0,.2-.212v-3.877c0-2.254.967-3.665,2.434-3.665a2.428,2.428,0,0,1,.656.079.21.21,0,0,0,.251-.158.238.238,0,0,0,0-.047v-1.02A.2.2,0,0,0,115.644-36.361Zm2.512.117h-1.231a.211.211,0,0,0-.21.212v8.565a.211.211,0,0,0,.2.212h1.237a.211.211,0,0,0,.2-.212v-8.555a.208.208,0,0,0-.207-.221Zm.1-3.883h-1.438a.211.211,0,0,0-.21.205v1.452a.211.211,0,0,0,.2.212h1.444a.211.211,0,0,0,.21-.212v-1.44a.211.211,0,0,0-.21-.212Zm14.968,3.883h-1.218a.22.22,0,0,0-.2.152L129.4-28.8h-.031l-2.355-7.289a.214.214,0,0,0-.2-.152h-3.3a.211.211,0,0,1-.21-.205v-.767c0-1.209.179-2.128,1.6-2.128a4.114,4.114,0,0,1,.556.041.2.2,0,0,0,.236-.142.24.24,0,0,0,0-.047l.047-.893a.218.218,0,0,0-.179-.221,6.338,6.338,0,0,0-.914-.079c-2.437,0-3,1.43-3,3.611v.631a.211.211,0,0,1-.2.212h-1.686a.211.211,0,0,0-.21.205v.925a.217.217,0,0,0,.21.215h1.7a.2.2,0,0,1,.21.2v7.229a.211.211,0,0,0,.21.212H123.1a.211.211,0,0,0,.21-.212v-7.226a.2.2,0,0,1,.2-.2h1.991a.21.21,0,0,1,.2.139l2.512,6.907a5.315,5.315,0,0,1,.314.947c0,.18-.493,2.21-1.959,2.21a2.384,2.384,0,0,1-.54-.063.207.207,0,0,0-.251.152.153.153,0,0,0,0,.038l-.069.909a.211.211,0,0,0,.173.221,4.711,4.711,0,0,0,.942.085c2.17,0,2.826-1.986,3.435-3.719l3.149-8.9a.212.212,0,0,0-.126-.271A.244.244,0,0,0,133.225-36.244Z"
              transform="translate(-54.021 43.583)"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const QualysIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '4.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="91.137" height="25" viewBox="0 0 91.137 25">
        <g id="Qualys_2017" transform="translate(-3.595 -4.254)">
          <path
            id="Fill-1"
            d="M10.564,0C17.444,0,20.9,3.658,20.9,3.658a47.92,47.92,0,0,1,.143,8.16C20.665,20.053,10.566,25,10.565,25S.466,20.053.084,11.818a47.909,47.909,0,0,1,.143-8.16S3.684,0,10.564,0"
            transform="translate(3.595 4.254)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Fill-4"
            d="M367.355,97.055a5.3,5.3,0,1,1,5.3-5.3,5.305,5.305,0,0,1-5.3,5.3m0-12.741a7.45,7.45,0,0,0-7.442,7.441,8.273,8.273,0,0,0,3.019,6.4,15.49,15.49,0,0,0,7.563,3.2,4.838,4.838,0,0,0,1.53-1.608,20.471,20.471,0,0,1-3.291-.775,4.294,4.294,0,0,0,.869-.174,7.339,7.339,0,0,0,5.194-7.047,7.474,7.474,0,0,0-7.442-7.441"
            transform="translate(-326.351 -75.873)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Fill-9"
            d="M826.558,135.142l-2.513-6.669h-2.24l3.667,9.309s-1.622,3.857-1.622,3.856c1.067,0,2.235-.075,2.692-1.2,1.071-2.638,4.8-11.965,4.8-11.965h-2.24Z"
            transform="translate(-743.987 -115.861)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Fill-11"
            d="M783.375,99.165s2.078-.039,2.078-1.449V85.047h-2.078Z"
            transform="translate(-709.348 -76.517)"
            fill="currentcolor"
            fillRule="evenodd"
          />
          <path
            id="Fill-13"
            d="M655.139,133.868a3.094,3.094,0,0,1-3.166-3.149V130.7a3.1,3.1,0,0,1,3.131-3.2,3.016,3.016,0,0,1,2.9,1.869,2.879,2.879,0,0,1,.265,1.344,3.443,3.443,0,0,1-.319,1.358A2.968,2.968,0,0,1,655.139,133.868Zm3.114-6.733a4.054,4.054,0,0,0-3.374-1.591,4.953,4.953,0,0,0-4.984,5.14,5.314,5.314,0,0,0,1.319,3.584,4.789,4.789,0,0,0,3.665,1.557,4.049,4.049,0,0,0,3.374-1.585v1.578s2.079-.033,2.079-1.448v-8.548h-2.079Z"
            transform="translate(-588.588 -113.19)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Fill-15"
            d="M544.044,133.891a2.309,2.309,0,0,1-2.453,2.627c-1.6,0-2.384-.911-2.384-2.784v-5.262h-2.079v5.679a5.047,5.047,0,0,0,.8,2.9,3.675,3.675,0,0,0,3.161,1.421h.058a3.45,3.45,0,0,0,3-1.372v1.365s1.974-.034,1.974-1.379v-8.618h-2.079Z"
            transform="translate(-486.624 -115.842)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Fill-17"
            d="M928.818,130.65c-1.08-.467-1.866-.871-1.852-1.5.009-.414.555-.811,1.123-.792a1.784,1.784,0,0,1,1.309.775s1.136-.971,1.273-1.075a3.184,3.184,0,0,0-2.708-1.547,2.883,2.883,0,0,0-2.962,2.8c-.03,1.365.77,1.929,1.818,2.524l1.726.971c.4.279.586.487.577.915a1.243,1.243,0,0,1-1.3,1.15,1.817,1.817,0,0,1-1.391-.713,2.332,2.332,0,0,0-2.434-.318,4.44,4.44,0,0,0,3.784,2.912,3.266,3.266,0,0,0,3.43-3.165c.019-.874-.364-1.96-2.389-2.935"
            transform="translate(-836.475 -114.068)"
            fill="currentColor"
            fillRule="evenodd"
          />
          <path
            id="Fill-19"
            d="M39.4,47.088a5.247,5.247,0,0,1,5.164-5.315,5.174,5.174,0,0,1,5.169,5.168c0,2.275-1.956,4.147-3.463,5.109a9.073,9.073,0,0,0,3.111.8,7.459,7.459,0,0,0,2.9-5.906,7.721,7.721,0,0,0-15.442,0,8.538,8.538,0,0,0,3.1,6.606,15.965,15.965,0,0,0,7.818,3.246,8.982,8.982,0,0,0,2.29-2.145,21.421,21.421,0,0,1-6.8-1.858c-2.554-1.34-3.849-3.259-3.849-5.7"
            transform="translate(-30.406 -31.939)"
            fill="#fff"
            fillRule="evenodd"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const TaniumIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '4.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="91.435" height="16.932" viewBox="0 0 91.435 16.932">
        <g id="Layer_1" data-name="Layer 1" transform="translate(0.004 0.041)" opacity="0.5">
          <path
            id="Path_11274"
            data-name="Path 11274"
            d="M5.108,7.161H16.5A8.334,8.334,0,0,0,7.468,0,8.415,8.415,0,0,0,.007,8.849,8.374,8.374,0,0,0,8.3,16.892a8.262,8.262,0,0,0,1.16-.085v-7H5.108Z"
            transform="translate(0 0)"
            fill="currentColor"
          />
          <path
            id="Path_11275"
            data-name="Path 11275"
            d="M71.22,60.521a8.3,8.3,0,0,0,3.937-5.781H71.22Z"
            transform="translate(-58.671 -44.803)"
            fill="currentColor"
          />
          <path
            id="Path_11276"
            data-name="Path 11276"
            d="M513.78,75.482h-.24V75.35h.631v.132h-.238v.65h-.153Zm1.3.035h0l-.222.614h-.118l-.235-.614h0v.614h-.152V75.35h.246l.2.529.2-.529h.246v.783h-.152Z"
            transform="translate(-423.801 -61.834)"
            fill="currentColor"
          />
          <path
            id="Path_11277"
            data-name="Path 11277"
            d="M111.19,26.36V28.7h3.92v7.075H117.9V28.7h3.919V26.36Z"
            transform="translate(-91.781 -21.534)"
            fill="currentColor"
          />
          <path
            id="Path_11278"
            data-name="Path 11278"
            d="M168.508,28.546l1.808,3.537h-3.571ZM166.7,26.36l-4.93,9.417h3.116l.816-1.664h5.654l.866,1.664h3.1l-5-9.417Z"
            transform="translate(-133.54 -21.534)"
            fill="currentColor"
          />
          <path
            id="Path_11279"
            data-name="Path 11279"
            d="M252.473,26.36V33.2h-.026l-5.235-6.839H242.89v9.417h2.788V28.937h.026l5.238,6.841h4.321V26.36Z"
            transform="translate(-200.496 -21.534)"
            fill="currentColor"
          />
          <rect
            id="Rectangle_4653"
            data-name="Rectangle 4653"
            width="2.788"
            height="9.417"
            transform="translate(56.326 4.826)"
            fill="currentColor"
          />
          <path
            id="Path_11280"
            data-name="Path 11280"
            d="M347.469,26.36h2.79V31.8c0,1.2.414,1.666,1.556,1.666h2.763c1.142,0,1.556-.469,1.556-1.666V26.36h2.79v6.061c0,2.342-1.166,3.459-3.731,3.459h-4c-2.562,0-3.731-1.117-3.731-3.459Z"
            transform="translate(-286.813 -21.424)"
            fill="currentColor"
          />
          <path
            id="Path_11281"
            data-name="Path 11281"
            d="M421.75,26.36h4.285l3.153,6.061,3.152-6.061h4.285v9.417h-2.714V29.014h-.025l-3.655,6.764h-2.087l-3.655-6.762h-.025v6.762H421.75Z"
            transform="translate(-348.189 -21.534)"
            fill="currentColor"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ScienceLogicIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="98" height="20" viewBox="0 0 98 20">
        <g id="sciencelogic-seeklogo" transform="translate(0 -201.834)" opacity="0.5">
          <path
            id="Path_83099"
            data-name="Path 83099"
            d="M6.966,210.009a2.4,2.4,0,0,0-2.249-1.318,2.207,2.207,0,0,0-2.339,1.987c0,1.1,1.077,1.671,1.95,2.006l1,.391c1.987.78,3.475,1.876,3.475,4.217a4.319,4.319,0,0,1-4.268,4.368c-.069,0-.14,0-.209,0A4.367,4.367,0,0,1,0,217.735l1.764-.372a2.647,2.647,0,1,0,5.293,0l0-.073c0-1.506-1.207-2.155-2.414-2.657l-.966-.408c-1.6-.686-3.046-1.6-3.046-3.546,0-2.266,2.006-3.623,4.123-3.623a3.994,3.994,0,0,1,3.6,2.119Z"
            transform="translate(0 -4.222)"
            fill="currentColor"
          />
          <path
            id="Path_83100"
            data-name="Path 83100"
            d="M58.667,237.694a3.186,3.186,0,0,0-2.489-1.171,3.006,3.006,0,0,0-2.9,3.046,2.936,2.936,0,0,0,2.785,3.081c.075,0,.148,0,.222,0a3.119,3.119,0,0,0,2.377-1.153v2.136a4.859,4.859,0,0,1-2.4.577,4.626,4.626,0,1,1-.01-9.251l.1,0a4.71,4.71,0,0,1,2.3.575Z"
            transform="translate(-41.747 -26.862)"
            fill="currentColor"
          />
          <rect
            id="Rectangle_24474"
            data-name="Rectangle 24474"
            width="1.672"
            height="8.692"
            transform="translate(19.075 208.363)"
            fill="currentColor"
          />
          <path
            id="Path_83101"
            data-name="Path 83101"
            d="M125.478,238.611a2.347,2.347,0,0,0-2.4-2.174,2.49,2.49,0,0,0-2.471,2.174Zm-4.956,1.262a2.738,2.738,0,0,0,2.619,2.879,2.841,2.841,0,0,0,2.6-1.69l1.412.8a4.518,4.518,0,0,1-4.031,2.377c-2.657,0-4.272-2.006-4.272-4.569,0-2.674,1.431-4.68,4.234-4.68,2.766,0,4.067,2.044,4.067,4.625v.261Z"
            transform="translate(-96.104 -26.886)"
            fill="currentColor"
          />
          <path
            id="Path_83102"
            data-name="Path 83102"
            d="M171.3,236.406h.036a2.965,2.965,0,0,1,2.619-1.431c2.452,0,3.082,1.653,3.082,3.77v5.184h-1.671v-4.979c0-1.487-.2-2.489-1.914-2.489-2.155,0-2.155,1.895-2.155,3.475v3.994H169.63v-8.694H171.3v1.169Z"
            transform="translate(-137.165 -26.871)"
            fill="currentColor"
          />
          <path
            id="Path_83103"
            data-name="Path 83103"
            d="M224.047,237.694a3.186,3.186,0,0,0-2.489-1.171,3.006,3.006,0,0,0-2.9,3.046,2.936,2.936,0,0,0,2.785,3.081c.075,0,.149,0,.222,0a3.122,3.122,0,0,0,2.377-1.153v2.136a4.859,4.859,0,0,1-2.4.577,4.626,4.626,0,1,1-.01-9.251l.1,0a4.71,4.71,0,0,1,2.3.575Z"
            transform="translate(-175.485 -26.862)"
            fill="currentColor"
          />
          <path
            id="Path_83104"
            data-name="Path 83104"
            d="M266.248,238.611a2.347,2.347,0,0,0-2.4-2.174,2.49,2.49,0,0,0-2.471,2.174Zm-4.958,1.262a2.738,2.738,0,0,0,2.619,2.879,2.846,2.846,0,0,0,2.6-1.69l1.412.8a4.518,4.518,0,0,1-4.031,2.377c-2.657,0-4.272-2.006-4.272-4.569,0-2.674,1.431-4.68,4.234-4.68,2.766,0,4.067,2.044,4.067,4.625v.261Z"
            transform="translate(-209.932 -26.886)"
            fill="currentColor"
          />
          <path
            id="Path_83105"
            data-name="Path 83105"
            d="M313.117,221.016h4.253v1.6h-6v-14h1.745Z"
            transform="translate(-251.776 -5.478)"
            fill="currentColor"
          />
          <path
            id="Path_83106"
            data-name="Path 83106"
            d="M345.322,239.6a2.973,2.973,0,1,0,2.973-3.027,3.017,3.017,0,0,0-2.973,3.027m7.615,0a4.644,4.644,0,1,1-4.663-4.625h.019a4.571,4.571,0,0,1,4.644,4.5v.126"
            transform="translate(-277.881 -26.876)"
            fill="currentColor"
          />
          <path
            id="Path_83107"
            data-name="Path 83107"
            d="M459.359,223.382h-1.672V214.69h1.672Zm.314-12.667a1.151,1.151,0,1,1,0-.008v.008"
            transform="translate(-369.838 -6.246)"
            fill="currentColor"
          />
          <path
            id="Path_83108"
            data-name="Path 83108"
            d="M482.209,237.694a3.179,3.179,0,0,0-2.489-1.171,3.006,3.006,0,0,0-2.9,3.046,2.934,2.934,0,0,0,2.786,3.081c.075,0,.149,0,.224,0a3.122,3.122,0,0,0,2.377-1.153v2.136a4.859,4.859,0,0,1-2.4.577,4.626,4.626,0,0,1-.079-9.251c.058,0,.113,0,.171,0a4.715,4.715,0,0,1,2.3.575Z"
            transform="translate(-384.211 -26.862)"
            fill="currentColor"
          />
          <circle
            id="Ellipse_11797"
            data-name="Ellipse 11797"
            cx="1.621"
            cy="1.621"
            r="1.621"
            transform="translate(31.774 202.446)"
            fill="currentColor"
          />
          <circle
            id="Ellipse_11798"
            data-name="Ellipse 11798"
            cx="1.621"
            cy="1.621"
            r="1.621"
            transform="translate(26.914 205.149) rotate(-73.154)"
            fill="currentColor"
          />
          <circle
            id="Ellipse_11799"
            data-name="Ellipse 11799"
            cx="1.621"
            cy="1.621"
            r="1.621"
            transform="translate(22.456 205.148) rotate(-73.154)"
            fill="currentColor"
          />
          <circle
            id="Ellipse_11800"
            data-name="Ellipse 11800"
            cx="1.621"
            cy="1.621"
            r="1.621"
            transform="translate(17.788 204.591) rotate(-58.286)"
            fill="currentColor"
          />
          <path
            id="Path_83109"
            data-name="Path 83109"
            d="M404.63,240.069a3.04,3.04,0,0,1,.308,1.335,2.973,2.973,0,1,1-2.973-3.027,2.907,2.907,0,0,1,1.617.49l.46-1.621a4.769,4.769,0,0,0-2.075-.467,4.626,4.626,0,1,0,4.644,4.625,4.735,4.735,0,0,0-.351-1.8Z"
            transform="translate(-321.324 -28.338)"
            fill="currentColor"
          />
          <path
            id="Path_83110"
            data-name="Path 83110"
            d="M434.762,237.16l-.012-.042a3.363,3.363,0,0,0-2.307-2.268l-.914,3.236Z"
            transform="translate(-348.948 -26.744)"
            fill="currentColor"
          />
          <path
            id="Path_83111"
            data-name="Path 83111"
            d="M407.248,277.69a2.833,2.833,0,0,1-5.506.73l-1.533.525a4.459,4.459,0,0,0,8.5-2.705l-.031-.09A4.968,4.968,0,0,1,407.248,277.69Z"
            transform="translate(-323.628 -60.219)"
            fill="currentColor"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DatadogIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '4.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="92" height="23" viewBox="0 0 92 23">
        <g id="datadog" transform="translate(-0.25 -0.25)" opacity="0.5">
          <g id="Group_171647" data-name="Group 171647" transform="translate(25.994 6.734)">
            <path
              id="Path_83091"
              data-name="Path 83091"
              d="M227.766,68.441h-4.3V58.55h4.3q4.642,0,4.644,4.675Q232.409,68.439,227.766,68.441Zm-2.461-1.592h2.183q3.084,0,3.083-3.624,0-3.086-3.083-3.087h-2.183v6.711Z"
              transform="translate(-223.47 -58.479)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83092"
              data-name="Path 83092"
              d="M303.506,68.441H301.62l4.208-9.891H307.8l4.3,9.891h-1.975l-1.248-2.7H305.7l.631-1.591h2.058l-1.622-3.715Z"
              transform="translate(-292.643 -58.479)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83093"
              data-name="Path 83093"
              d="M383.82,58.55h7.52v1.59H388.5v8.3h-1.836v-8.3H383.82Z"
              transform="translate(-365.4 -58.479)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83094"
              data-name="Path 83094"
              d="M442.966,68.441H441.08l4.208-9.891h1.975l4.3,9.891h-1.977l-1.247-2.7h-3.174l.631-1.591h2.059l-1.621-3.715Z"
              transform="translate(-416.083 -58.479)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83095"
              data-name="Path 83095"
              d="M547.216,68.441h-4.3V58.55h4.3q4.645,0,4.644,4.675Q551.86,68.439,547.216,68.441Zm-2.459-1.592h2.183q3.083,0,3.086-3.624,0-3.086-3.086-3.087h-2.183Z"
              transform="translate(-506.224 -58.479)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83096"
              data-name="Path 83096"
              d="M631.58,62.96q0-5.031,4.977-5.03,4.9,0,4.9,5.03,0,5-4.9,5Q631.8,67.963,631.58,62.96Zm4.977,3.408q2.991,0,2.992-3.452,0-3.4-2.992-3.4-3.071,0-3.07,3.4Q633.487,66.37,636.557,66.369Z"
              transform="translate(-584.699 -57.93)"
              fill="currentColor"
              fillRule="evenodd"
            />
            <path
              id="Path_83097"
              data-name="Path 83097"
              d="M734.861,63.89v2.316a4.571,4.571,0,0,1-1.137.165q-3.369,0-3.367-3.562,0-3.289,3.569-3.288a6.921,6.921,0,0,1,2.772.554V58.414a8.223,8.223,0,0,0-2.911-.484q-5.336,0-5.337,4.879,0,5.152,5.246,5.154a7.472,7.472,0,0,0,3-.526V62.262h-2.966l-.62,1.627Z"
              transform="translate(-670.442 -57.93)"
              fill="currentColor"
              fillRule="evenodd"
            />
          </g>
          <g id="Group_171649" data-name="Group 171649" transform="translate(0.25 0.25)">
            <g id="Group_171648" data-name="Group 171648">
              <path
                id="Path_83098"
                data-name="Path 83098"
                d="M18.471,16.58l-1.938-1.262-1.616,2.667-1.879-.542-1.655,2.5.085.785,9-1.637-.523-5.553ZM10.08,14.187l1.444-.2a3.687,3.687,0,0,0,.677.213,2.145,2.145,0,0,0,1.689-.152,2.935,2.935,0,0,0,.682-.6l5.915-1.06.6,7.213-10.133,1.8Zm10.986-2.6-.584.11L19.361.25.25,2.439,2.6,21.313l2.237-.321a5.019,5.019,0,0,0-.932-.947A1.518,1.518,0,0,1,3.873,18c.513-.979,3.164-2.225,3.013-3.791A2.747,2.747,0,0,0,6.2,12.395a1.775,1.775,0,0,0,.016.414,2.132,2.132,0,0,1-.33-.655,3.214,3.214,0,0,1-.31-.385,1.421,1.421,0,0,0-.071.483,3.027,3.027,0,0,1-.209-.774,1.214,1.214,0,0,0-.133.46,2.952,2.952,0,0,1-.18-1.018A4.75,4.75,0,0,1,4.654,8.6a1.916,1.916,0,0,0,2.368-.421c.167-.242.28-.9-.083-2.2A19.376,19.376,0,0,0,5.9,3.439l-.026.019c.118.379.363,1.172.457,1.558a4.157,4.157,0,0,1,.226,2.112c-.114.468-.385.774-1.074,1.117s-1.6-.492-1.662-.538A3.629,3.629,0,0,1,2.58,5.9c-.06-.457.267-.732.432-1.105a4.345,4.345,0,0,0-.5.185,5.925,5.925,0,0,1,.7-.6,4.031,4.031,0,0,0,.423-.31c-.245,0-.443,0-.443,0A7.848,7.848,0,0,1,4.024,3.7c-.309-.014-.605,0-.605,0s.91-.4,1.628-.7a.985.985,0,0,1,1.248.246A2.168,2.168,0,0,0,7.815,4.2a6.573,6.573,0,0,1,1.245-.487,1.979,1.979,0,0,1,.96-.66.96.96,0,0,0-.265.488,7.157,7.157,0,0,1,.64-.436,2.209,2.209,0,0,0-.25.408l.028.041a6.342,6.342,0,0,1,.774-.377s-.119.15-.26.343c.269,0,.813.011,1.024.035,1.248.027,1.506-1.316,1.985-1.484.6-.211.867-.339,1.889.651.876.851,1.561,2.373,1.221,2.714-.285.283-.848-.11-1.47-.878a3.3,3.3,0,0,1-.695-1.5,1.467,1.467,0,0,0-.481-.814,2.655,2.655,0,0,1,.222.92,2.991,2.991,0,0,0,.412,1.61c-.038.073-.055.357-.1.412A5.881,5.881,0,0,0,13.14,4.169a8.74,8.74,0,0,1,2.2,2.347c.44.888.18,1.7.4,1.914a7.488,7.488,0,0,1,1.117,1.694,3.237,3.237,0,0,1-.37,2.574l-1.083.167A1.546,1.546,0,0,1,15,12.718a2.657,2.657,0,0,0,.235-.549l-.061-.106a4.557,4.557,0,0,1-1.372,1.194,2.345,2.345,0,0,1-1.783.15,10.2,10.2,0,0,1-2.843-1.5s-.009.183.047.225a10.072,10.072,0,0,0,1.816,1.5L9.487,13.8l.736,5.661c-.326.047-.377.069-.734.119a3.794,3.794,0,0,0-1.575-2.234,3,3,0,0,0-2.148-.3l-.049.057a2.8,2.8,0,0,1,1.808.426,4.092,4.092,0,0,1,1.334,2.036,2.6,2.6,0,0,1-.241,2.522,2.2,2.2,0,0,1-2.9.227,1.764,1.764,0,0,0,1.212.91,2.336,2.336,0,0,0,2.044-.549,3.265,3.265,0,0,0,.609-2.355l.693-.1.25,1.758L22,20.607ZM14.085,6.812c-.032.073-.083.119-.007.354l0,.014.011.031.031.07a1.413,1.413,0,0,0,.535.667,1.878,1.878,0,0,1,.2-.022A.917.917,0,0,1,15.338,8a.984.984,0,0,0,0-.213c-.018-.348.07-.941-.608-1.253A.7.7,0,0,0,14,6.6a.23.23,0,0,1,.056.012c.183.061.061.123.028.2m1.9,3.251a2.508,2.508,0,0,0-.8,0,3.858,3.858,0,0,0-1.289.356.4.4,0,0,0,.046.632,2.006,2.006,0,0,0,1.389.551c.281-.036.53-.477.705-.877.122-.275.122-.571-.055-.667m-4.925-2.82c.157-.148-.781-.34-1.509.15a.986.986,0,0,0-.04,1.577,1.319,1.319,0,0,0,.133.1,4.263,4.263,0,0,1,.518-.2A4.6,4.6,0,0,1,11,8.676a.885.885,0,0,0,.2-.714c-.043-.521-.441-.438-.141-.719"
                transform="translate(-0.25 -0.25)"
                fill="currentColor"
                fillRule="evenodd"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SplunkIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '3.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="74.14" height="21.806" viewBox="0 0 74.14 21.806">
        <path
          id="Splunk_logo_1_"
          data-name="Splunk_logo (1)"
          d="M7.908,13.2A3,3,0,0,1,7.6,14.559a3.076,3.076,0,0,1-.87,1.052,4.156,4.156,0,0,1-1.331.674,5.657,5.657,0,0,1-1.689.24,5.985,5.985,0,0,1-1.965-.3A5.919,5.919,0,0,1,0,15.245L.964,13.7a6.148,6.148,0,0,0,1.251.84,2.6,2.6,0,0,0,1.141.263,1.73,1.73,0,0,0,1.153-.366,1.211,1.211,0,0,0,.432-.989,1.409,1.409,0,0,0-.369-.977,3.9,3.9,0,0,0-.582-.531c-.242-.189-.559-.429-.951-.709-.294-.206-.588-.423-.87-.64A7.031,7.031,0,0,1,1.4,9.878a3.23,3.23,0,0,1-.559-.846A2.594,2.594,0,0,1,.624,7.969,2.768,2.768,0,0,1,.912,6.712a2.905,2.905,0,0,1,.79-.966,3.628,3.628,0,0,1,1.2-.623A5,5,0,0,1,4.433,4.9a6.1,6.1,0,0,1,1.682.229,5.693,5.693,0,0,1,1.51.674L6.755,7.2a3.256,3.256,0,0,0-1.879-.623,1.554,1.554,0,0,0-.991.309.953.953,0,0,0-.392.777,1.115,1.115,0,0,0,.346.8,11.842,11.842,0,0,0,1.193,1c.571.411,1.043.777,1.412,1.086a6.077,6.077,0,0,1,.887.88,2.472,2.472,0,0,1,.45.834,3.276,3.276,0,0,1,.127.943Zm9.883-2.611a7.641,7.641,0,0,1-.138,1.388,4.74,4.74,0,0,1-.461,1.378,3.043,3.043,0,0,1-.853,1.04A2.116,2.116,0,0,1,15,14.811a2.485,2.485,0,0,1-2.126-1.069,4.87,4.87,0,0,1-.778-2.926,5.14,5.14,0,0,1,.79-3.029,2.477,2.477,0,0,1,2.138-1.12,2.357,2.357,0,0,1,2.022,1.052,4.912,4.912,0,0,1,.744,2.874Zm3.123-.109a6.8,6.8,0,0,0-.357-2.257,5.143,5.143,0,0,0-1.02-1.754A4.692,4.692,0,0,0,17.97,5.33a5.164,5.164,0,0,0-4.132.04,5.09,5.09,0,0,0-1.7,1.428l-.017-1.617H9.245V21.8h2.87V14.783a7.408,7.408,0,0,0,.818.823,4.014,4.014,0,0,0,.8.543,3.216,3.216,0,0,0,.853.3,5.315,5.315,0,0,0,3.095-.366,5.03,5.03,0,0,0,1.706-1.275,6.17,6.17,0,0,0,1.124-1.914,6.767,6.767,0,0,0,.409-2.406Zm1.279,5.778h2.95V0h-2.95Zm15.534.006V5.171h-2.95v5.972a8.4,8.4,0,0,1-.069,1.235,3.238,3.238,0,0,1-.225.789,2.3,2.3,0,0,1-2.293,1.378,1.9,1.9,0,0,1-1.821-.932,2.36,2.36,0,0,1-.282-.8,7.968,7.968,0,0,1-.081-1.292V5.171h-2.95v6.306c0,.429.006.794.012,1.086a7.541,7.541,0,0,0,.058.789c.029.229.063.434.1.612a2.2,2.2,0,0,0,.167.509A2.993,2.993,0,0,0,28.7,16.015a4.4,4.4,0,0,0,2.224.52,4.973,4.973,0,0,0,2.086-.411,5.473,5.473,0,0,0,1.746-1.343l.006,1.486,2.962.006Zm12.715-.006V9.969c0-.429-.006-.794-.012-1.1a6,6,0,0,0-.058-.789,5.329,5.329,0,0,0-.109-.583q-.069-.257-.156-.5A3.1,3.1,0,0,0,48.8,5.465a4.288,4.288,0,0,0-2.23-.543,4.973,4.973,0,0,0-2.086.411,5.552,5.552,0,0,0-1.746,1.343l-.006-1.486H39.764V16.264h2.974V10.287A9.321,9.321,0,0,1,42.8,9.081a3.182,3.182,0,0,1,.213-.812A2.058,2.058,0,0,1,43.9,7.263a2.806,2.806,0,0,1,1.423-.343,1.9,1.9,0,0,1,1.821.932,2.5,2.5,0,0,1,.277.794A7.891,7.891,0,0,1,47.5,9.932V16.25l2.945.006Zm12.207-.732L58.156,9.993l3.8-4.034L59.717,5,55.764,9.548h-.311V0H52.479V16.266h2.973V10.374L59.9,16.486l2.748-.955Zm11.492-3.977V9.742L65.2,5.3V7.289l6.929,3.343L65.2,14.021V15.97l8.941-4.417ZM66.069,2.418a1,1,0,1,0,.991.995A.993.993,0,0,0,66.069,2.418Zm.006.154a.822.822,0,0,1,.8.846.8.8,0,1,1-1.608-.005A.823.823,0,0,1,66.075,2.573Zm-.19.943h.12a.209.209,0,0,1,.242.194.874.874,0,0,0,.075.28h.19a.817.817,0,0,1-.069-.274c-.023-.166-.087-.251-.19-.269V3.436a.277.277,0,0,0,.219-.269.258.258,0,0,0-.1-.229.572.572,0,0,0-.346-.086,1.615,1.615,0,0,0-.317.029V3.99h.179V3.516Zm0-.514a.635.635,0,0,1,.127-.011c.2,0,.271.1.271.194,0,.137-.127.189-.271.189h-.127V3Z"
          transform="translate(-0.001 0.002)"
          fill="currentColor"
          opacity="0.5"
        />
      </svg>
    </SvgIcon>
  );
};

export const VMToolsIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '6.2rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="127" height="32" viewBox="0 0 127 32">
        <g id="Group_172758" data-name="Group 172758" transform="translate(-12360 8241)">
          <rect
            id="Rectangle_147705"
            data-name="Rectangle 147705"
            width="32"
            height="32"
            transform="translate(12360 -8241)"
            fill="#fff"
            opacity="0"
          />
          <g id="noun-vm-5507643" transform="translate(12355.707 -8249.965)">
            <path
              id="Path_83364"
              data-name="Path 83364"
              d="M37.694,41.956V39.015l2.932,1.705a.793.793,0,0,0,.793,0l2.932-1.7v2.94l-3.328,1.922Z"
              transform="translate(-20.729 -17.321)"
              fill="currentColor"
            />
            <path
              id="Path_83365"
              data-name="Path 83365"
              d="M42.853,29.752l2.526,1.458L42.853,32.68l-2.525-1.47Z"
              transform="translate(-22.56 -10.882)"
              fill="currentColor"
            />
            <path
              id="Path_83366"
              data-name="Path 83366"
              d="M7.871,17.328A3.232,3.232,0,0,1,11.1,14.1H29.482a3.232,3.232,0,0,1,3.232,3.232V28.347a3.232,3.232,0,0,1-3.232,3.232h-8.4v2.67h4.8a.793.793,0,1,1,0,1.586H14.7a.793.793,0,1,1,0-1.586h4.8v-2.67H11.1a3.232,3.232,0,0,1-3.232-3.232Zm12.026-.06a.793.793,0,0,1,.793,0l4.121,2.379a.793.793,0,0,1,.4.687v4.759a.793.793,0,0,1-.4.687L20.69,28.159a.793.793,0,0,1-.793,0l-4.121-2.379a.793.793,0,0,1-.4-.687V20.334a.793.793,0,0,1,.4-.687Z"
              fill="currentColor"
              fillRule="evenodd"
            />
          </g>
          <text
            id="VM_Tools"
            data-name="VM Tools"
            transform="translate(12400 -8218)"
            fill="currentColor"
            fontSize="20"
            fontFamily="Gotham-Bold, Gotham"
            fontWeight="700"
            letterSpacing="-0.04em"
          >
            <tspan x="0" y="0">
              VM Tools
            </tspan>
          </text>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const GraniteIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '4.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="89.699" height="16.672" viewBox="0 0 89.699 16.672">
        <g id="Layer_1" data-name="Layer 1" transform="translate(0 0.005)">
          <g id="Group_172616" data-name="Group 172616" transform="translate(0 -0.005)">
            <path
              id="Path_83292"
              data-name="Path 83292"
              d="M436.332,6.618V.452h2.489a.519.519,0,0,1,.046.046V12.024c-.112,0-.232.023-.321-.061l-7.011-6.29v6.046H429V.146c.088.005.186-.024.261.029l6.888,6.337.183.107Z"
              transform="translate(-374.374 -0.124)"
              fill="currentColor"
            />
            <path
              id="Path_83293"
              data-name="Path 83293"
              d="M8.493,3.883c0-.213,0-.428,0-.642H7.821V2.57H9.135v.642h1.284V2.57h.672V3.883h-.642V5.165h.642v.672h-.642v.642h.642v.672h-.626a.519.519,0,0,1-.046-.046v-.6H9.807V7.792h.642v.642a4.894,4.894,0,0,0-.6,0c-.025.005-.076.024-.076.046v.6h.672V8.434c.437-.005.876,0,1.314,0v.642h.611V7.793h.672V9.717h.642V9.075h.672v.672h-.642V11.03h.611v-.642H15.6a.519.519,0,0,0,.046-.046v-.6H15V7.792h.642V9.074h.626a.519.519,0,0,1,.046.046v1.893a.519.519,0,0,1-.046.046h-.626v.6a.519.519,0,0,0,.046.046h.626v1.282h-.672v-.642h-.672V11.059h-.611v1.924H15v1.282h.626a.519.519,0,0,1,.046.046v.626h.642v.672H15v.611h1.268a.519.519,0,0,1,.046.046v1.3h-.672v-.642H15v.642h-.642v.611H15v.672H14.33v-.642H13.047v.642H11.733v-.642H10.418v-.642H9.807v1.282H7.821v-.642H6.553a.519.519,0,0,1-.046-.046v-.6H5.9v1.282H5.223V15.578H6.507V14.311a.519.519,0,0,1,.046-.046h.611a.518.518,0,0,1,.046.046v.626h.642v.642H9.777v-.611H9.135V14.3H7.821v-.642H7.179v-.642H6.537v.642H5.9v1.282H5.223V12.982h.642V12.34h.642v-.626a.519.519,0,0,1,.046-.046H8.493v.672H7.851v.642H9.135V11.669h1.284v-.611h-2.6V9.715h.672v.672H10.42V9.745H9.136V8.463c0-.031-.02-.028-.045-.032-.171-.022-.415,0-.6,0V7.789h.642V6.477h.642V5.835h.642V5.193H9.136v.642H8.494V7.117H7.822V6.506a1.367,1.367,0,0,0-.642,0c.031.152-.041.486,0,.611.01.031.013.028.045.032a5.734,5.734,0,0,0,.628,0V8.431h.611a.04.04,0,0,0,.031.031c-.029.152.041.488,0,.611H7.822c-.042-.127.042-.484,0-.611-.01-.031-.013-.028-.045-.032-.171-.022-.415,0-.6,0V7.788H6.539V8.43c.214,0,.428,0,.642,0V8.46c0,.427,0,.856,0,1.282H6.539V8.46H5.9V9.743H3.91V9.1H3.3v1.282H4.583v.672H2.627v.611h.642v1.985H2.627v-.626a.519.519,0,0,0-.046-.046H1.313v-.642H2.627V11.7H1.955v-.672h.672v-.642H1.955V9.1H.672V8.458H.046A.519.519,0,0,1,0,8.412V7.786H1.986v.642H3.912V7.786H5.868V7.144H5.226V5.831h.642V5.189H5.226V4.517H7.182v.672H6.54V6.472h.642c.008-.2-.01-.409,0-.611.16-.084,1.285.024,1.314-.061.041-.12-.031-.462,0-.611H7.823V3.875H8.5c0,.213,0,.428,0,.642H9.779V3.875c-.428,0-.856,0-1.284,0ZM6.539,7.12v-.6a.519.519,0,0,0-.046-.046H5.9V7.12ZM5.225,8.432H3.941v.642H5.225Zm6.507,1.313H11.09v.642h-.642v.642h.642v.642h.642v.672h-.6a.519.519,0,0,0-.046.046v.6h2.6v-.6a.519.519,0,0,0-.046-.046h-.6v-.6A.519.519,0,0,0,13,11.7h-.626v-.642H11.73V9.747Zm1.284,0H12.4v1.282h.611Zm.672,1.313h-.642v.611h.642ZM10.418,12.34H9.807v1.282h.611Zm3.91.671h-.611v.611h.611Zm-4.552.642H9.135v.6a.519.519,0,0,0,.046.046h.6Zm3.269,0H11.09v1.924h1.956Zm-2.627.64H9.807v.642h.611ZM15,14.935h-.642v.642H15Zm-7.822.672H6.537v.642H5.9v.642H7.179V15.609Zm1.314.64H7.851v.642H7.225a.519.519,0,0,0-.046.046v.6h.672v.642H9.135v-.611H8.493V16.25Zm2.6.642c0-.213,0-.428,0-.642H9.181a.519.519,0,0,0-.046.046v.6c.651,0,1.3,0,1.956,0,0,.213,0,.428,0,.642h.626a.519.519,0,0,1,.046.046V18.2h.611V16.891c-.428,0-.856,0-1.284,0Zm1.955-.642H12.4v.642h.642Zm1.284,0h-.611v.642h-.626a.519.519,0,0,0-.046.046v.6h1.284V16.25Z"
              transform="translate(0 -2.242)"
              fill="currentColor"
            />
            <path
              id="Path_83294"
              data-name="Path 83294"
              d="M168.311,7.1h-2.871V5.268h5.225V9.5a7.279,7.279,0,0,1-.642.7,6.018,6.018,0,0,1-10.174-4.855A6.085,6.085,0,0,1,168.87.756a5.2,5.2,0,0,1,1.087.809c.028.107-1.147,1.585-1.264,1.812l-.031.025a5.012,5.012,0,0,0-1.818-.928c-2.957-.733-5.045,1.882-4.225,4.68a3.5,3.5,0,0,0,5.691,1.58V7.1Z"
              transform="translate(-139.469 0.005)"
              fill="currentColor"
            />
            <path
              id="Path_83295"
              data-name="Path 83295"
              d="M265.442,13.719h-2.856l-2.139-3.481h-1.36v3.481h-2.6V2.5c0-.025.074-.053.106-.047,2.488.15,5.912-.632,7.685,1.606a3.716,3.716,0,0,1-1.343,5.65l2.5,4.014Zm-6.355-5.5a5.442,5.442,0,0,0,2.394-.279,1.819,1.819,0,0,0,.152-3.1,4.83,4.83,0,0,0-2.545-.372Z"
              transform="translate(-223.83 -2.124)"
              fill="currentColor"
            />
            <path
              id="Path_83296"
              data-name="Path 83296"
              d="M650.867,2.57v2.2h-5.041V7.424h4.4V9.44h-4.4v2.2h5.224v2.2h-7.79V2.57Z"
              transform="translate(-561.351 -2.242)"
              fill="currentColor"
            />
            <path
              id="Path_83297"
              data-name="Path 83297"
              d="M344.063,11.716H341.39c-.267-.5-.429-1.065-.688-1.573l-4.564-.011-.707,1.584H332.79L338.333.141c.073.006.159-.02.215.032l5.514,11.542Zm-4.155-3.542L338.441,4.6l-1.527,3.572Z"
              transform="translate(-290.415 -0.121)"
              fill="currentColor"
            />
            <path
              id="Path_83298"
              data-name="Path 83298"
              d="M566.142,2.57v2.2H562.69v9.069h-2.6V4.768H556.64V2.57Z"
              transform="translate(-485.761 -2.242)"
              fill="currentColor"
            />
            <rect
              id="Rectangle_147667"
              data-name="Rectangle 147667"
              width="2.566"
              height="11.267"
              transform="translate(66.388 0.328)"
              fill="currentColor"
            />
            <path
              id="Path_83299"
              data-name="Path 83299"
              d="M96.713,7.149H92.13V2.57h4.537a.519.519,0,0,1,.046.046V7.15ZM96.04,3.241H92.8V6.478H96.04Z"
              transform="translate(-80.399 -2.242)"
              fill="currentColor"
            />
            <path
              id="Path_83300"
              data-name="Path 83300"
              d="M4.792,2.57V7.15H.24V2.57Zm-.642.671H.882V6.478H4.15Z"
              transform="translate(-0.209 -2.242)"
              fill="currentColor"
            />
            <path
              id="Path_83301"
              data-name="Path 83301"
              d="M4.792,94.64v4.55H.24V94.64Zm-.642.642H.882v3.267H4.15V95.282Z"
              transform="translate(-0.209 -82.588)"
              fill="currentColor"
            />
            <path
              id="Path_83302"
              data-name="Path 83302"
              d="M164.219,112.133l.474,1.5.549-1.5.32.005.507,1.462.474-1.466h.581l-.87,2.323c-.094-.02-.344.034-.4-.032l-.443-1.222c-.078-.013-.047.006-.064.043-.084.185-.359,1.108-.439,1.179s-.317.032-.414.015l-.854-2.306h.581Z"
              transform="translate(-142.803 -97.851)"
              fill="currentColor"
            />
            <path
              id="Path_83303"
              data-name="Path 83303"
              d="M370.095,112.073c.056.018.564.8.688.885a.124.124,0,0,1,.031-.075,9.393,9.393,0,0,1,.626-.81h.55a.518.518,0,0,1,.046.046v2.274h-.5a.518.518,0,0,1-.046-.046v-1.511l-.718.857-.09-.033c-.2-.247-.382-.545-.587-.784-.019-.022,0-.057-.07-.038v1.557h-.52v-2.32c.149.029.472-.039.6,0Z"
              transform="translate(-322.45 -97.791)"
              fill="currentColor"
            />
            <path
              id="Path_83304"
              data-name="Path 83304"
              d="M683.925,112.073c.053.017.642.918.688.885a.124.124,0,0,1,.031-.075,9.375,9.375,0,0,1,.626-.81h.55a.519.519,0,0,1,.046.046v2.274h-.5a.518.518,0,0,1-.046-.046v-1.511c-.122.1-.632.848-.718.857a.09.09,0,0,1-.09-.033c-.2-.247-.382-.545-.587-.784-.019-.022,0-.057-.07-.038v1.557h-.519v-2.32c.149.029.472-.039.6,0Z"
              transform="translate(-596.319 -97.791)"
              fill="currentColor"
            />
            <path
              id="Path_83305"
              data-name="Path 83305"
              d="M497.985,112.073c.053.017.642.918.688.885a.124.124,0,0,1,.031-.075,9.387,9.387,0,0,1,.626-.81h.55a.518.518,0,0,1,.046.046v2.274h-.5a.518.518,0,0,1-.046-.046v-1.511c-.122.1-.632.848-.718.857a.09.09,0,0,1-.09-.033c-.2-.247-.382-.545-.587-.784-.019-.022,0-.057-.07-.038v1.557h-.519v-2.32c.149.029.472-.039.6,0Z"
              transform="translate(-434.055 -97.791)"
              fill="currentColor"
            />
            <path
              id="Path_83306"
              data-name="Path 83306"
              d="M541,112.086c.1.028.95,1.3,1.146,1.435l-.031-1.435h.55v2.274c0,.067-.415.064-.476.018l-1.113-1.407c-.042.429.081.907.032,1.329,0,.031-.014.106-.047.106h-.5v-2.32C540.681,112.108,540.9,112.057,541,112.086Z"
              transform="translate(-471.728 -97.805)"
              fill="currentColor"
            />
            <path
              id="Path_83307"
              data-name="Path 83307"
              d="M416.031,112.152c.023-.073.4-.061.475-.017l1.143,1.436-.031-1.466h.52v2.32H417.7c-.386-.474-.732-.988-1.146-1.435l.031,1.435h-.5a.518.518,0,0,1-.046-.046v-2.228Z"
              transform="translate(-363.055 -97.825)"
              fill="currentColor"
            />
            <path
              id="Path_83308"
              data-name="Path 83308"
              d="M274.366,111.694a1.223,1.223,0,1,1,.144,2.436A1.223,1.223,0,0,1,274.366,111.694Zm-.062.486a.751.751,0,0,0,.056,1.455A.744.744,0,1,0,274.3,112.181Z"
              transform="translate(-238.444 -97.465)"
              fill="currentColor"
            />
            <path
              id="Path_83309"
              data-name="Path 83309"
              d="M216.017,112.05a.779.779,0,0,1,.168,1.511c.163.21.378.418.532.629.039.055.089.1.079.181h-.581a.142.142,0,0,0-.029-.075c-.192-.209-.354-.456-.526-.681l-.36-.006v.717a.521.521,0,0,1-.046.046H214.8a.518.518,0,0,1-.046-.046V112.1c0-.009.036-.031.046-.046.381.037.853-.05,1.222,0Zm-.749.458v.626a.517.517,0,0,0,.046.046h.611a.359.359,0,0,0,.284-.3c.01-.11-.106-.374-.223-.374h-.718Z"
              transform="translate(-187.405 -97.769)"
              fill="currentColor"
            />
            <path
              id="Path_83310"
              data-name="Path 83310"
              d="M459.41,113.012h-.55v-.428h1.008v.992c0,.124-.39.344-.506.379a1.234,1.234,0,0,1-1.6-1.625,1.218,1.218,0,0,1,2.007-.4,1.333,1.333,0,0,1-.293.312c-.031.006-.25-.138-.351-.159a.736.736,0,1,0-.024,1.433c.331-.064.325-.2.3-.506Z"
              transform="translate(-399.406 -97.357)"
              fill="currentColor"
            />
            <path
              id="Path_83311"
              data-name="Path 83311"
              d="M253.44,113.066h1.008v-.916h.52v2.32h-.52v-.916H253.44v.916h-.5a.518.518,0,0,1-.046-.046V112.2a.519.519,0,0,1,.046-.046h.458a.52.52,0,0,1,.046.046Z"
              transform="translate(-220.688 -97.869)"
              fill="currentColor"
            />
            <path
              id="Path_83312"
              data-name="Path 83312"
              d="M522.849,112.656v.428h1.131v.458h-1.131V114H524.1v.489h-1.727a.518.518,0,0,1-.046-.046V112.17h1.7c.069,0,.069.489,0,.489h-1.177Z"
              transform="translate(-455.82 -97.886)"
              fill="currentColor"
            />
            <path
              id="Path_83313"
              data-name="Path 83313"
              d="M480.851,112.15v.489H479.66v.427h1.131v.458H479.66v.458h1.253v.488H479.14v-2.32Z"
              transform="translate(-418.129 -97.869)"
              fill="currentColor"
            />
            <path
              id="Path_83314"
              data-name="Path 83314"
              d="M338.091,112.15v.489H336.9v.427h1.131v.458H336.9v.458h1.253v.488H336.38v-2.32Z"
              transform="translate(-293.547 -97.869)"
              fill="currentColor"
            />
            <path
              id="Path_83315"
              data-name="Path 83315"
              d="M666.8,112.15v.489H665.61v.427h1.131v.458H665.61v.458h1.253v.488H665.09v-2.32Z"
              transform="translate(-580.401 -97.869)"
              fill="currentColor"
            />
            <path
              id="Path_83316"
              data-name="Path 83316"
              d="M236.6,112.15v.489H235.41v.428h1.085c.078,0,.036.383.046.458H235.41v.458h1.222v.489H234.89v-2.32H236.6Z"
              transform="translate(-204.98 -97.869)"
              fill="currentColor"
            />
            <path
              id="Path_83317"
              data-name="Path 83317"
              d="M438.338,114.41h-.581a1.169,1.169,0,0,0-.172-.393c-.335.023-.742-.045-1.067-.006-.181.022-.194.4-.274.4h-.535c.313-.69.587-1.407.921-2.087a1.78,1.78,0,0,1,.1-.2,1.17,1.17,0,0,1,.582,0l1.022,2.291Zm-1.681-.854h.733a5.263,5.263,0,0,0-.292-.761c-.018-.039-.032-.14-.089-.124a5.364,5.364,0,0,0-.337.763.289.289,0,0,0-.014.124Z"
              transform="translate(-380.229 -97.809)"
              fill="currentColor"
            />
            <path
              id="Path_83318"
              data-name="Path 83318"
              d="M395.126,114.436h-.55c-.048-.089-.1-.312-.149-.378s-1.021,0-1.174-.02l-.188.4h-.535l1.053-2.323c.112.024.493-.038.551.033l.993,2.291Zm-.917-.854-.382-.917-.382.917Z"
              transform="translate(-342.547 -97.835)"
              fill="currentColor"
            />
            <path
              id="Path_83319"
              data-name="Path 83319"
              d="M191.23,114.453l1.053-2.323.531.008,1.014,2.315h-.535l-.188-.4c-.153.022-1.135-.032-1.174.02s-.1.288-.149.378h-.55Zm1.68-.854-.382-.917-.382.917Z"
              transform="translate(-166.88 -97.851)"
              fill="currentColor"
            />
            <path
              id="Path_83320"
              data-name="Path 83320"
              d="M593.175,111.486a1.2,1.2,0,0,1,.767.224c.276.22-.064.247-.2.439-.165-.244-.969-.309-.932.074.025.256.53.232.733.278a.7.7,0,0,1,.628.872c-.148.768-1.636.755-1.965.106l.313-.32a.922.922,0,0,0,.933.29c.22-.066.306-.329.089-.457-.332-.2-1.051.014-1.219-.553s.323-.937.849-.952Z"
              transform="translate(-516.801 -97.289)"
              fill="currentColor"
            />
            <path
              id="Path_83321"
              data-name="Path 83321"
              d="M629.356,112.421c.187.186.9.015,1.2.438a.774.774,0,0,1,0,.82,1.324,1.324,0,0,1-1.741.033c-.238-.241.038-.3.185-.489.154.284.993.474,1.129.152.186-.438-.556-.388-.8-.452-.8-.214-.708-1.151.079-1.334.3-.07.958-.022,1.118.292.033.065-.218.292-.284.3a3.246,3.246,0,0,0-.516-.176c-.223,0-.606.181-.37.415Z"
              transform="translate(-548.661 -97.354)"
              fill="currentColor"
            />
            <path
              id="Path_83322"
              data-name="Path 83322"
              d="M317.94,111.481a2.249,2.249,0,0,1,.349.031c.134.027.556.2.507.368a1.4,1.4,0,0,1-.285.236c-.024,0-.223-.122-.315-.145-.154-.038-.514-.051-.595.121-.185.392.514.354.737.41.932.232.767,1.359-.195,1.445a1.258,1.258,0,0,1-1.174-.447l.293-.345a1.031,1.031,0,0,0,.725.322c.52-.013.639-.451.113-.555-.367-.073-.928-.047-1.028-.531-.12-.586.335-.9.867-.91Z"
              transform="translate(-276.609 -97.284)"
              fill="currentColor"
            />
            <path
              id="Path_83323"
              data-name="Path 83323"
              d="M297.31,112.15v1.42a.694.694,0,0,0,.213.367.556.556,0,0,0,.778-.11c.013-.024.079-.215.079-.227v-1.45h.52v1.511a1,1,0,0,1-1.054.872,1.049,1.049,0,0,1-1.054-.9V112.15Z"
              transform="translate(-258.999 -97.869)"
              fill="currentColor"
            />
            <path
              id="Path_83324"
              data-name="Path 83324"
              d="M609.791,112.15l.535.885c.116-.084.527-.885.581-.885h.6c.008.059-.036.093-.061.138-.106.182-.856,1.175-.856,1.252v.931h-.52v-.9c0-.069-.651-.993-.746-1.147a.938.938,0,0,1-.14-.272Z"
              transform="translate(-531.611 -97.869)"
              fill="currentColor"
            />
            <path
              id="Path_83325"
              data-name="Path 83325"
              d="M648.3,112.15v.489h-.7v1.832h-.52v-1.832h-.657a.518.518,0,0,1-.046-.046v-.443Z"
              transform="translate(-564.065 -97.869)"
              fill="currentColor"
            />
            <path
              id="Path_83326"
              data-name="Path 83326"
              d="M562.645,112.608h-.7v1.817a.515.515,0,0,1-.046.046h-.474v-1.817a.522.522,0,0,0-.046-.046h-.657v-.458H562.6C562.677,112.15,562.635,112.533,562.645,112.608Z"
              transform="translate(-489.321 -97.869)"
              fill="currentColor"
            />
            <rect
              id="Rectangle_147668"
              data-name="Rectangle 147668"
              width="0.642"
              height="2.626"
              transform="translate(0.031 8.785)"
              fill="currentColor"
            />
            <path
              id="Path_83327"
              data-name="Path 83327"
              d="M42.976,63.95v.672h-1.91a.519.519,0,0,1-.046-.046V63.95Z"
              transform="translate(-35.797 -55.806)"
              fill="currentColor"
            />
            <rect
              id="Rectangle_147669"
              data-name="Rectangle 147669"
              width="0.642"
              height="1.313"
              transform="translate(6.539 0.328)"
              fill="currentColor"
            />
            <rect
              id="Rectangle_147670"
              data-name="Rectangle 147670"
              width="1.284"
              height="0.642"
              transform="translate(3.941 9.457)"
              fill="currentColor"
            />
            <rect
              id="Rectangle_147671"
              data-name="Rectangle 147671"
              width="1.284"
              height="0.642"
              transform="translate(0.031 7.503)"
              fill="currentColor"
            />
            <path
              id="Path_83328"
              data-name="Path 83328"
              d="M41.7,8.272H41.03V7.6h.626a.519.519,0,0,1,.046.046Z"
              transform="translate(-35.805 -6.632)"
              fill="currentColor"
            />
            <path
              id="Path_83329"
              data-name="Path 83329"
              d="M31.382,84.33V85h-.626a.519.519,0,0,1-.046-.046v-.581a.519.519,0,0,1,.046-.046Z"
              transform="translate(-26.8 -73.591)"
              fill="currentColor"
            />
            <rect
              id="Rectangle_147672"
              data-name="Rectangle 147672"
              width="0.672"
              height="0.642"
              transform="translate(13.687 5.549)"
              fill="currentColor"
            />
            <path
              id="Path_83330"
              data-name="Path 83330"
              d="M57.69,27.98c-.029.085-1.152-.023-1.314.061,0-.02,0-.041,0-.061Z"
              transform="translate(-49.197 -24.417)"
              fill="currentColor"
            />
            <path
              id="Path_83331"
              data-name="Path 83331"
              d="M67.132,48.573c-.208-.014-.438.019-.642,0a.041.041,0,0,1-.031-.031h.031a5.5,5.5,0,0,1,.6,0c.024,0,.046,0,.045.032Z"
              transform="translate(-57.997 -42.352)"
              fill="currentColor"
            />
            <path
              id="Path_83332"
              data-name="Path 83332"
              d="M62.092,53.64c-.015.045-.041.028-.075.032a3.64,3.64,0,0,1-.553,0c-.031,0-.034,0-.045-.032Z"
              transform="translate(-53.599 -46.809)"
              fill="currentColor"
            />
            <path
              id="Path_83333"
              data-name="Path 83333"
              d="M57.022,48.574H56.38v-.031a5.838,5.838,0,0,1,.6,0c.031,0,.034,0,.045.032Z"
              transform="translate(-49.201 -42.354)"
              fill="currentColor"
            />
            <path
              id="Path_83334"
              data-name="Path 83334"
              d="M57.022,38.3h.672c0,.031-.02.028-.045.032a5.839,5.839,0,0,1-.6,0,5.435,5.435,0,0,1-.628,0c-.031,0-.034,0-.045-.032Z"
              transform="translate(-49.201 -33.422)"
              fill="currentColor"
            />
            <path
              id="Path_83335"
              data-name="Path 83335"
              d="M57.022,33.238H56.38A1.367,1.367,0,0,1,57.022,33.238Z"
              transform="translate(-49.201 -28.972)"
              fill="currentColor"
            />
            <rect
              id="Rectangle_147673"
              data-name="Rectangle 147673"
              width="0.672"
              height="0.642"
              transform="translate(11.731 12.052)"
              fill="currentColor"
            />
            <rect
              id="Rectangle_147674"
              data-name="Rectangle 147674"
              width="1.956"
              height="1.955"
              transform="translate(13.045 1.641)"
              fill="currentColor"
            />
            <path
              id="Path_83336"
              data-name="Path 83336"
              d="M12.266,12.88v1.955H10.31V12.926a.519.519,0,0,1,.046-.046Z"
              transform="translate(-8.997 -11.239)"
              fill="currentColor"
            />
            <path
              id="Path_83337"
              data-name="Path 83337"
              d="M12.266,104.96v1.955h-1.91a.518.518,0,0,1-.046-.046V104.96Z"
              transform="translate(-8.997 -91.594)"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const UploadIPIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="28.347" height="32.301" viewBox="0 0 28.347 32.301">
        <g id="Group_14577" data-name="Group 14577">
          <rect id="Rectangle_3634" data-name="Rectangle 3634" width="28.347" height="32.301" transform="translate(0 -0.011)" fill="none" />
          <path
            id="Path_10720"
            data-name="Path 10720"
            d="M79.393,7.555,72.719.912A3.744,3.744,0,0,0,70.279-.1H59.145a3.033,3.033,0,0,0-3.034,3.026v10.1a1.015,1.015,0,0,0,2.03,0V2.926a1.012,1.012,0,0,1,1.015-1.012H68.27A2.025,2.025,0,0,1,70.3,3.938v3.79A2.283,2.283,0,0,0,72.579,10h3.8a2.025,2.025,0,0,1,2.03,2.024V29.2A1.012,1.012,0,0,1,77.4,30.209H59.145A1.012,1.012,0,0,1,58.13,29.2a1.015,1.015,0,0,0-2.03,0,3.033,3.033,0,0,0,3.034,3.026H77.363A3.033,3.033,0,0,0,80.4,29.2V10.01A3.743,3.743,0,0,0,79.393,7.555Z"
            transform="translate(-52.072 0.089)"
            fill="currentColor"
          />
          <rect id="Rectangle_3635" data-name="Rectangle 3635" width="28.347" height="32.301" transform="translate(0 -0.011)" fill="none" />
          <path
            id="Path_10721"
            data-name="Path 10721"
            d="M41.893,140.6H19.615a1.012,1.012,0,0,0-1.015,1.012v10.1a1.012,1.012,0,0,0,1.015,1.012H41.882a1.012,1.012,0,0,0,1.015-1.012v-10.1A.99.99,0,0,0,41.893,140.6Zm-18.7,7.763,1.253-1.82-1.058-1.626a4.28,4.28,0,0,1-.227-.409.739.739,0,0,1-.076-.323.376.376,0,0,1,.14-.291.51.51,0,0,1,.346-.129.458.458,0,0,1,.367.14,5.019,5.019,0,0,1,.367.517l.842,1.357.9-1.357c.076-.118.14-.215.184-.291a1.007,1.007,0,0,1,.151-.2.353.353,0,0,1,.162-.118.633.633,0,0,1,.205-.043.473.473,0,0,1,.346.129.418.418,0,0,1,.14.3,1.383,1.383,0,0,1-.292.689l-1.1,1.647,1.188,1.82a3.762,3.762,0,0,1,.238.4.825.825,0,0,1,.076.3.5.5,0,0,1-.065.248.471.471,0,0,1-.184.172.556.556,0,0,1-.27.065.527.527,0,0,1-.27-.065.66.66,0,0,1-.184-.161l-.259-.388-.983-1.55-1.047,1.593c-.086.129-.14.215-.173.269a1.45,1.45,0,0,1-.119.151.7.7,0,0,1-.173.118.521.521,0,0,1-.238.043.463.463,0,0,1-.486-.5A1.472,1.472,0,0,1,23.189,148.363ZM30,148.6l-.9-3.553V148.9a.7.7,0,0,1-.14.485.523.523,0,0,1-.756,0,.7.7,0,0,1-.14-.485v-4.414a.549.549,0,0,1,.194-.5.923.923,0,0,1,.518-.129h.356a1.548,1.548,0,0,1,.464.054.442.442,0,0,1,.216.2,2.946,2.946,0,0,1,.151.485l.81,3.058.81-3.058a2.947,2.947,0,0,1,.151-.485.367.367,0,0,1,.216-.2,1.548,1.548,0,0,1,.464-.054h.356a.923.923,0,0,1,.518.129.549.549,0,0,1,.194.5V148.9a.7.7,0,0,1-.14.485.5.5,0,0,1-.389.161.468.468,0,0,1-.367-.161.7.7,0,0,1-.14-.485v-3.855l-.9,3.553c-.054.226-.108.4-.14.506a.657.657,0,0,1-.205.291.6.6,0,0,1-.41.129.643.643,0,0,1-.335-.086.686.686,0,0,1-.216-.215,1.173,1.173,0,0,1-.119-.291A.9.9,0,0,1,30,148.6Zm5.734-4.145v4.059h2.3a.616.616,0,0,1,.421.129.441.441,0,0,1,.151.334.416.416,0,0,1-.14.334.616.616,0,0,1-.421.129H35.317a.723.723,0,0,1-.529-.161.735.735,0,0,1-.162-.528v-4.3a.8.8,0,0,1,.151-.517.575.575,0,0,1,.821,0A.867.867,0,0,1,35.738,144.455Z"
            transform="translate(-18.622 -125.462)"
            fill="currentColor"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AddCircleRoundIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '4.5rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
        <path
          id="add_circle_24dp_5F6368_FILL0_wght400_GRAD0_opsz24_1_"
          data-name="add_circle_24dp_5F6368_FILL0_wght400_GRAD0_opsz24 (1)"
          d="M94.4-862.4v4.8a1.548,1.548,0,0,0,.46,1.14A1.548,1.548,0,0,0,96-856a1.548,1.548,0,0,0,1.14-.46,1.548,1.548,0,0,0,.46-1.14v-4.8h4.8a1.547,1.547,0,0,0,1.14-.46A1.547,1.547,0,0,0,104-864a1.548,1.548,0,0,0-.46-1.14,1.548,1.548,0,0,0-1.14-.46H97.6v-4.8a1.549,1.549,0,0,0-.46-1.14A1.548,1.548,0,0,0,96-872a1.548,1.548,0,0,0-1.14.46,1.549,1.549,0,0,0-.46,1.14v4.8H89.6a1.548,1.548,0,0,0-1.14.46A1.548,1.548,0,0,0,88-864a1.547,1.547,0,0,0,.46,1.14,1.547,1.547,0,0,0,1.14.46ZM96-848a15.58,15.58,0,0,1-6.24-1.26,16.156,16.156,0,0,1-5.08-3.42,16.154,16.154,0,0,1-3.42-5.08A15.581,15.581,0,0,1,80-864a15.58,15.58,0,0,1,1.26-6.24,16.164,16.164,0,0,1,3.42-5.08,16.161,16.161,0,0,1,5.08-3.42A15.583,15.583,0,0,1,96-880a15.583,15.583,0,0,1,6.24,1.26,16.162,16.162,0,0,1,5.08,3.42,16.164,16.164,0,0,1,3.42,5.08A15.581,15.581,0,0,1,112-864a15.581,15.581,0,0,1-1.26,6.24,16.154,16.154,0,0,1-3.42,5.08,16.157,16.157,0,0,1-5.08,3.42A15.581,15.581,0,0,1,96-848Zm0-3.2a12.353,12.353,0,0,0,9.08-3.72A12.353,12.353,0,0,0,108.8-864a12.353,12.353,0,0,0-3.72-9.08A12.353,12.353,0,0,0,96-876.8a12.353,12.353,0,0,0-9.08,3.72A12.354,12.354,0,0,0,83.2-864a12.353,12.353,0,0,0,3.72,9.08A12.353,12.353,0,0,0,96-851.2ZM96-864Z"
          transform="translate(-80 880)"
          fill="currentColor"
        />
      </svg>
    </SvgIcon>
  );
};

export const BulkVMImport = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '1.5rem' }}>
      <svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 383.05 425.94">
        <g id="Layer_2-2" data-name="Layer 2">
          <g id="Layer_1-2" data-name="Layer 1-2">
            <g id="DFynrW.tif">
              <g>
                <path
                  d="M0,56.76C8.83,10.96,44.66-4.03,88.28.89c16.35,1.84,23.56,21.71,13.46,34.42-12.94,16.29-40.79-1.34-54.38,15.44-1.12,1.39-4.47,7.24-4.47,8.5v200.03l14.43-3.03h267.38l15.41,3.03V59.26c0-1.26-3.34-7.11-4.47-8.5-14.83-18.32-48.76,4.67-57.84-21.96-3.78-11.1,2.46-25.01,14.38-27.46,6.84-1.41,26.03-1.06,33.51-.48,30.79,2.41,55.76,29.82,57.36,60.35l-.06,306.32c-2.97,30.38-26.73,55.38-57.3,58.41l-269.32-.05c-27.83-2.48-53.39-28.15-56.37-55.85V56.76h0ZM61.61,298.46c-9.56,1.73-17.57,8.05-18.72,18.19s-1.3,41.43.24,50.65c1.81,10.84,12.99,16.71,23.16,16.74h251.43c9.74-.21,20.44-6.56,22.17-16.75,1.32-7.76,1.33-45.72-.2-53.19-1.7-8.28-9.48-13.9-17.46-15.46l-260.61-.18h-.01Z"
                  fill="currentColor"
                />
                <path
                  d="M170.47,144.35V16.15c0-.37,2.98-5.61,3.55-6.42,8.59-12.17,26.2-12.17,34.79,0,.58.82,3.55,6.06,3.55,6.42v128.2c10.14-6.35,18.54-17.25,28.01-24.36,23.97-17.98,48.4,11.46,29.38,31.39-18.35,19.23-43.71,35.88-62.37,55.34-8.92,8.49-20.63,8.99-30.41,1.46-21.86-16.85-41.72-39.88-63.9-56.8-14.15-14.23-4.48-38.2,16.03-37.01,14.59.85,28.95,23.11,41.36,29.98h.01Z"
                  fill="currentColor"
                />
                <path
                  d="M271.03,320.37c4.99-1.01,25.29-.91,30.72-.25,22.64,2.77,22.75,38.45.91,41.87-5.53.87-24.15.79-29.89.09-22.11-2.7-23.02-37.38-1.75-41.7h0Z"
                  fill="currentColor"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const UsageMetricsViewIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '1.5rem' }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="23.935" viewBox="0 0 24 23.935" fill="currentColor">
        <g id="noun-analytics-221489" transform="translate(-13 -16.8)">
          <g id="Group_172654" data-name="Group 172654" transform="translate(13 16.8)">
            <g id="Group_172653" data-name="Group 172653" transform="translate(0)">
              <g id="Group_172652" data-name="Group 172652">
                <g id="Group_172646" data-name="Group 172646" transform="translate(12.973)">
                  <path
                    id="Path_83373"
                    data-name="Path 83373"
                    d="M64.027,28.768a12.4,12.4,0,0,1-.357,2.919L59.13,30.162a6.461,6.461,0,0,0,.13-1.362A7.217,7.217,0,0,0,53,21.632V16.8A11.967,11.967,0,0,1,64.027,28.768Z"
                    transform="translate(-53 -16.8)"
                    fill="currentColor"
                  />
                </g>
                <g id="Group_172647" data-name="Group 172647" transform="translate(12.973 15.178)">
                  <path
                    id="Path_83374"
                    data-name="Path 83374"
                    d="M58.514,63.6l4.541,1.524A11.994,11.994,0,0,1,53,72.324V67.557A7.209,7.209,0,0,0,58.514,63.6Z"
                    transform="translate(-53 -63.6)"
                    fill="currentColor"
                  />
                </g>
                <g id="Group_172648" data-name="Group 172648" transform="translate(5.319 18.195)">
                  <path
                    id="Path_83375"
                    data-name="Path 83375"
                    d="M35.108,73.841v4.8A11.919,11.919,0,0,1,29.4,76.662L32.384,72.9A7.768,7.768,0,0,0,35.108,73.841Z"
                    transform="translate(-29.4 -72.9)"
                    fill="currentColor"
                  />
                </g>
                <g id="Group_172649" data-name="Group 172649" transform="translate(5.741)">
                  <path
                    id="Path_83376"
                    data-name="Path 83376"
                    d="M35.986,16.8v4.8a7.121,7.121,0,0,0-2.465.811L30.7,18.551A11.018,11.018,0,0,1,35.986,16.8Z"
                    transform="translate(-30.7 -16.8)"
                    fill="currentColor"
                  />
                </g>
                <g id="Group_172650" data-name="Group 172650" transform="translate(0.973 2.886)">
                  <path
                    id="Path_83377"
                    data-name="Path 83377"
                    d="M22,29.559a6.96,6.96,0,0,0-1.459,2.011L16,30.014A11.685,11.685,0,0,1,19.178,25.7Z"
                    transform="translate(-16 -25.7)"
                    fill="currentColor"
                  />
                </g>
                <g id="Group_172651" data-name="Group 172651" transform="translate(0 9.049)">
                  <path
                    id="Path_83378"
                    data-name="Path 83378"
                    d="M19.778,52.614l-2.984,3.762A12.023,12.023,0,0,1,13,47.619a12.4,12.4,0,0,1,.357-2.919L17.9,46.224a6.461,6.461,0,0,0-.13,1.362A7.11,7.11,0,0,0,19.778,52.614Z"
                    transform="translate(-13 -44.7)"
                    fill="currentColor"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const OnboardNewGroup = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '2rem' }}>
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169249">
            <g id="Group_169241-5">
              <rect id="Rectangle_22041-35" width="32" height="32" fill="none" />
              <g>
                <path
                  d="M3.37,23.95c0-.52.13-.98.4-1.38s.62-.72,1.07-.94c.95-.46,1.91-.82,2.87-1.08.96-.25,2.02-.38,3.17-.38,1.15,0,2.21.13,3.17.38.96.25,1.92.61,2.87,1.08.44.22.8.54,1.07.94s.4.86.4,1.38v.7c0,.4-.15.76-.44,1.06s-.65.46-1.08.46H4.89c-.42,0-.78-.15-1.08-.44-.3-.3-.44-.65-.44-1.08,0,0,0-.7,0-.7ZM22.25,26.17h-2.27c.13-.23.22-.47.29-.73s.1-.52.1-.79v-.83c0-.66-.16-1.28-.48-1.88-.32-.59-.78-1.1-1.37-1.53.67.1,1.31.25,1.91.46.6.21,1.18.46,1.73.74.52.28.92.6,1.2.97.28.37.42.78.42,1.23v.83c0,.42-.15.78-.44,1.08-.3.3-.65.44-1.08.44ZM10.87,18.55c-.96,0-1.79-.34-2.47-1.03-.69-.69-1.03-1.51-1.03-2.47,0-.96.34-1.79,1.03-2.47.69-.69,1.51-1.03,2.47-1.03s1.79.34,2.47,1.03c.69.69,1.03,1.51,1.03,2.47s-.34,1.79-1.03,2.47c-.69.69-1.51,1.03-2.47,1.03ZM19.5,15.05c0,.96-.34,1.79-1.03,2.47-.69.69-1.51,1.03-2.47,1.03-.11,0-.26-.01-.43-.04-.17-.03-.32-.05-.43-.08.39-.47.7-1,.91-1.58.21-.58.32-1.18.32-1.8s-.11-1.22-.32-1.79c-.22-.57-.52-1.1-.9-1.58.14-.05.29-.08.43-.1.14-.02.29-.02.43-.02.96,0,1.79.34,2.47,1.03.69.69,1.03,1.51,1.03,2.47ZM4.87,24.67h12v-.72c0-.21-.05-.39-.16-.56-.1-.16-.27-.31-.5-.43-.82-.42-1.67-.75-2.54-.96-.87-.22-1.81-.33-2.8-.33s-1.93.11-2.8.33c-.87.22-1.72.54-2.54.96-.23.12-.39.26-.5.43-.1.16-.16.35-.16.56,0,0,0,.72,0,.72ZM10.87,17.05c.55,0,1.02-.2,1.41-.59.39-.39.59-.86.59-1.41,0-.55-.2-1.02-.59-1.41-.39-.39-.86-.59-1.41-.59s-1.02.2-1.41.59c-.39.39-.59.86-.59,1.41s.2,1.02.59,1.41c.39.39.86.59,1.41.59Z"
                  fill="currentColor"
                />
                <g id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24-3">
                  <path
                    d="M28.63,9.17c0,.18-.07.36-.2.48-.12.12-.29.2-.47.2h-2v2c0,.18-.07.35-.2.47-.12.12-.29.2-.47.2s-.35-.08-.48-.21c-.12-.11-.19-.28-.19-.46v-2h-2.01c-.18.01-.35-.08-.47-.21-.13-.11-.2-.29-.2-.47s.07-.35.2-.47c.12-.13.3-.2.48-.2h1.99v-2c0-.18.08-.35.21-.48.11-.12.28-.19.46-.19h0c.18,0,.35.07.47.2.12.12.2.29.2.47v2h2c.19.02.35.07.48.2.12.12.19.29.19.47Z"
                    fill="currentColor"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const OnboardNewProject = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '2rem' }}>
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169205">
            <rect id="Rectangle_22041-20" width="32" height="32" fill="none" />
            <g>
              <path
                d="M7.4,27.87c-.64,0-1.18-.22-1.62-.66-.44-.44-.66-.98-.66-1.62v-.98c0-.45.16-.84.48-1.16s.71-.48,1.16-.48h1.09v-10.77c0-.45.16-.84.48-1.16s.71-.48,1.16-.48h9.47c.45,0,.84.16,1.16.48s.48.71.48,1.16v13.4c0,.64-.22,1.18-.66,1.62-.44.44-.98.66-1.62.66H7.4ZM18.34,26.51c.26,0,.47-.09.65-.26s.26-.39.26-.65v-13.4c0-.08-.03-.15-.08-.2-.05-.05-.12-.08-.2-.08h-9.47c-.08,0-.15.03-.2.08-.05.05-.08.12-.08.2v10.77h6.56c.45,0,.84.16,1.16.48s.48.71.48,1.16v.98c0,.26.09.47.26.65s.39.26.65.26ZM10.99,16.2c-.19,0-.36-.07-.49-.2-.13-.13-.2-.29-.2-.49,0-.19.07-.36.2-.49.13-.13.29-.2.49-.2h6.49c.19,0,.36.07.49.2.13.13.2.29.2.49s-.07.36-.2.49c-.13.13-.29.2-.49.2h-6.49ZM10.99,18.83c-.19,0-.36-.07-.49-.2-.13-.13-.2-.29-.2-.49,0-.19.07-.36.2-.49.13-.13.29-.2.49-.2h6.49c.19,0,.36.07.49.2.13.13.2.29.2.49s-.07.36-.2.49c-.13.13-.29.2-.49.2h-6.49ZM7.4,26.51h8.66v-1.89c0-.08-.03-.15-.08-.2-.05-.05-.12-.08-.2-.08H6.77c-.08,0-.15.03-.2.08-.05.05-.08.12-.08.2v.98c0,.26.09.47.26.65.17.17.39.26.65.26ZM7.4,26.51h-.91,9.57H7.4Z"
                fill="currentColor"
              />
              <g id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24-3">
                <path
                  d="M26.52,7.84c0,.18-.07.36-.2.48-.12.12-.29.2-.47.2h-2v2c0,.18-.07.35-.2.47-.12.12-.29.2-.47.2s-.35-.08-.48-.21c-.12-.11-.19-.28-.19-.46v-2h-2.01c-.18.01-.35-.08-.47-.21-.13-.11-.2-.29-.2-.47s.07-.35.2-.47c.12-.13.3-.2.48-.2h1.99v-2c0-.18.08-.35.21-.48.11-.12.28-.19.46-.19h0c.18,0,.35.07.47.2.12.12.2.29.2.47v2h2c.19.02.35.07.48.2.12.12.19.29.19.47Z"
                  fill="currentColor"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ServiceCatalogAccess = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '2rem' }}>
      <svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169181">
            <rect id="Rectangle_22041-6" width="32" height="32" fill="none" />
            <g>
              <g>
                <path
                  d="M11.89,17.54h1.68c.46,0,.84.38.84.84v1.68c0,.46-.38.84-.84.84h-1.68c-.46,0-.84-.38-.84-.84v-1.68c0-.46.38-.84.84-.84Z"
                  fill="currentColor"
                />
                <path
                  d="M11.89,11.64h1.68c.46,0,.84.38.84.84v1.68c0,.46-.38.84-.84.84h-1.68c-.46,0-.84-.38-.84-.84v-1.68c0-.46.38-.84.84-.84Z"
                  fill="currentColor"
                />
                <path
                  d="M18.09,17.54h1.68c.46,0,.84.38.84.84v1.68c0,.46-.38.84-.84.84h-1.68c-.46,0-.84-.38-.84-.84v-1.68c0-.46.38-.84.84-.84Z"
                  fill="currentColor"
                />
                <path
                  d="M18.09,11.64h1.68c.46,0,.84.38.84.84v1.68c0,.46-.38.84-.84.84h-1.68c-.46,0-.84-.38-.84-.84v-1.68c0-.46.38-.84.84-.84Z"
                  fill="currentColor"
                />
              </g>
              <path
                d="M21.14,8.46h-10.61c-1.18,0-2.16.85-2.35,1.98-1.12.19-1.99,1.16-1.99,2.34v10.6c0,1.33,1.08,2.4,2.4,2.4h10.61c.93,0,1.67-.45,2.12-1.31-.3.02-.39.03-.59.03l-1.53.02h-10.61c-.62,0-1.13-.51-1.13-1.15v-10.6c0-.46.28-.86.68-1.04v9.71c0,1.32,1.07,2.4,2.4,2.4h10.61c1.32,0,2.4-1.09,2.4-2.4v-10.6c0-1.33-1.08-2.4-2.4-2.4ZM22.28,21.46c0,.63-.5,1.14-1.14,1.14h-10.61c-.62,0-1.14-.5-1.14-1.14v-10.6c0-.63.51-1.15,1.14-1.15h10.61c.63,0,1.14.51,1.14,1.15v10.6Z"
                fill="currentColor"
              />
            </g>
            <g id="add_home_24dp_5F6368_FILL1_wght300_GRAD0_opsz24-3">
              <path
                d="M20.9,5.74c0-.18.07-.34.19-.46s.28-.19.46-.19h4.91s-.95-.79-.95-.79c-.28-.23-.3-.64-.08-.91l.04-.04c.23-.23.62-.26.88-.04l2.25,1.9.06.04s.08.08.11.13c.03.04.04.07.06.11.02.04.04.08.04.11.02.06.02.12.01.18,0,.03,0,.06,0,.09,0,.16-.05.3-.16.43l-1.98,2.19c-.22.23-.65.28-.92.04-.27-.24-.28-.65-.04-.92l1.11-1.22h-5.33c-.35,0-.65-.3-.65-.65Z"
                fill="currentColor"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const Permissions = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '4.5rem', marginTop: '0' }}>
      <svg xmlns="http://www.w3.org/2000/svg" id="Layer_2" viewBox="0 0 32 32">
        <g id="Layer_1-2" data-name="Layer_1">
          <g id="Group_169190">
            <rect id="Rectangle_22041-12" width="32" height="32" fill="none" />
            <path
              d="M14.5,24.15h-1.28c-1.36,0-2.05,0-2.67-.32-.55-.28-.99-.72-1.27-1.27-.32-.62-.32-1.3-.32-2.67v-7.8c0-1.36,0-2.05.32-2.67.28-.55.72-.99,1.27-1.27.62-.32,1.3-.32,2.67-.32h4.8c1.36,0,2.05,0,2.67.32.55.28.99.72,1.27,1.27.32.62.32,1.3.32,2.67v2.4c0,.36-.29.65-.65.65s-.65-.29-.65-.65v-2.4c0-1.12,0-1.73-.17-2.08-.15-.3-.4-.54-.7-.7-.34-.17-.96-.17-2.08-.17h-4.8c-1.12,0-1.73,0-2.08.17-.3.15-.54.4-.7.7-.17.34-.17.96-.17,2.08v7.8c0,1.12,0,1.73.17,2.08.15.3.4.54.7.7.34.17.96.17,2.08.17h1.28c.36,0,.65.29.65.65s-.29.65-.65.65ZM21.17,23.4h-2.85c-.49,0-.79,0-1.09-.15-.26-.13-.47-.34-.61-.61-.15-.3-.15-.6-.15-1.09v-.6c0-.49,0-.78.15-1.08.14-.27.35-.48.62-.62.17-.09.34-.12.54-.14v-.67c0-1.08.88-1.96,1.96-1.96s1.96.88,1.96,1.96v.67c.21.02.38.05.55.14.26.13.47.34.61.61.15.3.15.6.15,1.09v.6c0,.49,0,.79-.15,1.09-.14.27-.35.48-.62.61-.29.15-.59.15-1.08.15ZM18.32,20.4c-.22,0-.45,0-.51.02-.04.08-.04.31-.04.53v.6c0,.22,0,.45.02.51.08.04.31.04.53.04h2.85c.22,0,.45,0,.51-.02.04-.08.04-.31.04-.53v-.6c0-.22,0-.45-.02-.51-.08-.04-.31-.04-.53-.04h-2.85ZM19.09,19.1h1.32v-.66c0-.37-.3-.66-.66-.66s-.66.3-.66.66v.66ZM14.88,18.9h-2.25c-.36,0-.65-.29-.65-.65s.29-.65.65-.65h2.25c.36,0,.65.29.65.65s-.29.65-.65.65ZM16.38,15.9h-3.75c-.36,0-.65-.29-.65-.65s.29-.65.65-.65h3.75c.36,0,.65.29.65.65s-.29.65-.65.65ZM18.62,12.9h-6c-.36,0-.65-.29-.65-.65s.29-.65.65-.65h6c.36,0,.65.29.65.65s-.29.65-.65.65Z"
              fill="currentColor"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const History = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '4.5rem', marginTop: '0' }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="14.769" viewBox="0 0 16 14.769">
        <g id="Group_172865" data-name="Group 172865" transform="translate(0 -0.001)">
          <path
            id="Union_8"
            data-name="Union 8"
            d="M7.409,14.643a7.738,7.738,0,0,1-3.545-1.53A7.943,7.943,0,0,1,1.3,8.686a.718.718,0,0,1,.575-.842.728.728,0,0,1,.848.568,6.533,6.533,0,0,0,2.13,3.648,6.324,6.324,0,0,0,2.815,1.174,5.833,5.833,0,0,0,4.524-.989.11.11,0,0,0,.027-.017,5.025,5.025,0,0,0,2.231-3.414,7.2,7.2,0,0,0-.287-3.569,5.744,5.744,0,0,0-1.613-2.369A5.845,5.845,0,0,0,4.6,3.107c-.007.006-.014.01-.016.016L2.549,5.007h2.52a.714.714,0,1,1,0,1.428H.764A.728.728,0,0,1,.2,6.212.7.7,0,0,1,.023,5.9s0,0,0,0,0-.021-.007-.031l0-.009c0-.007,0-.016,0-.025l0-.014c0-.007,0-.012,0-.02s0-.012,0-.019,0-.011,0-.016,0-.015,0-.021V5.729s0-.006,0-.01V1.428A.719.719,0,0,1,.725.713a.719.719,0,0,1,.724.715V4.064L3.579,2.087A7.3,7.3,0,0,1,6.682.28a7.394,7.394,0,0,1,3.613-.1A7.319,7.319,0,0,1,13.506,1.8a7.2,7.2,0,0,1,2.017,2.962,8.533,8.533,0,0,1,.344,4.3,6.471,6.471,0,0,1-2.838,4.341,7.1,7.1,0,0,1-4.221,1.359A8.1,8.1,0,0,1,7.409,14.643Z"
            transform="translate(0 0.001)"
            fill="currentColor"
          />
          <path
            id="Path_87058"
            data-name="Path 87058"
            d="M9.512,7.141,5.854,9.253a.236.236,0,0,1-.354-.2V4.825a.236.236,0,0,1,.354-.2L9.512,6.732a.236.236,0,0,1,0,.409Z"
            transform="translate(2.192 0.449)"
            fill="currentColor"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const Rotational = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '4.5rem', marginTop: '0' }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
        <g id="Group_172834" data-name="Group 172834" transform="translate(-2731 -1199)">
          <g
            id="Rectangle_147857"
            data-name="Rectangle 147857"
            transform="translate(2731 1199)"
            fill="#17aa06"
            stroke="#707070"
            // stroke-width="1"
            opacity="0"
          >
            <rect width="24" height="24" stroke="none" />
            <rect x="0.5" y="0.5" width="23" height="23" fill="none" />
          </g>
          <path
            id="refresh_24dp_5F6368_FILL0_wght400_GRAD0_opsz24"
            d="M169-782a8.686,8.686,0,0,1-6.384-2.616A8.686,8.686,0,0,1,160-791a8.686,8.686,0,0,1,2.616-6.384A8.686,8.686,0,0,1,169-800a8.908,8.908,0,0,1,3.712.8,8.575,8.575,0,0,1,3.038,2.292v-1.969a1.089,1.089,0,0,1,.323-.8,1.088,1.088,0,0,1,.8-.323,1.088,1.088,0,0,1,.8.323,1.089,1.089,0,0,1,.323.8v5.625a1.089,1.089,0,0,1-.323.8,1.088,1.088,0,0,1-.8.323H171.25a1.088,1.088,0,0,1-.8-.323,1.089,1.089,0,0,1-.323-.8,1.089,1.089,0,0,1,.323-.8,1.088,1.088,0,0,1,.8-.323h3.6a6.566,6.566,0,0,0-2.461-2.475,6.668,6.668,0,0,0-3.389-.9,6.509,6.509,0,0,0-4.781,1.969A6.509,6.509,0,0,0,162.25-791a6.509,6.509,0,0,0,1.969,4.781A6.509,6.509,0,0,0,169-784.25a6.59,6.59,0,0,0,3.5-.97,6.7,6.7,0,0,0,2.461-2.6,1.2,1.2,0,0,1,.633-.548,1.2,1.2,0,0,1,.83-.014,1.011,1.011,0,0,1,.647.591.894.894,0,0,1-.028.844,9.084,9.084,0,0,1-3.291,3.6A8.719,8.719,0,0,1,169-782Z"
            transform="translate(2574 2002)"
            fill="currentColor"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const HistoryIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon sx={{ ...props.sx, fontSize: '4.5rem', marginTop: '0' }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="14.769" viewBox="0 0 16 14.769">
        <g id="Group_172865" data-name="Group 172865" transform="translate(0 -0.001)">
          <path
            id="Union_8"
            data-name="Union 8"
            d="M7.409,14.643a7.738,7.738,0,0,1-3.545-1.53A7.943,7.943,0,0,1,1.3,8.686a.718.718,0,0,1,.575-.842.728.728,0,0,1,.848.568,6.533,6.533,0,0,0,2.13,3.648,6.324,6.324,0,0,0,2.815,1.174,5.833,5.833,0,0,0,4.524-.989.11.11,0,0,0,.027-.017,5.025,5.025,0,0,0,2.231-3.414,7.2,7.2,0,0,0-.287-3.569,5.744,5.744,0,0,0-1.613-2.369A5.845,5.845,0,0,0,4.6,3.107c-.007.006-.014.01-.016.016L2.549,5.007h2.52a.714.714,0,1,1,0,1.428H.764A.728.728,0,0,1,.2,6.212.7.7,0,0,1,.023,5.9s0,0,0,0,0-.021-.007-.031l0-.009c0-.007,0-.016,0-.025l0-.014c0-.007,0-.012,0-.02s0-.012,0-.019,0-.011,0-.016,0-.015,0-.021V5.729s0-.006,0-.01V1.428A.719.719,0,0,1,.725.713a.719.719,0,0,1,.724.715V4.064L3.579,2.087A7.3,7.3,0,0,1,6.682.28a7.394,7.394,0,0,1,3.613-.1A7.319,7.319,0,0,1,13.506,1.8a7.2,7.2,0,0,1,2.017,2.962,8.533,8.533,0,0,1,.344,4.3,6.471,6.471,0,0,1-2.838,4.341,7.1,7.1,0,0,1-4.221,1.359A8.1,8.1,0,0,1,7.409,14.643Z"
            transform="translate(0 0.001)"
            fill="#0F3054"
          />
          <path
            id="Path_87058"
            data-name="Path 87058"
            d="M9.512,7.141,5.854,9.253a.236.236,0,0,1-.354-.2V4.825a.236.236,0,0,1,.354-.2L9.512,6.732a.236.236,0,0,1,0,.409Z"
            transform="translate(2.192 0.449)"
            fill="#0F3054"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const StartIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg
        id="Action_Icon_Button"
        data-name="Action Icon Button"
        xmlns="http://www.w3.org/2000/svg"
        width="52"
        height="52"
        viewBox="0 0 52 52"
        style={{ transform: 'scale(1.5)' }}
      >
        <circle id="Ellipse_9389" data-name="Ellipse 9389" cx="26" cy="26" r="26" fill="#f5fafe" />
        <path
          id="play_circle_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24"
          d="M89.75-861.15l9.1-5.85-9.1-5.85ZM93-854a12.659,12.659,0,0,1-5.07-1.024A13.128,13.128,0,0,1,83.8-857.8a13.13,13.13,0,0,1-2.779-4.128A12.658,12.658,0,0,1,80-867a12.658,12.658,0,0,1,1.024-5.07A13.129,13.129,0,0,1,83.8-876.2a13.128,13.128,0,0,1,4.128-2.779A12.659,12.659,0,0,1,93-880a12.659,12.659,0,0,1,5.07,1.024A13.128,13.128,0,0,1,102.2-876.2a13.129,13.129,0,0,1,2.779,4.128A12.658,12.658,0,0,1,106-867a12.658,12.658,0,0,1-1.024,5.07A13.13,13.13,0,0,1,102.2-857.8a13.128,13.128,0,0,1-4.128,2.779A12.659,12.659,0,0,1,93-854Zm0-2.6a10.037,10.037,0,0,0,7.378-3.022A10.037,10.037,0,0,0,103.4-867a10.037,10.037,0,0,0-3.022-7.378A10.037,10.037,0,0,0,93-877.4a10.037,10.037,0,0,0-7.378,3.023A10.037,10.037,0,0,0,82.6-867a10.037,10.037,0,0,0,3.022,7.378A10.037,10.037,0,0,0,93-856.6ZM93-867Z"
          transform="translate(-67 893)"
          fill="#04213b"
          stroke="currentColor"
          strokeWidth="0.5"
        />
      </svg>
    </SvgIcon>
  );
};

export const RefreshStatusIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg
        id="Action_Icon_Button"
        data-name="Action Icon Button"
        xmlns="http://www.w3.org/2000/svg"
        width="52"
        height="52"
        viewBox="0 0 52 52"
        style={{ transform: 'scale(1.5)' }}
      >
        <circle id="Ellipse_9389" data-name="Ellipse 9389" cx="26" cy="26" r="26" fill="#f5fafe" />
        <path
          id="autorenew_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24"
          d="M161.375-901.187a10.314,10.314,0,0,1-1.031-2.437,9.621,9.621,0,0,1-.344-2.562,9.755,9.755,0,0,1,2.906-7.125A9.6,9.6,0,0,1,170-916.25h.219l-2-2,1.75-1.75,5,5-5,5-1.75-1.75,2-2H170a7.207,7.207,0,0,0-5.312,2.2,7.321,7.321,0,0,0-2.187,5.359,6.806,6.806,0,0,0,.188,1.594,7.147,7.147,0,0,0,.563,1.531Zm8.656,8.688-5-5,5-5,1.75,1.75-2,2H170a7.207,7.207,0,0,0,5.313-2.2,7.321,7.321,0,0,0,2.188-5.359,6.805,6.805,0,0,0-.187-1.594,7.146,7.146,0,0,0-.562-1.531l1.875-1.875a10.311,10.311,0,0,1,1.031,2.438,9.62,9.62,0,0,1,.344,2.563,9.755,9.755,0,0,1-2.906,7.125A9.6,9.6,0,0,1,170-896.25h-.219l2,2Z"
          transform="translate(-144 932.25)"
          fill="#04213b"
          stroke="#f5fafe"
          strokeWidth="0.5"
        />
      </svg>
    </SvgIcon>
  );
};

export const ReconfigureIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        fontSize: '2rem',
        ...props.sx,
      }}
    >
      <svg
        id="Action_Icon_Button"
        data-name="Action Icon Button"
        xmlns="http://www.w3.org/2000/svg"
        width="52"
        height="52"
        viewBox="0 0 52 52"
        style={{ transform: 'scale(1.5)' }}
      >
        <circle id="Ellipse_9389" data-name="Ellipse 9389" cx="26" cy="26" r="26" fill="#f5fafe" />
        <path
          id="tune_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24"
          d="M128.889-820v-6.667h2.222v2.222H140v2.222h-8.889V-820ZM120-822.222v-2.222h6.667v2.222Zm4.444-4.444v-2.222H120v-2.222h4.444v-2.222h2.222v6.667Zm4.444-2.222v-2.222H140v2.222Zm4.444-4.444V-840h2.222v2.222H140v2.222h-4.444v2.222ZM120-835.556v-2.222h11.111v2.222Z"
          transform="translate(-104 856)"
          fill="#04213b"
          stroke="#f5fafe"
          strokeWidth="0.5"
        />
      </svg>
    </SvgIcon>
  );
};
export const RestartIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        fontSize: '2rem',
        ...props.sx,
      }}
    >
      <svg
        id="Action_Icon_Button"
        data-name="Action Icon Button"
        xmlns="http://www.w3.org/2000/svg"
        width="52"
        height="52"
        viewBox="0 0 52 52"
        style={{ transform: 'scale(2)' }}
      >
        <circle id="Ellipse_9389" data-name="Ellipse 9389" cx="26" cy="26" r="26" fill="#f5fafe" />
        <g id="Group_173021" data-name="Group 173021">
          <path
            id="Path_93513"
            data-name="Path 93513"
            d="M46,67.8a2.237,2.237,0,0,1,1.4.4c9.427,4.872,5.478,19.455-5.232,18.849-2.432-.137-6.136-1.778-7.381-3.967a.88.88,0,0,1,1.227-1.227c.315.2.82.943,1.17,1.266A8.232,8.232,0,1,0,46.77,69.883c-.508-.292-1.386-.479-1.423-1.183A.928.928,0,0,1,46,67.8"
            transform="translate(-16.717 -49.547)"
            fill="#04213b"
          />
          <path
            id="Path_93514"
            data-name="Path 93514"
            d="M165.545.015a.891.891,0,0,1,1.068.842l0,11.24a.889.889,0,0,1-1.774-.053l0-11.24a.9.9,0,0,1,.705-.789"
            transform="translate(-139.695 14.476)"
            fill="#04213b"
          />
          <path
            id="Path_93515"
            data-name="Path 93515"
            d="M36.591,51.4c-.038-.038-.377.209-.438.254-.393.285-1.043,1.037-1.472,1.075a.893.893,0,0,1-.906-1.295,7.647,7.647,0,0,1,1.639-1.4c.045-.035.1,0,.07-.126-.622-.108-1.473-.065-2.061-.236a.9.9,0,0,1,.261-1.757c1.323.28,2.842.23,4.147.5.666.14.781.574.755,1.187a31.276,31.276,0,0,1-.452,3.755.894.894,0,0,1-1.761.169,13.082,13.082,0,0,1,.218-2.127"
            transform="translate(-14.989 -30.777)"
            fill="#04213b"
          />
          <path
            id="Path_93516"
            data-name="Path 93516"
            d="M13.811,152.428a.928.928,0,1,1-.928-.928.928.928,0,0,1,.928.928"
            transform="translate(4.707 -128.621)"
            fill="#04213b"
          />
          <path
            id="Path_93517"
            data-name="Path 93517"
            d="M1.851,210.258a.926.926,0,1,1-.926-.926.926.926,0,0,1,.926.926"
            transform="translate(16 -183.245)"
            fill="#04213b"
          />
          <path
            id="Path_93518"
            data-name="Path 93518"
            d="M9.645,271.8a.925.925,0,1,1-.925-.925.925.925,0,0,1,.925.925"
            transform="translate(8.638 -241.377)"
            fill="#04213b"
          />
          <rect
            id="Rectangle_148100"
            data-name="Rectangle 148100"
            width="20"
            height="23.047"
            transform="translate(16 14.477)"
            fill="none"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SecretDeviceAssociationIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '2rem',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="27.001" height="27.001" viewBox="0 0 27.001 27.001">
        <g id="Group_173080" data-name="Group 173080" transform="translate(12215 -6324)">
          <g id="Group_173061" data-name="Group 173061" transform="translate(-12215 6324)">
            <g id="Group_173081" data-name="Group 173081">
              <path
                id="Path_93520"
                data-name="Path 93520"
                d="M13.823,0a2.279,2.279,0,0,1,.129,4.48l-.02,1.636a7.945,7.945,0,0,1,4.5,1.845l1.133-1.138a2.278,2.278,0,0,1,2.384-3.476,2.341,2.341,0,0,1,1.733,1.8,2.279,2.279,0,0,1-3.495,2.3L19.048,8.579a7.992,7.992,0,0,1,1.848,4.5l1.632-.017a2.279,2.279,0,1,1,.025.919L20.9,13.931a7.708,7.708,0,0,1-1.847,4.459l1.137,1.171a2.269,2.269,0,0,1,3.5,2.2,2.265,2.265,0,1,1-4.121-1.574l-1.17-1.137a7.692,7.692,0,0,1-4.458,1.847l.046,1.657a2.22,2.22,0,1,1-.918-.025l.017-1.633a7.987,7.987,0,0,1-4.5-1.848L7.451,20.184a2.276,2.276,0,0,1-2.3,3.494,2.306,2.306,0,0,1-1.8-1.732,2.28,2.28,0,0,1,3.475-2.385l1.138-1.133a7.95,7.95,0,0,1-1.845-4.5l-1.635.02A2.278,2.278,0,0,1,.008,13.82c.018-.2-.025-.437,0-.633a2.393,2.393,0,0,1,1.409-1.781,2.28,2.28,0,0,1,3.07,1.653l1.635.02a7.91,7.91,0,0,1,1.845-4.5L6.829,7.446A2.308,2.308,0,0,1,3.677,6.8a2.259,2.259,0,0,1,3.1-3.151,2.312,2.312,0,0,1,.677,3.174L8.584,7.961a7.906,7.906,0,0,1,4.5-1.845l-.02-1.636A2.279,2.279,0,0,1,11.74.843,2.525,2.525,0,0,1,13.191,0Zm1.094,2.256a1.411,1.411,0,1,0-1.411,1.412,1.411,1.411,0,0,0,1.411-1.412m-7.95,3.3A1.409,1.409,0,1,0,5.558,6.96a1.41,1.41,0,0,0,1.409-1.41m15.894,0a1.408,1.408,0,1,0-1.408,1.408,1.408,1.408,0,0,0,1.408-1.408M20.044,13.5a6.538,6.538,0,1,0-6.538,6.54,6.539,6.539,0,0,0,6.538-6.54m-16.371,0a1.411,1.411,0,1,0-1.411,1.412A1.412,1.412,0,0,0,3.674,13.5m22.489,0a1.411,1.411,0,1,0-1.411,1.411A1.411,1.411,0,0,0,26.163,13.5m-19.2,7.952a1.409,1.409,0,1,0-1.409,1.409,1.409,1.409,0,0,0,1.409-1.409m15.9,0a1.409,1.409,0,1,0-1.409,1.409,1.409,1.409,0,0,0,1.409-1.409m-7.947,3.3a1.412,1.412,0,1,0-1.412,1.412,1.412,1.412,0,0,0,1.412-1.412"
                transform="translate(0)"
                fill="currentColor"
              />
            </g>
          </g>
          <g id="Group_173074" data-name="Group 173074" transform="translate(-12204.852 6333.633)">
            <path
              id="Path_93524"
              data-name="Path 93524"
              d="M6.145,2.514H5.028V1.676a1.676,1.676,0,1,0-3.352,0v.838H.559A.559.559,0,0,0,0,3.073V6.983a.559.559,0,0,0,.559.559H6.145A.559.559,0,0,0,6.7,6.983V3.073a.559.559,0,0,0-.559-.559M2.235,1.676a1.117,1.117,0,1,1,2.235,0v.838H2.235ZM6.145,6.983H.559V3.073H6.145Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth="0.5"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CreateSecretIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1rem',
        width: '3em',
        height: '3em',
      }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="38.124" height="45.476" viewBox="0 0 38.124 45.476">
        <g id="Group_173082" data-name="Group 173082" transform="translate(11866.001 -6342)">
          <g id="Group_173065" data-name="Group 173065" transform="translate(-11866.001 6342)">
            <g id="Group_173084" data-name="Group 173084" transform="translate(0 0)">
              <path
                id="Path_93522"
                data-name="Path 93522"
                d="M29.6,12.119H24.253a.016.016,0,0,1-.016-.016V8.293A8.219,8.219,0,0,0,16.433,0,8.083,8.083,0,0,0,8.076,8.081V12.1a.016.016,0,0,1-.016.016H2.709A2.709,2.709,0,0,0,0,14.829V33.647a2.71,2.71,0,0,0,2.709,2.71h9.385a1.345,1.345,0,0,0,0-2.69H2.709a.016.016,0,0,1-.016-.016V14.833a.02.02,0,0,1,.02-.02H29.6a.02.02,0,0,1,.02.02v5.693a1.347,1.347,0,1,0,2.693,0V14.833A2.714,2.714,0,0,0,29.6,12.119m-8.06-.016a.018.018,0,0,1-.02.016H10.79a.018.018,0,0,1-.02-.016V8.081A5.388,5.388,0,0,1,16.4,2.7a5.5,5.5,0,0,1,5.142,5.55Z"
                transform="translate(0 0)"
                fill="#04213b"
              />
            </g>
          </g>
          <g id="Group_173069" data-name="Group 173069" transform="translate(-11850.658 6364.694)">
            <path
              id="Path_93523"
              data-name="Path 93523"
              d="M11.265,0A11.266,11.266,0,1,0,22.531,11.266,11.265,11.265,0,0,0,11.265,0m0,20.8A9.532,9.532,0,1,1,20.8,11.266,9.533,9.533,0,0,1,11.265,20.8m5.2-9.532a.867.867,0,0,1-.867.867H12.132V15.6a.867.867,0,1,1-1.733,0V12.132H6.933a.867.867,0,0,1,0-1.733H10.4V6.933a.867.867,0,1,1,1.733,0V10.4H15.6a.867.867,0,0,1,.867.867"
              transform="translate(0 0)"
              fill="#04213b"
              stroke="#04213b"
              strokeWidth="0.5"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SuspendIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '3rem',
      }}
    >
      <svg
        id="Action_Icon_Button"
        data-name="Action Icon Button"
        xmlns="http://www.w3.org/2000/svg"
        width="52"
        height="52"
        viewBox="0 0 52 52"
        style={{ transform: 'scale(1.5)' }}
      >
        <circle id="Ellipse_9389" data-name="Ellipse 9389" cx="26" cy="26" r="26" fill="#f5fafe" />
        <path
          id="Path_93539"
          data-name="Path 93539"
          d="M38,24A14,14,0,1,0,52,38,14,14,0,0,0,38,24Zm0,25.846A11.846,11.846,0,1,1,49.846,38,11.846,11.846,0,0,1,38,49.846ZM35.846,33.692v8.615a1.077,1.077,0,1,1-2.154,0V33.692a1.077,1.077,0,0,1,2.154,0Zm6.462,0v8.615a1.077,1.077,0,1,1-2.154,0V33.692a1.077,1.077,0,0,1,2.154,0Z"
          transform="translate(-12 -12)"
          fill="#04213b"
        />
      </svg>
    </SvgIcon>
  );
};

export const ResetIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '3rem',
      }}
    >
      <svg
        id="Action_Icon_Button"
        data-name="Action Icon Button"
        xmlns="http://www.w3.org/2000/svg"
        width="52"
        height="52"
        viewBox="0 0 52 52"
        style={{ transform: 'scale(1.5)' }}
      >
        <circle id="Ellipse_9389" data-name="Ellipse 9389" cx="26" cy="26" r="26" fill="#f5fafe" />
        <path
          id="refresh_24dp_E3E3E3_FILL0_wght400_GRAD0_opsz24"
          d="M173-774a12.547,12.547,0,0,1-9.222-3.778A12.546,12.546,0,0,1,160-787a12.546,12.546,0,0,1,3.778-9.222A12.546,12.546,0,0,1,173-800a12.866,12.866,0,0,1,5.363,1.158,12.387,12.387,0,0,1,4.387,3.311V-800H186v11.375H174.625v-3.25h6.825a9.484,9.484,0,0,0-3.555-3.575,9.632,9.632,0,0,0-4.9-1.3,9.4,9.4,0,0,0-6.906,2.844A9.4,9.4,0,0,0,163.25-787a9.4,9.4,0,0,0,2.844,6.906A9.4,9.4,0,0,0,173-777.25a9.527,9.527,0,0,0,5.647-1.788,9.439,9.439,0,0,0,3.534-4.712h3.413a12.831,12.831,0,0,1-4.631,7.028A12.609,12.609,0,0,1,173-774Z"
          transform="translate(-147 813)"
          fill="#04213b"
          strokeWidth="1"
        />
      </svg>
    </SvgIcon>
  );
};

export const NamespaceIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon
      sx={{
        ...props.sx,
        fontSize: '1.6rem',
      }}
    >
      <svg
        id="namespaceIcon"
        data-name="namespaceIcon"
        xmlns="http://www.w3.org/2000/svg"
        width="21.667"
        height="20"
        viewBox="0 0 21.667 20"
      >
        <defs>
          <clipPath id="clip-path">
            <rect id="Rectangle_148105" data-name="Rectangle 148105" width="21.667" height="20" fill="none" />
          </clipPath>
        </defs>
        <g id="namespaceIcon" data-name="namespaceIcon" clipPath="url(#clip-path)">
          <path
            id="Path_93538"
            data-name="Path 93538"
            d="M20,0H1.667A1.667,1.667,0,0,0,0,1.666V15.832A1.667,1.667,0,0,0,1.667,17.5H3.333v1.667a.833.833,0,1,0,1.667,0V17.5H16.667v1.667a.833.833,0,1,0,1.667,0V17.5H20a1.667,1.667,0,0,0,1.667-1.667V1.666A1.667,1.667,0,0,0,20,0m0,15.833H1.667V1.666H20V8.332H18.263a5,5,0,1,0,0,1.667H20Zm-5.225-7.5a1.667,1.667,0,1,0,0,1.667h1.788a3.333,3.333,0,1,1,0-1.667Z"
            transform="translate(0 0.001)"
            fill="currentColor"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};
