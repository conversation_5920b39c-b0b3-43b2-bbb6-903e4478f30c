import { Divider } from '@mui/material';
import { styled } from '@mui/system';

interface StyledDividerProps {
  radius: number;
  strokeWidth: string | number;
  color: string | undefined;
  orientation: 'vertical' | 'horizontal';
  mt: string | number;
  mb: string | number;
  length: string;
  opacity: number;
}

const StyledDivider = styled(Divider)<StyledDividerProps>(({ theme, opacity, color, radius, orientation, strokeWidth, mt, mb, length }) => {
  return {
    backgroundColor: color || theme.palette.nbldivider.variant1,
    opacity: opacity,
    borderRadius: `${radius * 10}%`,
    ...(orientation == 'horizontal' ? { width: length, height: 0 } : { height: length, width: 0 }),
    padding: strokeWidth,
    marginTop: mt,
    marginBottom: mb,
  };
});

export default StyledDivider;
