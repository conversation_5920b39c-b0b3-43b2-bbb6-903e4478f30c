import { Tab, Tabs } from '@mui/material';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

interface StyledTabsProps {
  theme?: NebulaTheme;
}

interface StyledTabProps {
  theme?: NebulaTheme;
  id: string;
}

const StyledTabs = styled(Tabs)<StyledTabsProps>(({ theme }) => ({
  '& .MuiTabs-indicator': {
    backgroundColor: theme.palette.primary.shade5, // Use the provided color or fallback to theme primary color
  },
  '.MuiTabs-scroller': {
    display: 'flex',
  },
}));

const StyledTab = styled(Tab)<StyledTabProps>(({ theme }) => ({
  gap: '8px',
  minHeight: 0,
  paddingBottom: 0,
  paddingTop: 0,
  paddingRight: 0,
  paddingLeft: 0,
  marginRight: '16px',
  '&.MuiTab-root': {
    ...theme.typography.subtitle1,
    letterSpacing: '0px',
    textTransform: 'capitalize',
    minWidth: 'auto',
    '&.Mui-disabled': {
      color: theme.palette.secondary.shade4,
      cursor: 'not-allowed',
      pointerEvents: 'auto',
    },
  },
  '&.MuiTab-textColorPrimary': {
    color: theme.palette.secondary.shade4,
    '&.Mui-selected': {
      color: theme.palette.typography.shade1,
    },
  },
}));

export { StyledTab, StyledTabs };
