type CreateStorageNFS = {
  domain: string;
  name?: string;
  project: {
    id: string;
    appId: string;
    appName: string;
    emailDistribution: string;
    projectName: string;
    projectShortName: string;
  };
  platformContext: {
    catalogId: string;
    domainId: string;
    envId: string;
    domainName: string;
    environmentName: string;
  };
  dataCenter: string;
  fileSystemNameDesc: string;
  fileSystemName: string;
  quota: string;
  quotaUnit?: string; // Optional if not in JSON
  nfsVersionV3: boolean;
  nfsVersionV4: boolean;
  sourceIpAddress: string;
  reason: string;
  deeplinkUrl: string;
  shortName: string;
};

export default CreateStorageNFS;
