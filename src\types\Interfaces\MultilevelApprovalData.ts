export default interface MultiLevelApprovalData {
  approvedOrRejectedBy: string;
  approvalStatus: string;
  approvalDetails: any;
  approvalGroup: string;
  level: number;
  groupId: string;
  id?: string;
  rejectedReason?:string;
  requiredApprovalsCount: number;
  currentCount: number;
  approvedOrRejectedAt: string;
  existingApprovals:
    | {
        approvalGroup?: string;
        approvalStatus?: string;
        approvedOrRejectedAt?: string;
        approvedOrRejectedBy?: string;
        level?: number;
        rejectedReason?: null | string;
      }[]
    | [];
}
