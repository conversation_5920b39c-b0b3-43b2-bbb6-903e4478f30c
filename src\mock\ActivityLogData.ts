import { ACTIVITYLOGSTATUS } from 'types/Enums/ActivityLogStatus';

export const ActivityLogData = {
  status: true,
  data: [
    {
      eventName: 'Create VM Request',
      eventCode: 'NEB-EVENT-VM-1001',
      startTime: '2024-08-21T17:06:46.619Z',
      endTime: '2024-08-21T17:06:51.806Z',
      status: ACTIVITYLOGSTATUS.COMPLETED,
      timeTaken: '5.19 s',
    },
    {
      eventName: 'Request Approval',
      eventCode: 'NEB-EVENT-VM-1001',
      startTime: '2024-08-21T17:06:46.619Z',
      endTime: '2024-08-21T17:06:51.806Z',
      status: ACTIVITYLOGSTATUS.COMPLETED,
      timeTaken: '5.19 s',
    },
    {
      eventName: 'IP Reservation',
      eventCode: 'NEB-EVENT-VM-1001',
      startTime: '2024-08-21T17:06:46.619Z',
      endTime: '2024-08-21T17:06:51.806Z',
      status: ACTIVITYLOGSTATUS.FAILED,
      timeTaken: '5.19 s',
    },
    {
      eventName: 'VM Creation - Morpheus',
      eventCode: 'NEB-EVENT-VM-2001',
      startTime: '2024-08-21T17:06:51.901Z',
      endTime: '2024-08-21T17:06:52.989Z',
      status: ACTIVITYLOGSTATUS.COMPLETED,
      timeTaken: '1.09 s',
    },
    {
      eventName: 'VM status check',
      eventCode: 'NEB-EVENT-VM-3001',
      startTime: '2024-08-21T17:06:53.059Z',
      endTime: '2024-08-21T17:14:40.259Z',
      status: ACTIVITYLOGSTATUS.STARTED,
      timeTaken: '7.79 min',
    },
    {
      eventName: 'Create Resources - Nebula',
      eventCode: 'NEB-EVENT-VM-4001',
      startTime: '2024-08-21T17:14:40.410Z',
      endTime: '2024-08-21T17:14:40.624Z',
      status: ACTIVITYLOGSTATUS.SKIPPED,
      timeTaken: '214 ms',
    },
    {
      eventName: 'VM Inventory',
      eventCode: 'NEB-EVENT-VM-5001',
      startTime: '2024-08-21T17:14:40.778Z',
      endTime: '2024-08-21T17:15:01.603Z',
      status: ACTIVITYLOGSTATUS.NOT_STARTED,
      timeTaken: '20.83 s',
    },
    {
      eventName: 'VM Observability',
      eventCode: 'NEB-EVENT-VM-5020',
      startTime: '2024-08-21T17:15:01.778Z',
      endTime: '2024-08-21T17:15:01.603Z',
      status: ACTIVITYLOGSTATUS.NOT_STARTED,
      timeTaken: '20.83 s',
    },
    {
      eventName: 'VM Compliance',
      eventCode: 'NEB-EVENT-VM-5010',
      startTime: '2024-08-21T17:15:02.178Z',
      endTime: '2024-08-21T17:15:01.603Z',
      status: ACTIVITYLOGSTATUS.NOT_STARTED,
      timeTaken: '20.83 s',
    },
    {
      eventName: 'VM Security',
      eventCode: 'NEB-EVENT-VM-5030',
      startTime: '2024-08-21T17:15:02.533Z',
      endTime: '2024-08-21T17:15:02.787Z',
      status: ACTIVITYLOGSTATUS.COMPLETED,
      timeTaken: '254 ms',
    },
    {
      eventName: 'Request status - COMPLETED',
      eventCode: 'NEB-EVENT-VM-9001',
      startTime: '2024-08-21T17:15:03.239Z',
      endTime: '2024-08-21T17:15:03.402Z',
      status: ACTIVITYLOGSTATUS.COMPLETED,
      timeTaken: '163 ms',
    },
  ],
};
