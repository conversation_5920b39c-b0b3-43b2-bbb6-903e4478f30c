import { Box, styled } from '@mui/material';
import { keyframes } from '@mui/styled-engine';
import { NebulaTheme } from 'NebulaTheme/type';

export type StyledSpinnerProps = {
  theme?: NebulaTheme;
};

const rotation = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

export const StyledSpinner = styled(Box, { shouldForwardProp: (prop) => prop != 'color' && prop != 'theme' })<StyledSpinnerProps>(
  ({ theme, display }) => ({
    width: display === 'block' ? 56 : 40,
    height: display === 'block' ? 56 : 40,
    border: `3px solid ${display === 'inline' ? theme.palette.primary.main : theme.palette.secondary.main}`,
    borderRadius: '50%',
    display: 'inline-block',
    position: 'relative',
    boxSizing: 'border-box',
    animation: `${rotation} 1s linear infinite`,
    [theme.breakpoints.down('xl')]: {
      width: display === 'block' ? 45 : 40,
      height: display === 'block' ? 45 : 40,
    },
    '&:after, &:before': {
      content: "''",
      position: 'absolute',
      boxSizing: 'border-box',
      left: 0,
      top: 0,
      background: display === 'inline' ? theme.palette.primary.main : theme.palette.secondary.main,
      width: 6,
      height: 6,
      transform: 'translate(150%, 150%)',
      borderRadius: '50%',
    },
    '&:before': {
      left: 'auto',
      top: 'auto',
      right: 0,
      bottom: 0,
      transform: 'translate(-150%, -150%)',
    },
  })
);
