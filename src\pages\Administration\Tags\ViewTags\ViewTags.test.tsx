import { render, act } from '@testing-library/react';
import { Provider as ReduxProvider } from 'react-redux';
import ThemeProvider from 'mock/ThemeProvider';
import { BrowserRouter as Router } from 'react-router-dom';
import configureMockStore from 'redux-mock-store';

import ViewTags from '..';

describe('View roles component', () => {
  test('Should render component passed', async () => {
    const mockStore = configureMockStore();
    const store = mockStore({
      user: {
        userDetails: {
          isAdmin: true,
        },
      },
    });
    const component = await act(
      async () =>
        render(
          <Router>
            <ReduxProvider store={store}>
              <ThemeProvider>
                <ViewTags />
              </ThemeProvider>
            </ReduxProvider>
          </Router>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
