import { yupMatchesParams } from 'utils/common';
import * as yup from 'yup';

const PermissionSchema = yup.object().shape({
  group: yup.string().required('Group is required'),
  domain: yup
    .string()
    .nullable(true)
    .test('not-empty', 'Domain is required', (value) => value === null || Bo<PERSON>an(value)),
  modifyRequest: yup.boolean().required('Modify Request is required'),
  customApprover: yup.boolean().required('Custom Approver is required'),
});

const ManageApprovalSchema = yup.object().shape({
  description: yup
    .string()
    .trim()
    .required('Description is required')
    .matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage)
    .test('maxWords', 'Maximum 500 words allowed', (value) => {
      if (!value || value.trim().length === 0) return true;
      const count = value
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 0).length;
      return count <= 500;
    }),
  customApproval: yup.boolean().required('Custom Approver is required'),
  permissions: yup
    .array()
    .of(PermissionSchema)
    .required('At least one catalog  approval record is required')
    .min(1, 'At least one catalog  approval record is required')
    .test('unique-permission-combo', 'Duplicate groups are not allowed', (permissions) => {
      if (!permissions) return true;
      const seen = new Set();
      for (const { group } of permissions) {
        const key = `${group}`;
        if (seen.has(key)) return false;
        seen.add(key);
      }
      return true;
    }),
  isPermissionAdded: yup.boolean().test('is-Permissions-added', 'Permissions should be  added', function (value) {
    if (!value) {
      return this.createError({
        message: 'Atleast one Permission should be added',
      });
    }
    return true;
  }),
});

export default ManageApprovalSchema;
