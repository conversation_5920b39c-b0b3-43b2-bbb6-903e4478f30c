//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';

type StoryProps = ComponentProps<typeof NblCheckBox>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblCheckbox',
  component: NblCheckBox,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

const Template = (args: StoryProps) => {
  const [checked, setChecked] = useState(args.checked);
  return <NblCheckBox {...args} checked={checked} onChange={() => setChecked((prev) => !prev)} />;
};

export const NblCheckbox: Story = {
  args: {
    label: 'Nbl Checkbox',
    disabled: true,
    checked: false,
    error: true,
  },
  render: (args) => {
    return (
      <NebulaTheme>
        <Template {...args} />
      </NebulaTheme>
    );
  },
};
