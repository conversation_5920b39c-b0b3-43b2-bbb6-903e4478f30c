import { useEffect, useState, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
// eslint-disable-next-line no-unused-vars
import { NblTable, ColumnData } from 'sharedComponents/NblTable';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblAutoComplete from 'sharedComponents/NblFormInputs/NblAutoComplete';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import ActionsColumn from 'componentsV2/Administration/ActionsColumn';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { useApiService } from 'api/ApiService/context';
import { AddDiskIcon } from 'assets/images/icons/custom-icons';
import NblDivider from 'sharedComponents/NblDivider';

interface AddProjectProps {
  rows: { id: string; project: string }[];
  setRows?: React.Dispatch<React.SetStateAction<{ id: string; project: string }[]>>;
  onProjectsChange?: (projects: string[]) => void;
}

const AddProject: React.FC<AddProjectProps> = ({ rows, setRows, onProjectsChange }) => {
  const { apiComputeService } = useApiService();
  const dispatch = useDispatch();
  const theme = useTheme<NebulaTheme>();

  const [projectOptions, setProjectOptions] = useState<{ value: string; label: string }[]>([]);
  const [localRows, setLocalRows] = useState(rows);
  const [nextId, setNextId] = useState(() => (rows.length ? Math.max(...rows.map((r) => parseInt(r.id))) + 1 : 1));
  const prevProjectsRef = useRef<string[]>(rows.map((r) => r.project));

  const effectiveRows = setRows ? rows : localRows;
  const updateRows = setRows ?? setLocalRows;

  useEffect(() => {
    const fetchProjectOptions = async () => {
      dispatch(showSpinner({ id: SPINNER_IDS.vmNetworkOptions, status: true, message: 'Loading project options...' }));
      try {
        const res = await apiComputeService.getMultiENVProjectDetails();
        if (res?.status && Array.isArray(res.data)) {
          setProjectOptions(res.data.map((project) => ({ value: project.id, label: project.name })));
        }
      } finally {
        dispatch(showSpinner({ id: SPINNER_IDS.vmNetworkOptions, status: false, message: '' }));
      }
    };
    fetchProjectOptions();
  }, [apiComputeService, dispatch]);

  useEffect(() => {
    const currentProjects = effectiveRows.map((r) => r.project).filter(Boolean);
    if (JSON.stringify(currentProjects) !== JSON.stringify(prevProjectsRef.current)) {
      onProjectsChange?.(currentProjects);
      prevProjectsRef.current = currentProjects;
    }
  }, [effectiveRows]);

  useEffect(() => {
    if (!setRows) {
      setLocalRows(rows);
    }
  }, [rows, setRows]);

  const handleAddRow = () => {
    const newRow = { id: `${nextId}`, project: '' };
    updateRows([...effectiveRows, newRow]);
    setNextId((id) => id + 1);
  };

  const handleDeleteRow = (id: string) => {
    if (effectiveRows.length <= 1) return;
    updateRows(effectiveRows.filter((r) => r.id !== id));
  };

  const handleProjectChange = (value: string, id: string) => {
    updateRows(effectiveRows.map((r) => (r.id === id ? { ...r, project: value } : r)));
  };

  const lastRowHasValue = effectiveRows[effectiveRows.length - 1]?.project?.trim() !== '';

  const columns: ColumnData[] = [
    {
      field: 'project',
      headerName: 'Project',
      flex: 1,
      renderCell: (params) => (
        <NblFlexContainer padding="3px" width="80%">
          <NblAutoComplete
            name={`project-${params.id}`}
            value={params.row.project}
            onChange={(val) => handleProjectChange(val, params.row.id)}
            options={projectOptions}
            label=""
            placeholder="Select Project"
          />
        </NblFlexContainer>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      renderCell: (params) => {
        const isOnlyRow = effectiveRows.length === 1;
        return (
          <ActionsColumn
            disableEdit
            permissions={{ canDelete: !isOnlyRow }}
            onDeleteHandler={() => handleDeleteRow(params.row.id)}
            showConfirmWarning={false}
          />
        );
      },
    },
  ];

  return (
    <NblFlexContainer height="100%">
      <NblDivider
        orientation="vertical"
        length="100%"
        borderRadius={1}
        color={theme.palette.nbldivider.variant3}
        opacity={0.4}
        strokeWidth={0.3}
      />
      <NblFlexContainer direction="column" margin="0px 0px 0px 20px">
        <NblFlexContainer justifyContent="end" margin="10px 0px" height="auto">
          <NblButton
            variant="outlined"
            color="primary"
            onClick={handleAddRow}
            buttonID="add-project-btn"
            startIcon={<AddDiskIcon />}
            disabled={!lastRowHasValue}
            tooltip={!lastRowHasValue ? 'Please fill the current row before adding a new one' : undefined}
          >
            Add Project
          </NblButton>
        </NblFlexContainer>
        <NblTable
          columns={columns}
          rows={effectiveRows}
          rowsOverlayMessage="No Project Info Found"
          showResetFilter={false}
          hideFooterAndPagination
        />
      </NblFlexContainer>
    </NblFlexContainer>
  );
};

export default AddProject;
