// ==============================|| OVERRIDES - Data Grid Table ||============================== //

export default function DataGrid(theme) {
  const {
    palette: { table },
    typography,
  } = theme;
  const primaryLightTextColor = '#03213B';

  return {
    MuiDataGrid: {
      styleOverrides: {
        root: {
          borderColor: table.borderColor,
          borderRadius: 0,
          backdropFilter: 'blur(50px)',
          '& .MuiDataGrid-columnHeaders': {
            borderRadius: 0,
            backgroundColor: table.headerBgColor,
            color: primaryLightTextColor,
            fontFamily: typography.primaryFontFamily,
            fontWeight: 500,
            fontSize: '1.125rem',
            lineHeight: 1.16,
            borderColor: table.borderColor,
            '& .MuiDataGrid-columnHeader': {
              '&:focus': {
                outline: 'none',
              },
              '&:not(:last-child)': {
                borderRight: `1px solid ${table.columnDividerColor}`,
              },
              '& .MuiDataGrid-columnHeaderTitle': {
                whiteSpace: 'break-spaces',
                textAlign: 'center',
                fontSize: '1rem',
                lineHeight: 1.2,
                [theme.breakpoints.down('xl')]: {
                  fontSize: '0.625rem',
                },
              },
              '& .MuiDataGrid-columnSeparator': {
                visibility: 'hidden',
              },
              '& .MuiDataGrid-menuIcon': {
                width: 'auto',
                visibility: 'visible',
              },
              '& svg': {
                color: primaryLightTextColor,
              },
            },
          },
          '& .MuiDataGrid-row': {
            color: table.rowTextColor,
            fontSize: '1rem',
            lineHeight: 1.18,
            '&.even': {
              backgroundColor: table.evenRowBgColor,
            },
            '&.odd': {
              backgroundColor: table.oddRowBgColor,
            },
            '&:hover': {
              backgroundColor: 'rgba(208, 208, 208, 0.7)',
            },
            '& .action-button': {
              padding: '0 12px',
            },
            '& .MuiDataGrid-cell--withRightBorder': {
              borderRight: `1px solid ${table.columnDividerColor}`,
              borderWidth: '1px',
            },
            '& .MuiCheckbox-root': {
              color: table.rowTextColor,
              '&.Mui-checked': {
                color: table.headerBgColor,
              },
              '&.Mui-disabled': {
                color: '#787878',
              },
            },
          },
          '& .MuiDataGrid-row .MuiDataGrid-cell': {
            paddingTop: '12px',
            paddingBottom: '12px',
            textWrap: 'wrap',
            wordBreak: 'break-word'
          },
          '& .MuiDataGrid-row:not(.MuiDataGrid-row--dynamicHeight)>.MuiDataGrid-cell': {
            '&:focus': {
              outline: 'none',
            },
          },
          '& .MuiDataGrid-overlayWrapper': {
            minHeight: '100px',
          },
          '& .MuiDataGrid-footerContainer': {
            color: table.footerTextColor,
            backgroundColor: table.footerBgColor,
            borderColor: table.borderColor,
            height: '56px',

            '& .MuiTablePagination-input': {
              marginTop: '3px',
            },
            '& .MuiTablePagination-root': {
              color: 'inherit',
            },
            '& .MuiTablePagination-select': {
              fontSize: '1.25rem',
              display: 'flex',
              alignItems: 'center',
            },
          },
          [theme.breakpoints.down('xl')]: {
            '& .MuiDataGrid-columnHeaders': {
              fontSize: '0.625rem',
              lineHeight: 1.2,
            },
            '& .MuiDataGrid-row': {
              fontSize: '0.563rem',
              lineHeight: 1.2,
            },
            '& .MuiDataGrid-footerContainer': {
              '& .MuiTablePagination-selectLabel, & .MuiTablePagination-select, & .MuiTablePagination-displayedRows, & .MuiTablePagination-actions':
              {
                fontSize: '0.625rem',
              },
            },
          },
        },
      },
    },
  };
}
