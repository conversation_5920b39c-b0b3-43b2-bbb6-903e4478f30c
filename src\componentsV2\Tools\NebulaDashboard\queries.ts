export const GENERATE_QUERY_ALL_REQUEST = (
  year: number,
  quarter: number[],
  catalogs1: string[],
  catalogs3: string[],
  catalogs4: string[]
) => {
  let catalogs1String = '';
  let catalogs3String = '';
  let catalogs4String = '';

  catalogs1.forEach((name) => {
    catalogs1String = catalogs1String ? catalogs1String + `,` + `"${name}"` : `"${name}"`;
  });

  catalogs3.forEach((name) => {
    catalogs3String = catalogs3String ? catalogs3String + `,` + `"${name}"` : `"${name}"`;
  });

  catalogs4.forEach((name) => {
    catalogs4String = catalogs4String ? catalogs4String + `,` + `"${name}"` : `"${name}"`;
  });

  return `{
      getRequestSummary(year:${year},
      catalogs1: [${catalogs1String} ]
      catalogs3: [${catalogs3String} ]
      catalogs4: [${catalogs4String} ]
      quarter: [${quarter}]
    )
    {
      totalRequests
      approvedRequests
      processingRequests
      completedRequests
      failedRequests
      rejectedRequests
      cancelledRequests  
      statusUntrackedRequests    
    }
} `;
};

export const GENERATE_QUERY = (
  year: number,
  quarter: number[],
  catalogs1: string[],
  catalogs3?: string[],
  catalogs4?: string[],
  isRequestRequired: boolean = true
) => {
  let catalogs1String = '';
  catalogs1?.forEach((name) => {
    catalogs1String = catalogs1String ? catalogs1String + `,` + `"${name}"` : `"${name}"`;
  });

  let catalogs3String = '';
  catalogs3?.forEach((name) => {
    catalogs3String = catalogs3String ? catalogs3String + `,` + `"${name}"` : `"${name}"`;
  });

  let catalogs4String = '';
  catalogs4?.forEach((name) => {
    catalogs4String = catalogs4String ? catalogs4String + `,` + `"${name}"` : `"${name}"`;
  });

  let requestGroup = isRequestRequired
    ? `groupedRequests { 
    month 
    requests { 
    status 
    catalogName 
    date
    requestType
    } 
  }`
    : '';

  return `{
    metricsData(
    year: ${year}
    quarter: [${quarter}]
    catalogs1: [${catalogs1String}]
    catalogs3: [
    ${catalogs3String}
    ]
    catalogs4: [
      ${catalogs4String}
    ]
    ) {
      totalRequests
      approvedRequests
      processingRequests
      completedRequests 
      failedRequests
      rejectedRequests
      cancelledRequests
      statusUntrackedRequests
      ${requestGroup} 
    }
    }
    `;
};

export const GENERATE_REQUEST_BY_MONTH_QUERY = (
  year: number,
  quarter: number[],
  catalogs1: string[],
  catalogs3?: string[],
  catalogs4?: string[]
) => {
  let catalogs1String = '';
  catalogs1?.forEach((name) => {
    catalogs1String = catalogs1String ? catalogs1String + `,` + `"${name}"` : `"${name}"`;
  });

  let catalogs3String = '';
  catalogs3?.forEach((name) => {
    catalogs3String = catalogs3String ? catalogs3String + `,` + `"${name}"` : `"${name}"`;
  });

  let catalogs4String = '';
  catalogs4?.forEach((name) => {
    catalogs4String = catalogs4String ? catalogs4String + `,` + `"${name}"` : `"${name}"`;
  });

  return `{
    getRequestSummaryByMonth(
    year: ${year}
    quarter: [${quarter}]
    catalogs1: [${catalogs1String}]
    catalogs3: [
    ${catalogs3String}
    ]
    catalogs4: [
      ${catalogs4String}
    ]
    )
    {
      requestType
      summary{
          month
          count
      }
    }
  }`;
};

export const GENERATE_QUERY_FOR_CATALOG = () => {
  return `{ catalogs {
        id
        value
        name
        subCatalogs {
        id
        value
        name
        subCatalogs{
        id
        value
        name
        subCatalogs{
        id
        value
        name
        requestType
        }
      }
    }
  }
}
`;
};

export const GENERATE_REQUEST_QUERY = (
  year: number,
  quarter: number[],
  catalogs1: string[],
  catalog3: string[],
  catalog4: string[],
  status?: string[],
  month: number = 0,
  page: number = 1,
  pageSize: number = 100000,
  totalCreatedRequests: boolean = true
) => {
  let catalogs1String = '';
  catalogs1?.forEach((name) => {
    catalogs1String = catalogs1String ? catalogs1String + `,` + `"${name}"` : `"${name}"`;
  });

  let catalogs3String = '';
  catalog3?.forEach((name) => {
    catalogs3String = catalogs3String ? catalogs3String + `,` + `"${name}"` : `"${name}"`;
  });

  let catalogs4String = '';
  catalog4?.forEach((name) => {
    catalogs4String = catalogs4String ? catalogs4String + `,` + `"${name}"` : `"${name}"`;
  });

  let statusString = '';
  status?.forEach((name) => {
    statusString = statusString ? statusString + `,` + `"${name}"` : `"${name}"`;
  });

  const _month = month ? 'month :' + month : '';
  const statusField = statusString ? statusString : [];
  const createdRequests = totalCreatedRequests ? totalCreatedRequests : false;

  return `{
      getRequestList(
      year: ${year}
      catalogs1:[${catalogs1String}]
      catalogs3: [${catalogs3String}]
      catalogs4: [${catalogs4String}]
      quarter: [${quarter}]
      page: ${page},
      pageSize: ${pageSize},
      totalCreatedRequests: ${createdRequests}
      status:[${statusField}]
      ${_month}
       )
       {
        metaData {
            totalCount
        }
        data {
          id
          serviceRequestId
          status
          requestType
          catalogName
          projectName
          createdBy
          date
        }
       }
      }
    `;
};

export const GENERATE_QUERY_LATEST_REQUEST = (catalogs1: string[], catalog3: string[], catalog4: string[], count: number = 10) => {
  let catalogs1String = '';
  catalogs1?.forEach((name) => {
    catalogs1String = catalogs1String ? catalogs1String + `,` + `"${name}"` : `"${name}"`;
  });

  let catalogs3String = '';
  catalog3?.forEach((name) => {
    catalogs3String = catalogs3String ? catalogs3String + `,` + `"${name}"` : `"${name}"`;
  });

  let catalogs4String = '';
  catalog4?.forEach((name) => {
    catalogs4String = catalogs4String ? catalogs4String + `,` + `"${name}"` : `"${name}"`;
  });

  return `{
      getLastestRequestStatus (
        catalogs1: [${catalogs1String}]
        catalogs3: [${catalogs3String}]
        catalogs4: [${catalogs4String}]
        count: ${count}
       )
       {
        _id      
        requests
          {          
              status          
              serviceRequestId          
              date      
          }   
       }
      }
    `;
};
