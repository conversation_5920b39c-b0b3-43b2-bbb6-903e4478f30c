import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';

import useShowNavigationWarning from 'hooks/useShowNavigationWarning';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import AddRoleForm from 'components/Administration/AddRoleForm';
import RoleService from 'api/ApiService/RoleService';
import { AdminEditRouteParams } from 'types/Enums';
import useNblNavigate from 'hooks/useNblNavigate';

const EditRole = () => {
  const dispatch = useDispatch();
  const roleService = new RoleService();

  const [editRoleDetails, setEditRoleDetails] = useState<any>();

  const { [AdminEditRouteParams.roles]: roleName } = useParams();
  const navigate = useNblNavigate();
  useShowNavigationWarning();

  const fetchRoleDetails = () => {
    if (roleName) {
      dispatch(showSpinner({ id: SPINNER_IDS.roleDetails, status: true, message: 'Loading role details...' }));
      roleService
        .getRoleDetails(roleName)
        .then((res) => {
          if (res.status) {
            setEditRoleDetails(res.data);
          }
        })
        .finally(() => {
          dispatch(showSpinner({ id: SPINNER_IDS.roleDetails, status: false, message: '' }));
        });
    }
  };

  useEffect(() => {
    fetchRoleDetails();
  }, [roleName]);

  const navigateToPermissionDetails = () => {
    navigate('/administration/roles/view-roles');
  };

  return (
    <AddRoleForm
      title="Edit Role"
      permissions={{}}
      editRoleDetails={editRoleDetails}
      onSuccess={navigateToPermissionDetails}
      onClose={navigateToPermissionDetails}
    />
  );
};

export default EditRole;
