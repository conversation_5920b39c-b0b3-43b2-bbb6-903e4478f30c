import { Box, useTheme } from '@mui/material';
import ColorIndicatorIcon from '../../../components/ColorIndicationIcon';
import { getUsageColor } from '../../../utils/colors';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblTypography from 'sharedComponents/NblTypography';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import NblDivider from 'sharedComponents/NblDivider';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

interface CardBodyProps {
  usage: number;
  label: string;
  divider: boolean;
}
const CardBody: React.FunctionComponent<CardBodyProps> = ({ usage, label, divider }) => {
  const color = getUsageColor(usage);
  const theme: NebulaTheme = useTheme();
  return (
    <NblGridItem colspan={4}>
      <NblFlexContainer>
        <NblGridContainer justifyContent="center" alignItems="center" overflowY="hidden">
          <NblGridItem>
            <NblTypography variant="subtitle1" color="shade1">
              {label}
            </NblTypography>
          </NblGridItem>
          <NblGridItem>
            <Box display="flex" flexDirection="row" alignItems="center" height="100%" justifyContent="center">
              <ColorIndicatorIcon color={color} />
              <NblTypography variant="body1" color="shade1">
                {usage}
              </NblTypography>
            </Box>
          </NblGridItem>
        </NblGridContainer>
        {divider ? <NblDivider orientation="vertical" color={theme.palette.secondary.shade5} length={'3rem'} /> : ''}
      </NblFlexContainer>
    </NblGridItem>
  );
};

export default CardBody;
