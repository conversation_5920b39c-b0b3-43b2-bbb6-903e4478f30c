import { render, screen, fireEvent } from '@testing-library/react';
// eslint-disable-next-line no-unused-vars
import OperatingSystems, { OSLayout } from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

const mockOSLayouts: OSLayout[] = [
  {
    disabled: false,
    imageName: 'Ubuntu Image',
    imagePath: '/images/ubuntu.img',
    layoutMor: 'layout-1',
    layoutName: 'Ubuntu Layout',
    osName: 'Ubuntu',
    restricted: false,
    shortName: 'UBT',
    projects: ['Project A'],
  },
  {
    disabled: true,
    imageName: 'Windows Image',
    imagePath: '/images/windows.img',
    layoutMor: 'layout-2',
    layoutName: 'Windows Layout',
    osName: 'Windows',
    restricted: true,
    shortName: 'WIN',
    projects: ['Project B'],
  },
];

const mockOnChange = jest.fn();

describe('OperatingSystems Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all OS layout rows', () => {
    render(
      <NebulaThemeProvider>
        <OperatingSystems osLayouts={mockOSLayouts} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.getByText('Ubuntu Layout')).toBeInTheDocument();
    expect(screen.getByText('Windows Layout')).toBeInTheDocument();
    expect(screen.getByText('Ubuntu')).toBeInTheDocument();
    expect(screen.getByText('Windows')).toBeInTheDocument();
  });

  it('renders image names and paths', () => {
    render(
      <NebulaThemeProvider>
        <OperatingSystems osLayouts={mockOSLayouts} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.getByText('Ubuntu Image')).toBeInTheDocument();
    expect(screen.getByText('/images/ubuntu.img')).toBeInTheDocument();
    expect(screen.getByText('Windows Image')).toBeInTheDocument();
    expect(screen.getByText('/images/windows.img')).toBeInTheDocument();
  });

  it('toggles disabled checkbox and triggers onChange', () => {
    render(
      <NebulaThemeProvider>
        <OperatingSystems osLayouts={mockOSLayouts} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[0]);
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('handles empty OS layout list gracefully', () => {
    render(
      <NebulaThemeProvider>
        <OperatingSystems osLayouts={[]} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.getByText('No OS Layouts Found')).toBeInTheDocument();
  });
});
