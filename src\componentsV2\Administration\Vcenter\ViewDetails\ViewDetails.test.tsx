import React from 'react';
import { render, screen } from '@testing-library/react';
import ViewDetails from '.';
import { Provider as ReduxProvider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import { MemoryRouter } from 'react-router-dom';

const middlewares = [thunk];
const mockStore = configureStore(middlewares);

jest.mock('api/ApiService/context', () => ({
  useApiService: () => ({
    apiVCenterService: {
      addVCenter: jest.fn(() => Promise.resolve({ message: { requestId: '12345' } })),
    },
  }),
}));

jest.mock('sharedComponents/NblContainers/NblFormContainer', () => {
  return {
    __esModule: true,
    default: ({ children }: any) => <div data-testid="form-container">{children}</div>,
    useNblForms: () => ({
      nblFormProps: {
        setFieldValue: jest.fn(),
      },
      nblFormValues: {
        cloudDetails: [
          {
            cloudId: '1',
            datacenter: 'TestDC',
          },
        ],
      },
    }),
  };
});

jest.mock('./VCenterDetails', () => () => <div data-testid="vcenter-details">Mocked VCenterDetails</div>);
jest.mock('./CloudDetails', () => () => <div data-testid="cloud-details">Mocked CloudDetails</div>);

const mockPayload = {
  vCenterName: 'TestVC',
  vCenterHost: 'vc.example.com',
  vCenterPort: '443',
  vCenterProtocol: 'https',
  vCenterUser: 'admin',
  vCenterPassword: 'password',
  domain: 'corp.local',
  cloudDatacenter: '',
};

const mockResponse = {
  cloudDetails: [
    {
      cloudId: '1',
      cloudName: 'Test Cloud',
    },
  ],
};

describe('ViewDetails', () => {
  it('renders ViewDetails with mocked children', async () => {
    const store = mockStore({});
    render(
      <MemoryRouter>
        {' '}
        {/* ✅ Fix: wrap in MemoryRouter for useNavigate */}
        <ReduxProvider store={store}>
          <NebulaThemeProvider>
            <ViewDetails payload={mockPayload} response={mockResponse} />
          </NebulaThemeProvider>
        </ReduxProvider>
      </MemoryRouter>
    );

    expect(await screen.findByTestId('form-container')).toBeInTheDocument();
    expect(await screen.findByTestId('vcenter-details')).toBeInTheDocument();
  });
});
