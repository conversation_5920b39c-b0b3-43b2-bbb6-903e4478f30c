import { render, screen, fireEvent } from '@testing-library/react';
import Networks from '.';
// eslint-disable-next-line no-unused-vars
import { Network } from 'types/Interfaces/LookUpVCenterResponse';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('Networks Component', () => {
  const mockNetworks: Network[] = [
    {
      dhcp: true,
      disabled: false,
      dnsDomain: 'example.local',
      ipv4DhcpServer: '***********',
      ipv4DnsPrimary: '*******',
      ipv4DnsSecondary: '*******',
      ipv4Enabled: false,
      ipv4Gateway: '***********',
      ipv4Subnet: '',
      ipv6DhcpServer: 'fe80::2',
      ipv6DnsPrimary: '2001:4860:4860::8888',
      ipv6DnsSecondary: '2001:4860:4860::8844',
      ipv6Enabled: false,
      ipv6Gateway: 'fe80::1',
      ipv6Subnet: 'fe80::/64',
      name: 'Network One',
      networkMor: 'net-1',
      type: 'VM',
    },
    {
      dhcp: true,
      disabled: false,
      dnsDomain: 'example.local',
      ipv4DhcpServer: '***********',
      ipv4DnsPrimary: '*******',
      ipv4DnsSecondary: '*******',
      ipv4Enabled: false,
      ipv4Gateway: '***********',
      ipv4Subnet: '***********/24',
      ipv6DhcpServer: 'fe80::2',
      ipv6DnsPrimary: '2001:4860:4860::8888',
      ipv6DnsSecondary: '2001:4860:4860::8844',
      ipv6Enabled: false,
      ipv6Gateway: 'fe80::1',
      ipv6Subnet: 'fe80::/64',
      name: 'Network Two',
      networkMor: 'net-2',
      type: 'VMKernel',
    },
  ];

  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders network rows correctly', () => {
    render(
      <NebulaThemeProvider>
        <Networks networks={mockNetworks} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.getByText('Network One')).toBeInTheDocument();
    expect(screen.getByText('Network Two')).toBeInTheDocument();
  });

  it('renders all checkboxes and toggles ipv4Enabled', () => {
    render(
      <NebulaThemeProvider>
        <Networks networks={mockNetworks} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes.length).toBeGreaterThanOrEqual(6);

    fireEvent.click(checkboxes[0]);
    expect(mockOnChange).toHaveBeenCalledTimes(1);
    expect(mockOnChange.mock.calls[0][0][0].ipv4Enabled).toBe(true);
  });

  it('toggles disabled checkbox and updates state', () => {
    render(
      <NebulaThemeProvider>
        <Networks networks={mockNetworks} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[5]);
    expect(mockOnChange).toHaveBeenCalledTimes(1);
    expect(mockOnChange.mock.calls[0][0][1].disabled).toBe(false);
  });

  it('renders autocomplete and changes dnsDomain', () => {
    render(
      <NebulaThemeProvider>
        <Networks networks={mockNetworks} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );

    const selects = screen.getAllByPlaceholderText('Select');
    const input = selects[0];
    expect(input).toBeInTheDocument();
    fireEvent.change(input, { target: { value: 'newdomain.com' } });
    fireEvent.blur(input);
  });

  it('handles empty networks list', () => {
    render(
      <NebulaThemeProvider>
        <Networks networks={[]} onChange={mockOnChange} />
      </NebulaThemeProvider>
    );
    expect(screen.queryByText('Network One')).not.toBeInTheDocument();
  });
});
