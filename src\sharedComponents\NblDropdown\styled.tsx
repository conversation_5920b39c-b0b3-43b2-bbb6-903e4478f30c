import { MenuItem, Select } from '@mui/material';
import { styled } from '@mui/system';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledSelect = styled(Select)<{ theme?: NebulaTheme }>(({ theme }) => {
  const {
    typography,
    palette: { dropdown },
  } = theme;
  return {
    '& .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
    textTransform: 'capitalize',
    ...typography.subtitle2,
    fontWeight: typography.medium.fontWeight,
    width: '100%',
    color: dropdown.text,
  };
});

export const StyledMenuItem = styled(MenuItem)<{ theme?: NebulaTheme }>(({ theme }) => {
  const { typography, palette } = theme;
  const { dropdown } = palette;

  return {
    ...typography.body3,
    fontWeight: typography.medium.fontWeight,
    '&.MuiMenuItem-root.menuItem': {
      color: dropdown.menuItem,
      lineHeight: '2',
      textTransform: 'capitalize',
      border: 'none',
      background: 'none',
      '&:hover': {
        color: dropdown.text,
        background: 'none',
      },
    },
    '& .MuiSelect-icon': {
      transition: 'none',
      transform: 'none',
    },
  };
});
