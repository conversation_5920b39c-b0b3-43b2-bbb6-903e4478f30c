//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTooltip from 'sharedComponents/NblTooltip';
import NblTypography from 'sharedComponents/NblTypography';

type StoryProps = ComponentProps<typeof NblTooltip>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'NblTooltip',
  component: NblTooltip,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    tooltipMessage: { type: 'string' },
  },
};

export default meta;

export const Tooltip: Story = {
  args: {
    tooltipMessage: 'Please connect nebula admin',
  },
  render: (args) => (
    <NebulaTheme>
      <NblFlexContainer margin='16px'>
        <NblTooltip {...args} ><NblTypography variant='h5'>Help</NblTypography></NblTooltip>
      </NblFlexContainer>
    </NebulaTheme>
  ),
};
