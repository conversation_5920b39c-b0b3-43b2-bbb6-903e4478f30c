import React from 'react';
import { NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
// eslint-disable-next-line no-unused-vars
import { ColumnData, NblTable } from 'sharedComponents/NblTable';

export interface Host {
  hostMor: string;
  name: string;
  connectionState: string;
  powerState: string;
  disabled: boolean;
  location: string;
  vendor: string;
  model: string;
  cpu: number;
  core: number;
  memory: number;
}

interface Props {
  hosts: Host[];
}

const ClusterHostsDetails: React.FC<Props> = ({ hosts }) => {
  const columns: ColumnData[] = [
    { field: 'name', headerName: 'Host Name', flex: 1 },
    { field: 'location', headerName: 'Location', flex: 1 },
    { field: 'vendor', headerName: 'Vendor', flex: 1 },
    { field: 'model', headerName: 'Model', flex: 1 },
    { field: 'cpu', headerName: 'CPU', flex: 1 },
    { field: 'core', headerName: 'Cores', flex: 1 },
    { field: 'memory', headerName: 'Memory', flex: 1 },
  ];
  const rows = hosts.map((host) => ({
    id: host.hostMor,
    name: host.name,
    location: host.location,
    vendor: host.vendor,
    model: host.model,
    cpu: host.cpu,
    core: host.core,
    memory: host.memory,
  }));

  return (
    <NblGridItem colspan={12}>
      <NblTable columns={columns} rows={rows} rowsOverlayMessage="No Host Info Found" autoHeight />
    </NblGridItem>
  );
};

export default ClusterHostsDetails;
