import { useEffect, useState } from 'react';
import { useApiService } from '../api/ApiService/context';
import DomainList from '../types/Interfaces/DomainList';

const useGetDomainData = () => {
  const { apiPermissionService } = useApiService();

  const [domainData, setDomainData] = useState<DomainList[]>([]);

  useEffect(() => {
    const fetchDomainData = async () => {
      apiPermissionService.getDomains().then((res) => {
        if (res.status) {
          setDomainData(
            res.data.map((domainData) => ({
              ...domainData,
              domainName: domainData.domainName,
            }))
          );
        }
      });
    };
    fetchDomainData();
  }, []);
  return { domainData };
};

export default useGetDomainData;
