import React, { useRef, useState } from 'react';
import { ErrorText, StyledCloseIcon } from './styled';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import { NblGridContainer } from 'sharedComponents/NblContainers/NblGridContainer';
import NblButton from 'sharedComponents/Buttons/NblButton';
import NblTypography from 'sharedComponents/NblTypography';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { UploadIPIcon } from 'assets/images/icons/custom-icons';
import { NebulaTheme } from 'NebulaTheme/type';
import { useTheme } from '@mui/material';

interface FileUploadProps {
  accept?: string;
  maxSizeKB?: number;
  file?: File | null;
  onFileRemove?: () => void;
  onFileUpload: (file: File | null) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ accept = '.xlsx', file = null, maxSizeKB = 500, onFileUpload, onFileRemove }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(file);
  const [error, setError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const theme = useTheme<NebulaTheme>();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size / 1024 > maxSizeKB) {
        setError(`File size should be less than ${maxSizeKB}KB`);
        setSelectedFile(null);
        onFileUpload(null);
        return;
      }
      setError('');
      setSelectedFile(file);
      onFileUpload(file);
    }
  };
  return (
    <NblGridContainer alignItems="flex-start">
      <NblBorderContainer borderRadius="8px" borderType="dashed" padding="70px" width="420px">
        <NblButton buttonID={'choose-file-btn'} startIcon={<AttachFileIcon />} variant="contained" color="info" onClick={() => fileInputRef.current?.click()}>
          Choose File
          <input type="file" accept={accept} hidden ref={fileInputRef} onChange={handleFileChange} />
        </NblButton>
        <NblTypography variant="body2" color="shade1">
          Only {accept} files. {maxSizeKB}KB max file size.
        </NblTypography>
      </NblBorderContainer>
      {selectedFile && (
        <NblFlexContainer backgroundColor={theme.palette.tertiary.shade2.light} width="420px" padding="10px" justifyContent="space-between">
          <NblFlexContainer center={true}>
            <UploadIPIcon />
            <NblTypography variant="body2" color="shade8">
              {selectedFile.name}
            </NblTypography>
          </NblFlexContainer>
          <StyledCloseIcon
            onClick={() => {
              onFileRemove?.();
              setSelectedFile(null);
              onFileUpload(null);
            }}
          />
        </NblFlexContainer>
      )}

      {error && <ErrorText>{error}</ErrorText>}
    </NblGridContainer>
  );
};

export default FileUpload;
