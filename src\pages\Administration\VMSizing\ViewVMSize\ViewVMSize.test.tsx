import React from 'react';
import { render } from '@testing-library/react';
import ViewVMSizeForm from 'componentsV2/Administration/VMSize/ViewVMSizeForm';

jest.mock('componentsV2/Administration/VMSize/ViewVMSizeForm', () => ({
  __esModule: true,
  default: () => <div>Mocked ViewVMSizeForm</div>,
}));

describe('ViewVMSize', () => {
  it('renders the ViewVMSizeForm component', () => {
    const { getByText } = render(<ViewVMSizeForm permissions={{}} />);
    expect(getByText('Mocked ViewVMSizeForm')).toBeInTheDocument();
  });
});
