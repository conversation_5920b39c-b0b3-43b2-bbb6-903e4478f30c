import { Accordion, AccordionDetails, AccordionSummary, styled } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { ExpandIconSize } from '.';

export const StyledAccordion = styled(Accordion, { shouldForwardProp: (prop) => prop != 'bgColor' && prop != 'expandIconSize' })<{
  theme?: NebulaTheme;
  bgColor?: string;
  expandIconSize?: ExpandIconSize;
  border?: string;
}>(({ theme, bgColor, expandIconSize, border }) => {
  const { accordion, typography } = theme.palette;

  return {
    boxShadow: 'none !important',
    borderRadius: '10px !important',
    backgroundColor: bgColor || accordion.backgroundColor,
    width: '100%',
    '& .MuiAccordionSummary-expandIconWrapper': {
      color: typography.shade1,
    },
    ...(border && { border }),
    ...(expandIconSize === 'large' && {
      '&': {
        paddingTop: '20px',
        paddingBottom: '20px',
        '& .MuiAccordionSummary-content .MuiSvgIcon-root, & .MuiAccordionSummary-expandIconWrapper .MuiSvgIcon-root': {
          width: '2rem',
          height: '2rem',
          [theme.breakpoints.down('2K')]: {
            width: '1.5rem',
            height: '1.5rem',
          },
        },
      },
    }),
    '&:before': {
      display: 'none',
    },
  };
});

export const StyledAccordionSummary = styled(AccordionSummary)<{ theme?: NebulaTheme; margin?: string }>(({ theme, margin }) => {
  const { typography } = theme.palette;
  return {
    color: typography.shade1,
    margin: margin ? margin : '0px 30px',
    minWidth: '50px !important',
    '&.Mui-expanded': {
      minHeight: '50px',
    },
  };
});

export const StyledAccordionDetails = styled(AccordionDetails)<{ margin?: string }>(({ margin }) => ({
  alignContent: 'center',
  alignItems: 'left',
  ...(margin
    ? {
        margin,
      }
    : {
        marginLeft: '30px',
        marginRight: '30px',
      }),
}));
