import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblRadioGroup from '.';

describe('NblRadioGroup component', () => {
  const props = {
    label: 'IPv4',
    name: 'ipv4',
    disabled: false,
    checked: false,
    error: false,
  };
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblRadioGroup options={[]} {...props} onChange={() => {}} onBlur={() => {}} />
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
