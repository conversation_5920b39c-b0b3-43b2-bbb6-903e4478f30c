import { styled } from '@mui/system';
import { FormHelperText } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';

export const StyledFormHelperText = styled(FormHelperText)<{ theme?: NebulaTheme; error?: boolean }>(({ theme, error }) => {
  const { typography, palette } = theme;
  return {
    whiteSpace: 'break-spaces',
    '&.MuiFormHelperText-root': {
      ...typography.body3,
      bottom: 0,
      color: error ? palette.tertiary.shade3.medium : palette.secondary.shade5,
    },
  };
});
