import { useEffect, useState } from "react";
import { useNblForms } from "sharedComponents/NblContainers/NblFormContainer";
import { NblGridContainer, NblGridItem } from "sharedComponents/NblContainers/NblGridContainer";
import NblSelect from "sharedComponents/NblFormInputs/NblSelect";
import NblTextField from "sharedComponents/NblFormInputs/NblTextField";
import NblSpinner from "sharedComponents/NblSpinner";
import JiraService from 'api/ApiService/JiraService';
import { CAPABILITY_AREAS, JIRA_PRIORITY_LEVELS, REQUEST_FEATURE_HOVER_TEXTS } from "utils/constant";
import { FormValues } from "..";


const RequestFeatureForm = () => {
  const { nblFormProps, nblFormValues } = useNblForms<FormValues>();
  const jiraService = new JiraService();
  const [requestingOrganizationDropdowns, setRequestingOrganizationDropdowns] = useState<Array<{value: string, id: string}>>();
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchRequestingOrganizationDropdowns = () => {
    jiraService.requestFeaturesDropdowns().then((res) => {
      if (res.status) {
        setRequestingOrganizationDropdowns(res.data);
        setIsLoading(false);
      }
    });
  };

  useEffect(() => {
    fetchRequestingOrganizationDropdowns();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) {
    return (
      <NblSpinner
          display="inline"
          spinnerData={[
            {
              id: 'requestingOrganization',
              message: 'Please wait while Loading data',
              status: true,
            },
          ]}
        />
    )
  }

  return (
    <NblGridContainer columns={2} spacing={3}>
      <NblGridItem>
        <NblSelect
          label="Requesting Organization *"
          name="requestingOrganization"
          value={nblFormValues.requestingOrganization}
          placeholder="Select"
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          error={Boolean(nblFormProps.touched.requestingOrganization && nblFormProps.errors.requestingOrganization)}
          options={requestingOrganizationDropdowns?.map((data) => ({ label: data.value, value: data.value }))}
          helperText={REQUEST_FEATURE_HOVER_TEXTS['requestingOrganization']}
        />
      </NblGridItem>
      <NblGridItem>
        <NblSelect
          label="Capability Area *"
          name="capabilityArea"
          value={nblFormValues.capabilityArea}
          placeholder="Select"
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          error={Boolean(nblFormProps.touched.capabilityArea && nblFormProps.errors.capabilityArea)}
          options={CAPABILITY_AREAS.map((area) => ({ label: area, value: area }))}
          helperText={REQUEST_FEATURE_HOVER_TEXTS['capabilityArea']}
        />
      </NblGridItem>
      <NblGridItem>
        <NblSelect
          name="priority"
          label="Priority *"
          value={nblFormValues.priority}
          placeholder="Select"
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          error={Boolean(nblFormProps.touched.priority && nblFormProps.errors.priority)}
          options={JIRA_PRIORITY_LEVELS.map((level) => ({ label: level.label, value: level.value }))}
          helperText={REQUEST_FEATURE_HOVER_TEXTS['priority']}
        />
      </NblGridItem>
      <NblGridItem>
        <NblTextField
          type="text"
          name="summary"
          label="Summary *"
          placeholder="Jira summary"
          value={nblFormValues.summary}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          error={Boolean(nblFormProps.touched.summary && nblFormProps.errors.summary)}
          helperText={REQUEST_FEATURE_HOVER_TEXTS['summary']}
          />
      </NblGridItem>
      <NblGridItem>
        <NblTextField
          type="text"
          name="description"
          label="Description *"
          placeholder="Jira description"
          value={nblFormValues.description}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          minRows={4}
          multiline
          error={Boolean(nblFormProps.touched.description && nblFormProps.errors.description)}
          helperText={REQUEST_FEATURE_HOVER_TEXTS['description']}
          />
      </NblGridItem>
      <NblGridItem>
        <NblTextField
          type="text"
          name="justification"
          label="Justification *"
          placeholder="Justification"
          value={nblFormValues.justification}
          handleChange={nblFormProps.handleChange}
          handleBlur={nblFormProps.handleBlur}
          rows={4}
          multiline
          error={Boolean(nblFormProps.touched.justification && nblFormProps.errors.justification)}
          helperText={REQUEST_FEATURE_HOVER_TEXTS['justification']}
        />
      </NblGridItem>
    </NblGridContainer>
  );
}

export default RequestFeatureForm;