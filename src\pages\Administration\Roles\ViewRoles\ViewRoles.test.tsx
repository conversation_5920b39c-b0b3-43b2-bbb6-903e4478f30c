import { render, act } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';
import { BrowserRouter as Router } from 'react-router-dom';

import ViewRoles from '..';

describe('View roles component', () => {
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <ThemeProvider>
            <Router>
              <ViewRoles />
            </Router>
          </ThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
