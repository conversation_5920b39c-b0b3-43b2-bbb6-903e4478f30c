export default interface CertificateFormValues {
  domain: string;
  projectName: string;
  application: string;
  environment: string;
  appId: string;
  applicationName: string;

  //Internal
  certificateName: string;
  certificateEmail: string;
  orgName: string;
  orgUnit: string;
  state: string;
  city: string;
  dnsName: string;
  divisionName: string;
  locality: string;
  keySize: string;
  country: string;
  subjectAlternateNames: string[];
  keyType: string;
  validity: string;
  certPassword: string;
  policyFolder: { name: string; value: string };
  format: string;
  subject: string;
  certificateType: string;
  certificateFormat: string;
}
