import { calculateStatusLevel } from './status';
const lowUsage = '#0E99D8';
const mediumUsage = '#16AA04';
const highUsage = '#F4AE13';
const warningUsage = '#FD7602';
const criticalUsage = '#FD0901';
const deadUsage = '#000000';

export const usageColorsArray = [lowUsage, mediumUsage, highUsage, warningUsage, criticalUsage, deadUsage];

export const usageColors = {
  lowUsage,
  mediumUsage,
  highUsage,
  warningUsage,
  criticalUsage,
  deadUsage,
};

export const getUsageColor = (status: number) => usageColorsArray[calculateStatusLevel(status)];
