import { render, screen } from '@testing-library/react';
import { useApiService } from 'api/ApiService/context';
import { MemoryRouter } from 'react-router-dom';
import EditSecretDetails from 'componentsV2/SecretsManagement/CreateNewSecret/EditSecretDetails';
import ReduxProvider from 'mock/ReduxProvider';

jest.mock('api/ApiService/context', () => ({
  useApiService: jest.fn(),
}));

jest.mock('sharedComponents/NblContainers/NblFormContainer', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

jest.mock('componentsV2/SecretsManagement/CreateNewSecret/CreateNewSecretForm', () => () => <div>CreateNewSecretForm Rendered</div>);
jest.mock('componentsV2/SecretsManagement/CreateNewSecret/EditSecretDetails', () => () => <div>Edit Secret Details Rendered</div>);

describe('CreateNewSecret Component', () => {
  beforeEach(() => {
    (useApiService as jest.Mock).mockReturnValue({
      apiAssetService: {
        getMyResourcesv2: jest.fn().mockResolvedValue({ status: true, data: { items: [] } }),
      },
    });
  });

  it('renders correctly in edit mode and shows the form', () => {
    render(
      <MemoryRouter>
        <ReduxProvider>
          <EditSecretDetails />
        </ReduxProvider>
      </MemoryRouter>
    );
    expect(screen.getByText('Edit Secret Details Rendered')).toBeInTheDocument();
  });
});
