import { act, render } from '@testing-library/react';

import NebulaThemeProvider from 'mock/NebulaThemeProvider';
import NblAccordion from '.';

describe('NblAccordion component', () => {
  const props = {
    summary: 'VM Details',
    hasDivider: true,
    defaultExpanded: false,
  }
  test('Should render component passed', async () => {
    const component = await act(
      async () =>
        render(
          <NebulaThemeProvider>
            <NblAccordion {...props}>
              VM Details
            </NblAccordion>
          </NebulaThemeProvider>
        ).baseElement
    );
    expect(component).toBeInTheDocument();
  });
});
