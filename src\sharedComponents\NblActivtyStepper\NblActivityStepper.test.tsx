// NblActivityStepper.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import NblActivityStepper, { ActivityStatus } from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('NblActivityStepper Component', () => {
  const activities = [
    {
      status: 'completed' as ActivityStatus,
      name: 'Activity 1',
      timeTaken: '2 min',
      startTime: '2023-01-01T10:00:00',
      endTime: '2023-01-01T10:02:00',
    },
    {
      status: 'progress' as ActivityStatus,
      name: 'Activity 2',
      timeTaken: '5 min',
      startTime: '2023-01-01T10:10:00',
      endTime: '2023-01-01T10:15:00',
    },
    {
      status: 'failed' as ActivityStatus,
      name: 'Activity 3',
      timeTaken: '3 min',
      startTime: '2023-01-01T10:20:00',
      endTime: '2023-01-01T10:23:00',
    },
  ];

  test('renders the activities correctly', () => {
    render(
      <NebulaThemeProvider>
        <NblActivityStepper activities={activities} />
      </NebulaThemeProvider>
    );

    // Check if the activity names are rendered
    activities.forEach((activity) => {
      expect(screen.getByText(activity.name)).toBeInTheDocument();
    });

    // Check if the status of each activity is displayed correctly
    expect(screen.getByText('completed')).toBeInTheDocument();
    expect(screen.getByText('progress')).toBeInTheDocument();
    expect(screen.getByText('failed')).toBeInTheDocument();
  });

  test('calls onActivityClick when an activity is clicked', () => {
    const onActivityClick = jest.fn();
    render(
      <NebulaThemeProvider>
        <NblActivityStepper activities={activities} onActivityClick={onActivityClick} />
      </NebulaThemeProvider>
    );

    // Click on the first activity
    fireEvent.click(screen.getByText('Activity 1'));

    // Ensure the click handler is called with the correct activity name
    expect(onActivityClick).toHaveBeenCalledWith('Activity 1');
  });
});
