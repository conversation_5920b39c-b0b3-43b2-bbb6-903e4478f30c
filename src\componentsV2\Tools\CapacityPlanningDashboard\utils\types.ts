export interface CalculatedVropsResource {
  resourceid: string;
  resourcename: string;
  resourcelabel: string | undefined;
  facilityName?: string;
  facilityId?: number;
  cpu: string;
  memory: string;
  storage: string;
  cpuUtilized: number;
  memoryUtilized: number;
  storageUtilized: number;
  cpuAllocated?: number;
  memoryAllocated?: number;
  storageAllocated?: number;
  status: string;
  domainid: number;
  domainname: string;
}
export interface VropResourceStatistic {
  lasttimestamp: number;
  resourceid?: string;
  statname: string;
  statvalue: number;
}

export interface VropResource {
  resourceid: string;
  resourcetype: string;
  resourcename: string;
  parentid?: string;
  resourcelevel?: number;
  lateststats?: VropResourceStatistic[];
  label?: string;
  latitude?: number;
  root_id?: string;
  longitude?: number;
  domainid: number;
  domainname: string;
}
export interface Facility {
  facilityid: number;
  facilityname: string;
  streetaddress: string;
  city: string;
  latitude: number;
  longitude: number;
  statecode: string;
  zip: number;
  facilitytype: string;
  region: string;
  resources: VropsResourceInFacility[];
}
export interface CalculatedFacility {
  facilityid: number;
  facilityname: string;
  streetaddress: string;
  city: string;
  latitude: number;
  longitude: number;
  statecode: string;
  zip: number;
  facilitytype: string;
  region: string;
  resources: CalculatedVropsResource[];
}

export interface VropsResourceInFacility {
  resourceid: string;
  resourcename: string;
  resourcelabel: string;
  latitude: number;
  longitude: number;
  domainid: number;
  domainname: string;
}

export interface TableDetailsDataType {
  site: string;
  label: string;
  statusAndUtilizationIcon: JSX.Element;
  cpuAndUtilizationIcon: JSX.Element;
  memoryAndUtilizationIcon: JSX.Element;
  storageAndUtilizationIcon: JSX.Element;
  resource: VropResource;
  maxutilized: number;
}
export interface TableBodyProps {
  data: TableDetailsDataType;
  handleHoveredResource: (resource: VropResource) => void;
  handleClickedResource: (resourceID: string) => void;
}
