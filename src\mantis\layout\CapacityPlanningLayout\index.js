import { useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

// material-ui
import { useTheme, styled } from '@mui/material/styles';
import { Box, Toolbar, Typography, useMediaQuery } from '@mui/material';
import { clearBreadcrumb } from 'store/reducers/breadcrumbs';

// project import

import Spinner from 'components/Spinner';
import ErrorBoundary from 'ErrorBoundary';

// types
import { openDrawer } from 'store/reducers/menu';
import Header from '../MainLayout/Header';
import Drawer from '../MainLayout/Drawer';
import Footer from '../MainLayout/Footer';

const StyledSpinnerWrapper = styled(Box)(({ theme }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.8)',
  zIndex: 99999,
  color: theme.palette.common.white,
}));

const StyledLayoutWrapper = styled(Box)(() => ({
  display: 'flex',
  width: '100%',
  backgroundColor: 'white',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
  backgroundSize: 'cover',
  height: '100%;',
}));

const StyledOutletWrapper = styled(Box)(() => ({
  '.CatalogTiles-Grid > .MuiGrid-root': {
    boxShadow: '1px 1px 10px 3px lightgrey',
  },
  '.Catalog-Grid': {
    boxShadow: '1px 1px 10px 3px lightgrey',
    padding: 40,
  },
  '.Catalog-Grid > .MuiGrid-root': {
    boxShadow: '1px 1px 10px 3px lightgrey',
  },
  '.ContentViewport-StyledGrid': {
    padding: 0,
  },
}));

// ==============================|| MAIN LAYOUT ||============================== //

const CapacityPlanningLayout = () => {
  const theme = useTheme();
  const {
    palette: { common },
  } = theme;
  const matchDownLG = useMediaQuery(theme.breakpoints.down('lg'));
  const dispatch = useDispatch();

  const { drawerOpen } = useSelector((state) => state.menu);
  const { spinnerData } = useSelector((state) => state.spinner);

  // drawer toggler
  const [open, setOpen] = useState(drawerOpen);
  const handleDrawerToggle = () => {
    setOpen(!open);
    dispatch(openDrawer({ drawerOpen: !open }));
  };

  // set media wise responsive drawer
  useEffect(() => {
    setOpen(!matchDownLG);
    dispatch(openDrawer({ drawerOpen: !matchDownLG }));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matchDownLG]);

  useEffect(() => {
    if (open !== drawerOpen) setOpen(drawerOpen);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [drawerOpen]);
  useEffect(()=>{
    return () => {
      dispatch(clearBreadcrumb()) 
    }
  },[])

  const getMessage = () => {
    return spinnerData.map((row, index) => <Typography key={`message-${index}`}>{row.message}</Typography>);
  };
  //const location = useLocation();

  return (
    <StyledLayoutWrapper>
      {spinnerData?.length && (
        <StyledSpinnerWrapper>
          <Spinner color={common.white} message={getMessage()} />
        </StyledSpinnerWrapper>
      )}
      <Header open={open} />
      <Drawer open={open} handleDrawerToggle={handleDrawerToggle} />
      <Box
        component="main"
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          width: '100%',
          minHeight: '100vh',
          pb: 8,
          flexGrow: 1,
          overflowY: 'auto',
          // backgroundColor: location.pathname.includes("nebuladashboard/usagemetrics") ? "#F0F3F5" : "#FFFFFF"
        }}
      >
        <Box sx={{ p: { xs: 2, sm: theme.spacing(3, 5.25, 2, 4) }, height: `calc(100vh - ${54}px)`, overflow: 'auto' }}>
          <Toolbar />
          <StyledOutletWrapper>
            <ErrorBoundary>
              <Outlet />
            </ErrorBoundary>
          </StyledOutletWrapper>
        </Box>
        <Footer />
      </Box>
    </StyledLayoutWrapper>
  );
};

export default CapacityPlanningLayout;
