import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import withAdminPermissions from 'hoc/withAdminPermissions';
import RequestsGrid from 'components/RequestsGrid';
import AdministrationService from 'api/ApiService/AdministrationService';
import TagDataProps from 'types/Interfaces/TagDataProps';
import ActionsColumn from '../ActionsColumn';
import { SPINNER_IDS, showSpinner } from 'store/reducers/spinner';
// eslint-disable-next-line no-unused-vars
import { AdminGridProps, AdministrationDialogData, AdminComponent } from 'types';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'mantis/themes/type';
import { useMediaQuery, useTheme } from '@mui/material';
import { getAdminColumnWidth } from 'utils/common';

interface ViewTagsTableProps extends AdminGridProps {}

const DIALOG_DATA: AdministrationDialogData = {
  initialState: { open: false, type: '', title: '', content: '', confirmationText: '', data: {} },
  deleteDialog: {
    title: 'Confirmation',
    content: 'Shall we proceed with this delete request?',
    confirmationText: 'Delete',
  },
};

const ViewTagsTable: AdminComponent<ViewTagsTableProps> = ({ permissions }: ViewTagsTableProps) => {
  const apiAdministrationService = new AdministrationService();
  const dispatch = useDispatch();
  const theme: NebulaTheme = useTheme();
  const isSmaller = useMediaQuery(theme.breakpoints.down('xl'));
  const [confirmDialog, setConfirmDialog] = useState<AdministrationDialogData['initialState']>(DIALOG_DATA.initialState);
  const [viewTagsListData, setTagsListData] = useState<TagDataProps[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [viewDetailsDialog, setViewDetailsDialog] = useState<{ open: boolean; id: string }>({ open: false, id: '' });

  const fetchTagsList = () => {
    setIsLoading(true);
    dispatch(showSpinner({ id: SPINNER_IDS.tags, status: true, message: 'Fetching tags...' }));
    apiAdministrationService
      .getTags()
      .then((res) => {
        if (res.status) {
          setTagsListData(
            res.data.map((viewData) => ({
              tagName: viewData?.tags?.map((tag) => tag.tagKey).join(','),
              id: viewData?.serviceCatalogId,
              description: viewData?.description,
              catalogItem: viewData?.name,
              catalogShortName: viewData?.shortName,
            }))
          );
        } else {
          setTagsListData([]);
        }
      })
      .finally(() => {
        setIsLoading(false);
        dispatch(showSpinner({ id: SPINNER_IDS.tags, status: false, message: '' }));
      });
  };
  useEffect(() => {
    fetchTagsList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: GridColDef[] = [
    {
      field: 'tagName',
      headerName: 'Tag Name',
      flex: 1,
    },
    {
      field: 'catalogItem',
      headerName: 'Service Catalog Item',
      flex: 1,
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      width: getAdminColumnWidth(isSmaller),
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        const {
          row: { catalogShortName },
        } = params;
        const deleteClickHandler = () => {
          setConfirmDialog({ open: true, type: 'DELETED', ...DIALOG_DATA.deleteDialog, data: params });
        };
        return (
          <ActionsColumn
            permissions={permissions}
            disableDelete
            editUrl={`/administration/tags/${catalogShortName}`}
            onDeleteHandler={() => deleteClickHandler}
          />
        );
      },
    },
  ];

  const dialogConfirmHandler = () => {
    const {
      row: { id },
    } = confirmDialog.data;
    setTagsListData(viewTagsListData.filter((obj) => obj.id !== id));
    setConfirmDialog(DIALOG_DATA.initialState);
  };

  const dialogCloseHandler = (): void => {
    setConfirmDialog(DIALOG_DATA.initialState);
  };

  return (
    <>
      <RequestsGrid
        isLoading={isLoading}
        rows={viewTagsListData}
        columns={columns}
        viewDetailsDialog={viewDetailsDialog}
        setViewDetailsDialog={setViewDetailsDialog}
        confirmDialog={confirmDialog}
        dialogConfirmHandler={dialogConfirmHandler}
        dialogCloseHandler={dialogCloseHandler}
        fetchData={fetchTagsList}
      />
    </>
  );
};

ViewTagsTable.type = ADMIN_TILE_PERMISSION_TYPE.grid;

export default withAdminPermissions(ViewTagsTable);
