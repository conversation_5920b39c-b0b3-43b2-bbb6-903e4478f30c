import { useEffect, useState } from 'react';
import { getServiceCatalogData } from 'api/static-data';
import Catalog from 'components/Catalog';
import { CatalogTilesData } from 'types';

export default function ServiceCatalogGroup() {
  const [content, setContent] = useState<CatalogTilesData[]>([]);
  useEffect(() => {
    async function getCatalogItems() {
      try {
        const data = await getServiceCatalogData();
        console.log(data);
        setContent(data);
      } catch (error) {
        console.log(error);
      }
    }
    getCatalogItems();
  }, []);

  return content?.length ? <Catalog catalogItems={content} isAdminTiles /> : <div>No items to display</div>;
}
