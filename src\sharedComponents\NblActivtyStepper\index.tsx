import { KeyboardArrowLeft, KeyboardArrowRight } from '@mui/icons-material';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import DoneRoundedIcon from '@mui/icons-material/DoneRounded';
import HourglassEmptyRoundedIcon from '@mui/icons-material/HourglassEmptyRounded';
import ReportProblemRoundedIcon from '@mui/icons-material/ReportProblemRounded';
import LayersOutlinedIcon from '@mui/icons-material/LayersOutlined';
import NblDivider from 'sharedComponents/NblDivider';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import React, { useEffect, useRef } from 'react';
import { useTheme } from '@mui/material';
import { NebulaTheme } from 'NebulaTheme/type';
import { StyledTypography } from './styled';
import NblTooltip from 'sharedComponents/NblTooltip';
import { getHexColorWithOpacity } from 'utils/common';

export const NBLACTIVITYSTATUS = {
  progress: 'progress',
  completed: 'completed',
  pending: 'pending',
  failed: 'failed',
  skipped: 'skipped',
  'not started': 'not-started',
  'completed with error': 'completed with error',
};

export type ActivityStatus = keyof typeof NBLACTIVITYSTATUS;

export interface NblActivityStepperProps {
  activities: {
    status: ActivityStatus;
    name: string;
    timeTaken: string;
    startTime: string;
    endTime: string;
    toolTipMessage?: string;
    hasSubtasks?: boolean;
  }[];
  onActivityClick?: (activity: string) => void;
  selectedActivity?: string;
}

interface ActivityStatusIconProps {
  status: ActivityStatus;
  index?: number;
}

const NblActivityStepper: React.FC<NblActivityStepperProps> = ({ activities, onActivityClick, selectedActivity }) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();
  const containerRef = useRef<HTMLDivElement>(null);
  const activityRef = useRef<HTMLDivElement>(null);

  //Local variables
  const totalActivities = activities?.length;
  const lastIndex = totalActivities - 1;
  const palette = theme.palette.activityStepper;

  //Utils
  function hideDivider(show: boolean) {
    return show ? 'visible' : 'hidden';
  }

  function getDividerColor(index: number, prev: boolean) {
    if (prev) {
      const prevStatus = activities[index - 1].status;
      return ['pending', 'progress'].includes(prevStatus) ? palette.variant.pending.icon : palette.variant[prevStatus].icon;
    } else {
      return palette.variant[activities[index].status].icon;
    }
  }

  function scrollOnArrowClick(direction: number) {
    containerRef.current?.scrollBy({ left: direction * containerRef.current.offsetWidth, behavior: 'smooth' });
  }

  //Side Effects
  useEffect(() => {
    if (containerRef.current && activityRef.current) {
      const container = containerRef.current;
      const activity = activityRef.current;
      const activityCard = activity.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      if (activityCard.right > containerRect.right) {
        container?.scrollBy({ left: activityCard.width * 2.5, behavior: 'smooth' });
      }
      if (activityCard.left < containerRect.left + 50) {
        container?.scrollBy({ left: -activityCard.width * 2.5, behavior: 'smooth' });
      }
    }
  }, [selectedActivity]);

  //Renders
  const arrow = (left?: boolean) => {
    const color = theme.palette.primary.main;
    return (
      <NblFlexContainer
        center
        width="25px"
        height="25px"
        borderRadius="50%"
        flex="0 0 25px"
        cursor="pointer"
        border={`2px solid ${color}`}
        onClick={() => scrollOnArrowClick(left ? -1 : 1)}
      >
        {left ? <KeyboardArrowLeft sx={{ color: color }} /> : <KeyboardArrowRight sx={{ color: color }} />}
      </NblFlexContainer>
    );
  };

  //JSX
  return (
    <NblFlexContainer alignItems="center" spacing={2}>
      {arrow(true)}
      <NblFlexContainer ref={containerRef} overflowX="hidden" width="calc(100% - 50px)" spacing={0}>
        {activities?.map((activity, index) => (
          <NblTooltip tooltipMessage={activity.toolTipMessage || ''} key={activity.name}>
            <NblFlexContainer
              ref={selectedActivity === activity.name ? activityRef : null}
              padding="8px 0"
              alignItems="center"
              direction="column"
              minWidth="250px"
              minHeight="173px"
              borderRadius="5px"
              cursor="pointer"
              backgroundColor={selectedActivity === activity.name ? palette.selectedBg : ''}
              onClick={(e) => {
                e.stopPropagation();
                onActivityClick?.(activity.name);
              }}
              flex={'1 0 250px'}
            >
              <NblFlexContainer alignItems="center" height="auto" spacing={0.5}>
                <NblDivider
                  length="calc(50% - 15px - 4px)" //50% - padding left -spacing(8*0.5)
                  visibility={hideDivider(index > 0)}
                  color={getDividerColor(index, index > 0)}
                  opacity={['pending', 'progress'].includes(activities[index - 1]?.status) ? 0.1 : 1}
                  strokeWidth={0.3}
                  mt={3}
                />
                <NblFlexContainer
                  backgroundColor={palette.variant[activity.status].icon}
                  center
                  width="30px"
                  height="30px"
                  borderRadius="100%"
                >
                  <ActivityStatusIcon status={activity.status} index={index} />
                </NblFlexContainer>
                <NblDivider
                  length="calc(50% - 15px - 4px)" //50% - padding right -spacing(8*0.5)
                  visibility={hideDivider(index < lastIndex)}
                  color={getDividerColor(index, false)}
                  opacity={activity.status === 'pending' ? 0.1 : 1}
                  strokeWidth={0.3}
                  mt={3}
                />
              </NblFlexContainer>
              <StyledTypography variant="subtitle2" color={palette.activityName} title={activity.name}>
                {activity.name}
              </StyledTypography>
              <NblFlexContainer
                width="calc(100% - 16px)"
                direction="column"
                borderRadius="6px"
                height="100px"
                spacing={0}
                backgroundColor={palette.cardBody}
              >
                <NblFlexContainer
                  justifyContent="space-between"
                  alignItems="center"
                  padding="4px"
                  height="30px"
                  backgroundColor={palette.variant[activity.status].cardHeader}
                  borderRadius="6px 6px 0 0"
                >
                  <StyledTypography variant="body3" color={palette.variant[activity.status].cardHeaderText}>
                    {activity.status}
                  </StyledTypography>
                  <StyledTypography variant="body3" color={palette.variant[activity.status].cardHeaderText}>
                    {activity.timeTaken}
                  </StyledTypography>
                </NblFlexContainer>
                <NblGridContainer
                  columns={2}
                  padding="4px"
                  height="calc(100% - 30px)"
                  border={`1px solid ${palette.variant[activity.status].cardBorder}`}
                  borderTop="none"
                >
                  <NblGridItem>
                    <NblFlexContainer direction="column">
                      <NblTypography variant="subtitle2" color="shade1">
                        Start
                      </NblTypography>
                      <NblTypography variant="body3" color="shade1" weight="medium">
                        {activity.startTime ? new Date(activity.startTime).toLocaleString() : '-'}
                      </NblTypography>
                    </NblFlexContainer>
                  </NblGridItem>
                  <NblGridItem>
                    <NblFlexContainer direction="column" alignItems="flex-end">
                      <NblTypography variant="subtitle2" color="shade1" textAlign="right">
                        End
                      </NblTypography>
                      <NblTypography variant="body3" color="shade1" weight="medium" textAlign="right">
                        {activity.endTime ? new Date(activity.endTime).toLocaleString() : '-'}
                      </NblTypography>
                    </NblFlexContainer>
                  </NblGridItem>
                </NblGridContainer>
              </NblFlexContainer>
              {
                activity.hasSubtasks && (
                  <NblFlexContainer
                    backgroundColor={getHexColorWithOpacity(palette.variant[activity.status].cardHeader, 40)}
                    borderRadius="6px"
                    center
                    width={`calc(100% - ${16}px)`}
                    height={'30px'}
                  >
                    <LayersOutlinedIcon sx={{color: palette.variant[activity.status].cardHeader}}></LayersOutlinedIcon>
                    <StyledTypography variant="subtitle2" color={palette.variant[activity.status].cardHeader}>
                      SubTask Included
                    </StyledTypography>
                  </NblFlexContainer>
                )
              }
            </NblFlexContainer>
          </NblTooltip>
        ))}
      </NblFlexContainer>
      {arrow()}
    </NblFlexContainer>
  );
};

export const ActivityStatusIcon: React.FC<ActivityStatusIconProps> = ({ status, index }) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();
  const iconColor = theme.palette.secondary.main;

  //Local
  const palette = theme.palette.activityStepper.variant;
  let icon;
  switch (status) {
    case 'completed':
      icon = <DoneRoundedIcon sx={{ color: iconColor }} />;
      break;
    case 'progress':
      icon = <HourglassEmptyRoundedIcon sx={{ color: iconColor }} />;
      break;
    case 'failed':
      icon = <ReportProblemRoundedIcon sx={{ color: iconColor }} />;
      break;
    case 'completed with error':
      icon = <ReportProblemRoundedIcon sx={{ color: iconColor }} />;
      break;
    default:
      icon = (
        <NblTypography variant="subtitle1" color="shade4">
          {index ? index + 1 : null}
        </NblTypography>
      );
  }

  //JSX
  return (
    <NblFlexContainer backgroundColor={palette[status].icon} center width="30px" height="30px" borderRadius="100%">
      {icon}
    </NblFlexContainer>
  );
};

export default NblActivityStepper;
