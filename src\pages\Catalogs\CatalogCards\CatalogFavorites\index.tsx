import React from 'react';
import NblCard from 'sharedComponents/NblCard';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

interface CatalogFavoritesProps {
  icon: string;
  id: string;
  shortName: string;
  name: string;
  navigateToCatalogItem: () => void;
}
const CatalogFavorites: React.FC<CatalogFavoritesProps> = ({ icon, id, shortName, name, navigateToCatalogItem }) => {
  return (
    <NblFlexContainer flex={'0 0 auto'} width="auto">
      <NblCard
        expanded={false}
        color="primary"
        width="100%"
        icon={icon}
        id={id}
        onClickCard={navigateToCatalogItem}
        shortName={shortName}
        title={name}
      ></NblCard>
    </NblFlexContainer>
  );
};

export default CatalogFavorites;
