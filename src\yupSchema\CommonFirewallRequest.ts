import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';
import { validateIPAddress } from 'yupSchema/AddFirewallRules';
import { COMMONFIREWALL_FORM_HOVER_TEXTS } from 'utils/constant';

type ValidationSchema = {
  setIpAddressCorrection: (params: string) => void;
};

const allowedProtocols = ['TCP', 'UDP', 'HTTP', 'SMTP', 'FTP', 'SNMP'];

const validatePortProtocol = (value: string | undefined) => {
  if (value) {
    const multiplePortsProtocols = value.split(',');
    for (let multiplePortProtocol of multiplePortsProtocols) {
      const splittedProtocol = multiplePortProtocol.split(':');
      const [port, protocol] = splittedProtocol;
      const trimmedPort = port?.trim();
      const trimmedProtocol = protocol?.trim();

      if (splittedProtocol.length !== 2) {
        // should not allow 8080:tcp:xyz format
        return false;
      }

      if (!trimmedPort || !/^\d+$/.test(trimmedPort)) {
        // only allow digit without spaces
        return false;
      }

      const portNumber = parseInt(trimmedPort, 10);

      if (isNaN(portNumber) || portNumber < 0 || portNumber > 65535) {
        return false;
      }

      if (!trimmedProtocol || !/^\w+$/.test(trimmedProtocol) || !allowedProtocols.includes(trimmedProtocol.toUpperCase())) {
        return false;
      }
    }
    return true;
  }
  // Now Inbound and Outbound Port field are optional
  return true;
};

export const ipAddressSchema = (setIpAddressCorrection: (param: string) => void) =>
  yup.string().test('valid-ip-address', 'Please enter valid IP addresses', function (value) {
    const validIPAddress = validateIPAddress(value, this.createError);
    if (validIPAddress && validIPAddress.correctionMade && validIPAddress.correctedValue !== value) {
      setIpAddressCorrection(validIPAddress.correctedValue);
      return false;
    }
    return validIPAddress;
  });

const commonFirewallRequestSchema = ({ setIpAddressCorrection }: ValidationSchema) =>
  yup.object().shape({
    project: yup
      .string()
      .required('Project is required'),
    requestCreator: yup.string(),
    // appId: yup.string().matches(yupMatchesParams.appId.pattern, yupMatchesParams.appId.errorMessage), APP ID will be set automatically onSubmit
    genomeId: yup.string().trim().matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),
    requestName: yup
      .string()
      .required('Request Name is required')
      .trim()
      .matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),
    commonApplication: yup
      .string()
      .trim()
      .required('Common Application is required')
      .matches(yupMatchesParams.alphaNumericChars.pattern, yupMatchesParams.alphaNumericChars.errorMessage),
    ipAddress: yup
      .array()
      .of(ipAddressSchema(setIpAddressCorrection))
      .required('Application IP Addresses/Ranges')
      .min(1, 'Please enter atleast one Application IP Address/Range')
      .test('unique', 'Duplicate IP Addresses are not allowed', function (value) {
        const ipAddresses = value || [];
        const uniqueIpAddresses = new Set(ipAddresses);
        return ipAddresses.length === uniqueIpAddresses.size;
      }),
    inboundPort: yup
      .string()
      .test('at-least-one', 'Inbound Port and Protocol', function (value) {
        const { outboundPort } = this.parent;
        if (!value && !outboundPort) {
          return this.createError({ message: COMMONFIREWALL_FORM_HOVER_TEXTS.atleastInboundOutboundPort });
        }
        return true;
      })
      .test('is-valid-port-protocol', 'Please follow this format "port:protocol" (e.g., "8088:TCP,8080:UDP")', validatePortProtocol),
    outboundPort: yup
      .string()
      .test('at-least-one', 'Outbound Port and Protocol is required', function (value) {
        const { inboundPort } = this.parent;
        if (!value && !inboundPort) {
          return this.createError({ message: COMMONFIREWALL_FORM_HOVER_TEXTS.atleastInboundOutboundPort });
        }
        return true;
      })
      .test('is-valid-port-protocol', 'Please follow this format "port:protocol" (e.g., "8088:TCP,8080:UDP")', validatePortProtocol),
    reason: yup.string().trim().matches(yupMatchesParams.description.pattern, yupMatchesParams.description.errorMessage),
  });

export default commonFirewallRequestSchema;
