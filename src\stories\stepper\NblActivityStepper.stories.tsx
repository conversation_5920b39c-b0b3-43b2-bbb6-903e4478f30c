//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import { ComponentProps, useState } from 'react';
import NebulaTheme from 'NebulaTheme';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblActivityStepper from 'sharedComponents/NblActivtyStepper';

type StoryProps = ComponentProps<typeof NblActivityStepper>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'stepper/NblActivityStepper',
  component: NblActivityStepper,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: { onActivityClick: { action: 'onActivityClick', type: 'function' } },
};

export default meta;

const Template = (args: StoryProps) => {
  //Hooks
  const [selectedActivity, setSelectedActivity] = useState(args.selectedActivity);
  return <NblActivityStepper {...args} selectedActivity={selectedActivity} onActivityClick={(v) => setSelectedActivity(v)} />;
};

export const Stepper: Story = {
  args: {
    selectedActivity: 'Request Approval',
    activities: [
      {
        name: 'Create VM Request',
        status: 'completed',
        timeTaken: '00:10:32',
        startTime: '08/08/2024, 09:47:36',
        endTime: '08/08/2024, 09:48:23',
      },
      {
        name: 'Request Approval',
        status: 'progress',
        timeTaken: '-',
        startTime: '-',
        endTime: '-',
      },
      {
        name: 'IP Reservation',
        status: 'pending',
        timeTaken: '-',
        startTime: '-',
        endTime: '-',
      },
    ],
  },
  render: (args) => (
    <NebulaTheme>
      <NblFlexContainer padding="10px">
        <Template {...args} />
      </NblFlexContainer>
    </NebulaTheme>
  ),
};
