import AccessKeys from './AccessKeys';
import AddDisk from './AddDisk';
import ComplianceDetails from './ComplianceDetails';
import SystemUpdate from './SystemUpdate';

export default interface ResourcesDetails {
  domain: string;
  application: string;
  environment: string;
  owner?: string;
  projectId: string;
  layoutShortName?: string;
  platformContext: {
    catalogId: string;
    domainId: string;
    envId: string;
    applicationName: string;
    environmentName: string;
    domainName: string;
    applicationId: string;
  };
  datacenter: string;
  description?: string;
  resourceSpecificDetails?: any;
  certificateDetails?: any;
  requestId: string;
  id: number;
  resourceId: string;
  catalogLevel03: string;
  catalogType: string;
  resourceStatus: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  projectName: string;
  resourceName: string;
  instanceType: string;
  cloud: string;
  cloudId: string;
  group: string;
  layout: string;
  plan: string;
  instancePrice: string;
  ipaddress: string;
  crowdStrikeStatus: string;
  customCores: number;
  customMemory: number;
  status: string;
  statusMessage: string | null;
  cpuUsage: number;
  maxMemory: number;
  maxStorage: number;
  usedCPU: number;
  usedMemory: number;
  usedStorage: number;
  externalFqdn: string;
  tags: Array<{ name: string; value: string }>;
  systemUpdate: SystemUpdate;
  ipAddress: string;
  clusterSize: number;
  appCode: string;
  dbName: string;
  enableDelete?: boolean;
  enableUpdate?: boolean;
  ipv4Address?: string;
  ipv6Address?: string;
  dbUsername: string;
  dbVersion: string;
  clusterName: string;
  parentId?: string | null;
  childId?: string[] | null;
  addDisks?: AddDisk[];
  diskCount?: string;
  statusDate?: string;
  complianceResult?: ComplianceDetails;
  groupQuota: number;
  nfsCreatedAt: string;
  rules: string;
  userQuota: number;
  bucketName?: string;
  accountName?: string;
  bucketCreatedAt?: string;
  versioning?: string;
  users?: any[];
  rubrikSLA?: string;
  accessKeys?: AccessKeys[];
  isCopied?: boolean;
  isRegeneratable?: boolean;
  requestType?: string;
  instancename?: string;
  version?: string;
  displayName?: string;
  swap?: number;
  secretExpiry?: string;
  ttlInHours?: number;
  path?: string;
  parentID?: string;
  coresPerSocket?: string;
  network?: string;
  ipMode?: string;
  subnetIpv4?: string;
  subnetIpv6?: string;
  networkDetails?: {
    network: {
      id: string;
      name: string;
    };
    ipMode?: string;
    ipv4?: string;
    ipv6?: string;
  }[];
  namespaceName: string;
  namespacePath: string;
  dnsDomainName?: string;
  resourcesDetails?: {
    namespace?: string;
    hostName?: string;
    owner?: string;
    data: {
      path: string;
    };
  };
}
