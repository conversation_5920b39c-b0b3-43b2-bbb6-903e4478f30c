import MainLayout from 'componentsV2/Layout';
import NebulaTheme from 'NebulaTheme';
import LandingPage from 'pages/LandingPage';
import Catalogs from 'pages/Catalogs';
import Requests from 'pages/RequestsV2';
import { nebulaBasePath } from 'utils/constant';
import Approvals from 'pages/ApprovalV2';
import RequestDetails from 'pages/RequestorDetails';
import ApprovalDetails from 'pages/ApprovalDetails';
import MyResources from 'pages/MyResources';
import ResourceDetails from 'pages/ResourceDetails';
import EditableForm from 'componentsV2/EditableForm';
import { Authenticator } from 'components/Authenticator/Authenticator';
import MantisTheme from 'mantis/themes';
import Administration from 'pages/Administration';
import RequestFeature from 'pages/RequestFeature';
import GenericSearch from 'pages/GenericSearch';
import AdvancedSearch from 'pages/AdvancedSearch';
import MyProjects from 'pages/MyProjects';
import TaniumCompliance from 'pages/TaniumCompliance';
import { AdminEditRouteParams } from '../types/Enums';
import ProjectPermission from '../componentsV2/Administration/Projects/ProjectPermission';
import ManageMyProjects from '../pages/ManageMyProject';
// eslint-disable-next-line no-unused-vars
import { RouteObject } from 'react-router';
import EditSecretPage from 'pages/SecretManagement/EditSecret';
import CreateNewSecretPage from 'pages/SecretManagement/CreateNewSecretPage';
import CreatePasswordPolicyPage from 'pages/SecretManagement/CreatePasswordPolicyPage';
import SecretDeviceAssociation from 'componentsV2/IaaS/SecretDeviceAssociation';

const NebulaV2Routes: RouteObject = {
  path: '/' + nebulaBasePath,
  element: (
    <NebulaTheme>
      <MainLayout />
    </NebulaTheme>
  ),
  children: [
    { path: '', element: <LandingPage /> },
    { path: ':catalogLevel1/*', element: <Catalogs /> },
    { path: 'playground', element: <p>Playground V2 Placholder</p> },
    { path: 'requests', element: <Requests /> },
    { path: 'approvals', element: <Approvals /> },
    {
      path: 'resources',
      children: [
        { index: true, path: '', element: <MyResources /> },
        {
          path: ':resourceId',
          children: [
            { index: true, path: '', element: <ResourceDetails /> },
            { path: 'compliance-findings', element: <TaniumCompliance /> },
            { path: ':action', element: <EditableForm /> },
            {
              path: 'create-new-secret',
              children: [
                {
                  path: '',
                  element: <CreateNewSecretPage />,
                },
                { path: 'create-password-policy', element: <CreatePasswordPolicyPage /> },
              ],
            },
            { path: 'secret-device', element: <EditSecretPage /> },
            {
              path: 'secret-device-association',
              element: <SecretDeviceAssociation onClose={() => {}} permissions={{}} />,
            },
          ],
        },
      ],
    },
    { path: 'requests/:serviceRequestId', element: <RequestDetails /> },
    { path: 'approvals/:serviceRequestId', element: <ApprovalDetails /> },
    { path: 'reauth', element: <Authenticator /> },
    { path: 'nebula/auth/cb', element: <Authenticator /> },
    { path: 'feature-requests', element: <RequestFeature /> },
    { path: 'search', element: <GenericSearch /> },
    { path: 'advanced-search', element: <AdvancedSearch /> },
    { path: 'my-projects', element: <MyProjects /> },
    { path: `my-projects/:${AdminEditRouteParams.projects}`, element: <ManageMyProjects /> },

    {
      path: `my-projects/:${AdminEditRouteParams.projects}/project-permissions`,
      element: <ProjectPermission permissions={{}} onClose={() => {}} />,
    },
    {
      path: 'administration/*',
      element: (
        <MantisTheme>
          <Administration />
        </MantisTheme>
      ),
    },
  ],
};

export default NebulaV2Routes;
