export const CreateVmFormPayload = {
  name: 'Linux 8 & 9',
  shortName: 'linux8and9',
  projectName: 'Nebula Q1 Release Copy',
  id: '667cf67326f361813ec28130',
  projectDL: 'testing',
  projectId: '667cf67326f361813ec28130',
  catalogId: '65fd4594fada3fc9c74bfb9e',
  size: 'medium',
  targetLayout: {
    id: 3771,
    code: 'a69e81c3-971b-41e0-981b-0f777306e598',
    shortName: 'alma88',
    name: 'Alma-8.8',
    groups: [],
    instanceTypeId: 322,
  },
  group: {
    id: 109,
    name: 'AP-CMP-APOImpSupp-ReliabilityEng',
    cloud: [
      {
        id: 7,
        name: 'STAMP NDC',
      },
    ],
  },
  datacenter: 'STAMP',
  cloudId: 7,
  hostname: 'pr-test-vn',
  availableHostNames: ['pr-test-vn'],
  vmCount: 1,
  resource: {
    id: 136,
    name: 'CDPTPABB04-SGPCIL02',
  },
  network: {
    description: null,
    displayName: 'VLAN449_APS_NEBULA_TEST_VM_VLAN: *************/25',
    id: 1846,
    name: 'STAMP NDC-VLAN449_APS_NEBULA_TEST_VM_VLAN',
    subnetIpv4: '*************/25',
    subnetIpv6: '2001:1998:64a:27::/64',
    zoneId: '7',
    zoneName: 'STAMP NDC',
  },
  ipv4: false,
  ipv6: true,
  ipModes: 'static',
  ipv4Addresses: [],
  ipv6Addresses: ['2001:1998:64a:27::4d'],
  securityTools: ['Crowd Strike'],
  complianceTools: [],
  observabilityTools: [],
  inventoryTools: [],
  description: 'test',
  password: 'Test@123456789',
  diskCount: 1,
  diskFileSystem: 'xfs',
  sshpubkey: 'ssh-rsa 123',
  rubrikSLA: 'RPO24-RSC',
  dirrtAction: 'Yes',
  addDisks: [
    {
      diskValue: 20,
      diskName: 'mount1',
    },
  ],
  deeplinkUrl: '/IaaS/compute/virtualMachine/linux8and9',
};

export const CreateVmFormResponse = {
  status: true,
  data: {
    id: '65a12a734fc49491faccbe13',
    message: 'Request submitted for approval',
  },
};

export const ADDITIONAL_DISK_DETAILS = {
  addDisks: [
    {
      diskName: 'string',
      diskValue: 10,
    },
    {
      diskName: 'string1',
      diskValue: 1001,
    },
  ],
  diskFileSystem: 'xfs',
  diskCount: 2,
};

export const VM_PROJECT_TEST_SETTINGS = [
  {
    projectName: 'Nebula Test 1',
    id: '667cf67326f361813ec28130',
    appId: 'APP1234',
    projectSettings: {
      dataCenters: [
        {
          name: 'Test NCE',
          description: 'test data',
          networks: [
            {
              id: '1',
              name: 'VLAN01',
              displayName: 'Test VLAN01',
              subnetIpv4: '************/27',
              subnetIpv6: '2001:1998:64a:23::/64',
              zoneName: 'zone1',
              zoneId: '7',
              description: 'test network',
              morpheusNetworkName: '',
            },
            {
              id: '2',
              name: 'VLAN02',
              displayName: 'Test VLAN02',
              subnetIpv4: '************/27',
              subnetIpv6: '2001:1998:64a:23::/64',
              zoneName: 'zone1',
              zoneId: '8',
              description: 'test network',
              morpheusNetworkName: '',
            },
          ],
        },
      ],
      tags: [
        {
          name: 'test tag',
          value: 'test value',
        },
      ],
    },
    createdAt: '2024-04-05T12:06:22.464Z',
    updatedAt: '2024-04-05T12:06:22.464Z',
    description: 'Nebula Test 1',
  },
  {
    projectName: 'ProjectSchemaUpdate',
    id: '667cf67326f361813ec28188',
    description: 'Test description',
    emailDistribution: 'TestDL',
    appId: 'TestAppId',
    appName: 'TestAppName',
    projectSettings: {
      dataCenters: [
        {
          name: 'nam',
          description: 'Test datacenter',
          networks: [
            {
              id: '1285',
              name: 'vlan1440_citools',
              displayName: 'vlan1440_citools',
              subnetIpv4: '************/24',
              subnetIpv6: '2001:1998:64a:23::/64',
              zoneName: 'STAMP NDC',
              zoneId: '7',
              description: 'test network',
              morpheusNetworkName: '',
            },
          ],
        },
      ],
      tags: [
        {
          name: 'test tag',
          value: 'test value',
        },
      ],
    },
    createdAt: '2024-04-05T12:06:22.464Z',
    updatedAt: '2024-04-05T12:06:22.464Z',
    __v: 0,
  },
];

export const VM_GROUPS = [
  {
    id: 2,
    name: 'ap-cmp-admins',
    cloud: [
      {
        id: 21,
        name: 'LegacyD1',
      },
      {
        id: 22,
        name: 'Stamp NDC',
      },
      {
        id: 23,
        name: 'testMetal',
      },
    ],
  },
  {
    id: 4,
    name: 'ap-cmp-aposc',
    cloud: [
      {
        id: 41,
        name: 'LegacyD2',
      },
      {
        id: 42,
        name: 'Stamp NDC',
      },
    ],
  },
];

export const VM_RESOURCES = [
  {
    id: 18,
    name: 'cdptpabb04-cdvr01stg',
  },
  {
    id: 19,
    name: 'cdptpabb04-cdvr02stg',
  },
  {
    id: 16,
    name: 'cdptpabb04-cdvr03stg',
  },
  {
    id: 17,
    name: 'cdptpabb04-gpcaz01',
  },
  {
    id: 15,
    name: 'cdptpabb04-mgcaz01',
  },
];

export const VM_TOOLS = {
  securityTools: [
    {
      name: 'crowdstrike',
      label: 'Crowd Strike',
      disabled: false,
      icon: 'CrowdStrikeIcon',
      description:
        'CrowdStrike offers cloud-based security solutions, including endpoint protection, cloud security, identity protection, and threat intelligence.',
    },
    {
      name: 'centrify',
      label: 'Centrify',
      disabled: false,
      icon: 'CentrifyIcon',
      description:
        'Centrify is a leading provider of privileged access management (PAM) solutions, helping organizations secure their critical systems and data.',
    },
    {
      name: 'qualys',
      label: 'Qualys',
      disabled: false,
      icon: 'QualysIcon',
      description: 'Qualys provides cloud-based security and compliance solutions.',
    },
  ],
  complianceTools: [
    { name: 'tanium', label: 'Tanium', disabled: false, icon: 'TaniumIcon', description: 'Tanium provides unified endpoint management.' },
  ],
  observabilityTools: [
    {
      name: 'scienceLogic',
      label: 'Science Logic',
      disabled: false,
      icon: 'ScienceLogicIcon',
      description:
        'ScienceLogic provides IT infrastructure monitoring and AIOps solutions to help organizations manage and optimize their IT Ops',
    },
    {
      name: 'splunk',
      label: 'Splunk OS',
      disabled: true,
      icon: 'SplunkIcon',
      description: 'Splunk helps organizations analyze machine data to improve operations and security.',
    },
    {
      name: 'vmTools',
      label: 'VM Tools',
      disabled: true,
      icon: 'VMToolsIcon',
      description: 'VM Tools is a set of services and modules that enable several features in VM.',
    },
  ],

  inventoryTools: [
    {
      name: 'granite',
      label: 'Granite',
      disabled: false,
      icon: 'GraniteIcon',
      description: 'Granite WMS helps businesses manage their warehouse operations.',
    },
  ],
};

export const GetVmProjectDetails = {
  status: true,
  data: VM_PROJECT_TEST_SETTINGS,
};

export const GetVmReferenceData = {
  status: true,
  data: {
    size: {
      tiny: {
        customMemory: 16,
        root: '40',
        home: '8',
        opt: '8',
        var: '4',
        var_log: '4',
        var_log_audit: '4',
        var_tmp: '4',
        tmp: '4',
        customCores: 4,
      },
    },
    groups: [
      {
        id: 97,
        name: 'ap-cmp-Access-Architecture-Eng',
        cloud: [
          {
            id: 7,
            name: 'STAMP NDC',
          },
        ],
      },
    ],
    layouts: [
      {
        id: 3457,
        code: '6afb8b48-5c56-4247-9f24-95600e17b184',
        shortName: 'rhel8.6',
      },
    ],
  },
};

export const GetVmResourcesData = {
  status: true,
  data: [
    {
      id: 121,
      name: '*************',
    },
    {
      id: 118,
      name: 'cdptpabb04-hyp37-d3c5s8.stage.com',
    },
  ],
};

export const VM_DETAILS = {
  name: 'RHEL 9',
  shortName: 'rhel-9',
  id: 'rhel-9',
  icon: 'RedHat',
  description: '',
  path: '/IaaS/compute/virtual-machines/rhel-9',
  variants: {
    small: {
      cpu: '4',
      memory: '16 GB',
      storage: '128 GB',
      usage: 'Web server',
      type: 'Build',
      price: '$500.00 / Month',
    },
    large: {
      cpu: '16',
      memory: '64 GB',
      storage: '1024 GB',
      usage: 'Web server',
      type: 'Build',
      price: '$700.00 / Month',
    },
  },
  layouts: ['Rhel9.0', 'Rhel9.2'],
  maxVmCount: 3,
  groups: [
    {
      id: '2',
      name: 'ap-cmp-admins',
      cloud: [
        {
          id: '21',
          name: 'LegacyD1',
        },
      ],
    },
  ],
  projects: [
    {
      projectName: 'Nebula A',
      projectSettings: {
        network: [
          {
            name: 'VLAN01',
          },
          {
            name: 'VLAN02',
          },
        ],
        dataCenter: [
          {
            name: 'NCE',
          },
          {
            name: 'NCW',
          },
        ],
      },
    },
  ],
};

export const DeleteVMResponse = {
  status: true,
  data: {
    successCode: 200,
    message: 'Delete vm request queued successfully',
  },
};

export const IP_ADDRESS_MOCK = [
  {
    a: '1',
    c: '2001:db8:aaaa:bbbb:cccc:dddd:0000:ffff',
    d: '2001:db8:aaaa:bbbb:cccc:dddd:0000:ffff',
    e: 'Sample PlaceHolder',
    id: 1,
  },
  {
    a: '1',
    c: '2001:db8:aaaa:bbbb:cccc:dddd:0000:ffff',
    d: '2001:db8:aaaa:bbbb:cccc:dddd:0000:ffff',
    e: 'Sample PlaceHolder',
    id: 2,
  },
  {
    a: '1',
    c: '2001:db8:aaaa:bbbb:cccc:dddd:0000:ffff',
    d: '2001:db8:aaaa:bbbb:cccc:dddd:0000:ffff',
    e: 'Sample PlaceHolder',
    id: 2,
  },
];

export const SEARCH_RESULTS = [
  {
    instanceId: 50776,
    name: 'nakhdv005cdpdc',
    createdBy: 'P3252272',
    displayName: 'nakhdv005cdpdc',
    hostName: 'nakhdv005cdpdc',
    group: 'ap-cmp-Access-Architecture-Eng',
    cloud: 'STAMP NDC',
    instanceType: 'linux8_9',
    plan: 'Charter Self-Service',
    layout: 'RHEL-8',
    network: 'VLAN449_APS_NEBULA_TEST_VM_VLAN',
    ipaddress: '*************',
    ipv6Address: '2001:1998:64a:27::5f',
    rubrikSLA: 'RPO24-RSC',
    maxMemory: 4294967296,
    maxStorage: 1260572901376,
    maxCores: 1,
    coresPerSocket: 1,
    crowdStrikeStatus: 'off',
    customCores: 8,
    customMemory: 16,
    addDisks: [
      {
        diskName: 'oracledb',
        diskValue: 500,
      },
      {
        diskName: 'oraclebackup',
        diskValue: 500,
      },
      {
        diskName: 'oraclelog',
        diskValue: 40,
      },
      {
        diskName: 'oraclehome',
        diskValue: 50,
      },
    ],
    diskCount: '4',
    diskFilesystem: 'ext4',
    cpuUsage: 0,
    usedCPU: 0,
    usedMemory: 84934656,
    usedStorage: 0,
    status: 'running',
    statusMessage: null,
    tags: [
      {
        id: 4427833,
        name: 'BizOwner',
        value: 'Testing project tags biz1',
      },
      {
        id: 4437607,
        name: 'DevOwner',
        value: 'This value comes from project tags2',
      },
      {
        id: 4438006,
        name: 'OpsOwner',
        value: 'APS-SUPPORT-CENTER',
      },
      {
        id: 4427830,
        name: 'SecOwner',
        value: 'charter1',
      },
      {
        id: 3609103,
        name: 'csdisabledReason',
        value: 'Nebula security service will enable CrowdStrike later.',
      },
    ],
    errorMessage: null,
    instancePrice: '150.88USD/month',
    instanceCost: '127.4USD/month',
  },
];

export const VM_REF_DATA = {
  size: {
    '2x8': {
      id: 294,
      code: '2x8',
      maxMemory: 8589934592,
      maxCores: 2,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 150,
    },
    '4x16': {
      code: '4x16',
      id: 293,
      maxMemory: 17179869184,
      maxCores: 4,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 150,
    },
    '4x32': {
      code: '4x32',
      id: 295,
      maxMemory: 34359738368,
      maxCores: 4,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 150,
    },
    '8x32': {
      code: '8x32',
      id: 296,
      maxMemory: 34359738368,
      maxCores: 8,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 0,
    },
    '8x64': {
      code: '8x64',
      id: 297,
      maxMemory: 68719476736,
      maxCores: 8,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 0,
    },
    '8x96': {
      code: '8x96',
      id: 298,
      maxMemory: 103079215104,
      maxCores: 8,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 0,
    },
    '12x128': {
      code: '12x128',
      id: 300,
      maxMemory: 137438953472,
      maxCores: 12,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 0,
    },
    '16x64': {
      code: '16x64',
      id: 299,
      maxMemory: 68719476736,
      maxCores: 16,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 0,
    },
    '16x256': {
      code: '16x256',
      id: 301,
      maxMemory: 274877906944,
      maxCores: 16,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 0,
    },
    '32 x 256': {
      code: '32 x 256',
      id: 302,
      maxMemory: 274877906944,
      maxCores: 32,
      coresPerSocket: 2,
      maxCoresPerSocket: 2,
      size: 150,
    },
  },
  groups: [
    {
      id: 16,
      name: 'Nebula',
      cloud: [
        {
          id: 6,
          name: 'CDP-AZ05',
        },
        {
          id: 11,
          name: 'NCE-AZ01',
        },
        {
          id: 10,
          name: 'NCE-AZ02',
        },
        {
          id: 9,
          name: 'NCE-AZ03',
        },
        {
          id: 8,
          name: 'NCW-AZ04',
        },
        {
          id: 7,
          name: 'NCW-AZ05',
        },
        {
          id: 4,
          name: 'NCW-AZ06',
        },
      ],
    },
  ],
  layouts: [
    {
      id: 1162,
      code: 'd0b6b329-f1c9-41d3-9241-1ce663057f4e',
      shortName: 'RHEL7',
      name: 'RHEL 7',
      instanceTypeId: 81,
    },
    {
      id: 1113,
      code: 'b5f32a5e-0e3b-4995-ac09-384df779254e',
      shortName: 'RHEL8',
      name: 'RHEL 8',
      instanceTypeId: 81,
    },
    {
      id: 1116,
      code: '24808287-961c-4461-8bbf-a8bb6e4c27d8',
      shortName: 'RHEL9',
      name: 'RHEL 9',
      instanceTypeId: 81,
    },
    {
      id: 1119,
      code: 'd3ede010-71be-4f8e-b6c8-51e1d697f510',
      shortName: 'Rocky8',
      name: 'Rocky 8',
      instanceTypeId: 81,
    },
    {
      id: 1120,
      code: '22721a44-4b9b-4134-979f-ad75bcb2e735',
      shortName: 'Rocky9',
      name: 'Rocky 9',
      instanceTypeId: 81,
    },
  ],
  vmMaxCount: '32',
  backupOptions: [
    {
      name: 'Afternoon',
      value: 'snapBkupAfternoon',
    },
    {
      name: 'Evening',
      value: 'snapBkupEvening',
    },
    {
      name: 'Midnight',
      value: 'snapBkupAfterMidnight',
    },
    {
      name: 'Morning',
      value: 'snapBkupMorning',
    },
  ],
  patchingCycle: [
    {
      name: '1st Mon 12AM - 6AM EST',
      value: 'Maintenance Cycle Q1',
    },
    {
      name: '1st Sat 12AM - 6AM EST',
      value: 'Maintenance Cycle D4',
    },
    {
      name: '1st Thu 12AM - 6AM EST',
      value: 'Maintenance Cycle D1',
    },
    {
      name: '1st Tue 12AM - 6AM EST',
      value: 'Maintenance Cycle Q4',
    },
    {
      name: '2nd Mon 12AM - 6AM EST',
      value: 'Maintenance Cycle 13',
    },
    {
      name: '2nd Sat 12AM - 6AM EST',
      value: 'Maintenance Cycle 07',
    },
    {
      name: '2nd Sun 12AM - 6AM EST',
      value: 'Maintenance Cycle 10',
    },
    {
      name: '2nd Thu 12AM - 6AM EST',
      value: 'Maintenance Cycle 04',
    },
    {
      name: '2nd Tue 12AM - 6AM EST',
      value: 'Maintenance Cycle 16',
    },
    {
      name: '2nd Wed 12AM - 6AM EST',
      value: 'Maintenance Cycle 01',
    },
    {
      name: '3rd Sat 12AM - 6AM EST',
      value: 'Maintenance Cycle 25',
    },
    {
      name: '3rd Sun 12AM - 6AM EST',
      value: 'Maintenance Cycle 28',
    },
    {
      name: '3rd Thu 12AM - 6AM EST',
      value: 'Maintenance Cycle 22',
    },
    {
      name: '3rd Wed 12AM - 6AM EST',
      value: 'Maintenance Cycle 19',
    },
  ],
  appTier: [
    {
      name: 'App',
      value: 'a',
    },
    {
      name: 'Database',
      value: 'd',
    },
    {
      name: 'Web',
      value: 'w',
    },
  ],
  environment: [
    {
      name: '1st Mon 12AM - 6AM EST',
      value: 'Maintenance Cycle Q1',
    },
    {
      name: '1st Sat 12AM - 6AM EST',
      value: 'Maintenance Cycle D4',
    },
    {
      name: '1st Thu 12AM - 6AM EST',
      value: 'Maintenance Cycle D1',
    },
    {
      name: '1st Tue 12AM - 6AM EST',
      value: 'Maintenance Cycle Q4',
    },
    {
      name: '2nd Mon 12AM - 6AM EST',
      value: 'Maintenance Cycle 13',
    },
    {
      name: '2nd Sat 12AM - 6AM EST',
      value: 'Maintenance Cycle 07',
    },
    {
      name: '2nd Sun 12AM - 6AM EST',
      value: 'Maintenance Cycle 10',
    },
    {
      name: '2nd Thu 12AM - 6AM EST',
      value: 'Maintenance Cycle 04',
    },
    {
      name: '2nd Tue 12AM - 6AM EST',
      value: 'Maintenance Cycle 16',
    },
    {
      name: '2nd Wed 12AM - 6AM EST',
      value: 'Maintenance Cycle 01',
    },
    {
      name: '3rd Sat 12AM - 6AM EST',
      value: 'Maintenance Cycle 25',
    },
    {
      name: '3rd Sun 12AM - 6AM EST',
      value: 'Maintenance Cycle 28',
    },
    {
      name: '3rd Thu 12AM - 6AM EST',
      value: 'Maintenance Cycle 22',
    },
    {
      name: '3rd Wed 12AM - 6AM EST',
      value: 'Maintenance Cycle 19',
    },
  ],
  datacenter: [
    {
      id: 6,
      name: 'CDP-AZ05',
    },
    {
      id: 11,
      name: 'NCE-AZ01',
    },
    {
      id: 10,
      name: 'NCE-AZ02',
    },
    {
      id: 9,
      name: 'NCE-AZ03',
    },
    {
      id: 1,
      name: 'NCE-POD2',
    },
    {
      id: 7,
      name: 'NCW-AZ05',
    },
    {
      id: 4,
      name: 'NCW-AZ06',
    },
    {
      id: 2,
      name: 'NCW-POD2',
    },
  ],
};

export const AD_GROUPS = [
  {
    name: 'QA_Supervisor',
    value: 'QA_Supervisor',
  },
  {
    name: 'QA_KMA_Admin',
    value: 'QA_KMA_Admin',
  },
  {
    name: 'QA_Techs',
    value: 'QA_Techs',
  },
];

export const CORPNET_VM_NETWORKS = {
  networks: [
    {
      id: 1621997,
      name: 'vm_ncw-az05-vcf-w1c1_vmaas_local',
      displayName: 'NCW-AZ05-VMaaS-Linux',
      zonePool: {
        id: 105,
        name: 'ncw-az05-vcf-w1c5',
      },
    },
  ],
  networkCount: 1,
  meta: {
    size: 1,
    total: 1,
    offset: 0,
    max: 25,
  },
};

export const APP_INSTANCES = [
  {
    name: 'NEBULA-CLOUD SELF SERVICE PLATFORM',
    value: 'cssp1',
  },
  {
    name: 'NEBULA-CLOUD SELF SERVICE PLATFORM-DEV',
    value: 'cssp3',
  },
  {
    name: 'NEBULA-CLOUD SELF SERVICE PLATFORM-UAT',
    value: 'cssp2',
  },
];

export const MULTI_ENV_PROJECTS = [
  {
    _id: '67f8ef2e747d83f79dccf7ec',
    projectName: 'Nebula Suriya',
    emailDistribution: ['<EMAIL>'],
    applications: [
      {
        name: 'Nebula App',
        appId: 'APP1234',
        tags: [
          {
            key: '6683a93595ad22b1e3d80297',
            value: '1',
            active: true,
            description: '',
            overridable: false,
          },
        ],
        environments: [
          {
            name: 'DEV',
            approvalRequiredToModifyResources: false,
            networkSettings: {
              domain: [
                {
                  _id: '67ebc4fd49be555801fec525',
                  domainName: 'CUSTOMERNET',
                },
              ],
              dataCenter: [
                {
                  _id: '66216af38b22d42be179edde',
                  dataCenterName: 'NCW',
                  asn: '40294',
                  description: 'Main data center',
                  inventorySite: 'ENWDCOCD-PEAKVIEW-SRDC',
                  createdAt: '2024-04-18T18:48:19.445Z',
                  updatedAt: '2024-04-18T18:48:19.445Z',
                  __v: 0,
                  zones: [
                    {
                      name: 'NCW Pod 1',
                      id: '0',
                    },
                  ],
                  shortForm: 'pvdcco',
                  inventoryAsn: '40294',
                  storageBaseUrl: 'https://pvdcco-s3caas.cloud.charter.com',
                  qualysActivationId: '307725b0-2ae1-49db-8273-48477e26edbd',
                },
              ],
              key: 'bizowner',
              value: '1',
              active: true,
              type: 'VLAN',
              description: '',
            },
            tags: [
              {
                key: '66bfba8a9b2b5dbb1823fd74',
                value: '11',
                active: true,
                description: '11',
                overridable: false,
              },
            ],
            id: '01962466-4e5b-72cc-84bd-91337476717d',
          },
        ],
        id: '01962466-4e5c-74fd-ba3e-88bde31e53a7',
      },
    ],
    tags: [
      {
        key: '6683a93595ad22b1e3d80297',
        value: '123',
        active: true,
        description: '',
        overridable: false,
      },
      {
        key: '673c2ab35c189eae2b9c97bd',
        value: '124',
        active: true,
        description: '',
        overridable: false,
      },
    ],
    projectShortName: 'nebulasuriya',
  },
];
