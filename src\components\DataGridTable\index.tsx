import React, { useEffect, useState } from 'react';
import { Box, Stack, SxProps, Typography, Button } from '@mui/material';
// eslint-disable-next-line no-unused-vars
import { GridFilterModel, GridRowParams, GridSortModel, GridPaginationModel } from '@mui/x-data-grid';
import {
  DataGrid,
  GridColDef,
  GridRowSelectionModel,
  GridColumnVisibilityModel,
  GridToolbarContainer,
  GridToolbarColumnsButton,
} from '@mui/x-data-grid';
import { ReloadOutlined } from '@ant-design/icons';

import TableSkeleton from '../TableSkeleton';
import GridTableButton from '../GridTableButton';

// eslint-disable-next-line
import { NebulaTheme } from 'mantis/themes/type';
import { PaginationConfiguration } from '../../types/Interfaces/PaginationResponse';
import FirewallService from 'api/ApiService/SecurityService';
import { downloadFile } from 'utils/common';
import { toast } from 'react-toastify';

interface DataGridTableProps extends PaginationConfiguration {
  actions?: React.ReactNode;
  columns: GridColDef[];
  rows: any;
  loading?: boolean;
  refreshHandler?: () => void;
  removeAllHandler?: () => void;
  sx?: SxProps;
  title?: string;
  showResetFilter?: boolean;
  checkboxSelection?: boolean;
  onRowSelectionModelChange?: (selected: GridRowSelectionModel) => void;
  rowSelectionModel?: GridRowSelectionModel;
  columnVisibilityModel?: GridColumnVisibilityModel;
  onColumnVisibilityModelChange?: (newModel: GridColumnVisibilityModel) => void;
  isToolBarVisible?: boolean;
  pageSize?: number;
  renderCheckbox?: () => React.ReactElement;
  rowsOverlayMessage?: string;
  pageSizeOptions?: Array<number>;
  isRowsSelectable?: (params: GridRowParams) => boolean;
  filterModel?: GridFilterModel;
  hideFooterAndPagination?: boolean;
  clientRefreshData?: (filter: GridFilterModel) => void;
  handleRowEdit?: any;
  onPageChange?: (params: GridPaginationModel) => void;
  allSelectedRows?: number;
  AddUser?: () => void;
  AddGroup?: () => void;
  onFilterModelChange?: (filter: GridFilterModel) => void;
  onSortModelChange?: (sortModel: GridSortModel) => void;
  onResetHandler?: () => void;
  AddButtonLabel?: string;
  isAddButtonDisabled?: boolean;
  isDeleteButtonDisabled?: boolean;
  DeleteButtonLabel?: string;
  deleteUser?: () => void;
  isSearch?: boolean;
  serviceRequestId?: string;
  serviceRequestStatus?: string;
  isConsolidatedTab?: boolean;
}

const DataGridTable: React.FunctionComponent<DataGridTableProps> = ({
  rows,
  columns,
  actions = null,
  checkboxSelection,
  refreshHandler,
  removeAllHandler,
  loading,
  title,
  rowSelectionModel,
  columnVisibilityModel,
  onColumnVisibilityModelChange,
  showResetFilter = true,
  isToolBarVisible,
  pageSize = 10,
  renderCheckbox,
  rowsOverlayMessage = 'No Record Found',
  onRowSelectionModelChange,
  isRowsSelectable,
  filterModel,
  hideFooterAndPagination = false,
  serverPagination,
  pageInfo,
  serverPaginationFn,
  componentApiMappings,
  clientRefreshData,
  handleRowEdit,
  onPageChange,
  onFilterModelChange,
  onSortModelChange,
  allSelectedRows,
  AddUser,
  deleteUser,
  AddButtonLabel,
  DeleteButtonLabel,
  isDeleteButtonDisabled,
  isAddButtonDisabled,
  AddGroup,
  onResetHandler,
  isSearch,
  serviceRequestId,
  serviceRequestStatus,
  isConsolidatedTab,
  ...rest
}: DataGridTableProps) => {
  const [muiTableKey, setMuiTableKey] = useState(1);
  const [paginationModel, setPaginationModel] = useState({
    page: 1,
    pageSize: pageSize,
  });
  const [sortingModel, setSortingModel] = useState([] as GridSortModel);
  const [filteringModel, setFilteringModel] = useState({} as GridFilterModel);

  const [resubmitButtonText, setResubmitButtonText] = useState('Re-Submit');
  const [isResubmitButtonDisabled, setIsResubmitButtonDisabled] = useState(false);
  const apiFirewallService = new FirewallService();

  const resetHandler = () => {
    setMuiTableKey(muiTableKey + 1);
    if (serverPagination) {
      setFilteringModel({} as GridFilterModel);
    }
    if (onRowSelectionModelChange) {
      onRowSelectionModelChange([]);
    }
    onResetHandler?.();
  };

  const paginationModelChange = (pageModel: { page: number; pageSize: number }) => {
    setPaginationModel({
      ...pageModel,
      page: pageModel.page + 1,
    });
  };

  const fetchRows = async () => {
    if (!serverPagination) {
      return;
    }
    let sortString = '';
    let filterString = '';
    if (sortingModel?.length > 0) {
      const sortObject = sortingModel.reduce((acc: Record<string, number>, curr) => {
        const field = componentApiMappings?.[curr.field] || curr.field;
        acc[field] = curr.sort === 'desc' ? -1 : 1;
        return acc;
      }, {});
      sortString = JSON.stringify(sortObject);
    }
    if (Object.keys(filteringModel).length != 0) {
      const filterObject: Record<string, Record<string, string>> = filteringModel.items.reduce(
        (acc: Record<string, Record<string, string>>, curr) => {
          const field = componentApiMappings?.[curr.field] || curr.field;
          acc[field] = { [curr.operator]: curr.value };
          return acc;
        },
        {}
      );
      filterString = JSON.stringify(filterObject);
    }
    serverPaginationFn?.(paginationModel.page, paginationModel.pageSize, sortString, filterString);
  };

  const refreshData = async () => {
    clientRefreshData?.(filteringModel);
    refreshHandler?.();
  };

  useEffect(() => {
    if (serverPagination) {
      fetchRows();
    }
  }, [paginationModel, sortingModel, filteringModel]);

  function CustomToolbar() {
    return (
      <GridToolbarContainer>
        <GridToolbarColumnsButton />
      </GridToolbarContainer>
    );
  }

  const handleFilterModelChange = (newFilterModel: GridFilterModel) => {
    const trimmedFilterModel = {
      ...newFilterModel,
      items: newFilterModel.items.map((item) => ({
        ...item,
        value: item.value?.trim() || '',
      })),
    };
    setFilteringModel(trimmedFilterModel);
    onFilterModelChange?.(trimmedFilterModel);
  };

  const handleReSubmitFormV2 = (res: { status: boolean }) => {
    if (res?.status === true) {
      setResubmitButtonText('Re-Submitted');
      setIsResubmitButtonDisabled(true);
      toast.success('Request re-submitted successfully', {
        position: toast.POSITION.BOTTOM_CENTER,
      });
    } else {
      toast.error('Re-Submit failed', {
        position: toast.POSITION.BOTTOM_CENTER,
      });
    }
  };

  const handleResubmit = () => {
    if (isResubmitButtonDisabled) {
      return;
    }

    if (serviceRequestId) {
      apiFirewallService.firewallReSubmitFormV2(serviceRequestId).then((res) => handleReSubmitFormV2(res));
    }
  };

  const handleDownloadDFM = (res: { status: boolean; data: Blob }) => {
    if (res?.status) {
      downloadFile(res.data, 'firewallrules.xlsx');
    }
  };

  const handleDownloadDFMrules = () => {
    if (serviceRequestId) {
      apiFirewallService.firewallDownloadDFM(serviceRequestId).then((res) => handleDownloadDFM(res));
    }
  };

  return (
    <Stack>
      {loading ? (
        <TableSkeleton columnsNum={columns.length} rowsNum={5} />
      ) : (
        <>
          <Box display={'flex'} justifyContent={'space-between'} alignItems={'center'}>
            {title && <Typography variant="h5">{title}</Typography>}
            {renderCheckbox && <Box>{renderCheckbox()}</Box>}
            <Box sx={{ mb: 1, ml: 'auto', display: 'flex' }}>
              {actions}

              {refreshHandler && (
                <GridTableButton
                  label={<ReloadOutlined />}
                  buttonSize="30px !important"
                  buttonMinSize="30px !important"
                  buttonId="refresh-icon-btn"
                  handleClick={serverPagination ? fetchRows : refreshData}
                />
              )}
              {removeAllHandler && <GridTableButton label={'Remove All'} handleClick={removeAllHandler} />}
              {isConsolidatedTab && serviceRequestId?.includes('-FW-') && serviceRequestStatus !== 'COMPLETED' && (
                <GridTableButton label={resubmitButtonText} handleClick={handleResubmit} isDisabled={isResubmitButtonDisabled} />
              )}
              {isConsolidatedTab && serviceRequestId?.includes('-FW-') && (
                <GridTableButton label={'Download DFM'} handleClick={handleDownloadDFMrules} />
              )}
              {showResetFilter && <GridTableButton label={'Reset Filter'} handleClick={resetHandler} />}
              {AddUser && (
                <Button sx={{ marginLeft: '8px' }} variant="contained" onClick={AddUser} disabled={isAddButtonDisabled}>
                  {AddButtonLabel ? AddButtonLabel : '+ Add User'}
                </Button>
              )}
              {deleteUser && (
                <Button sx={{ marginLeft: '8px' }} variant="contained" color="error" onClick={deleteUser} disabled={isDeleteButtonDisabled}>
                  {DeleteButtonLabel ? DeleteButtonLabel : '+ Delete User'}
                </Button>
              )}
              {AddGroup && (
                <Button sx={{ marginLeft: '8px' }} variant="contained" onClick={AddGroup}>
                  + Add Group Permission
                </Button>
              )}
            </Box>
          </Box>
          <Box position={'relative'}>
            <DataGrid
              loading={loading}
              key={muiTableKey}
              rows={rows}
              columns={columns}
              checkboxSelection={checkboxSelection}
              rowSelectionModel={rowSelectionModel}
              autoHeight
              getRowHeight={() => 'auto'}
              columnVisibilityModel={columnVisibilityModel}
              onColumnVisibilityModelChange={onColumnVisibilityModelChange}
              hideFooterPagination={hideFooterAndPagination}
              hideFooter={hideFooterAndPagination}
              initialState={{
                pagination: {
                  paginationModel: {
                    pageSize: paginationModel?.pageSize || pageSize,
                  },
                },
              }}
              pageSizeOptions={[pageSize, 20, 40, 60]}
              slots={{
                toolbar: isToolBarVisible ? CustomToolbar : undefined,
                noRowsOverlay: () => (
                  <Stack sx={{ fontWeight: 'bold' }} height="100%" alignItems="center" justifyContent="center">
                    {rowsOverlayMessage}
                  </Stack>
                ),
                noResultsOverlay: () => (
                  <Stack sx={{ fontWeight: 'bold' }} height="100%" alignItems="center" justifyContent="center">
                    {isSearch ? 'Search Returns no result' : 'Filter returns no result'}
                  </Stack>
                ),
              }}
              disableDensitySelector
              getRowClassName={(params) => (params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd')}
              disableRowSelectionOnClick
              showCellVerticalBorder={true}
              onRowSelectionModelChange={onRowSelectionModelChange}
              isRowSelectable={isRowsSelectable}
              filterModel={filterModel}
              {...rest}
              pagination
              rowCount={serverPagination ? pageInfo?.totalDocs || paginationModel.pageSize : undefined}
              paginationMode={serverPagination ? 'server' : 'client'}
              onSortModelChange={(newSortModel) => {
                if (serverPagination) {
                  setSortingModel(newSortModel || null);
                }
                onSortModelChange?.(newSortModel);
              }}
              onFilterModelChange={handleFilterModelChange}
              onPaginationModelChange={
                serverPagination
                  ? (newPaginationModel) => {
                      paginationModelChange(newPaginationModel);
                    }
                  : (newPaginationModel) => {
                      onPageChange?.(newPaginationModel);
                    }
              }
              hideFooterSelectedRowCount={Boolean(allSelectedRows)}
              processRowUpdate={handleRowEdit}
            />
            {!!allSelectedRows && (
              <Typography sx={{ position: 'absolute', bottom: '22px', left: '16px', color: 'black' }}>
                {allSelectedRows} rows selected
              </Typography>
            )}
          </Box>
        </>
      )}
    </Stack>
  );
};

export default DataGridTable;
