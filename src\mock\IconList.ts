import icons from 'assets/images/icons';
export const IconLists = [
  //level01 icons
  { id: '1', icon_name: 'CloudOutlined', icon: icons.CloudOutlined },
  { id: '2', icon_name: 'CloudSyncOutlined', icon: icons.CloudSyncOutlined },
  { id: '3', icon_name: 'BuildOutlined', icon: icons.BuildOutlined },
  //level03 icons
  { id: '3', icon_name: 'LanOutlined', icon: icons.LanOutlined },
  { id: '4', icon_name: 'MonitorOutlined', icon: icons.MonitorOutlined },
  { id: '5', icon_name: 'DeviceHubRounded', icon: icons.DeviceHubRounded },
  { id: '6', icon_name: 'VM', icon: icons.VM },
  { id: '7', icon_name: 'Baremetal', icon: icons.Baremetal },
  { id: '8', icon_name: 'AWS', icon: icons.AWS },
  { id: '9', icon_name: 'Security', icon: icons.Security },
  { id: '10', icon_name: 'MongoDBDBaaS', icon: icons.MongoDBDBaaS },
  { id: '11', icon_name: 'OracleDBaaS', icon: icons.OracleDBaaS },
  { id: '12', icon_name: 'RedisDBaaS', icon: icons.RedisDBaaS },
  { id: '13', icon_name: 'PostgresDBaaS', icon: icons.PostgresDBaaS },
  { id: '14', icon_name: 'TableOutlined', icon: icons.TableOutlined },
  { id: '15', icon_name: 'MapOutlined', icon: icons.MapOutlined },
  { id: '16', icon_name: 'CardOutlined', icon: icons.CardOutlined },
  { id: '17', icon_name: 'NetToolsIcon', icon: icons.NetToolsIcon },
  { id: '18', icon_name: 'CertificateIcon', icon: icons.CertificateIcon },

  //level04 icons
  { id: '13', icon_name: 'AdminPanelSettingsOutlined', icon: icons.AdminPanelSettingsOutlined },
  { id: '14', icon_name: 'AssignmentTurnedInOutlined', icon: icons.AssignmentTurnedInOutlined },
  { id: '15', icon_name: 'OutputOutlined', icon: icons.OutputOutlined },
  { id: '16', icon_name: 'FoundationOutlined', icon: icons.FoundationOutlined },
  { id: '17', icon_name: 'AddOutlined', icon: icons.AddOutlined },
  { id: '18', icon_name: 'EjectOutlined', icon: icons.EjectOutlined },
  { id: '19', icon_name: 'CloseOutlined', icon: icons.CloseOutlined },
  { id: '20', icon_name: 'RedHat', icon: icons.RedHat },
  { id: '21', icon_name: 'Ubuntu', icon: icons.Ubuntu },
  { id: '22', icon_name: 'Windows', icon: icons.Windows },
  { id: '23', icon_name: 'AddToQueue', icon: icons.AddToQueue },
  { id: '24', icon_name: 'VisibilityOutlined', icon: icons.VisibilityOutlined },
  { id: '25', icon_name: 'MoveToInboxOutlined', icon: icons.MoveToInboxOutlined },
  { id: '26', icon_name: 'SettingsInputAntennaOutlined', icon: icons.SettingsInputAntennaOutlined },
  { id: '27', icon_name: 'PolicyOutlined', icon: icons.PolicyOutlined },
  { id: '28', icon_name: 'OnPrem', icon: icons.OnPrem },
  { id: '29', icon_name: 'PureS3', icon: icons.PureS3 },
  { id: '30', icon_name: 'NFS', icon: icons.NFS },
  { id: '31', icon_name: 'PathAnalysisIcon', icon: icons.PathAnalysisIcon },
  { id: '32', icon_name: 'InternalCertificateIcon', icon: icons.InternalCertificateIcon },
  { id: '33', icon_name: 'LoadBalancerIcon', icon: icons.LoadBalancerIcon },
  { id: '34', icon_name: 'F5Icon', icon: icons.F5Icon },
  { id: '35', icon_name: 'BulkVMImport', icon: icons.BulkVMImport },
  { id: '36', icon_name: 'UsageMetricsViewIcon', icon: icons.UsageMetricsViewIcon },
  { id: '37', icon_name: 'RedHatCorpnet', icon: icons.RedHatCorpnet },
  { id: '38', icon_name: 'WindowsCorpnet', icon: icons.WindowsCorpnet },
  { id: '39', icon_name: 'AddToQueueV2', icon: icons.AddToQueueV2 },
  { id: '40', icon_name: 'AddToQueueMigrate', icon: icons.AddToQueueMigrate },
  { id: '41', icon_name: 'DynamicAccessPolicy', icon: icons.DynamicAccessPolicy },
  { id: '42', icon_name: 'OnboardNewGroup', icon: icons.OnboardNewGroup },
  { id: '43', icon_name: 'OnboardNewProject', icon: icons.OnboardNewProject },
  { id: '44', icon_name: 'ServiceCatalogAccess', icon: icons.ServiceCatalogAccess },
  { id: '45', icon_name: 'SecretDeviceAssociationIcon', icon: icons.SecretDeviceAssociationIcon },
  { id: '46', icon_name: 'NamespaceIcon', icon: icons.NamespaceIcon },
];
