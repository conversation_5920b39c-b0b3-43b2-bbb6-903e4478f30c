import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import { StyledCircle } from './styled';

import masterPalette from 'NebulaTheme/bundle/light/master';

interface ColorPaletteProps {
}

const ColorPalette: React.FC<ColorPaletteProps> = () => {

  const renderColors = (colors: any, title: string) => {
    return (
      <NblFlexContainer wrap='wrap' spacing={2} width='auto' height='auto' margin={'16px 0 24px'} padding={'8px'} border={'1px solid #B2BBC3'} borderRadius={'10px'}>
        <NblTypography variant='h5' textTransform={'capitalize'}>{title}</NblTypography>
        {Object.entries(colors).map(([key, value]) => {
          if (typeof value === 'object' && !Array.isArray(value)) {
            // If the value is an object (like shades), recursively render it
            return renderColors(value, key);
          }
          return (
            <NblFlexContainer key={key} direction='column' alignItems='center' width='auto' height='auto' margin='0 8px'>
              <StyledCircle color={value as string} />
              <NblTypography variant='h6'>{`${key}`}</NblTypography>
              <NblTypography variant='h6'>{`${value}`}</NblTypography>
            </NblFlexContainer>
          );
        })}
      </NblFlexContainer>
    );
  };

  return (
    <NblBorderContainer>
      <NblFlexContainer direction='column' overflowY='auto' padding={'8px'}>
        {renderColors(masterPalette.primary, 'Primary')}
        {renderColors(masterPalette.secondary, 'Secondary')}
        {renderColors(masterPalette.tertiary, 'Tertiary')}
      </NblFlexContainer>
    </NblBorderContainer>
  );
};

export default ColorPalette;