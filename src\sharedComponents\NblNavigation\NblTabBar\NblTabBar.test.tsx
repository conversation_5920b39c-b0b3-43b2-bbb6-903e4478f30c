import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { NblTabBar } from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('NblTabBar and NblTab Component Tests', () => {
  // Basic Rendering Tests

  test('renders NblTabBar with default props and tabs', () => {
    render(<NebulaThemeProvider><NblTabBar tabs={[{ label: 'Tab1' }, { label: 'Tab2' }]} /></NebulaThemeProvider>);
    expect(screen.getByText('Tab1')).toBeInTheDocument();
    expect(screen.getByText('Tab2')).toBeInTheDocument();
  });

  // Interaction Tests

  test('changes active tab on click in NblTabBar', () => {
    const handleTabChange = jest.fn();
    render(<NebulaThemeProvider><NblTabBar tabs={[{ label: 'Tab1' }, { label: 'Tab2' }]} activeTab={0} onTabChange={handleTabChange} /></NebulaThemeProvider>);
    const tab2 = screen.getByText('Tab2');
    fireEvent.click(tab2);
    expect(handleTabChange).toHaveBeenCalledWith('Tab2');
  });

  // Styling and Accessibility Tests

  test('throws an error when duplicate tabs are provided', () => {
    const consolespy = jest.spyOn(console, 'error').mockImplementation(() => {});
    expect(() => render(<NebulaThemeProvider><NblTabBar tabs={[{ label: 'test' }, { label: 'test' }]} /></NebulaThemeProvider>)).toThrow(
      'All tabs should be unique. Found duplicate tab'
    );
    consolespy.mockRestore();
  });
});
