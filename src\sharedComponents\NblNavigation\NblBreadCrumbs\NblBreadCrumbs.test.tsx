import { render, screen, fireEvent } from '@testing-library/react';
import NblBreadCrumbs, { NblBreadcrumbItem } from './';

// Mock navigate hook
const mockNavigate = jest.fn();
jest.mock('hooks/useNblNavigate', () => () => mockNavigate);


// Mock MUI icon to avoid complex rendering
jest.mock('@mui/icons-material/NavigateNext', () => () => <span data-testid="navigate-next-icon">&gt;</span>);

// Mock styled components (optional)
jest.mock('./styled', () => ({
  StyledLink: ({ children, onClick, variant }: any) => (
    <a href="#" onClick={onClick} data-variant={variant}>
      {children}
    </a>
  ),
  StyledBreadCrumbs: ({ children, separator }: any) => (
    <nav aria-label="nblBreadcrumb">
      {children}
      <span data-testid="separator">{separator}</span>
    </nav>
  ),
}));

describe('NblBreadCrumbs', () => {
  const breadCrumbs: NblBreadcrumbItem[] = [
    { label: 'Home', route: '/home' },
    { label: 'IaaS', route: '/iaas' },
    { label: 'PaaS', route: '/paas' },
  ];

  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders all breadcrumb items with correct variant', () => {
    render(<NblBreadCrumbs breadCrumbs={breadCrumbs} />);

    const links = screen.getAllByRole('link');
    expect(links).toHaveLength(breadCrumbs.length);

    // Check variant attribute: last one 'body3', others 'button'
    links.forEach((link, idx) => {
      if (idx === breadCrumbs.length - 1) {
        expect(link).toHaveAttribute('data-variant', 'body3');
      } else {
        expect(link).toHaveAttribute('data-variant', 'button');
      }
      expect(link).toHaveTextContent(breadCrumbs[idx].label);
    });
  });

  it('renders the separator icon', () => {
    render(<NblBreadCrumbs breadCrumbs={breadCrumbs} />);
    expect(screen.getByTestId('separator')).toBeInTheDocument();
    expect(screen.getByTestId('navigate-next-icon')).toBeInTheDocument();
  });

  it('calls onClick callback with correct args when provided', () => {
    const onClick = jest.fn();
    render(<NblBreadCrumbs breadCrumbs={breadCrumbs} onClick={onClick} />);

    const links = screen.getAllByRole('link');
    fireEvent.click(links[1]);

    expect(onClick).toHaveBeenCalledWith(breadCrumbs[1], 1);
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('calls navigate with correct route when onClick not provided', () => {
    render(<NblBreadCrumbs breadCrumbs={breadCrumbs} />);

    const links = screen.getAllByRole('link');
    fireEvent.click(links[0]);

    expect(mockNavigate).toHaveBeenCalledWith(breadCrumbs[0].route);
  });
});
