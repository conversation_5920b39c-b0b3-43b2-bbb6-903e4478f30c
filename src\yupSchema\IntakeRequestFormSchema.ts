import * as yup from 'yup';

export const validationSchema = yup.object().shape({
  requestingOrganization: yup.string().required('Requesting Organization is required'),
  capabilityArea: yup.string().required('Capability Area is required'),
  priority: yup.string().required('Priority is required'),
  summary: yup.string().max(255, 'Summary must be less than 255 characters').required('Summary is required'),
  description: yup.string().required().trim(),
  justification: yup.string().required('Justification is required'),
});