import { MemoryRouter as Router } from 'react-router-dom';
import { act, render, screen, fireEvent } from '@testing-library/react';
import configureMockStore from 'redux-mock-store';
import { Provider as ReduxProvider } from 'react-redux';

import AddTagForm from '../AddTagForm';
import ThemeProvider from 'mock/ThemeProvider';
import AdministrationService from 'api/ApiService/AdministrationService';
import { ServiceCatalogMetaData } from 'mock/ServiceCatalogMetaData';
import { GetTagKeysData } from 'mock/GetTagKeysData';
import { GetTagValuesData } from 'mock/GetTagValuesData';

import * as api from 'api/static-data';
import { GetAdminstrationCatalogItems } from 'mock/AdminTiles';

jest.mock('react-toastify');

const mockStore = configureMockStore();
const store = mockStore({
  authorization: {
    adminPermissions: [{ shortName: 'tags', canCreate: true, canRead: true, canUpdate: true, canDelete: false }],
  },
  common: {
    isDialogMaximized: false,
  },
});

const BASE_ROUTE = ['/administration/tags/add-tag'];

describe('Create AddGroup new request form', () => {
  let getLevel4ItemsSpy: jest.SpyInstance;
  let getTagKeysSpy: jest.SpyInstance;
  let getTagValuesSpy: jest.SpyInstance;

  let getAdminCatalogItemsSpy: jest.SpyInstance;

  beforeEach(async () => {
    getAdminCatalogItemsSpy = jest.spyOn(api, 'getAdministrationCatalogItems');
    getAdminCatalogItemsSpy.mockResolvedValue(GetAdminstrationCatalogItems);

    getLevel4ItemsSpy = jest.spyOn(AdministrationService.prototype, 'getLevel4Items');
    getLevel4ItemsSpy.mockResolvedValue(ServiceCatalogMetaData.data);

    getTagKeysSpy = jest.spyOn(AdministrationService.prototype, 'getTagKeys');
    getTagKeysSpy.mockResolvedValue(GetTagKeysData.data.tagKeys);

    getTagValuesSpy = jest.spyOn(AdministrationService.prototype, 'getTagValues');
    getTagValuesSpy.mockResolvedValue(GetTagValuesData.data.tagValues);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const handleClose = jest.fn();
  const handleSuccess = jest.fn();

  test('Should render the form with all the fields', async () => {
    const { getByText } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AddTagForm title="Add Tag" permissions={{ canCreate: true }} onClose={handleClose} onSuccess={handleSuccess} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );
    expect(screen.getByText('Service Catalog Item *')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();

    const submitButton = getByText('Submit');
    const cancelButton = getByText('Cancel');
    expect(submitButton).toBeDisabled();
    expect(cancelButton).toBeEnabled();
  });

  test('Should submit the request for adding the tags', async () => {
    const { getByLabelText, getByTestId } = await act(async () =>
      render(
        <Router initialEntries={BASE_ROUTE}>
          <ReduxProvider store={store}>
            <ThemeProvider>
              <AddTagForm title="Add Tag" permissions={{ canCreate: true }} onClose={handleClose} onSuccess={handleSuccess} />
            </ThemeProvider>
          </ReduxProvider>
        </Router>
      )
    );

    fireEvent.change(getByTestId('catalog-item'), { target: { value: 'linux8and9' } });
    fireEvent.change(getByLabelText('Description'), { target: { value: 'Adding tag for linux 8 and 9' } });

    expect(getByTestId('add-tag')).toBeInTheDocument();
  });
});
