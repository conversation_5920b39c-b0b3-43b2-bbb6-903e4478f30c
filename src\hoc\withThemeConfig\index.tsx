import React, { useState, useEffect } from 'react';
import useToolsRoute from 'hooks/useToolsRoute';

import Spinner from 'components/Spinner';

// eslint-disable-next-line
import { ThemeConfig } from 'api/ApiService/type';
import { THEMES } from 'mantis/themes/bundle';

export type ThemeConfigState = ThemeConfig & {
  backgroundImage: string;
  nebulaLogo: string;
  spectrumLogo: string;
};

const withThemeConfig = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  const WithThemeConfig: React.FC<P> = (props) => {
    const defaultTheme = THEMES.lightBlue.name;
    const [isThemeLoading, setIsThemeLoading] = useState<boolean>(false);
    const [themeConfig, setThemeConfig] = useState<ThemeConfigState>(THEMES[defaultTheme as keyof typeof THEMES]);
    const isToolsRoute = useToolsRoute();
    useEffect(() => {
      if (isToolsRoute) {
        setThemeConfig({
          ...THEMES.lightBlue,
        });
      } else {
        setThemeConfig(THEMES[defaultTheme as keyof typeof THEMES]);
      }
    }, [isToolsRoute]);
    useEffect(() => {
      const timeoutId = setTimeout(() => {
        setIsThemeLoading(false);
        clearTimeout(timeoutId);
      }, 1000);
    }, [themeConfig]);

    const themeSwitcher = (themeName: string) => {
      setIsThemeLoading(true);
      switch (themeName) {
        case THEMES.blue.name:
          setThemeConfig({
            ...THEMES.blue,
          });
          break;
        case THEMES.lightBlue.name:
          setThemeConfig({
            ...THEMES.lightBlue,
          });
          break;
      }
      localStorage.setItem('themePreference', themeName);
    };

    if (isThemeLoading) {
      return <Spinner color={themeConfig.palette.spinner.themeLoaderIconColor} />;
    }

    return <WrappedComponent {...props} themeConfig={themeConfig} themeSwitcher={themeSwitcher} />;
  };

  return WithThemeConfig;
};

export default withThemeConfig;
