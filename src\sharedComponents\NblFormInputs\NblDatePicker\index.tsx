import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { Dayjs } from 'dayjs';
import React from 'react';
import NblInputLabel from '../NblInputLabel';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblInputHelperText from '../NblInputHelperText';
import { StyledDatePicker, StyledDateTimePicker, StyledDay } from './styled';

interface DatePickerProps {
  disabled?: boolean;
  error?: boolean | string;
  label: string;
  name: string;
  placeholder?: string;
  value: Dayjs | null;
  minDate?: Dayjs;
  handleChange: (event: any) => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  helperText?: string;
  mandatory?: boolean;
  withTimePicker?: boolean;
}

const NblDatePicker: React.FC<DatePickerProps> = ({
  label,
  name,
  value,
  disabled,
  error,
  minDate,
  handleChange,
  onMouseEnter,
  onMouseLeave,
  helperText = '',
  withTimePicker,
}) => {
  return (
    <NblFlexContainer direction="column" position="relative">
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <NblInputLabel label={label} name={name} disabled={disabled} />
        {withTimePicker ? (
          <StyledDateTimePicker
            value={value}
            name={name}
            disabled={disabled}
            minDate={minDate}
            onChange={(newValue) => handleChange(newValue)}
            slotProps={{
              textField: { id: name, variant: 'outlined', onMouseEnter: onMouseEnter, onMouseLeave: onMouseLeave },
              digitalClockSectionItem: {
                sx: {
                  width: '25px',
                },
              },
            }}
            slots={{ day: StyledDay }}
          />
        ) : (
          <StyledDatePicker
            value={value}
            name={name}
            disabled={disabled}
            minDate={minDate}
            onChange={(newValue) => handleChange(newValue)}
            slotProps={{ textField: { id: name, variant: 'outlined', onMouseEnter: onMouseEnter, onMouseLeave: onMouseLeave } }}
            slots={{ day: StyledDay }}
          />
        )}
      </LocalizationProvider>
      <NblInputHelperText error={Boolean(error)} helperText={helperText} />
    </NblFlexContainer>
  );
};

export default NblDatePicker;
