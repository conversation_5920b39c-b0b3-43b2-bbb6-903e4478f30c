export function calculateStatusLevel(status: number) {
  switch (true) {
    case status >= 0 && status < 20:
      return 0;
    case status >= 20 && status < 40:
      return 1;
    case status >= 40 && status < 60:
      return 2;
    case status >= 60 && status < 80:
      return 3;
    case status >= 80 && status < 100:
      return 4;
    case status >= 100:
      return 5;
    default:
      return 0;
  }
}

export function getStatusLabel(status: number) {
  const level = calculateStatusLevel(status);
  switch (level) {
    case 0:
      return 'Low';
    case 1:
      return 'Normal';
    case 2:
      return 'Attention';
    case 3:
      return 'Warning';
    case 4:
      return 'Danger';
    case 5:
      return 'Critical';
    default:
      return 'Attention';
  }
}
