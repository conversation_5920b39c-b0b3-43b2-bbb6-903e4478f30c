import { MuiTelInput } from 'mui-tel-input';
import { Stack, FormHelperText, InputLabel } from '@mui/material';

interface PhoneInputFieldProps {
  error?: boolean | string;
  label: string;
  name: string;
  placeholder?: string;
  readOnly?: boolean;
  value: string;
  handleChange: (event: any) => void;
  handleBlur: (event: any) => void;
  restrictCountries?: boolean;
}

const PhoneInputField = ({
  error,
  label,
  name,
  placeholder,
  value,
  readOnly,
  handleBlur,
  handleChange,
  restrictCountries = true,
}: PhoneInputFieldProps) => {
  const defaultCountry = 'US';
  return (
    <Stack spacing={1}>
      <InputLabel htmlFor={name}>{label}</InputLabel>
      <MuiTelInput
        defaultCountry={defaultCountry}
        disableFormatting
        id={name}
        name={name}
        placeholder={placeholder}
        disabled={readOnly}
        fullWidth
        value={value}
        error={Boolean(error)}
        onBlur={handleBlur}
        onChange={handleChange}
        {...(restrictCountries && { onlyCountries: ['US'] })}
        disableDropdown={defaultCountry === 'US'}
        forceCallingCode={defaultCountry === 'US'}
      />
      <FormHelperText error>{error}</FormHelperText>
    </Stack>
  );
};

export default PhoneInputField;
