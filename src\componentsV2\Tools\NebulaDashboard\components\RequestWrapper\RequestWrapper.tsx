import { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import RequestCard from '../RequestCard/RequestCard';
import { MetricsData } from '../../responseInterface';
import { GENERATE_QUERY_ALL_REQUEST } from '../../queries';

import { StyledUMProgressBarComplete, StyledUMProgressBarFailed, StyledUMCard, StyledLegendIcon } from '../../styled';
import UsageMetricsService from 'api/ApiService/UsageMetricsService';
// eslint-disable-next-line
import { State } from 'store/reducers/type';
import { useDispatch, useSelector } from 'react-redux';
import { updateRequestOverviewType } from 'store/reducers/requestOverview';
import { RequestSummaryApiServices } from 'api/ApiService/type';
import useNblNavigate from 'hooks/useNblNavigate';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblTypography from 'sharedComponents/NblTypography';
import { getRequestStatusGroup, RequestStatusGroupIdentifier } from 'types/Enums/RequestStatusGroup';
import NblTooltip from 'sharedComponents/NblTooltip';

const RequestWrapper: React.FunctionComponent = () => {
  const dispatch = useDispatch();
  const [metricsData, setmetricsData] = useState<MetricsData | null>(null);
  const [prevMetricsData, setprevMetricsData] = useState<MetricsData | null>(null);
  const [isLoading, setisLoading] = useState<boolean>(false);
  const usageMetricsService = new UsageMetricsService();
  const { allCatalog1, allCatalog3, allCatalog4, selectedCatalog1, selectedCatalog3, selectedCatalog4, year, quarters } = useSelector(
    (state: State) => state.RequestFilterConfig
  );
  const catalog1 = allCatalog1.filter(({ id }) => selectedCatalog1.includes(id)).map(({ value }) => value);
  const catalog3 = allCatalog3.filter(({ id }) => selectedCatalog3.includes(id)).map(({ value }) => value);
  const catalog4 = allCatalog4.filter(({ id }) => selectedCatalog4.includes(id)).map(({ value }) => value);

  useEffect(() => {
    if (catalog1.length && catalog3.length && catalog4.length) {
      fetchData();
    }
  }, [selectedCatalog1, selectedCatalog3, selectedCatalog4, year, quarters]);

  const getPreviousRequests = (): number => {
    if (quarters.length === 1) {
      const currentQuarter = quarters[0];
      if (currentQuarter > 1) {
        return currentQuarter - 1;
      } else return 4; //if current quarter is 1, i.e, previous quarter is 4th quarter of previous year
    } else return 0; // 0 is considered as falsi statement in JS
  };

  const getTotalSubmittedRequest = (data: MetricsData | null) => {
    if (!data) return 0;
    return (
      (data.approvedRequests || 0) +
      (data.processingRequests || 0) +
      (data.rejectedRequests || 0) +
      (data.cancelledRequests || 0) +
      (data.failedRequests || 0) +
      (data.completedRequests || 0)
    );
  };
  const fetchData = async () => {
    try {
      setisLoading(true);
      setmetricsData(null);
      setprevMetricsData(null);
      const payloadforPresentQuarter = { query: GENERATE_QUERY_ALL_REQUEST(year, quarters, catalog1, catalog3, catalog4) };
      const previousQuarter = getPreviousRequests();
      let responses: Promise<any>[] = [];
      responses.push(usageMetricsService.customGraphQL<RequestSummaryApiServices>(payloadforPresentQuarter));

      if (previousQuarter) {
        const payloadforPreviousQuarter = {
          query: GENERATE_QUERY_ALL_REQUEST(previousQuarter === 4 ? year - 1 : year, [previousQuarter], catalog1, catalog3, catalog4),
        };
        responses.push(usageMetricsService.customGraphQL<RequestSummaryApiServices>(payloadforPreviousQuarter));
      }
      const response = await Promise.all(responses);
      const responseforPresentQuarter = response[0];
      setmetricsData(responseforPresentQuarter && responseforPresentQuarter.data.data.getRequestSummary);
      if (previousQuarter) {
        const responseforPreviousQuarter = response[1];
        setprevMetricsData(responseforPreviousQuarter && responseforPreviousQuarter.data.data.getRequestSummary);
      } else {
        setprevMetricsData(null);
      }
      setisLoading(false);
    } catch (error) {
      setisLoading(false);
    }
  };
  const navigate = useNblNavigate();

  return (
    <NblGridContainer columns={14} spacing={2}>
      <NblGridItem colspan={2}>
        <RequestCard
          totalRequests={getTotalSubmittedRequest(metricsData)}
          previousRequests={getTotalSubmittedRequest(prevMetricsData)}
          title={'Total Requests'}
          catalogData={{
            catalog1: catalog1,
            catalog3: catalog3,
            catalog4: catalog4,
            year: year,
            quarters: quarters,
            status: getRequestStatusGroup(RequestStatusGroupIdentifier.TOTALREQUESTS),
            totalCreatedRequests: false,
          }}
          loading={isLoading}
          statusList={[]}
        />
      </NblGridItem>
      <NblGridItem colspan={2}>
        <RequestCard
          totalRequests={metricsData?.processingRequests!}
          previousRequests={prevMetricsData?.processingRequests}
          title={'Pending Requests'}
          catalogData={{
            catalog1: catalog1,
            catalog3: catalog3,
            catalog4: catalog4,
            status: getRequestStatusGroup(RequestStatusGroupIdentifier.PENDING),
            year: year,
            quarters: quarters,
            totalCreatedRequests: false,
          }}
          loading={isLoading}
          statusList={getRequestStatusGroup(RequestStatusGroupIdentifier.PENDING)}
        />
      </NblGridItem>
      <NblGridItem colspan={2}>
        <RequestCard
          totalRequests={metricsData?.approvedRequests!}
          previousRequests={prevMetricsData?.approvedRequests}
          title={'Approved Requests'}
          catalogData={{
            catalog1: catalog1,
            catalog3: catalog3,
            catalog4: catalog4,
            status: getRequestStatusGroup(RequestStatusGroupIdentifier.APPROVED),
            year: year,
            quarters: quarters,
            totalCreatedRequests: false,
          }}
          loading={isLoading}
          statusList={getRequestStatusGroup(RequestStatusGroupIdentifier.APPROVED)}
        />
      </NblGridItem>
      <NblGridItem colspan={2}>
        <RequestCard
          totalRequests={metricsData?.rejectedRequests!}
          previousRequests={prevMetricsData?.rejectedRequests}
          title={'Rejected Requests'}
          catalogData={{
            catalog1: catalog1,
            catalog3: catalog3,
            catalog4: catalog4,
            status: getRequestStatusGroup(RequestStatusGroupIdentifier.REJECTED),
            year: year,
            quarters: quarters,
            totalCreatedRequests: false,
          }}
          loading={isLoading}
          statusList={getRequestStatusGroup(RequestStatusGroupIdentifier.REJECTED)}
        />
      </NblGridItem>
      <NblGridItem colspan={2}>
        <RequestCard
          totalRequests={metricsData?.cancelledRequests!}
          previousRequests={prevMetricsData?.cancelledRequests}
          title={'Cancelled Requests'}
          catalogData={{
            catalog1: catalog1,
            catalog3: catalog3,
            catalog4: catalog4,
            status: getRequestStatusGroup(RequestStatusGroupIdentifier.CANCELLED),
            year: year,
            quarters: quarters,
            totalCreatedRequests: false,
          }}
          loading={isLoading}
          statusList={getRequestStatusGroup(RequestStatusGroupIdentifier.CANCELLED)}
        />
      </NblGridItem>
      <NblGridItem colspan={4}>
        <StyledUMCard>
          <NblFlexContainer flexType="flex" justifyContent="space-between">
            <NblTypography variant="subtitle1" color={'shade1'} weight="bold" width="100%">
              Request Overview
            </NblTypography>

            <NblFlexContainer flexType="flex" justifyContent="flex-end" alignItems="center" spacing={0}>
              <StyledLegendIcon bgColor="green" />
              <NblTypography variant="subtitle1" color={'shade1'} margin="0 10px 0 0">
                Completed
              </NblTypography>
              <StyledLegendIcon bgColor="red" />
              <NblTypography variant="subtitle1" color={'shade1'}>
                Failed
              </NblTypography>
            </NblFlexContainer>
          </NblFlexContainer>
          {!metricsData ? (
            <p>Loading...</p>
          ) : metricsData.failedRequests || metricsData.completedRequests ? (
            <NblFlexContainer justifyContent="space-between" margin="15px 0 0 0" spacing={1}>
              <Box
                style={{
                  width:
                    Math.round((metricsData.completedRequests / (metricsData.completedRequests + metricsData.failedRequests)) * 100) + '%',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  dispatch(
                    updateRequestOverviewType({
                      catalog1: catalog1,
                      catalog3: catalog3,
                      catalog4: catalog4,
                      catalog2: [],
                      status: getRequestStatusGroup(RequestStatusGroupIdentifier.COMPLETED),
                      year: year,
                      quarters: quarters,
                      totalCreatedRequests: false,
                    })
                  );
                  navigate('requestoverview');
                }}
              >
                <NblTooltip
                  tooltipMessage={getRequestStatusGroup(RequestStatusGroupIdentifier.COMPLETED).join('\n')}
                  key="completed-tooltip"
                >
                  <StyledUMProgressBarComplete></StyledUMProgressBarComplete>
                </NblTooltip>
                {metricsData.completedRequests > 0 && (
                  <NblTypography variant="subtitle1" weight="bold" textAlign="right" margin="10px 0 0 0">
                    {metricsData.completedRequests}
                  </NblTypography>
                )}
              </Box>

              <Box
                style={{
                  width:
                    Math.round((metricsData.failedRequests / (metricsData.completedRequests + metricsData.failedRequests)) * 100) + '%',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  dispatch(
                    updateRequestOverviewType({
                      catalog1: catalog1,
                      catalog3: catalog3,
                      catalog4: catalog4,
                      catalog2: [],
                      status: getRequestStatusGroup(RequestStatusGroupIdentifier.FAILED),
                      year: year,
                      quarters: quarters,
                      totalCreatedRequests: false,
                    })
                  );
                  navigate('requestoverview');
                }}
              >
                <NblTooltip tooltipMessage={getRequestStatusGroup(RequestStatusGroupIdentifier.FAILED).join('\n')} key="failed-tooltip">
                  <StyledUMProgressBarFailed></StyledUMProgressBarFailed>
                </NblTooltip>
                {metricsData.failedRequests > 0 && (
                  <NblTypography variant="subtitle1" weight="bold" textAlign="right" margin="10px 0 0 0">
                    {metricsData.failedRequests}
                  </NblTypography>
                )}
              </Box>
            </NblFlexContainer>
          ) : (
            'No Data Found'
          )}
        </StyledUMCard>
      </NblGridItem>
    </NblGridContainer>
  );
};

export default RequestWrapper;
