import React, { useState } from 'react';
import { useNblForms } from 'sharedComponents/NblContainers/NblFormContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import { FormValues } from '../..';
import { generateEnum, getQuotaUnits } from 'utils/common';
import NblTextField from 'sharedComponents/NblFormInputs/NblTextField';
import { STORAGE_HELPER_TEXT } from 'utils/constant';
import NblSelect from 'sharedComponents/NblFormInputs/NblSelect';
import NblCheckBox from 'sharedComponents/NblCheckBox';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';

interface BucketDetailsProps {
  isEditMode?: boolean;
  isRegenerateMode?: boolean;
}

const BucketDetails: React.FunctionComponent<BucketDetailsProps> = ({ isEditMode, isRegenerateMode }) => {
  //Custom Hook
  const { nblFormValues, nblFormProps } = useNblForms<FormValues>();

  //State
  const [isHovered, setIsHovered] = useState<string>('');

  //Local Variables
  const FIELDNAMES = generateEnum<FormValues>(nblFormValues);

  //Handlers
  const handleHovered = (hovered: string) => {
    setIsHovered(hovered);
  };

  //JSX
  return (
    <>
      <NblGridContainer columns={6} spacingY={2} spacingX={9}>
        <NblGridItem colspan={2}>
          <NblTextField
            type={'text'}
            label={'Bucket Name Description'}
            name={FIELDNAMES.bucketNameDesc}
            placeholder="Please Enter"
            value={nblFormValues.bucketNameDesc}
            readOnly={isEditMode}
            multiline
            disabled={isEditMode || isRegenerateMode}
            handleChange={nblFormProps.handleChange}
            handleBlur={nblFormProps.handleBlur}
            error={Boolean(nblFormProps.touched.bucketNameDesc && nblFormProps.errors.bucketNameDesc)}
            helperText={isHovered === 'BucketNameDesc' ? STORAGE_HELPER_TEXT.bucketNameDesc : nblFormProps.errors.bucketNameDesc}
            onMouseEnter={() => handleHovered('BucketNameDesc')}
            onMouseLeave={() => handleHovered('')}
          />
        </NblGridItem>
        <NblGridItem colspan={3}>
          <NblTextField
            type={'text'}
            label={'Bucket Name'}
            mandatory
            name={FIELDNAMES.bucketName}
            placeholder="Please Enter"
            value={nblFormValues.bucketName}
            multiline
            disabled
            handleChange={nblFormProps.handleChange}
            handleBlur={nblFormProps.handleBlur}
            error={Boolean(nblFormProps.touched.bucketName && nblFormProps.errors.bucketName)}
            helperText={isHovered === 'BucketName' ? STORAGE_HELPER_TEXT.bucketName : nblFormProps.errors.bucketName}
            onMouseEnter={() => handleHovered('BucketName')}
            onMouseLeave={() => handleHovered('')}
          />
        </NblGridItem>
        <NblGridItem colspan={2}>
          <NblTextField
            type={'text'}
            label={'Bucket Quota'}
            mandatory
            disabled={isEditMode || isRegenerateMode}
            name={FIELDNAMES.bucketQuota}
            placeholder="Please Enter"
            value={nblFormValues.bucketQuota}
            handleChange={nblFormProps.handleChange}
            handleBlur={nblFormProps.handleBlur}
            error={Boolean(nblFormProps.touched.bucketQuota && nblFormProps.errors.bucketQuota)}
            helperText={nblFormProps.errors.bucketQuota}
          />
        </NblGridItem>
        <NblGridItem colspan={1}>
          <NblSelect
            label={'Unit'}
            mandatory
            disabled={isEditMode || isRegenerateMode}
            name={FIELDNAMES.bucketQuotaUnit}
            placeholder={'Select'}
            value={nblFormValues.bucketQuotaUnit}
            handleChange={nblFormProps.handleChange}
            options={getQuotaUnits()}
            helperText={nblFormProps.errors.bucketQuotaUnit}
            error={Boolean(nblFormProps.touched.bucketQuotaUnit && nblFormProps.errors.bucketQuotaUnit)}
          />
        </NblGridItem>
        <NblGridItem colspan={3}>
          <NblFlexContainer alignItems="center">
            <NblCheckBox
              label={'Versioning'}
              name={FIELDNAMES.versioning}
              disabled={isRegenerateMode}
              checked={nblFormValues.versioning}
              onChange={nblFormProps.handleChange}
              onBlur={nblFormProps.handleBlur}
            />
          </NblFlexContainer>
        </NblGridItem>
        <NblGridItem colspan={2}>
          <NblTextField
            type={'text'}
            label={'Description'}
            mandatory
            name={FIELDNAMES.bucketDescription}
            placeholder="Please Enter"
            value={nblFormValues.bucketDescription}
            handleChange={nblFormProps.handleChange}
            handleBlur={nblFormProps.handleBlur}
            error={Boolean(nblFormProps.touched.bucketDescription && nblFormProps.errors.bucketDescription)}
            helperText={nblFormProps.errors.bucketDescription}
            disabled={isRegenerateMode}
          />
        </NblGridItem>
      </NblGridContainer>
    </>
  );
};
export default BucketDetails;
