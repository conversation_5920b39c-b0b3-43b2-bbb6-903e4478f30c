import React, { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { useTheme } from '@mui/material';
import { useSelector } from 'react-redux';
//eslint-disable-next-line no-unused-vars
import { Formik, FormikContext, FormikErrors, FormikHelpers, FormikProps, FormikValues, FormikConfig, FormikTouched } from 'formik';
import { NebulaTheme } from 'NebulaTheme/type';
import NblStepper, { NblStepperProps } from 'sharedComponents/NblStepper';
import NblBorderContainer from '../NblBorderContainer';
import NblFlexContainer from '../NblFlexContainer';
import NblTypography from '../../NblTypography';
import NblFormButtonGroup, { NblFormButtonGroupProps } from './NblFormButtonGroup';
import NblSuccessfulPage, { SuccessPageProps } from 'sharedComponents/NblSuccessfulPage';
import useNblNavigate from 'hooks/useNblNavigate';
import NblConfirmPopUp from 'sharedComponents/NblConfirmPopUp';
import { decodeJWTToken } from 'utils/common';
//eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';

//Types and Interface
export type NblFormValues = FormikValues;
export type NblFormProps<Values> = FormikProps<Values>;
export type NblFormHelpers<Values> = FormikHelpers<Values>;
export type NblFormErrors<Values> = FormikErrors<Values>;
export type NblFormTouched<Values> = FormikTouched<Values>;

interface NblFormContainerProps<Values extends NblFormValues> extends NblFormButtonGroupProps, NblStepperProps {
  children: React.ReactNode;
  title: string;
  caption: string;
  onSubmit?: (values: Values, helpers: NblFormHelpers<Values>) => void;
  formInitialValues: Values;
  formValidationSchema?: FormikConfig<Values>['validationSchema'];
  onPrevious?: (stepIndex: number) => void;
  onNext?: (stepIndex: number) => void;
  onStepChange?: (steps: NblStepperProps['steps']) => void;
  onPreview?: () => void;
  formType?: 'stepper' | 'simple' | 'subform';
  responseData?: SuccessPageProps;
  Icon?: React.ElementType;
  initialErrors?: FormikErrors<Values>;
  onCancel?: () => void;
}

interface NblFormContextProps {
  currentStep: number;
  steps: NblStepperProps['steps'];
  setSteps: (steps: NblStepperProps['steps']) => void;
  onPreviewMode: boolean;
  setCurrentStep: (step: number) => void;
  closePreview: () => void;
}

//Context
const NblFormContext = createContext<NblFormContextProps>({
  currentStep: 0,
  steps: [{ title: '', caption: '', icon: '', status: 'pending', errorFields: [] }],
  setSteps: () => {},
  onPreviewMode: false,
  setCurrentStep: () => {},
  closePreview: () => {},
});

//Custom Hook
export const useNblForms = <Values extends NblFormValues>(): {
  nblFormProps: Omit<NblFormProps<Values>, 'values'>;
  nblFormValues: Values;
} & NblFormContextProps => {
  const { values, ...nblFormProps } = useContext(FormikContext);
  const nblContext = useContext(NblFormContext);
  if (!nblContext) throw new Error('useNblForms hook should be called inside NblFormContainer');
  return { nblFormProps, nblFormValues: values, ...nblContext };
};

//Context Provider
export const NblFormProvider = <Values,>({
  children,
  value,
  nblFormProps,
  clearInitialMountRef,
}: {
  children: React.ReactNode;
  value: NblFormContextProps;
  nblFormProps: NblFormProps<Values>;
  clearInitialMountRef: () => void;
}) => {
  //Local
  const { currentStep, steps, setSteps } = value;
  const errors = nblFormProps.errors as { [key: string]: any };
  const touched = nblFormProps.touched as { [key: string]: any };
  const formValues = nblFormProps.values;

  //Memoization
  const hasError = useMemo(() => {
    const errorFields = steps[currentStep].errorFields;
    const isError = errorFields.some((errorField) => touched[errorField] && errors[errorField]);
    return isError;
  }, [steps, currentStep, errors, touched, formValues]);

  //Utils
  const updateCurrentStep = (isError: boolean) => {
    const newSteps = [...steps];
    newSteps[currentStep].status = isError ? 'error' : 'current';
    setSteps(newSteps);
  };

  //Side Effects
  useEffect(() => {
    //If there are errors in form then formik itself will handle the enable/disable of btns. So we no need the form initial mount state
    if (Object.keys(errors).length) {
      clearInitialMountRef();
    }
    if (hasError) {
      updateCurrentStep(hasError);
    } else if (steps[currentStep].status === 'error') {
      updateCurrentStep(hasError);
    }
  }, [errors, touched]);

  useEffect(() => {
    const values = nblFormProps.values as Record<string, any>;
    //If formik marked the form as dirty then we need to check all mandatory fields for the current step, if all are filled we can clear the form initial state
    if (nblFormProps.dirty && steps[currentStep].errorFields.every((field: string) => Boolean(values[field]))) clearInitialMountRef();
  }, [nblFormProps.dirty, nblFormProps.values, steps]);

  //JSX
  return <NblFormContext.Provider value={value}>{children}</NblFormContext.Provider>;
};

//Component
const NblFormContainer = <Values extends NblFormValues>({
  children,
  formInitialValues,
  formValidationSchema,
  title,
  caption,
  steps,
  onStepClick = () => {},
  wrap,
  onNext = () => {},
  onPreview = () => {},
  onPrevious = () => {},
  onStepChange = () => {},
  onSubmit = () => {},
  onCancel = () => {},
  showCancel,
  showNext,
  showPreview,
  showPrevious,
  showSubmit,
  canCreate = true,
  canUpdate = true,
  readOnly = false,
  formType = 'stepper',
  responseData,
  Icon,
  initialErrors,
  submitText,
}: NblFormContainerProps<Values>) => {
  //Hooks
  const theme = useTheme<NebulaTheme>();
  const navigate = useNblNavigate();
  const { userDetails } = useSelector((state: State) => state.user);

  //Local
  const totalSteps = steps.length - 1;
  const isStepper = formType === 'stepper';
  const submissionAllowedGroups = process.env.REACT_APP_FORM_SUBMISSION_ALLOWED_GROUPS;

  //States
  const [isFormInInitalState, setIsFormInInitialState] = useState(true);
  const [initialValue, setInitialValue] = useState(formInitialValues);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [stepsPrivate, setStepsPrivate] = useState(steps);
  const [disablePrevious, setDisablePrevious] = useState(true);
  const [disableNext, setDisableNext] = useState(false);
  const [showPreviousPrivate, setShowPreviousPrivate] = useState(true);
  const [showNextPrivate, setShowNextPrivate] = useState(true);
  const [showSubmitPrivate, setShowSubmitPrivate] = useState(false);
  const [showPreviewPrivate, setShowPreviewPrivate] = useState(false);
  const [showCancelPrivate, setShowCancelPrivate] = useState(false);
  const [onPreviewMode, setOnPreviewMode] = useState(false);
  const [showCancelPopup, setShowCancelPopup] = useState(false);
  const [hasSubmitAccess, setHasSubmitAccess] = useState(false);

  //Handlers
  const onNextClickHandler = (nblFormProps: NblFormProps<Values>) => {
    if (currentStep < totalSteps) {
      const newSteps = [...stepsPrivate];
      const valid = isValid(nblFormProps);
      if (valid) {
        newSteps[currentStep].status = 'completed';
        if (currentStep + 1 <= totalSteps) newSteps[currentStep + 1].status = 'current';
        onNext(currentStep + 1);
        setCurrentStep((prev) => prev + 1);
      } else {
        setDisableNext(true);
        newSteps[currentStep].status = 'error';
      }
      setStepsPrivate(newSteps);
      onStepChange(newSteps);
    }
  };

  const onPreviousClickHandler = (nblFormProps: NblFormProps<Values>) => {
    if (currentStep > 0) {
      const newSteps = [...stepsPrivate];
      const valid = isValid(nblFormProps);
      newSteps[currentStep].status = valid ? 'completed' : 'pending';
      newSteps[currentStep - 1].status = 'current';
      setStepsPrivate(newSteps);
      onStepChange(newSteps);
      onPrevious(currentStep - 1);
      setCurrentStep((prev) => prev - 1);
    }
  };

  const onStepClickHandler = (index: number, nblFormProps: NblFormProps<Values>) => {
    const valid = isValid(nblFormProps);
    if (index <= currentStep || valid) {
      const newSteps = [...stepsPrivate];
      newSteps[currentStep].status = valid ? 'completed' : 'pending';
      newSteps[index].status = 'current';
      setCurrentStep(index);
      setStepsPrivate(newSteps);
      onStepChange(newSteps);
      onStepClick(index);
    }
  };

  const onPreviewClickHandler = (nblFormProps: NblFormProps<Values>) => {
    const valid = isValid(nblFormProps);
    if (valid) {
      setShowPreviousPrivate(false);
      setShowPreviewPrivate(false);
      setOnPreviewMode(true);
      setShowCancelPrivate(true);
      onPreview();
    }
  };

  const onCancleClickHandler = (nblFormValues?: Values, nblFormProps?: NblFormProps<Values>) => {
    if (onPreviewMode) {
      setShowPreviousPrivate(true);
      setShowCancelPrivate(false);
      setShowPreviewPrivate(true);
      setOnPreviewMode(false);
    } else {
      if (canShowCancelDialog(nblFormValues, nblFormProps)) setShowCancelPopup(true);
      else navigate(-1);
    }
    onCancel();
  };

  const onSubmitClickHandler = (nblFormValues: Values, nblFormHelpers: NblFormHelpers<Values>) => {
    const newSteps = [...stepsPrivate];
    newSteps[currentStep].status = 'completed';
    setStepsPrivate(newSteps);
    onStepChange(newSteps);
    onSubmit(nblFormValues, nblFormHelpers);
  };

  //Utils
  function isValid(nblFormProps: NblFormProps<Values>) {
    const errorFields = steps[currentStep].errorFields;
    if (isFormInInitalState) {
      return false;
    } else {
      return errorFields.length === 0 || !errorFields.some((errorField) => nblFormProps.errors[errorField]);
    }
  }

  function canShowCancelDialog(nblFormValues?: Values, nblFormProps?: NblFormProps<Values>) {
    let show = false;
    if (!isFormInInitalState && nblFormProps?.dirty && nblFormProps?.touched && Object.keys(nblFormProps?.touched)) {
      for (const step of steps) {
        const anyFieldFilled = step.errorFields.some((field) => Boolean(nblFormValues?.[field]));
        if (anyFieldFilled) {
          show = true;
          break;
        }
      }
    }
    return show;
  }

  //Side Effects

  useEffect(() => {
    //Mimic the formik that initial values will be reinitialized after validation schema is passed dynamically.
    //This will help us to overcome the missing feature of dynamic validatiomSchema in formik
    setInitialValue(formInitialValues);
  }, [formValidationSchema, formInitialValues]);

  useEffect(() => {
    if (currentStep < totalSteps) {
      setShowNextPrivate(true);
      setShowSubmitPrivate(false);
      setShowPreviewPrivate(false);
    } else {
      setShowNextPrivate(false);
      setShowSubmitPrivate(true);
      setShowPreviewPrivate(isStepper);
    }
    if (currentStep < 1) {
      setDisablePrevious(true);
    } else {
      setDisablePrevious(false);
    }
  }, [currentStep, steps, totalSteps]);

  useEffect(() => {
    if (userDetails.accessToken) {
      const tokenData = decodeJWTToken(userDetails.accessToken);
      if (submissionAllowedGroups?.toLowerCase() === 'all') {
        setHasSubmitAccess(true);
      } else {
        const userGroups = tokenData.groups as Array<string>;
        const submissionAllowedGroupsArray = submissionAllowedGroups?.split(',');
        let hasAccess = submissionAllowedGroupsArray?.some((group) => userGroups.includes(group)) || false;
        setHasSubmitAccess(hasAccess);
      }
    }
  }, [userDetails, decodeJWTToken]);

  //Renders
  const renderContent = () => {
    return responseData?.requestId || responseData?.content ? (
      <NblSuccessfulPage {...responseData} />
    ) : (
      <NblFlexContainer>{children}</NblFlexContainer>
    );
  };

  //JSX
  return (
    <Formik<Values>
      initialValues={initialValue}
      validationSchema={formValidationSchema}
      onSubmit={onSubmitClickHandler}
      validateOnMount
      enableReinitialize
      initialErrors={initialErrors}
    >
      {(nblFormProps) => (
        <NblFormProvider<Values>
          value={{
            currentStep,
            steps: stepsPrivate,
            setSteps: setStepsPrivate,
            onPreviewMode,
            setCurrentStep,
            closePreview: onCancleClickHandler,
          }}
          nblFormProps={nblFormProps}
          clearInitialMountRef={() => {
            if (isFormInInitalState) setIsFormInInitialState(false);
          }}
        >
          <form onSubmit={nblFormProps.handleSubmit}>
            {formType !== 'subform' ? (
              <NblFlexContainer direction="column" height="auto" spacing={3}>
                <NblBorderContainer>
                  <NblFlexContainer direction="column" padding="15px" spacing={3} backgroundColor={theme.palette.secondary.main}>
                    <NblFlexContainer direction="row">
                      {Icon && <Icon sx={{ color: theme.palette.primary.main, fontSize: '2.5rem', marginTop: '10px' }} />}
                      <NblFlexContainer direction="column">
                        <NblTypography variant="h3" weight={'bold'} color={'shade1'}>
                          {title}
                        </NblTypography>
                        {!(responseData?.requestId || responseData?.content) && (
                          <NblTypography variant="body3" weight={'medium'} color={'shade1'}>
                            {caption}
                          </NblTypography>
                        )}
                      </NblFlexContainer>
                    </NblFlexContainer>
                    {!onPreviewMode && isStepper && (
                      <NblStepper steps={stepsPrivate} onStepClick={(index) => onStepClickHandler(index, nblFormProps)} wrap={wrap} />
                    )}
                    {renderContent()}
                  </NblFlexContainer>
                </NblBorderContainer>
                <NblFormButtonGroup<Values>
                  {...{
                    nblFormProps,
                    onNextClickHandler,
                    onPreviousClickHandler,
                    onPreviewClickHandler,
                    onCancel: onCancleClickHandler,
                    showCancel: showCancel ?? (!isStepper || showCancelPrivate),
                    showNext: showNext ?? (isStepper && showNextPrivate),
                    showPreview: showPreview ?? (showPreviewPrivate && !onPreviewMode),
                    showPrevious: showPrevious ?? (isStepper && showPreviousPrivate),
                    showSubmit: hasSubmitAccess && (showSubmit ?? (!isStepper || showSubmitPrivate)),
                    canCreate,
                    canUpdate,
                    readOnly,
                    disablePrevious,
                    disableNext,
                    isValid: isValid(nblFormProps),
                    onSuccess: Boolean(responseData?.requestId) || Boolean(responseData?.content),
                    submitText,
                  }}
                />
              </NblFlexContainer>
            ) : (
              children
            )}
          </form>
          {formType !== 'subform' && (
            <NblConfirmPopUp
              title="Cancel"
              content="You will loose all the changes you made to this form. Would you still like to cancel?"
              submitText="Yes"
              cancelText="No"
              open={showCancelPopup}
              onClose={() => setShowCancelPopup(false)}
              onSubmit={() => navigate(-1)}
            />
          )}
        </NblFormProvider>
      )}
    </Formik>
  );
};

export default NblFormContainer;
