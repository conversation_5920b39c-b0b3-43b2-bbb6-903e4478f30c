import { MetricType } from './statisticinfo';
export enum CplanResourceType {
  VCENTER = 'VMwareAdapter Instance',
  DATACENTER = 'Datacenter',
  CLUSTER = 'ClusterComputeResource',
  HOST = 'HostSystem',
  VM = 'VirtualMachine',
}

export enum exportFormats {
  Excel = 'xlsx',
  CSV = 'csv',
  JPEG = 'jpeg',
  PDF = 'pdf',
}

export const sortLabels: string[] = [
  `ALL`,
  `${MetricType.CPU} Only`,
  `${MetricType.Memory} Only`,
  `${MetricType.Storage} Only`,
  `Ascending Alphabetical`,
];

export enum TimeInterval {
  Hourly = 'HOURLY',
  Daily = 'DAILY',
}
