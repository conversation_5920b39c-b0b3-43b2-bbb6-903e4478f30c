import { useEffect, useState } from 'react';
// eslint-disable-next-line
import { GridColumnVisibilityModel } from '@mui/x-data-grid';

type GridType = 'approvals' | 'requests' | 'resources';
type Columns = { [column: string]: boolean; };

export interface UseDataGridVisibleColumnsProps {
  gridType: GridType;
}

interface HiddenColumns {
  approvals: Columns;
  requests: Columns;
  resources: Columns;
}

const useDataGridVisibleColumns = ({ gridType }: UseDataGridVisibleColumnsProps) => {
  const [columnVisibilityModel, setColumnVisibilityModel] = useState<GridColumnVisibilityModel>({});

  const handleColumnVisibilityChange = (newModel: GridColumnVisibilityModel) => {
    setColumnVisibilityModel(newModel);
    const currentHiddenColumns: string | null = localStorage.getItem('hidden-datagrid-columns');
    const hiddenColumns: HiddenColumns = currentHiddenColumns ? JSON.parse(currentHiddenColumns) : {};
    hiddenColumns[gridType] = newModel;
    localStorage.setItem('hidden-datagrid-columns', JSON.stringify(hiddenColumns));
  };

  useEffect(() => {
    const savedHiddenColumns = localStorage.getItem('hidden-datagrid-columns');
    const savedHiddenColumnsParsed: HiddenColumns = savedHiddenColumns && JSON.parse(savedHiddenColumns);
    setColumnVisibilityModel(savedHiddenColumnsParsed?.[gridType]);
  }, []);

  return {
    columnVisibilityModel,
    handleColumnVisibilityChange
  }
}

export default useDataGridVisibleColumns;