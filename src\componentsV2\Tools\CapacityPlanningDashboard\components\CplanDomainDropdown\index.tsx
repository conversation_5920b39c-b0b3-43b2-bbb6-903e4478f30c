import { useEffect, useState } from 'react';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import NblDropdown from 'sharedComponents/NblDropdown';
import NblTypography from 'sharedComponents/NblTypography';
import { useApiService } from 'api/ApiService/context';
import { useDispatch } from 'react-redux';
import { setSelectedDomainIds } from 'store/reducers/capacityplanning';

const CplanDomainDropdown: React.FC = () => {
  const [domainList, setDomainList] = useState<{ lasttimestamp: string; domainid: number; domainname: string }[]>([]);
  const [selectedDomain, setSelectedDomain] = useState<string[]>(['ALL']);
  const dispatch = useDispatch();
  const { apiCapacityPlanningService } = useApiService();
  const getDomainList = async () => {
    const Apiresponse = await apiCapacityPlanningService.getLatestTimeStamp();
    const domainData = Apiresponse.data;
    setDomainList(domainData);
  };

  const dropdownOptions = [
    { value: 'ALL', label: 'ALL' },
    ...domainList.map((domain) => ({
      value: domain.domainname,
      label: domain.domainname,
    })),
  ];

  const handleDomainChange = (selectedValue: string[]) => {
    let newSelection = [...selectedValue];
    if (selectedValue.includes('ALL')) {
      if (selectedDomain.includes('ALL')) {
        newSelection = selectedValue.filter((val) => val !== 'ALL');
      } else {
        newSelection = ['ALL'];
      }
    } else if (selectedValue.length === 0 || selectedValue.length === domainList.length) {
      newSelection = ['ALL'];
    }

    setSelectedDomain(newSelection);

    let selectedIds: number[] = [];
    if (newSelection.includes('ALL') || newSelection.length === domainList.length) {
      selectedIds = domainList.map((d) => d.domainid);
    } else {
      selectedIds = domainList.filter((d) => newSelection.includes(d.domainname)).map((d) => d.domainid);
    }
    dispatch(setSelectedDomainIds(selectedIds));
  };

  useEffect(() => {
    getDomainList();
  }, []);

  return (
    <NblFlexContainer alignItems="center" width="auto" height="2.5rem" alignSelf="start">
      <NblTypography variant="subtitle2" color="shade1">
        Domain
      </NblTypography>
      <NblDropdown options={dropdownOptions} value={selectedDomain} onChange={handleDomainChange} multiple={true}></NblDropdown>
    </NblFlexContainer>
  );
};

export default CplanDomainDropdown;
