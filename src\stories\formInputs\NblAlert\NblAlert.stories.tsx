//eslint-disable-next-line no-unused-vars
import { Meta, StoryObj } from '@storybook/react';
import NebulaTheme from 'NebulaTheme';
import { ComponentProps } from 'react';
import NblAlert from 'sharedComponents/NblFormInputs/NblAlert';

type StoryProps = ComponentProps<typeof NblAlert>;
type Story = StoryObj<StoryProps>;

const meta: Meta<StoryProps> = {
  title: 'FormInputs/NblAlert',
  component: NblAlert,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: { options: ['warning', 'success', 'info', 'error'] },
    title: { control: 'text' },
    message: { control: 'text' },
  },
};

export default meta;

export const Alert: Story = {
  args: {
    variant: 'warning',
    title: 'Warning',
    message: 'Template has changed, Download new Template',
  },
  render: (args) => {
    return (
      <NebulaTheme>
        <NblAlert {...args} />
      </NebulaTheme>
    );
  },
};
