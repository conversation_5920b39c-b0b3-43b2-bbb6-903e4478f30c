import * as yup from 'yup';
import { yupMatchesParams } from 'utils/common';

export const validationSchema = yup.object().shape({
  hostName: yup
    .string()
    .matches(yupMatchesParams.fqdn.pattern, yupMatchesParams.fqdn.errorMessage)
    .required('Device Name (FQDN) is required'),
  organizationId: yup.string().required('Organization is required'),
  snmpCredentialId: yup.string().required('SNMP Credential is required'),
  collectorGroupId: yup.string().required('Collector is required'),
});
