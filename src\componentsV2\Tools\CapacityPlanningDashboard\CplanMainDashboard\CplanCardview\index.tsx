import React, { useCallback, useMemo } from 'react';
import { Grid, useMediaQuery, useTheme } from '@mui/material';
import { useEffect, useState } from 'react';
// eslint-disable-next-line no-unused-vars
import { CalculatedVropsResource, Facility } from '../../utils/types';
import { sortCalcResourcesByMetric } from '../../utils/utilization';
import PaginationComponent from '../../components/PaginationComponent';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import TableExport from '../../components/TableExport';
import NoData from '../../components/NoData';
import { exportFormats } from '../../utils/constant';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'NebulaTheme/type';
import { setOverviewResource } from 'store/reducers/capacityplanning';
import { CalculateVropsResource } from '../../utils/utilization';
import { useApiService } from 'api/ApiService/context';
import NblFlexContainer from 'sharedComponents/NblContainers/NblFlexContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import NblDivider from 'sharedComponents/NblDivider';
import CplanCard from './CplanCard';
import CplanDropdown from '../../components/CplanDropdown';
import { showSpinner } from '../../../../../store/reducers/spinner';
import { sortLabels } from '../../utils/constant';
import CplanDomainDropdown from '../../components/CplanDomainDropdown';

const CplanCardView: React.FunctionComponent = () => {
  const theme: NebulaTheme = useTheme();
  const matchDown = useMediaQuery(theme.breakpoints.down('2K'));
  const dispatch = useDispatch();
  const { apiCapacityPlanningService } = useApiService();
  const hours = useSelector((state: State) => state.capacityPlanning.hours);
  const overviewResource = useSelector((state: State) => state.capacityPlanning.overviewResource);
  const [calcResources, setCalcResources] = useState<CalculatedVropsResource[] | []>([]);
  const [facilities, setFacilities] = useState([] as Facility[]);
  const [selectedSortMetric, setSelectedSortMetric] = useState(sortLabels[0]);
  const [selectedRegion, setSelectedRegion] = useState('ALL');
  const [page, setPage] = useState(1);
  const rowsPerPage = 9;
  const domainIds = useSelector((state: State) => state.capacityPlanning.selectedDomainIds);

  const getCalculatedResources = useCallback(
    async (hours: number) => {
      dispatch(showSpinner({ status: true, message: 'Fetching Vrops resources' }));
      const resources = (await apiCapacityPlanningService.getResources(hours)).data;
      const calculatedResources: CalculatedVropsResource[] = resources.map((res) => CalculateVropsResource(res));
      setCalcResources(calculatedResources);
      if (overviewResource.resourceid) {
        const hoveredResource = calculatedResources.find((res) => res.resourceid === overviewResource.resourceid);
        dispatch(setOverviewResource(hoveredResource));
      } else {
        const hoveredResource = calculatedResources[calculatedResources.length - 1];
        dispatch(setOverviewResource(hoveredResource));
      }
      dispatch(showSpinner({ status: false, message: '' }));
    },

    [apiCapacityPlanningService, hours]
  );

  useEffect(() => {
    getCalculatedResources(hours);
  }, [hours, getCalculatedResources]);

  const getFacilities = useCallback(async () => {
    const Apiresponse = await apiCapacityPlanningService.getFacilities();
    const response = Apiresponse.data;
    setFacilities(response);
  }, [apiCapacityPlanningService]);

  useEffect(() => {
    getFacilities();
  }, [getFacilities]);

  const handlePageChange = (event: any, value: number) => {
    setPage(value);
  };

  const getUniqueRegionOptions = useMemo(() => {
    const regions = [...new Set(facilities.map((facility) => facility.region))];
    const regionsOptions = regions.map((region) => {
      return { label: region, value: region };
    });
    return [{ label: 'ALL', value: 'ALL' }, ...regionsOptions];
  }, [facilities]);

  const metricLabels = sortLabels.map((metric: string) => {
    return {
      label: metric,
      value: metric,
    };
  });

  const facilityMap = useMemo(() => {
    if (facilities.length === 0) return new Map();
    const map = new Map();
    facilities?.forEach((fac) => {
      fac.resources &&
        fac.resources.forEach((r) => {
          map.set(r.resourceid, { facilityId: fac.facilityid, facilityName: fac.facilityname });
        });
    });
    return map;
  }, [facilities]);

  const filteredFacilityIdsByRegion = useMemo(() => {
    if (selectedRegion === 'ALL') return null;
    return new Set(facilities.filter((fac) => fac.region === selectedRegion).map((fac) => fac.facilityid));
  }, [selectedRegion, facilities]);

  const calcResourcesWithFac: CalculatedVropsResource[] = useMemo(() => {
    return calcResources.map((res) => {
      const facilityData = facilityMap.get(res.resourceid);
      return facilityData ? { id: res.resourceid, ...res, ...facilityData } : { id: res.resourceid, ...res };
    });
  }, [calcResources, facilityMap]);
  // Don't modify above logics unless its needed

  const regionFilteredResources = useMemo(() => {
    setPage(1);
    return calcResourcesWithFac.filter((res) => res?.facilityId && filteredFacilityIdsByRegion?.has(res?.facilityId));
  }, [filteredFacilityIdsByRegion]);

  let pageLength: number;
  // Add you filter/sort functions above (e.g. regionFilteredResources) and assign return to the result in filteredCalcResources

  const filteredCalcResources = (() => {
    let result: CalculatedVropsResource[] = calcResourcesWithFac;
    result = filteredFacilityIdsByRegion ? regionFilteredResources : calcResourcesWithFac;
    result = result.filter((res) => domainIds.includes(res.domainid));
    result = sortCalcResourcesByMetric(result, selectedSortMetric);
    pageLength = result.length;

    result = result?.slice((page - 1) * rowsPerPage, page * rowsPerPage);

    // Apply your future filtering sorting logic here to result variable only
    return result;
  })();

  return (
    <>
      <NblFlexContainer direction="column">
        <NblFlexContainer direction="row" height="auto">
          <CplanDomainDropdown />
          <NblDivider orientation="vertical" color={theme.palette.primary.shade6} length="70%" />
          <CplanDropdown
            label="Sortby"
            options={metricLabels}
            value={selectedSortMetric}
            onChange={(metric: string) => {
              setSelectedSortMetric(metric);
              setPage(1);
            }}
          />
          <NblDivider orientation="vertical" color={theme.palette.primary.shade6} length="70%" />
          <CplanDropdown
            label={'Sites'}
            options={getUniqueRegionOptions}
            value={selectedRegion}
            onChange={(region: string) => {
              setSelectedRegion(region);
            }}
          />
          <NblDivider orientation="vertical" color={theme.palette.primary.shade6} length="70%" />

          {calcResources.length !== 0 && (
            <TableExport
              downloadOptions={[exportFormats.Excel, exportFormats.CSV, exportFormats.JPEG, exportFormats.PDF]}
              exportSectionId="exportSection"
            ></TableExport>
          )}
        </NblFlexContainer>
        <NblGridItem>
          {filteredCalcResources.length !== 0 ? (
            <Grid id="exportSection">
              <NblGridContainer
                spacingX={2}
                spacingY={2}
                columnMinWidth={matchDown ? '200px' : '330px'}
                columns="auto-fill"
                overflowY="hidden"
              >
                {filteredCalcResources &&
                  filteredCalcResources.map((data: CalculatedVropsResource) => <CplanCard key={data.resourceid} data={data} />)}
              </NblGridContainer>
              <NblFlexContainer justifyContent="center" padding="1rem 0 0 0">
                <PaginationComponent pageLength={pageLength} rowsPerPage={rowsPerPage} handlePageChange={handlePageChange} page={page} />
              </NblFlexContainer>
            </Grid>
          ) : (
            <NoData />
          )}
        </NblGridItem>
      </NblFlexContainer>
    </>
  );
};

export default CplanCardView;
