import NblDataViewer from 'sharedComponents/NblDataViewer';
import { RequestPayloadMap } from 'types/Interfaces/RequestDetails';
import RequestType from 'types/Enums/RequestType';
import NblViewDetailsAccordion from 'componentsV2/NblViewDetailsAccordion';

interface CreateSpecFormS3DetailsProps {
  formName: string;
  requestPayload: RequestPayloadMap[RequestType.SPEC_FLOW_S3];
}

const CreateSpecFormS3Details: React.FunctionComponent<CreateSpecFormS3DetailsProps> = ({ formName, requestPayload }) => {
  const data = [
    { name: 'Project', value: requestPayload.projectName },
    { name: 'IAC Project Name', value: requestPayload.iacProjectName },
    { name: 'Namespace Id', value: requestPayload.namespaceId },
    { name: 'Bucket Name', value: requestPayload.bucket },
    { name: 'Versioning', value: requestPayload.versioning },
  ];

  return (
    <NblViewDetailsAccordion hasDivider summary={formName}>
      <NblDataViewer data={data} />
    </NblViewDetailsAccordion>
  );
};

export default CreateSpecFormS3Details;
