// types
import { createSlice } from '@reduxjs/toolkit';
import { CatalogList, selectedCatalogs } from 'api/ApiService/type';
import { getQuarter } from 'componentsV2/Tools/NebulaDashboard/utility';

export type RequestFilterConfigType = {
  year: number;
  quarters: number[];
  allcatalogList: CatalogList[];
  allCatalog1: selectedCatalogs[];
  allCatalog2: selectedCatalogs[];
  allCatalog3: selectedCatalogs[];
  allCatalog4: selectedCatalogs[];
  selectedCatalog1: string[];
  selectedCatalog2: string[];
  selectedCatalog3: string[];
  selectedCatalog4: string[];
  isFilterApplied: boolean;
  levelSelected: number;
};

// initial state
const dateNow = new Date();
const initialState: RequestFilterConfigType = {
  year: dateNow.getFullYear(),
  allcatalogList: [],
  allCatalog1: [],
  allCatalog2: [],
  allCatalog3: [],
  allCatalog4: [],
  selectedCatalog1: [],
  selectedCatalog2: [],
  selectedCatalog3: [],
  selectedCatalog4: [],
  isFilterApplied: false,
  quarters: [getQuarter(dateNow)],
  levelSelected: 1,
};

// ==============================|| SLICE - MENU ||============================== //

const RequestFilterConfigSlice = createSlice({
  name: 'RequestFilterConfigSlice',
  initialState,
  reducers: {
    updateFilterConfigSlice(state, action) {
      if (action.payload?.year) state.year = action.payload.year;
      if (action.payload?.selectedCatalog1) state.selectedCatalog1 = action.payload.selectedCatalog1;
      if (action.payload?.selectedCatalog2) state.selectedCatalog2 = action.payload.selectedCatalog2;
      if (action.payload?.selectedCatalog3) state.selectedCatalog3 = action.payload.selectedCatalog3;
      if (action.payload?.selectedCatalog4) state.selectedCatalog4 = action.payload.selectedCatalog4;
      if (action.payload?.quarters) state.quarters = action.payload.quarters;
      state.isFilterApplied = true;
      state.levelSelected = action.payload.levelSelected;
    },
    clearFilterConfig(state, action) {
      (state.year = initialState.year), (state.quarters = [getQuarter(dateNow)]);
      state.selectedCatalog1 = action.payload.selectedCatalog1;
      state.selectedCatalog2 = action.payload.selectedCatalog2;
      state.selectedCatalog3 = action.payload.selectedCatalog3;
      state.selectedCatalog4 = action.payload.selectedCatalog4;
    },

    updateLevelWiseList(state, action) {
      if (action.payload?.allcatalogList) state.allcatalogList = action.payload.allcatalogList;
      if (action.payload?.allCatalog1) state.allCatalog1 = action.payload.allCatalog1;
      if (action.payload?.allCatalog2) state.allCatalog2 = action.payload.allCatalog2;
      if (action.payload?.allCatalog3) state.allCatalog3 = action.payload.allCatalog3;
      if (action.payload?.allCatalog4) state.allCatalog4 = action.payload.allCatalog4;
    },
  },
});

export default RequestFilterConfigSlice.reducer;

export const { updateFilterConfigSlice, clearFilterConfig, updateLevelWiseList } = RequestFilterConfigSlice.actions;
