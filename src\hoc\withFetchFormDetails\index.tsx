import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

import FallbackComponent from 'components/Error/FallbackComponent';
import ServiceRequestService from 'api/ApiService/ServiceRequestService';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import { AssetPayload } from 'types';
import NblRefreshProvider, { useNblRefresh } from 'sharedComponents/NblUtils/NblRefreshProvider';

const withFetchFormDetails = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  const WithFetchFormDetails = (props: Omit<P & { requestId: string; serviceRequestId?: string }, 'refetchServiceRequestDetails'>) => {
    /**
     * requestId: it holds the _id for service request collection
     * serviceRequestId: this is created based on request type
     */
    const { requestId, serviceRequestId } = props;
    const requestFetchId = serviceRequestId || requestId;
    const { refreshData } = useNblRefresh();

    const apiServiceRequestService = new ServiceRequestService();
    const dispatch = useDispatch();

    const [formDetails, setDetails] = useState<AssetPayload | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [showErrorComponent, setShowErrorComponent] = useState<boolean>(false);

    const fetchServiceRequestDetails = async () => {
      dispatch(showSpinner({ id: SPINNER_IDS.viewDetails, status: true, message: 'Fetching details...' }));
      apiServiceRequestService
        .getServiceRequestDetails(requestFetchId)
        .then((res) => {
          if (res.status) {
            setDetails(res.data);
          } else {
            setShowErrorComponent(true);
          }
        })
        .finally(() => {
          setIsLoading(false);
          dispatch(showSpinner({ id: SPINNER_IDS.viewDetails, status: false, message: '' }));
        });
    };

    useEffect(() => {
      if (requestFetchId) {
        fetchServiceRequestDetails();
      } else {
        console.log('Request ID missing');
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (showErrorComponent) {
      return <FallbackComponent message={'Something went wrong while fetching details...'} maxWidth={300} />;
    }

    return (
      <NblRefreshProvider
        refreshData={() => {
          refreshData();
          fetchServiceRequestDetails();
        }}
      >
        <WrappedComponent
          {...(props as P)}
          formDetails={formDetails}
          isFormDetailsLoading={isLoading}
          refetchServiceRequestDetails={fetchServiceRequestDetails}
        />
      </NblRefreshProvider>
    );
  };

  return WithFetchFormDetails;
};

export default withFetchFormDetails;
