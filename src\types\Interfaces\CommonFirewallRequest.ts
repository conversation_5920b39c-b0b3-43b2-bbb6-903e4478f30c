type ServiceCatalog = {
  catalogName: string;
  catalogType: string;
};

type Device = {
  deviceHostname: string;
  deviceCommonFwConfig: string;
  deviceConfigDiff: string;
  approved: boolean;
};

type ApprovalDetail = {
  approvalGroup: string;
  approvalStatus: string;
  approvedOrRejectedAt: string;
  approvedOrRejectedBy: string;
  level: number;
  rejectedReason: string | null;
};

type DownStreamResponse = {
  jobName: string;
  timestamp: string;
  response: {
    nebulaRequestId: string;
    gitlabRequestId: string;
    result: string;
  };
  result: string;
};

type ChildServiceRequestDetail = {
  id: string;
  nebulaRequestId?: string;
  editedAt: string | null;
  requestType: string;
  status: string;
  createdBy: string;
  requesterEmail: string;
  serviceRequestId: string;
  parentServiceRequestId: string;
  metadata: {
    serviceCatalog: ServiceCatalog;
  };
  payload: {
    dapTaskID: string;
    nebulaRequestId: string;
    date: string;
    result: string;
    devices: Device[];
  };
  startedAt: string;
  completedAt: string | null;
  approvalStatus: string;
  catalogStepsId: string;
  applicableSubTypes: any[];
  schemaVersion: number;
  approvalDetails: ApprovalDetail[];
  downstreamError: any[];
  createdAt: string;
  updatedAt: string;
  reviewedBy: string;
  executionHistory: any[];
};

type DownstreamResponseData = {
  gitlabDetails: {
    mergeRequestId: string;
    webUrl: string;
  };
};

type SystemUpdate = {
  downStreamResponse: DownStreamResponse[];
  errors: any[];
};

type RequestPayload = {
  project: string;
  requestCreator: string;
  requestName: string;
  commonApplication: string;
  applicationIpAddresses: string;
  inboundPortAndProtocol: string;
  outboundPortAndProtocal: string;
  reason: string;
  deeplinkUrl: string;
  gitLabBranchName: string;
  childServiceRequestsDetails: ChildServiceRequestDetail[];
  result?: string;
};

type ApprovalDetails = {
  approvalGroup: string;
  approvalStatus: string;
  approvedOrRejectedAt: string;
  approvedOrRejectedBy: string;
  level: number;
  rejectedReason: string | null;
};

export interface CommonFirewallRequest {
  id: string;
  editedAt: string | null;
  requestType: string;
  status: string;
  createdBy: string;
  requesterEmail: string;
  serviceRequestId: string;
  metadata: {
    serviceCatalog: ServiceCatalog;
  };
  payload: RequestPayload;
  startedAt: string;
  completedAt: string | null;
  approvalStatus: string;
  catalogStepsId: string;
  applicableSubTypes: any[];
  schemaVersion: number;
  approvalDetails: ApprovalDetails[];
  downstreamError: any[];
  createdAt: string;
  updatedAt: string;
  downstreamResponseData: DownstreamResponseData;
  systemUpdate: SystemUpdate;
  reviewedBy: string;
  childServiceRequestIds: string[];
  executionHistory: any[];
}
