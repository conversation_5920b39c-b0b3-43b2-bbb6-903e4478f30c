type DbDetails = {
  projectName: string;
  platformContext: { domainId: string; envId: string; applicationName: string; domainName: string; environmentName: string };
  datacenterDetails: [{ id: number; datacenter: string; network: { name: string }; availableHostNames: []; primary: boolean }];
  cloudId: string;
  appCode: string;
  vmOption: string;
  clusterSize: string;
  vmUserName: string;
  vmPassword: string;
  dbSize: string;
  description: string;
  sharding: false;
  datacenter: string;
  network: string;
  serverCount: number;
  primaryDataCenter: boolean;
  dbDetails: Array<{
    dbName: string;
    dbConfig: Array<{
      dbUserName: string;
      permissions: string[];
    }>;
  }>;
};
export default DbDetails;
