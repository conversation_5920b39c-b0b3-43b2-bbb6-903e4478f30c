// eslint-disable-next-line no-unused-vars
import { SelectChangeEvent } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import NblSelect from 'sharedComponents/NblFormInputs/NblSelect';
import NblTypography from 'sharedComponents/NblTypography';
import { setSelectedHour } from 'store/reducers/capacityplanning';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import NblFlexContainer from '../../../../../sharedComponents/NblContainers/NblFlexContainer';

interface TimeSliceDropdownProps {}

const TimeSliceDropdown: React.FC<TimeSliceDropdownProps> = () => {
  const dispatch = useDispatch();
  const hours = useSelector((state: State) => state.capacityPlanning.hours);
  const sunburstLevel = useSelector((state: State) => state.capacityPlanning.sunburstLevel);

  const optionsArray = [
    { value: 48, label: 'Daily' },
    { value: 24, label: '24 HR' },
    { value: 12, label: '12 HR' },
    { value: 8, label: '8 HR' },
    { value: 4, label: '4 HR' },
    { value: 1, label: 'Last Hour' },
  ];

  const handleChange = (event: SelectChangeEvent) => {
    const selectedHour = Number(event.target.value);
    dispatch(setSelectedHour(selectedHour));
  };

  return (
    <NblFlexContainer minWidth="60" justifyContent="flex-end">
      <NblFlexContainer width="auto" alignSelf="center">
        <NblTypography variant="body3" color="shade1">
          Select Time Slice :
        </NblTypography>
      </NblFlexContainer>
      <NblFlexContainer width="150px">
        <NblSelect
          name={'timeSlice'}
          options={sunburstLevel === 4 ? [{ value: 48, label: 'Daily' }] : optionsArray}
          placeholder={''}
          value={sunburstLevel === 4 ? 48 : hours}
          handleChange={handleChange}
          variant="outlined"
        />
      </NblFlexContainer>
    </NblFlexContainer>
  );
};

export default TimeSliceDropdown;
