// eslint-disable-next-line no-unused-vars
import { useApiService } from 'api/ApiService/context';
import { useEffect, useState } from 'react';
// eslint-disable-next-line no-unused-vars
import { ColumnData, TableRowData } from 'sharedComponents/NblTable';
import { CatalogPermissions } from 'types';
import { CatalogPermission } from 'types/Interfaces/OnboardGroupRequest';
import { reverseGroupPermissions } from 'utils/common';

interface AccessRequest {
  permissions: CatalogPermission[];
}

const useCatalogAccessTable = ({ permissions }: AccessRequest) => {
  const [catalogPermissions, setCatalogPermissions] = useState<CatalogPermissions>({ catalogNames: [], catalogRoles: [] });
  const { apiPermissionService } = useApiService();

  const fetchPermissions = () => {
    apiPermissionService.getCatalogPermissions().then((res) => {
      if (res.status) {
        setCatalogPermissions(res.data.catalogPermissions);
      }
    });
  };

  useEffect(() => {
    fetchPermissions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ColumnData[] = [
    {
      field: 'catalogName',
      headerName: 'Catalog',
      flex: 1,
    },
    {
      field: 'domainName',
      headerName: 'Domain',
      flex: 1,
    },
    {
      field: 'roleName',
      headerName: 'Role',
      flex: 1,
    },
  ];
  const result = reverseGroupPermissions(permissions);

  const rows: TableRowData[] = result?.map((item, index) => {
    const domainLabel = catalogPermissions.catalogNames
      .flatMap((catalog) => catalog.applicableDomain || [])
      .find((domain) => domain.domainId === item.domainId)?.domainName;
    const catalogLabel = catalogPermissions.catalogNames.find((catalog) => catalog._id === item?.catalogId)?.name;
    const roleLabel = catalogPermissions.catalogRoles.find((catalog) => catalog._id === item?.roleId)?.roleName;
    return {
      id: index,
      catalogName: catalogLabel || '',
      domainName: domainLabel || '',
      roleName: roleLabel || '',
    };
  });

  return { columns, rows };
};

export default useCatalogAccessTable;
