import React, { useEffect, useState } from 'react';
//eslint-disable-next-line no-unused-vars
import { LoadComponentDetails } from 'types';

const useLoadComponentDetails = (componentName: string, type: LoadComponentDetails = 'RequestDetails') => {
  const CatalogFormFolders = ['IaaS', 'PaaS', 'Tools', 'OnBoarding', 'PaaSv2', 'SpecFlow'];

  const [Component, setComponent] = useState<React.ComponentType<any> | null>(null);
  const [comingSoon, setComingSoon] = useState<boolean>(false); 

  const loadComponent = async (componentName: string) => {
    for (const folder of CatalogFormFolders) {
      try {
        const module =
          type === 'Form'
            ? await import(`componentsV2/${folder}/${componentName}`)
            : await import(`componentsV2/${folder}/${componentName}/${type}`);

        if (module?.default) {
          return { default: module.default };
        } else {
          console.log(`Failed to load component "${componentName}" inside ${folder} but no default export`);
        }
      } catch {
        console.log(`Failed to load component "${componentName}" inside ${folder}`);
      }
    }
    const componentNotFound = `Failed to load component "${componentName}" in any of the specified folders`;
    throw new Error(componentNotFound);
  };

  useEffect(() => {
    const fetchComponent = async () => {
      try {
        if (componentName) {
          const { default: component } = await loadComponent(componentName);
          setComponent(() => component);
        }
      } catch (err) {
        setComingSoon(true);
        console.log('Error loading component');
      }
    };

    if (componentName) {
      fetchComponent();
    }
  }, [componentName]);

  return {
    comingSoon,
    Component,
  };
};

export default useLoadComponentDetails;
