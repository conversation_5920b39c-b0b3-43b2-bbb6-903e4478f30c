import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { NblSwitchTabBar } from '.';
import NebulaThemeProvider from 'mock/NebulaThemeProvider';

describe('NblSwitchTabBar', () => {
  const tabs = [{ label: 'Tab 1' }, { label: 'Tab 2' }, { label: 'Tab 3', disabled: true }];

  function renderComponent(activeTab = 0, onTabChange = jest.fn()) {
    return render(
      <NebulaThemeProvider>
        <NblSwitchTabBar tabs={tabs} activeTab={activeTab} onTabChange={onTabChange} />
      </NebulaThemeProvider>
    );
  }

  test('renders all tabs', () => {
    renderComponent();
    expect(screen.getByText('Tab 1')).toBeInTheDocument();
    expect(screen.getByText('Tab 2')).toBeInTheDocument();
    expect(screen.getByText('Tab 3')).toBeInTheDocument();
  });

  test('sets the correct active tab based on activeTab prop', () => {
    renderComponent(1);

    const tab2 = screen.getByText('Tab 2').parentElement!;
    const computedStyle = window.getComputedStyle(tab2);

    expect(computedStyle.backgroundColor).not.toBe('');
  });

  test('changes active tab when clicked and calls onTabChange', () => {
    const onTabChange = jest.fn();
    renderComponent(0, onTabChange);

    const tab2 = screen.getByText('Tab 2');
    fireEvent.click(tab2);

    expect(onTabChange).toHaveBeenCalledWith('Tab 2');
  });

  test('does not change active tab or call onTabChange when clicking a disabled tab', () => {
    const onTabChange = jest.fn();
    renderComponent(0, onTabChange);

    const disabledTab = screen.getByText('Tab 3');
    fireEvent.click(disabledTab);

    expect(onTabChange).not.toHaveBeenCalledWith('Tab 3');
  });

  test('handles missing onTabChange gracefully', () => {
    renderComponent();
    const tab2 = screen.getByText('Tab 2');
    fireEvent.click(tab2);
    // No error = pass
  });

  test('updates internal state when activeTab prop changes', () => {
    const { rerender } = render(
      <NebulaThemeProvider>
        <NblSwitchTabBar tabs={tabs} activeTab={0} />
      </NebulaThemeProvider>
    );

    const tab1 = screen.getByText('Tab 1').parentElement!;
    expect(window.getComputedStyle(tab1).backgroundColor).not.toBe('');

    rerender(
      <NebulaThemeProvider>
        <NblSwitchTabBar tabs={tabs} activeTab={1} />
      </NebulaThemeProvider>
    );

    const tab2 = screen.getByText('Tab 2').parentElement!;
    expect(window.getComputedStyle(tab2).backgroundColor).not.toBe('');
  });

  test('disabled tab has correct tabIndex and does not respond to events', () => {
    renderComponent();

    const disabledTab = screen.getByText('Tab 3').parentElement;
    expect(disabledTab).toHaveAttribute('tabIndex', '-1');
    expect(disabledTab).toHaveAttribute('role', 'button');
  });
});
