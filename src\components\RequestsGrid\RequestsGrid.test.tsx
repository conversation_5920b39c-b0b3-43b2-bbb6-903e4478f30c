import { act, render } from '@testing-library/react';
import ThemeProvider from 'mock/ThemeProvider';

import RequestsGrid from '.';
import { PendingRequestColumns, PendingRequestRows } from 'mock/Requests';

describe('Request Tabular View', () => {
  test('Should load pending request data in tabular view with page title and reset filters button', async () => {
    const mockFetchData = jest.fn();
    const mockSetViewDetailsDialog = jest.fn();
    const viewDetailsDialog = { open: false, id: '', serviceRequestId: '' };

    const { getByText } = await act(async () =>
      render(
        <ThemeProvider>
          <RequestsGrid
            isLoading={false}
            rows={PendingRequestRows}
            columns={PendingRequestColumns}
            title={'Pending Requests'}
            fetchData={mockFetchData}
            viewDetailsDialog={viewDetailsDialog}
            setViewDetailsDialog={mockSetViewDetailsDialog}
          />
        </ThemeProvider>
      )
    );

    expect(getByText('Pending Requests')).toBeInTheDocument();
    const resetButton = getByText('Reset Filters');
    expect(resetButton).toBeEnabled();
  });
});
