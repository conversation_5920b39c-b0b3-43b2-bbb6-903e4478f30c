import React from 'react';
import { useTheme } from '@mui/material';

import NblBorderContainer from 'sharedComponents/NblContainers/NblBorderContainer';
import { NblGridContainer, NblGridItem } from 'sharedComponents/NblContainers/NblGridContainer';
import StatsWidgets from 'componentsV2/LandingPageMetrics/StatsWidgets';
import RequestsGrid from 'componentsV2/LandingPageMetrics/RequestsGrid';
import FavouriteTools from 'componentsV2/LandingPageMetrics/FavouriteTools';
import { NebulaTheme } from 'NebulaTheme/type';
import useMediaQuery from 'hooks/useMediaQuery';
import FilterDropdown from 'componentsV2/LandingPageMetrics/FilterDropdown';

const LandingPage = () => {
  //Hooks
  const favAndUpdateRowSpan = useMediaQuery({ fav: 7, updates: 4 }, { fav: 7, updates: 3 });
  //Local
  const gridItemRowSpan = {
    requestsGrid: 7,
    ...favAndUpdateRowSpan,
  };

  //JSX
  return (
    <NblGridContainer columns={12} rows={12} spacing={2}>
      <NblGridItem width="auto" colspan={12}>
        <FilterDropdown />
      </NblGridItem>
      <StatsWidgets />
      <NblGridItem colspan={9} rowspan={gridItemRowSpan.requestsGrid}>
        <BorderContainer>
          <RequestsGrid />
        </BorderContainer>
      </NblGridItem>
      <NblGridItem colspan={3} rowspan={gridItemRowSpan.fav}>
        <BorderContainer>
          <FavouriteTools />
        </BorderContainer>
      </NblGridItem>
    </NblGridContainer>
  );
};

const BorderContainer = ({ children }: { children: React.ReactElement }) => {
  const theme = useTheme<NebulaTheme>();
  const gridBgColor = theme.palette.secondary.main;
  return <NblBorderContainer backgroundColor={gridBgColor}>{children}</NblBorderContainer>;
};

export default LandingPage;
