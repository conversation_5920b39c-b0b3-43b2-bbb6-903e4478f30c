import React, { useEffect, useRef, useState } from 'react';
import Bar1 from './Bars/Bar1';
import Bar2 from './Bars/Bar2';
import Bar3 from './Bars/Bar3';
//eslint-disable-next-line no-unused-vars
import { ItemProps, NblSideBarProps, SideBarMouseHandlerType } from './interface';
import { StyledSideBar } from './styled';
import { BarItem } from './BarItem';

const NblSideBar: React.FC<NblSideBarProps> = ({
  bar1Items,
  bar2Items,
  bar3Items,
  onLevel1Click,
  onMenuItemClick,
  onLogoClick = () => {},
  onLevel2Click,
  onLevel4Click,
  defaultLevel1,
}) => {
  //Refs
  const sidebarRef = useRef<HTMLDivElement | null>(null);
  //States
  const [selectedLevel1, setSelectedLevel1] = useState<ItemProps>(new BarItem());
  const [selectedLevel1Local, setSelectedLevel1Local] = useState<ItemProps>(selectedLevel1);
  const [selectedLevel2, setSelectedLevel2] = useState<ItemProps>(new BarItem());
  const [selectedMenu, setSelectedMenu] = useState<ItemProps>(new BarItem());
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [bar2ItemsPosition, setBar2ItemsPosition] = useState(0);
  const [bar2ItemsPositionLocal, setBar2ItemsPositionLocal] = useState(0);
  const [bar3ItemsPosition, setBar3ItemsPosition] = useState(0);
  const [level1Clicked, setLevel1Clicked] = useState(false);

  //Side Effects
  useEffect(() => {
    if (defaultLevel1) setSelectedLevel1(defaultLevel1);
  }, [defaultLevel1]);

  //Handlers
  function onMouseEnter() {
    setSidebarExpanded(true);
  }

  function onMouseLeave() {
    if (!level1Clicked) {
      setSelectedLevel1(new BarItem());
    } else {
      setSelectedLevel1(selectedLevel1Local);
      setBar2ItemsPosition(bar2ItemsPositionLocal);
      onLevel1Click(selectedLevel1Local, 'expand');
    }
    setSelectedLevel2(new BarItem());
    setSidebarExpanded(false);
  }

  function onTabout(e: React.FocusEvent<HTMLDivElement>) {
    setSidebarExpanded(sidebarRef?.current?.contains?.(e.relatedTarget) || false);
  }

  function onLogoClickHandler() {
    setSelectedLevel1(new BarItem());
    setSelectedLevel1Local(new BarItem());
    setSelectedLevel2(new BarItem());
    setSelectedMenu(new BarItem());
    onLogoClick();
  }

  function level1ClickHandler(level1: ItemProps, position: number, handlerType: SideBarMouseHandlerType) {
    if (handlerType === 'expand') {
      if (!sidebarExpanded) setSidebarExpanded(true);
      setSelectedLevel2(new BarItem());
      setSelectedLevel1(level1);
      setBar2ItemsPosition(position);
    } else {
      setLevel1Clicked(true);
      setSelectedLevel1Local(level1);
      setBar2ItemsPositionLocal(bar2ItemsPosition);
      setSelectedMenu(new BarItem());
      setSidebarExpanded(false);
    }
    onLevel1Click(level1, handlerType);
  }

  function level2ClickHandler(level2: ItemProps, position: number, handlerType: SideBarMouseHandlerType) {
    if (handlerType === 'expand') {
      setSelectedLevel2(level2);
      setBar3ItemsPosition(position);
    } else {
      setLevel1Clicked(true);
      setSelectedLevel2(new BarItem());
      setSelectedMenu(new BarItem());
      setSidebarExpanded(false);
    }
    onLevel2Click?.(selectedLevel1, level2, handlerType);
  }

  function level4ClickHandler(level3Item: ItemProps, level4Item: ItemProps) {
    onLevel4Click?.(selectedLevel1, selectedLevel2, level3Item, level4Item);
    setSidebarExpanded(false);
    setSelectedLevel2(new BarItem());
    setSelectedMenu(new BarItem());
    setLevel1Clicked(true);
  }

  function menuClickHandler(menuItem: ItemProps) {
    setSidebarExpanded(false);
    setLevel1Clicked(false);
    setSelectedLevel1(new BarItem());
    setSelectedLevel2(new BarItem());
    setSelectedMenu(menuItem);
    onMenuItemClick(menuItem);
  }

  //Jsx
  return (
    <StyledSideBar expanded={sidebarExpanded} onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} onBlur={onTabout} ref={sidebarRef}>
      <Bar1
        itemsList={bar1Items}
        onLevel1Click={level1ClickHandler}
        onMenuItemClick={menuClickHandler}
        selectedLevel1={selectedLevel1}
        sidebarExpanded={sidebarExpanded}
        selectedMenu={selectedMenu}
        onLogoClick={onLogoClickHandler}
      />
      <Bar2
        level={2}
        loading={bar2Items.loading}
        itemsList={bar2Items.items}
        onLevel2Click={level2ClickHandler}
        selectedLevel1={selectedLevel1}
        selectedLevel2={selectedLevel2}
        sidebarExpanded={sidebarExpanded}
        bar2ItemsPosition={bar2ItemsPosition}
      />
      <Bar3
        level={3}
        bar3Items={bar3Items}
        onLevel4Click={level4ClickHandler}
        selectedLevel2={selectedLevel2}
        sidebarExpanded={sidebarExpanded}
        bar3ItemsPosition={bar3ItemsPosition}
      />
    </StyledSideBar>
  );
};

export default NblSideBar;
