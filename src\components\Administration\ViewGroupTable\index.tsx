import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
// eslint-disable-next-line no-unused-vars
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Box, useMediaQuery, useTheme } from '@mui/material';

import { ADMIN_TILE_PERMISSION_TYPE } from 'utils/constant';
import withAdminPermissions from 'hoc/withAdminPermissions';
import { showSpinner, SPINNER_IDS } from 'store/reducers/spinner';
import DataGridTable from 'components/DataGridTable';
import PermissionService from 'api/ApiService/PermissionService';
import ActionsColumn from 'components/Administration/ActionsColumn';
// eslint-disable-next-line no-unused-vars
import { AdminGridProps, GroupDataProps, AdminComponent } from 'types';
import { dateFormatter, getAdminColumnWidth } from 'utils/common';
// eslint-disable-next-line no-unused-vars
import { NebulaTheme } from 'mantis/themes/type';

interface ViewGroupsFormProps extends AdminGridProps {}

const ViewGroupTable: AdminComponent<ViewGroupsFormProps> = ({ permissions }) => {
  const apiPermissionService = new PermissionService();
  const theme: NebulaTheme = useTheme();
  const isSmaller = useMediaQuery(theme.breakpoints.down('xl'));
  const dispatch = useDispatch();
  const [viewGroupListData, setGroupListData] = useState<GroupDataProps[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchGroupsList = () => {
    setIsLoading(true);
    dispatch(showSpinner({ id: SPINNER_IDS.groups, status: true, message: 'Fetching groups...' }));
    apiPermissionService
      .getGroups()
      .then((res) => {
        if (res.status) {
          setGroupListData(
            res.data.map((groupsData) => ({
              ...groupsData,
              id: groupsData._id,
              createdAt: groupsData?.createdAt ? dateFormatter(groupsData.createdAt) : '',
              updatedAt: groupsData?.updatedAt ? dateFormatter(groupsData.updatedAt) : '',
            }))
          );
        }
      })
      .finally(() => {
        setIsLoading(false);
        dispatch(showSpinner({ id: SPINNER_IDS.groups, status: false, message: '' }));
      });
  };

  useEffect(() => {
    fetchGroupsList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onDeleteHandler = (deleteApiParam: string) => {
    setIsLoading(true);
    apiPermissionService
      .deleteGroupPermissions(deleteApiParam)
      .then((res) => {
        if (res.status) {
          toast.success(res.data.message, {
            position: toast.POSITION.BOTTOM_CENTER,
          });
          fetchGroupsList();
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const columns: GridColDef[] = [
    {
      field: 'groupName',
      headerName: 'Group Name',
      flex: 1,
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
    },
    { field: 'createdAt', headerName: 'Created Date', flex: 1 },
    { field: 'updatedAt', headerName: 'Updated Date', flex: 1 },
    {
      field: 'actions',
      headerName: 'Actions',
      disableColumnMenu: true,
      sortable: false,
      width: getAdminColumnWidth(isSmaller),
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        const {
          row: { groupName },
        } = params;
        return (
          <ActionsColumn
            editUrl={`/administration/groups/${ encodeURIComponent(groupName)}`}
            onDeleteHandler={() => onDeleteHandler(groupName)}
            permissions={permissions}
            disableDelete
          />
        );
      },
    },
  ];

  return (
    <Box sx={{ mx: 'auto', mb: 3 }}>
      <DataGridTable loading={isLoading} rows={viewGroupListData} columns={columns} refreshHandler={fetchGroupsList} />
    </Box>
  );
};

ViewGroupTable.type = ADMIN_TILE_PERMISSION_TYPE.grid;

export default withAdminPermissions(ViewGroupTable);
