import { useSelector } from 'react-redux';
// eslint-disable-next-line no-unused-vars
import { CatalogItem } from 'types';
// eslint-disable-next-line no-unused-vars
import { State } from 'store/reducers/type';
import { useCallback } from 'react';

//Types
type Levels = 1 | 2 | 3 | 4;

type CatalogLevel = { [key: string]: string[] };

type CatalogLevels = {
  [key: string]: {
    1: CatalogLevel;
    2: CatalogLevel;
    3: CatalogLevel;
    4: CatalogLevel;
  };
};

type CatalogItems = {
  1: CatalogItem[];
  2: CatalogItem[];
  3: CatalogItem[];
  4: CatalogItem[];
};

//Application level Memoization
let cache: CatalogLevels = {
  requestType: {
    1: {},
    2: {},
    3: {},
    4: {},
  },
};

let catalogItems: { [key: string]: CatalogItems } = {
  requestType: {
    1: [],
    2: [],
    3: [],
    4: [],
  },
};

//Memoized function
function memoizedGroupRequestToTopLevel(
  topLevel: Levels,
  catalogItem: CatalogItem[],
  propertyName: keyof CatalogItem = 'requestType',
  topLevelPropName: keyof CatalogItem = 'name'
) {
  const cacheKey = topLevelPropName + propertyName;
  if (JSON.stringify(catalogItems[cacheKey]?.[topLevel]) === JSON.stringify(catalogItem) && cache[cacheKey]?.[topLevel]) {
    //Return memoized
    return cache[cacheKey][topLevel];
  } else {
    //Final Grouped Variable
    const grouped: CatalogLevel = {};

    //Utils
    const pushtoGroup = (key: string, value: any) => {
      if (!grouped[key]) grouped[key] = [];
      grouped[key].push(value);
    };

    const traverse = (subCatalogs: CatalogItem[], topLevelName: string) => {
      for (const catalog of subCatalogs) {
        if (catalog?.subCatalogs?.length) traverse(catalog.subCatalogs, topLevelName);
        else if (catalog[propertyName]) pushtoGroup(topLevelName, catalog[propertyName]);
      }
    };

    for (const catalog of catalogItem) {
      const topLevelName = String(catalog[topLevelPropName]);
      if (catalog?.subCatalogs?.length) traverse(catalog.subCatalogs, topLevelName);
      else if (catalog[propertyName]) pushtoGroup(topLevelName, catalog[propertyName]);
    }

    //Setting latest values to cache
    catalogItems[cacheKey] = {} as CatalogItems;
    cache[cacheKey] = {} as CatalogLevels[''];
    catalogItems[cacheKey][topLevel] = catalogItem;
    cache[cacheKey][topLevel] = grouped;

    //Return computed
    return grouped;
  }
}

const useCatalogRequests = () => {
  //Hooks
  const { allCatalogItems } = useSelector((state: State) => state.catalogs);

  //Memoization
  const getCatalogItemsForLevel = useCallback(
    (topLevel: Levels) => {
      let filteredCatalogItems = allCatalogItems;
      if (topLevel > 1) {
        filteredCatalogItems = filteredCatalogItems.map((level1) => level1.subCatalogs || []).flat();
        if (topLevel > 2) filteredCatalogItems = filteredCatalogItems.map((level2) => level2.subCatalogs || []).flat();
        if (topLevel > 3) filteredCatalogItems = filteredCatalogItems.map((level3) => level3.subCatalogs || []).flat();
      }
      return filteredCatalogItems;
    },
    [allCatalogItems]
  );

  const groupRequestType = (topLevel: Levels = 1, topLevelPropName: keyof CatalogItem = 'name') => {
    return memoizedGroupRequestToTopLevel(topLevel, getCatalogItemsForLevel(topLevel), 'requestType', topLevelPropName);
  };

  const groupCatalogItem = (topLevel: Levels = 1, topLevelPropName: keyof CatalogItem, propertyName: keyof CatalogItem) => {
    return memoizedGroupRequestToTopLevel(topLevel, getCatalogItemsForLevel(topLevel), propertyName, topLevelPropName);
  };

  return {
    groupRequestType,
    groupCatalogItem,
  };
};

export default useCatalogRequests;
